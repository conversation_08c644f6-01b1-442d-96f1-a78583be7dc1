/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ["class"],
    content: [
        "./src/**/*.{js,jsx,ts,tsx}",
        "./public/index.html"
    ],
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        extend: {
            fontFamily: {
                sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
                brand: ['Anton', 'ui-sans-serif', 'system-ui', 'sans-serif'],
            },
            backgroundImage: {
                'radial-gradient-vignette': 'radial-gradient(circle, transparent 50%, rgba(0, 0, 0, 0.7) 150%)',
            },
            height: {
                'safe-bottom': 'env(safe-area-inset-bottom, 0px)',
            },
            padding: {
                'safe-bottom': 'env(safe-area-inset-bottom, 0px)',
            },
            colors: {
                border: "hsl(var(--border))",
                input: "hsl(var(--input))",
                ring: "hsl(var(--ring))",
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                primary: {
                    DEFAULT: "hsl(var(--primary))",
                    foreground: "hsl(var(--primary-foreground))",
                    50: '#f0f9ff',
                    100: '#e0f2fe',
                    200: '#bae6fd',
                    300: '#7dd3fc',
                    400: '#38bdf8',
                    500: '#0ea5e9',
                    600: '#0284c7',
                    700: '#0369a1',
                    800: '#075985',
                    900: '#0c4a6e',
                },
                secondary: {
                    DEFAULT: "hsl(var(--secondary))",
                    foreground: "hsl(var(--secondary-foreground))",
                },
                destructive: {
                    DEFAULT: "hsl(var(--destructive))",
                    foreground: "hsl(var(--destructive-foreground))",
                },
                muted: {
                    DEFAULT: "hsl(var(--muted))",
                    foreground: "hsl(var(--muted-foreground))",
                },
                accent: {
                    DEFAULT: "hsl(var(--accent))",
                    foreground: "hsl(var(--accent-foreground))",
                },
                popover: {
                    DEFAULT: "hsl(var(--popover))",
                    foreground: "hsl(var(--popover-foreground))",
                },
                card: {
                    DEFAULT: "hsl(var(--card))",
                    foreground: "hsl(var(--card-foreground))",
                },
                // Keep existing custom colors
                'brand-blue': '#2563eb',
                'brand-light-blue': '#3b82f6',
                gold: {
                    50: '#FFFBEB',
                    100: '#FEF3C7',
                    200: '#FDE68A',
                    300: '#FCD34D',
                    400: '#FBBF24',
                    500: '#F59E42',
                    600: '#D97706',
                    700: '#B45309',
                    800: '#92400E',
                    900: '#78350F',
                },
                emerald: {
                    50: '#ECFDF5',
                    100: '#D1FAE5',
                    200: '#A7F3D0',
                    300: '#6EE7B7',
                    400: '#34D399',
                    500: '#10B981',
                    600: '#059669',
                    700: '#047857',
                    800: '#065F46',
                    900: '#064E3B',
                },
                sapphire: {
                    50: '#EFF6FF',
                    100: '#DBEAFE',
                    200: '#BFDBFE',
                    300: '#93C5FD',
                    400: '#60A5FA',
                    500: '#3B82F6',
                    600: '#2563EB',
                    700: '#1D4ED8',
                    800: '#1E40AF',
                    900: '#1E3A8A',
                },
                amethyst: {
                    50: '#F5F3FF',
                    100: '#EDE9FE',
                    200: '#DDD6FE',
                    300: '#C4B5FD',
                    400: '#A78BFA',
                    500: '#8B5CF6',
                    600: '#7C3AED',
                    700: '#6D28D9',
                    800: '#5B21B6',
                    900: '#4C1D95',
                },
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            boxShadow: {
                'glow-blue': '0 0 8px rgba(37, 99, 235, 0.6)', // Adjust the color and intensity as needed
            },
            keyframes: {
                "accordion-down": {
                    from: { height: 0 },
                    to: { height: "var(--radix-accordion-content-height)" },
                },
                "accordion-up": {
                    from: { height: "var(--radix-accordion-content-height)" },
                    to: { height: 0 },
                },
                "float-particle": {
                    "0%, 100%": { transform: "translateY(0) translateX(0)" },
                    "25%": { transform: "translateY(-10px) translateX(10px)" },
                    "50%": { transform: "translateY(-20px) translateX(0)" },
                    "75%": { transform: "translateY(-10px) translateX(-10px)" },
                },
                "pulse-slow": {
                    "0%, 100%": { transform: "scale(1)", opacity: "0.7" },
                    "50%": { transform: "scale(1.05)", opacity: "0.9" },
                },
                "pulse-light": {
                    "0%, 100%": { opacity: "0.6" },
                    "50%": { opacity: "1" },
                },
                "shimmer": {
                    "100%": { transform: "translateX(100%)" }
                },
                "progress-bar": {
                    "0%": { transform: "scaleX(0)" },
                    "100%": { transform: "scaleX(1)" }
                },
                "modal-slide-in": {
                    "0%": { transform: "translateY(20px)", opacity: "0" },
                    "100%": { transform: "translateY(0)", opacity: "1" }
                },
                "float": {
                    "0%, 100%": { transform: "translateY(0)" },
                    "50%": { transform: "translateY(-10px)" }
                },
                "bounce-slower": {
                    "0%, 100%": { transform: "translateY(0)" },
                    "50%": { transform: "translateY(-15px)" }
                },
                "float-delayed": {
                    "0%, 100%": { transform: "translateY(0)" },
                    "50%": { transform: "translateY(-8px)" }
                },
                "card-hover": {
                    "0%": { transform: "translateY(0)" },
                    "100%": { transform: "translateY(-8px)" }
                },
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
                "float-particle": "float-particle 8s ease-in-out infinite",
                "pulse-slow": "pulse-slow 4s ease-in-out infinite",
                "pulse-light": "pulse-light 2s ease-in-out infinite",
                "shimmer": "shimmer 2s infinite",
                "progress-bar": "progress-bar 5s linear",
                "modal-slide-in": "modal-slide-in 0.4s ease-out forwards",
                "float": "float 5s ease-in-out infinite",
                "bounce-slower": "bounce-slower 6s ease-in-out infinite",
                "float-delayed": "float-delayed 5s ease-in-out infinite 1s",
                "card-hover": "card-hover 0.3s ease-out forwards",
            },
        },
    },
    plugins: [
        require("tailwindcss-animate"),
        function({ addUtilities }) {
            const newUtilities = {
                '.custom-scrollbar': {
                    '&::-webkit-scrollbar': {
                        width: '6px',
                        height: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                        background: 'rgba(219, 234, 254, 0.1)',
                        borderRadius: '8px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                        background: 'linear-gradient(to bottom, #3b82f6, #4f46e5)',
                        borderRadius: '8px',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                        boxShadow: '0 0 5px rgba(59, 130, 246, 0.3)',
                        transition: 'all 0.3s ease',
                    },
                    '&::-webkit-scrollbar-thumb:hover': {
                        background: 'linear-gradient(to bottom, #2563eb, #4338ca)',
                        boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)',
                    },
                    '&::-webkit-scrollbar-thumb:active': {
                        background: 'linear-gradient(to bottom, #1d4ed8, #3730a3)',
                    },
                    'scrollbarWidth': 'thin',
                    'scrollbarColor': '#4f46e5 rgba(219, 234, 254, 0.1)',
                },
                '.scroll-shadow': {
                    'background': `
                        /* Shadow at the top */
                        linear-gradient(
                            to bottom,
                            rgba(255, 255, 255, 1) 0%,
                            rgba(255, 255, 255, 0.8) 5%,
                            rgba(255, 255, 255, 0) 15%
                        ) 0 0,
                        /* Shadow at the bottom */
                        linear-gradient(
                            to top,
                            rgba(255, 255, 255, 1) 0%,
                            rgba(255, 255, 255, 0.8) 5%,
                            rgba(255, 255, 255, 0) 15%
                        ) 0 100%,
                        /* Subtle blue glow at the top */
                        linear-gradient(
                            to bottom,
                            rgba(59, 130, 246, 0.05) 0%,
                            rgba(59, 130, 246, 0) 5%
                        ) 0 0,
                        /* Subtle blue glow at the bottom */
                        linear-gradient(
                            to top,
                            rgba(79, 70, 229, 0.05) 0%,
                            rgba(79, 70, 229, 0) 5%
                        ) 0 100%
                    `,
                    'backgroundRepeat': 'no-repeat',
                    'backgroundSize': '100% 40px, 100% 40px, 100% 15px, 100% 15px',
                    'backgroundAttachment': 'local, local, scroll, scroll',
                    'backgroundBlendMode': 'normal, normal, overlay, overlay',
                },
            }
            addUtilities(newUtilities, ['responsive', 'hover']);
        },
    ],
}
