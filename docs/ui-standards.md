# UI Standards for Mission X

This document outlines the UI standards derived from Home.js and Login.js that should be applied to the Mission Page UI.

## 1. Color Scheme

### Primary Colors
- **Indigo/Blue Gradient**: `from-indigo-600 to-blue-600` or `from-blue-600 to-indigo-600`
- **Indigo**: `indigo-600` (buttons, active states, links)
- **Blue**: `blue-600` (buttons, active states, links)

### Secondary Colors
- **Green**: `green-500` to `green-600` (success states)
- **Red**: `red-500` to `red-600` (error states)
- **Yellow/Amber**: `yellow-400` to `amber-500` (warning, achievements)
- **Purple**: `purple-500` to `purple-600` (special elements)

### Background Colors
- **Primary Background**: `bg-gradient-to-b from-indigo-50 to-white`
- **Card Background**: `bg-white` or `bg-white/80 backdrop-blur-md`
- **Secondary Background**: `bg-gray-50` or `bg-indigo-50`
- **Hero Background**: `bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800`

### Text Colors
- **Primary Text**: `text-gray-800` (headings), `text-gray-700` (body)
- **Secondary Text**: `text-gray-600` (less emphasis), `text-gray-500` (subtle text)
- **Accent Text**: `text-indigo-600`, `text-blue-600` (links, emphasis)
- **Light Text**: `text-white`, `text-blue-50` (on dark backgrounds)

## 2. Typography

### Font Sizes
- **Headings**: 
  - Large: `text-3xl md:text-4xl lg:text-5xl`
  - Medium: `text-2xl md:text-3xl`
  - Small: `text-xl md:text-2xl`
- **Body Text**: 
  - Regular: `text-base`
  - Small: `text-sm`
  - Extra Small: `text-xs`
- **Responsive Sizing**: Use different sizes for mobile/desktop with Tailwind breakpoints

### Font Weights
- **Bold**: `font-bold` (headings, important text)
- **Medium**: `font-medium` (buttons, semi-important text)
- **Regular**: Default weight (body text)

### Text Effects
- **Gradient Text**: `text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600`
- **Text Shadow**: `[text-shadow:0_2px_10px_rgba(0,0,0,0.3)]` (for text on images)

## 3. Layout & Spacing

### Container
- **Main Container**: `container mx-auto px-5 py-8`
- **Section Spacing**: `mb-8` (between major sections), `mb-6` (between related elements)
- **Element Spacing**: `space-y-4` (vertical), `space-x-4` (horizontal)

### Card Layout
- **Card Container**: `bg-white rounded-2xl shadow-md p-6 mb-8`
- **Card with Hover**: `hover:shadow-lg transition-all duration-300`
- **Card with Border**: `border border-gray-100` or `border border-indigo-100/50`

### Responsive Layout
- **Column to Row**: `flex flex-col md:flex-row`
- **Grid**: `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6`
- **Responsive Padding**: `p-4 sm:p-6 md:p-8`
- **Responsive Margin**: `mb-4 sm:mb-6 md:mb-8`

## 4. Components

### Buttons
- **Primary Button**: 
  ```
  px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium 
  hover:bg-indigo-700 transition-colors flex items-center
  ```
- **Secondary Button**: 
  ```
  px-4 py-2 bg-indigo-50 text-indigo-600 rounded-lg font-medium 
  hover:bg-indigo-100 transition-colors flex items-center
  ```
- **Button with Icon**: 
  ```
  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <!-- SVG path -->
  </svg>
  <span>Button Text</span>
  ```
- **Button Hover Effect**: `hover:-translate-y-0.5 active:translate-y-0`

### Form Elements
- **Input Field**: 
  ```
  w-full px-4 py-2 border border-gray-300 rounded-lg 
  focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
  ```
- **Input with Icon**: 
  ```
  <div className="relative">
    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <!-- Icon SVG -->
    </div>
    <input className="pl-10 ..." />
  </div>
  ```
- **Checkbox/Toggle**: Custom styled with peer classes

### Navigation
- **Main Navigation**: 
  ```
  bg-white/90 shadow-lg sticky top-0 z-50 backdrop-filter backdrop-blur-md
  ```
- **Nav Links**: 
  ```
  text-gray-600 hover:text-indigo-600 transition-colors relative group
  ```
- **Active Nav Link**: 
  ```
  text-indigo-600 font-medium relative
  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-600 rounded-full"></span>
  ```
- **Mobile Navigation**: Fixed bottom bar with icons and labels

## 5. Visual Effects

### Shadows
- **Regular Shadow**: `shadow-md`
- **Hover Shadow**: `hover:shadow-lg`
- **Inner Shadow**: `shadow-inner`

### Borders & Rounded Corners
- **Border Radius**: 
  - Small: `rounded-lg`
  - Medium: `rounded-xl`
  - Large: `rounded-2xl`
  - Full: `rounded-full`
- **Border**: `border border-gray-200` or `border border-indigo-100/50`

### Backdrop Blur
- **Glass Effect**: `backdrop-filter backdrop-blur-md bg-white/80`

### Gradients
- **Background Gradient**: 
  - `bg-gradient-to-b from-indigo-50 to-white`
  - `bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800`
- **Button Gradient**: `bg-gradient-to-r from-indigo-600 to-blue-600`
- **Text Gradient**: `text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600`

## 6. Animations & Transitions

### Transitions
- **Standard Transition**: `transition-all duration-300`
- **Color Transition**: `transition-colors duration-200`
- **Transform Transition**: `transition-transform duration-200`

### Hover Animations
- **Scale on Hover**: `hover:scale-105`
- **Translate on Hover**: `hover:-translate-y-0.5`
- **Color Change on Hover**: `hover:bg-indigo-700`

### Loading Animations
- **Spinner**: `animate-spin`
- **Pulse**: `animate-pulse`
- **Ping**: `animate-ping`

### Custom Animations
- **Shimmer Effect**: 
  ```
  <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent 
  via-white/20 to-transparent animate-shimmer transform -translate-x-full"></div>
  ```
- **Float Animation**: Custom keyframes for subtle floating motion
- **Fade In**: Custom keyframes for fade-in effects

## 7. Responsive Design

### Breakpoints
- **Mobile First**: Base styles for mobile, then add responsive classes
- **Tailwind Breakpoints**: `sm:`, `md:`, `lg:`, `xl:` prefixes

### Mobile Optimizations
- **Reduced Padding**: `p-4` on mobile, `p-6` or `p-8` on larger screens
- **Stacked Layout**: `flex-col` on mobile, `flex-row` on larger screens
- **Font Size Reduction**: Smaller text on mobile, larger on desktop
- **Hidden Elements**: `hidden md:block` to hide on mobile, show on desktop

## 8. Accessibility

### Focus States
- **Focus Ring**: `focus:ring-2 focus:ring-indigo-500 focus:outline-none`
- **Focus Visible**: `focus-visible:ring-2` for keyboard-only focus

### ARIA Attributes
- **aria-label**: For buttons without text
- **aria-current**: For active navigation items
- **aria-expanded**: For expandable sections

### Screen Reader Support
- **sr-only**: For text visible only to screen readers
- **aria-hidden**: For decorative elements

## 9. Special UI Patterns

### Cards with Hover Effects
- Scale and shadow changes on hover
- Subtle background color shifts

### Glassmorphism
- Backdrop blur with semi-transparent backgrounds
- Subtle borders and shadows

### Micro-interactions
- Button state changes on hover/active
- Form field focus states
- Subtle animations on user interaction

### Decorative Elements
- Subtle background patterns
- Gradient overlays
- Floating particles or shapes
