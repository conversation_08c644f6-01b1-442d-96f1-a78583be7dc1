{"name": "webapp", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.1.1", "@mui/material": "^7.1.1", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-slot": "^1.2.2", "@react-spring/web": "^10.0.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.80.5", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-virtual": "^3.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clientjs": "^0.2.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-flag-icons": "^1.5.19", "date-fns": "^4.1.0", "device-detector-js": "^3.0.3", "emoji-picker-react": "^4.12.2", "firebase": "^10.5.0", "formik": "^2.4.6", "framer-motion": "^12.9.4", "gsap": "^3.13.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "laravel-echo": "^2.1.5", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "luxon": "^3.6.1", "postcss-normalize": "^10.0.1", "pusher-js": "^8.4.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-icons": "^4.12.0", "react-intersection-observer": "^9.16.0", "react-parallax-tilt": "^1.7.298", "react-router-dom": "^7.5.2", "react-scripts": "5.0.1", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.5", "react-tsparticles": "^2.12.2", "react-use-gesture": "^9.1.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "tsparticles": "^3.8.1", "typescript": "^4.9.5", "url": "^0.11.4", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "test:auth": "node scripts/test-auth.js", "analyze": "node scripts/analyze-bundle.js", "analyze:webpack": "ANALYZE=true react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@tailwindcss/postcss": "^4.1.7", "assert": "^2.1.0", "autoprefixer": "^10.4.21", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chalk": "^4.1.2", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "node-fetch": "^2.6.7", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.6", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^4.1.7", "util": "^0.12.5"}}