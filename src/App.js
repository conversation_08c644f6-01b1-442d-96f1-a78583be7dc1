import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate, Outlet } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/ProfileContext';
import RTLProvider from './components/common/RTLProvider';
import EmailVerification from './components/auth/EmailVerification';
import EmailVerificationSuccess from './components/auth/EmailVerificationSuccess';
import EmailVerificationError from './components/auth/EmailVerificationError';
import OAuthCallback from './components/auth/OAuthCallback';
import AuthenticationHub from './components/auth/AuthenticationHub';
import './App.css';
import './styles/animations.css';
import { NotificationProvider } from './context/NotificationContext';
import ToastProvider, { useToast } from './components/common/ToastProvider';
import { PaymentProvider } from './contexts/PaymentContext';
import { OrderPaymentProvider } from './contexts/OrderPaymentContext';
import { HomepageProvider } from './contexts/HomepageContext';
import { FinancialProviders } from './features/FinancialProviders';
import LazyWelcomePage from './components/LazyWelcomePage';
import { LoadingProvider } from './contexts/LoadingContext';
import { PageLoader } from './components/ui/LoadingIndicator';
import ToastContainer from './components/notifications/ToastContainer';
import GiftToastContainer from './components/notifications/GiftToastContainer';
import orderAPI from './services/orderService';
import scheduledOrderService from './services/scheduledOrderService';
import * as LazyComponents from './lazyComponents';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import queryClient from './config/queryClient';
import EditProfileModal from './components/modals/EditProfileModal';
import TopUpPage from './pages/TopUpPage';
import BecomeTalent from './pages/BecomeTalent';
import { LazyMissionDetailPageHost } from './lazyComponents';
import OrderToast from './components/notifications/OrderToast';

// Dark mode utility functions
const getSystemPrefersDark = () => window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
const getStoredTheme = () => localStorage.getItem('theme');
const setStoredTheme = (theme) => localStorage.setItem('theme', theme);
const applyThemeClass = (theme) => {
  if (theme === 'dark') {
    document.documentElement.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
  }
};

function useDarkMode() {
  const [isDark, setIsDark] = useState(() => {
    const stored = getStoredTheme();
    if (stored) return stored === 'dark';
    return getSystemPrefersDark();
  });

  useEffect(() => {
    applyThemeClass(isDark ? 'dark' : 'light');
    setStoredTheme(isDark ? 'dark' : 'light');
  }, [isDark]);

  useEffect(() => {
    // On mount, sync with system if no stored theme
    const stored = getStoredTheme();
    if (!stored) {
      const prefersDark = getSystemPrefersDark();
      setIsDark(prefersDark);
      applyThemeClass(prefersDark ? 'dark' : 'light');
    }
    // Listen for system changes
    const mq = window.matchMedia('(prefers-color-scheme: dark)');
    const handler = (e) => {
      if (!getStoredTheme()) {
        setIsDark(e.matches);
        applyThemeClass(e.matches ? 'dark' : 'light');
      }
    };
    mq.addEventListener('change', handler);
    return () => mq.removeEventListener('change', handler);
  }, []);

  const toggle = () => setIsDark((d) => !d);
  return [isDark, toggle];
}

function DarkModeToggle() {
  const [isDark, toggle] = useDarkMode();
  return (
    <button
      onClick={toggle}
      className="fixed bottom-4 left-4 z-[1000] flex items-center gap-2 px-4 py-2 rounded-full shadow-lg bg-white/80 dark:bg-gray-900/80 border border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-100 backdrop-blur hover:bg-white dark:hover:bg-gray-800 transition-colors"
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      style={{ minWidth: 44, minHeight: 44 }}
    >
      {isDark ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m8.66-13.66l-.71.71M4.05 19.95l-.71.71M21 12h1M3 12H2m16.95 7.05l-.71-.71M4.05 4.05l-.71-.71M16 12a4 4 0 11-8 0 4 4 0 018 0z" /></svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12.79A9 9 0 1111.21 3a7 7 0 109.79 9.79z" /></svg>
      )}
      <span className="font-medium text-sm hidden sm:inline">{isDark ? 'Dark' : 'Light'} Mode</span>
    </button>
  );
}

// Create a protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, initialCheckDone } = useAuth();
  const location = useLocation();

  // For root path, always render children
  if (location.pathname === '/') {
    return children;
  }

  // Show loading while checking authentication status
  if (!initialCheckDone) {
    return <PageLoader message="Authenticating..." color="indigo" />;
  }

  // For all other routes, require authentication
  if (!isAuthenticated) {
    return <Navigate to="/" replace state={{ from: location }} />;
  }

  return children;
};

// Component to conditionally apply padding class
const AppLayout = () => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { success: showSuccessToast, error: showErrorToast, info: showInfoToast } = useToast();
  const authenticatedPaths = ['/home', '/orders', '/talent', '/explore', '/chat', '/profile', '/wallet', '/missions', '/coaches', '/bank-accounts', '/tdash'];
  const isAuthenticatedRoute =
    location.pathname === '/' ||
    authenticatedPaths.some(path => location.pathname.startsWith(path));

  // Handle order accept/reject from toast notifications
  const handleAcceptOrder = async (orderId, orderType) => {
    try {
      if (orderType && orderType.includes('scheduled')) {
        await scheduledOrderService.respondToScheduledOrder(orderId, { action: 'accept' });
      } else {
        await orderAPI.respondToOrder(orderId, { action: 'accept' });
      }
      console.log(`✅ Order ${orderId} accepted successfully`);
      showSuccessToast({ title: 'Order Accepted', description: `Order #${orderId} was accepted.` }, 4000);
    } catch (error) {
      console.error(`❌ Failed to accept order ${orderId}:`, error);
      showErrorToast({ title: 'Failed to accept order', description: 'Please try again.' }, 4000);
      throw error;
    }
  };

  const handleRejectOrder = async (orderId, orderType) => {
    try {
      if (orderType && orderType.includes('scheduled')) {
        await scheduledOrderService.respondToScheduledOrder(orderId, { action: 'reject' });
      } else {
        await orderAPI.respondToOrder(orderId, { action: 'reject' });
      }
      console.log(`✅ Order ${orderId} rejected successfully`);
      showInfoToast({ title: 'Order Rejected', description: `Order #${orderId} was rejected.` }, 4000);
    } catch (error) {
      console.error(`❌ Failed to reject order ${orderId}:`, error);
      showErrorToast({ title: 'Failed to reject order', description: 'Please try again.' }, 4000);
      throw error;
    }
  };


  return (
    <div className="App">
      <Outlet />
      {/* Order Toast Notifications - only show on authenticated routes */}
      {isAuthenticatedRoute && isAuthenticated && (
        <ToastContainer
          onAcceptOrder={handleAcceptOrder}
          onRejectOrder={handleRejectOrder}
        />
      )}
      {isAuthenticatedRoute && isAuthenticated && <GiftToastContainer />}
    </div>
  );
};

const EditProfileModalWrapper = () => {
  const navigate = useNavigate();
  return <EditProfileModal isOpen={true} onClose={() => navigate('/profile')} />;
};

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <ToastProvider>
          <AuthProvider>
            <NotificationProvider>
              <ProfileProvider>
                <LanguageProvider>
                  <RTLProvider>
                    <LoadingProvider>
                      <FinancialProviders>
                        <PaymentProvider>
                          <OrderPaymentProvider>
                            <HomepageProvider>
                              {/* Dark mode toggle always visible */}
                              <DarkModeToggle />
                              <Routes>
                                {/* Public routes - outside AppLayout */}
                                <Route path="/auth" element={<AuthenticationHub />} />
                                <Route path="/login" element={<AuthenticationHub />} />
                                <Route path="/signup" element={<AuthenticationHub initialMode="register" />} />
                                <Route path="/welcome" element={<LazyWelcomePage />} />
                                <Route path="/topup" element={<TopUpPage />} />
                                <Route path="/become-talent" element={<BecomeTalent />} />
                                <Route path="/payment-return" element={<LazyComponents.LazyPaymentReturnPage />} />

                                {/* Protected routes - inside AppLayout */}
                                <Route element={<AppLayout />}>
                                  <Route
                                    index
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyHome />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/home"
                                    element={
                                    <ProtectedRoute>
                                      <Navigate to="/" replace />
                                    </ProtectedRoute>
                                  }
                                />
                                  <Route
                                    path="/games"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Games..." color="indigo" />}>
                                          <LazyComponents.LazyAllGames />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talents/:talentId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Talent..." color="indigo" />}>
                                          <LazyComponents.LazyTalentProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderManagement />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders/:orderId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderDetails />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talent"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading Talents..." color="indigo" />}>
                                          <LazyComponents.LazyTalent />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/:userId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/edit-profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <EditProfileModalWrapper />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/setup"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfileSetup />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/chat"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyChat />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/explore"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyExplore />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your Wallet..." color="indigo" />}>
                                          <LazyComponents.LazyWallet />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturn />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturnPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/bank-accounts"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyBankAccountsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/availability"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyAvailabilityPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Mission routes */}
                                  <Route
                                    path="/missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading missions..." color="indigo" />}>
                                          <LazyComponents.LazyMissionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/my-missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your missions..." color="indigo" />}>
                                          <LazyComponents.LazyMyMissionsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/create"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading your mission..." color="indigo" />}>
                                          <LazyComponents.LazyMissionCreatePage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/edit"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionEditPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionDetailPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/applicants"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionApplicantsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  <Route
                                    path="/missions/:missionId/execute"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionExecutionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  <Route
                                    path="/missions/host/:missionId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyMissionDetailPageHost />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Authentication Routes */}
                                  <Route path="/email/verify" element={<EmailVerification />} />
                                  <Route path="/email-verification-success" element={<EmailVerificationSuccess />} />
                                  <Route path="/email-verification-error" element={<EmailVerificationError />} />
                                  <Route path="/oauth/:provider/callback" element={<OAuthCallback />} />
                                </Route>
                              </Routes>
                            </HomepageProvider>
                          </OrderPaymentProvider>
                        </PaymentProvider>
                      </FinancialProviders>
                    </LoadingProvider>
                  </RTLProvider>
                </LanguageProvider>
              </ProfileProvider>
            </NotificationProvider>
          </AuthProvider>
        </ToastProvider>
      </Router>
    </QueryClientProvider>
  );
};

export default App;

