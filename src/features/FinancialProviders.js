import React from 'react';
import { WalletProvider } from './wallet/contexts/WalletContext';
import { BankAccountProvider } from './banking/contexts/BankAccountContext';

/**
 * FinancialProviders Component
 *
 * This component combines all financial-related context providers (wallet and banking)
 * into a single provider component. It ensures proper nesting and initialization order
 * for all financial features.
 *
 * Usage:
 * ```jsx
 * import { FinancialProviders } from './features/FinancialProviders';
 *
 * function App() {
 *   return (
 *     <FinancialProviders>
 *       <YourAppComponents />
 *     </FinancialProviders>
 *   );
 * }
 * ```
 */
export const FinancialProviders = ({ children }) => {
  return (
    <WalletProvider>
      <BankAccountProvider>
        {children}
      </BankAccountProvider>
    </WalletProvider>
  );
};

/**
 * Individual provider exports for granular control
 */
export { WalletProvider } from './wallet/contexts/WalletContext';
export { BankAccountProvider } from './banking/contexts/BankAccountContext';

/**
 * Hook exports for easy access
 */
export { useWallet } from './wallet/contexts/WalletContext';
export { useBankAccounts } from './banking/contexts/BankAccountContext';

/**
 * Configuration for financial providers
 */
export const financialProvidersConfig = {
  // Global settings
  enableErrorBoundary: true,
  enableDevTools: process.env.NODE_ENV === 'development',

  // Wallet settings
  wallet: {
    autoInitialize: true,
    enableCaching: true,
    refreshOnFocus: true,
    balanceRefreshInterval: 30000, // 30 seconds
  },

  // Banking settings
  banking: {
    autoInitialize: true,
    enableCaching: true,
    refreshOnFocus: true,
    accountRefreshInterval: 60000, // 1 minute
  },

  // Integration settings
  integration: {
    syncBalanceOnWithdrawal: true,
    syncTransactionsOnPayment: true,
    enableCrossContextUpdates: true,
  }
};

/**
 * Provider utilities for debugging and testing
 */
export const financialProvidersUtils = {
  /**
   * Check if all financial providers are properly initialized
   */
  checkProviderStatus: () => {
    // This would be implemented to check context availability
    return {
      wallet: true,
      banking: true,
      integration: true
    };
  },

  /**
   * Reset all financial provider states
   * Useful for testing or user logout
   */
  resetAllProviders: () => {
    // This would trigger reset actions in all contexts
    console.log('All financial providers reset requested');
  },

  /**
   * Get combined financial state summary
   * Useful for debugging and analytics
   */
  getFinancialStateSummary: () => {
    if (process.env.NODE_ENV === 'development') {
      return {
        wallet: {
          balance: 'Available via useWallet hook',
          transactions: 'Available via useWallet hook',
          status: 'initialized'
        },
        banking: {
          accounts: 'Available via useBankAccounts hook',
          withdrawals: 'Available via useBankAccounts hook',
          status: 'initialized'
        }
      };
    }
    return null;
  }
};

export default FinancialProviders;
