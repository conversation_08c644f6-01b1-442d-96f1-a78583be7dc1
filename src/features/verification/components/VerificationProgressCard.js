import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faClock,
  faEnvelope,
  faIdCard,
  faUniversity,
  faSpinner,
  faArrowRight,
  faInfoCircle,
  faStar,
  faLock,
  faChevronRight,
  faTrophy
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../wallet/contexts/WalletContext';
import { createGlassCSS } from '../../wallet/components/common/WalletTheme';

/**
 * Enhanced Verification Progress Card
 * 
 * Compact verification progress display with visual indicators,
 * completion percentages, and quick action buttons.
 */
const VerificationProgressCard = ({
  variant = 'card', // 'card', 'compact', 'minimal'
  size = 'medium', // 'small', 'medium', 'large'
  showActions = true,
  showDetails = true,
  showBadges = true,
  onClick,
  onQuickAction,
  className = ''
}) => {
  const {
    verificationStatus,
    verificationLoading,
    verificationError,
    loadVerificationStatus
  } = useWallet();

  const [isHovered, setIsHovered] = useState(false);

  // Load verification status on mount
  useEffect(() => {
    if (!verificationStatus && !verificationLoading) {
      loadVerificationStatus();
    }
  }, [verificationStatus, verificationLoading, loadVerificationStatus]);

  // Get verification progress
  const getVerificationProgress = () => {
    if (!verificationStatus) {
      return {
        emailVerified: false,
        kycVerified: false,
        bankAccountsCount: 0,
        completedSteps: 0,
        totalSteps: 3,
        percentage: 0,
        level: 'Unverified',
        levelColor: 'red',
        nextStep: 'Verify your email address'
      };
    }

    const emailVerified = verificationStatus.email_verified || false;
    const kycVerified = verificationStatus.ekyc_verified || false;
    const bankAccountsCount = verificationStatus.bank_accounts_count || 0;

    let completedSteps = 0;
    if (emailVerified) completedSteps++;
    if (kycVerified) completedSteps++;
    if (bankAccountsCount > 0) completedSteps++;

    const totalSteps = 3;
    const percentage = (completedSteps / totalSteps) * 100;

    let level, levelColor, nextStep;
    if (completedSteps === 0) {
      level = 'Unverified';
      levelColor = 'red';
      nextStep = 'Verify your email address';
    } else if (completedSteps === 1) {
      level = 'Basic';
      levelColor = 'orange';
      nextStep = emailVerified ? 'Complete KYC verification' : 'Verify your email address';
    } else if (completedSteps === 2) {
      level = 'Verified';
      levelColor = 'green';
      nextStep = bankAccountsCount === 0 ? 'Add a bank account' : 'All steps completed';
    } else {
      level = 'Complete';
      levelColor = 'blue';
      nextStep = 'All verification steps completed';
    }

    return {
      emailVerified,
      kycVerified,
      bankAccountsCount,
      completedSteps,
      totalSteps,
      percentage,
      level,
      levelColor,
      nextStep
    };
  };

  const progress = getVerificationProgress();

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        icon: 'w-8 h-8 text-sm',
        title: 'text-sm font-semibold',
        subtitle: 'text-xs',
        badge: 'text-xs px-2 py-1',
        button: 'px-3 py-1.5 text-xs'
      },
      medium: {
        container: 'p-6',
        icon: 'w-10 h-10 text-base',
        title: 'text-base font-semibold',
        subtitle: 'text-sm',
        badge: 'text-xs px-2 py-1',
        button: 'px-4 py-2 text-sm'
      },
      large: {
        container: 'p-8',
        icon: 'w-12 h-12 text-lg',
        title: 'text-lg font-bold',
        subtitle: 'text-base',
        badge: 'text-sm px-3 py-1.5',
        button: 'px-6 py-3 text-base'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  // Handle card click
  const handleCardClick = () => {
    if (onClick) {
      onClick(progress);
    }
  };

  // Handle quick action
  const handleQuickAction = (e) => {
    e.stopPropagation();
    if (onQuickAction) {
      onQuickAction(progress);
    } else {
      // Default quick action based on progress
      if (!progress.emailVerified) {
        window.location.href = '/verify-email';
      } else if (!progress.kycVerified) {
        window.location.href = '/ekyc';
      } else if (progress.bankAccountsCount === 0) {
        console.log('Add bank account');
      }
    }
  };

  // Render loading state
  if (verificationLoading && !verificationStatus) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl ${sizeClasses.container} ${className}`}>
        <div className="flex items-center space-x-3">
          <div className={`${sizeClasses.icon} rounded-xl bg-gray-100 flex items-center justify-center`}>
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
          </div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (verificationError) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-red-200 rounded-2xl ${sizeClasses.container} ${className}`}>
        <div className="flex items-center space-x-3">
          <div className={`${sizeClasses.icon} rounded-xl bg-red-100 flex items-center justify-center`}>
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
          </div>
          <div className="flex-1">
            <h3 className={`${sizeClasses.title} text-red-900`}>Verification Error</h3>
            <p className={`${sizeClasses.subtitle} text-red-700`}>Unable to load status</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ 
        y: onClick ? -2 : 0,
        boxShadow: onClick ? '0 10px 30px rgba(0, 0, 0, 0.1)' : undefined
      }}
      transition={{ duration: 0.3 }}
      onClick={onClick ? handleCardClick : undefined}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`
        bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-lg
        ${onClick ? 'cursor-pointer hover:shadow-xl' : ''}
        ${sizeClasses.container} ${className}
      `}
      style={createGlassCSS('medium')}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`${sizeClasses.icon} rounded-xl bg-gradient-to-br flex items-center justify-center ${
            progress.levelColor === 'green' ? 'from-green-500 to-emerald-600' :
            progress.levelColor === 'orange' ? 'from-orange-500 to-amber-600' :
            progress.levelColor === 'blue' ? 'from-blue-500 to-indigo-600' :
            'from-red-500 to-rose-600'
          }`}>
            <FontAwesomeIcon 
              icon={progress.percentage === 100 ? faTrophy : faShieldAlt} 
              className="text-white" 
            />
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-1">
              <h3 className={`${sizeClasses.title} text-gray-900`}>
                Account Verification
              </h3>
              {showBadges && (
                <span className={`${sizeClasses.badge} rounded-full font-medium ${
                  progress.levelColor === 'green' ? 'bg-green-100 text-green-800' :
                  progress.levelColor === 'orange' ? 'bg-orange-100 text-orange-800' :
                  progress.levelColor === 'blue' ? 'bg-blue-100 text-blue-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {progress.level}
                </span>
              )}
            </div>
            <p className={`${sizeClasses.subtitle} text-gray-600`}>
              {progress.completedSteps} of {progress.totalSteps} steps completed
            </p>
          </div>
        </div>

        {onClick && (
          <motion.div
            animate={{ x: isHovered ? 5 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
          </motion.div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-gray-700">Progress</span>
          <span className="text-xs text-gray-600">{Math.round(progress.percentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${progress.percentage}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-2 rounded-full bg-gradient-to-r ${
              progress.percentage === 100 ? 'from-green-500 to-emerald-600' :
              progress.percentage >= 50 ? 'from-blue-500 to-indigo-600' :
              'from-orange-500 to-amber-600'
            }`}
          />
        </div>
      </div>

      {/* Verification Steps Icons */}
      {showDetails && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            {/* Email */}
            <div className="flex flex-col items-center space-y-1">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                progress.emailVerified ? 'bg-green-100' : 'bg-gray-100'
              }`}>
                <FontAwesomeIcon 
                  icon={progress.emailVerified ? faCheckCircle : faEnvelope} 
                  className={`text-sm ${progress.emailVerified ? 'text-green-600' : 'text-gray-400'}`} 
                />
              </div>
              <span className="text-xs text-gray-600">Email</span>
            </div>

            {/* KYC */}
            <div className="flex flex-col items-center space-y-1">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                progress.kycVerified ? 'bg-green-100' : 
                progress.emailVerified ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                <FontAwesomeIcon 
                  icon={progress.kycVerified ? faCheckCircle : faIdCard} 
                  className={`text-sm ${
                    progress.kycVerified ? 'text-green-600' : 
                    progress.emailVerified ? 'text-blue-600' : 'text-gray-400'
                  }`} 
                />
              </div>
              <span className="text-xs text-gray-600">KYC</span>
            </div>

            {/* Bank */}
            <div className="flex flex-col items-center space-y-1">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                progress.bankAccountsCount > 0 ? 'bg-green-100' : 
                progress.kycVerified ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                <FontAwesomeIcon 
                  icon={progress.bankAccountsCount > 0 ? faCheckCircle : faUniversity} 
                  className={`text-sm ${
                    progress.bankAccountsCount > 0 ? 'text-green-600' : 
                    progress.kycVerified ? 'text-blue-600' : 'text-gray-400'
                  }`} 
                />
              </div>
              <span className="text-xs text-gray-600">Bank</span>
            </div>
          </div>

          {/* Quick Action */}
          {showActions && progress.percentage < 100 && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleQuickAction}
              className={`${sizeClasses.button} bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 transition-all flex items-center space-x-1`}
            >
              <span>Continue</span>
              <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
            </motion.button>
          )}

          {progress.percentage === 100 && (
            <div className="flex items-center space-x-1 text-green-600">
              <FontAwesomeIcon icon={faCheckCircle} className="text-sm" />
              <span className="text-xs font-medium">Complete</span>
            </div>
          )}
        </div>
      )}

      {/* Next Step */}
      {showDetails && progress.percentage < 100 && (
        <div className="bg-blue-50 rounded-xl p-3">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 text-sm" />
            <p className="text-sm text-blue-800">
              <span className="font-medium">Next:</span> {progress.nextStep}
            </p>
          </div>
        </div>
      )}

      {/* Completion Message */}
      {showDetails && progress.percentage === 100 && (
        <div className="bg-green-50 rounded-xl p-3">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faTrophy} className="text-green-600 text-sm" />
            <p className="text-sm text-green-800">
              <span className="font-medium">Congratulations!</span> All verification steps completed
            </p>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default VerificationProgressCard;
