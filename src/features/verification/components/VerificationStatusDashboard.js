import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faClock,
  faEnvelope,
  faIdCard,
  faUniversity,
  faSpinner,
  faRefresh,
  faArrowRight,
  faInfoCircle,
  faStar,
  faLock,
  faGift
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../wallet/contexts/WalletContext';
import { createGlassCSS } from '../../wallet/components/common/WalletTheme';

/**
 * Enhanced Verification Status Dashboard
 * 
 * Comprehensive verification dashboard with step-by-step progress,
 * premium design, and clear user guidance for account verification.
 */
const VerificationStatusDashboard = ({
  variant = 'dashboard', // 'dashboard', 'card', 'compact'
  showActions = true,
  showProgress = true,
  showBenefits = true,
  onEmailVerify,
  onKYCStart,
  onBankAccountAdd,
  className = ''
}) => {
  const {
    verificationStatus,
    verificationLoading,
    verificationError,
    loadVerificationStatus,
    resendEmailVerification,
    clearError
  } = useWallet();

  const [refreshing, setRefreshing] = useState(false);
  const [emailResending, setEmailResending] = useState(false);

  // Load verification status on mount
  useEffect(() => {
    if (!verificationStatus && !verificationLoading) {
      loadVerificationStatus();
    }
  }, [verificationStatus, verificationLoading, loadVerificationStatus]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await loadVerificationStatus();
    } catch (error) {
      console.error('Failed to refresh verification status:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle email resend
  const handleEmailResend = async () => {
    try {
      setEmailResending(true);
      await resendEmailVerification();
    } catch (error) {
      console.error('Failed to resend email:', error);
    } finally {
      setEmailResending(false);
    }
  };

  // Get verification steps
  const getVerificationSteps = () => {
    const emailVerified = verificationStatus?.email_verified || false;
    const kycVerified = verificationStatus?.ekyc_verified || false;
    const bankAccountsCount = verificationStatus?.bank_accounts_count || 0;

    return [
      {
        id: 'email',
        title: 'Email Verification',
        description: 'Verify your email address to secure your account',
        icon: faEnvelope,
        status: emailVerified ? 'completed' : 'pending',
        required: true,
        action: emailVerified ? null : {
          label: 'Verify Email',
          onClick: onEmailVerify || (() => window.location.href = '/verify-email')
        },
        secondaryAction: emailVerified ? null : {
          label: 'Resend Email',
          onClick: handleEmailResend,
          loading: emailResending
        }
      },
      {
        id: 'kyc',
        title: 'Identity Verification',
        description: 'Complete KYC verification to unlock withdrawals',
        icon: faIdCard,
        status: kycVerified ? 'completed' : emailVerified ? 'available' : 'locked',
        required: true,
        action: kycVerified ? null : emailVerified ? {
          label: 'Start KYC',
          onClick: onKYCStart || (() => window.location.href = '/ekyc')
        } : null
      },
      {
        id: 'bank',
        title: 'Bank Account',
        description: 'Add a bank account for withdrawals',
        icon: faUniversity,
        status: bankAccountsCount > 0 ? 'completed' : kycVerified ? 'available' : 'locked',
        required: false,
        action: bankAccountsCount > 0 ? null : kycVerified ? {
          label: 'Add Bank Account',
          onClick: onBankAccountAdd || (() => console.log('Add bank account'))
        } : null,
        count: bankAccountsCount
      }
    ];
  };

  const steps = getVerificationSteps();
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  // Get overall verification level
  const getVerificationLevel = () => {
    if (completedSteps === 0) return { level: 'Unverified', color: 'red', description: 'Complete verification to unlock features' };
    if (completedSteps === 1) return { level: 'Basic', color: 'orange', description: 'Email verified - complete KYC for full access' };
    if (completedSteps === 2) return { level: 'Verified', color: 'green', description: 'Fully verified - all features unlocked' };
    return { level: 'Complete', color: 'blue', description: 'All verification steps completed' };
  };

  const verificationLevel = getVerificationLevel();

  // Render loading state
  if (verificationLoading && !verificationStatus) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-8 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 text-3xl mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Verification Status</h3>
          <p className="text-gray-600">Please wait while we check your verification status...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (verificationError) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-8 ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Load Verification Status</h3>
          <p className="text-gray-600 mb-6">{verificationError}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              clearError('verification');
              handleRefresh();
            }}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all"
          >
            <FontAwesomeIcon icon={faRefresh} className="mr-2" />
            Try Again
          </motion.button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-xl"
        style={createGlassCSS('medium')}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center ${
              verificationLevel.color === 'green' ? 'from-green-500 to-emerald-600' :
              verificationLevel.color === 'orange' ? 'from-orange-500 to-amber-600' :
              verificationLevel.color === 'blue' ? 'from-blue-500 to-indigo-600' :
              'from-red-500 to-rose-600'
            }`}>
              <FontAwesomeIcon icon={faShieldAlt} className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Account Verification</h2>
              <p className="text-gray-600">{verificationLevel.description}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              verificationLevel.color === 'green' ? 'bg-green-100 text-green-800' :
              verificationLevel.color === 'orange' ? 'bg-orange-100 text-orange-800' :
              verificationLevel.color === 'blue' ? 'bg-blue-100 text-blue-800' :
              'bg-red-100 text-red-800'
            }`}>
              {verificationLevel.level}
            </div>

            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={handleRefresh}
              disabled={refreshing}
              className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <FontAwesomeIcon 
                icon={faRefresh} 
                className={`text-gray-600 ${refreshing ? 'animate-spin' : ''}`} 
              />
            </motion.button>
          </div>
        </div>

        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Verification Progress</span>
              <span className="text-sm text-gray-600">{completedSteps} of {totalSteps} completed</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className={`h-3 rounded-full bg-gradient-to-r ${
                  progressPercentage === 100 ? 'from-green-500 to-emerald-600' :
                  progressPercentage >= 50 ? 'from-blue-500 to-indigo-600' :
                  'from-orange-500 to-amber-600'
                }`}
              />
            </div>
          </div>
        )}
      </motion.div>

      {/* Verification Steps */}
      <div className="space-y-4">
        {steps.map((step, index) => (
          <motion.div
            key={step.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className={`bg-white/80 backdrop-blur-sm border rounded-2xl p-6 transition-all ${
              step.status === 'completed' ? 'border-green-200 bg-green-50/50' :
              step.status === 'available' ? 'border-blue-200 bg-blue-50/50 hover:border-blue-300' :
              'border-gray-200 bg-gray-50/50'
            }`}
            style={createGlassCSS('light')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                  step.status === 'completed' ? 'bg-green-100' :
                  step.status === 'available' ? 'bg-blue-100' :
                  'bg-gray-100'
                }`}>
                  <FontAwesomeIcon 
                    icon={step.status === 'completed' ? faCheckCircle : step.icon} 
                    className={`text-lg ${
                      step.status === 'completed' ? 'text-green-600' :
                      step.status === 'available' ? 'text-blue-600' :
                      'text-gray-400'
                    }`} 
                  />
                </div>

                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className={`font-semibold ${
                      step.status === 'completed' ? 'text-green-900' :
                      step.status === 'available' ? 'text-blue-900' :
                      'text-gray-500'
                    }`}>
                      {step.title}
                    </h3>
                    {step.required && (
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Required</span>
                    )}
                    {step.count > 0 && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {step.count} added
                      </span>
                    )}
                  </div>
                  <p className={`text-sm ${
                    step.status === 'completed' ? 'text-green-700' :
                    step.status === 'available' ? 'text-blue-700' :
                    'text-gray-500'
                  }`}>
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                {step.secondaryAction && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={step.secondaryAction.onClick}
                    disabled={step.secondaryAction.loading}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    {step.secondaryAction.loading ? (
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                    ) : (
                      step.secondaryAction.label
                    )}
                  </motion.button>
                )}

                {step.action && showActions && (
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={step.action.onClick}
                    className={`px-4 py-2 text-sm font-medium rounded-lg transition-all flex items-center space-x-1 ${
                      step.status === 'available' 
                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    disabled={step.status !== 'available'}
                  >
                    <span>{step.action.label}</span>
                    <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                  </motion.button>
                )}

                {step.status === 'completed' && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <FontAwesomeIcon icon={faCheckCircle} className="text-sm" />
                    <span className="text-sm font-medium">Completed</span>
                  </div>
                )}

                {step.status === 'locked' && (
                  <div className="flex items-center space-x-1 text-gray-400">
                    <FontAwesomeIcon icon={faLock} className="text-sm" />
                    <span className="text-sm">Locked</span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Benefits Section */}
      {showBenefits && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl p-6 text-white"
        >
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faGift} className="text-2xl mr-3" />
            <h3 className="text-lg font-semibold">Verification Benefits</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <FontAwesomeIcon icon={faCheckCircle} className="text-green-300" />
              <span className="text-sm">Secure withdrawals</span>
            </div>
            <div className="flex items-center space-x-2">
              <FontAwesomeIcon icon={faCheckCircle} className="text-green-300" />
              <span className="text-sm">Higher transaction limits</span>
            </div>
            <div className="flex items-center space-x-2">
              <FontAwesomeIcon icon={faCheckCircle} className="text-green-300" />
              <span className="text-sm">Priority support</span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default VerificationStatusDashboard;
