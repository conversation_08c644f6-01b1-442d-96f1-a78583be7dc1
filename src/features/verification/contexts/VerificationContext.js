import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import verificationService from '../services/VerificationService';

/**
 * Verification Context Actions
 */
const VERIFICATION_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_VERIFICATION_STATUS: 'SET_VERIFICATION_STATUS',
  SET_WITHDRAWAL_ELIGIBILITY: 'SET_WITHDRAWAL_ELIGIBILITY',
  SET_KYC_STATUS: 'SET_KYC_STATUS',
  SET_EMAIL_RESENDING: 'SET_EMAIL_RESENDING',
  SET_INITIALIZED: 'SET_INITIALIZED',
  REFRESH_ALL: 'REFRESH_ALL'
};

/**
 * Initial verification state
 */
const initialState = {
  // Verification status
  verificationStatus: null,
  verificationLoading: false,
  verificationError: null,

  // Withdrawal eligibility
  withdrawalEligibility: null,
  eligibilityLoading: false,
  eligibilityError: null,

  // KYC status
  kycStatus: null,
  kycLoading: false,
  kycError: null,

  // Email verification
  emailResending: false,
  emailResendError: null,

  // Global state
  isInitialized: false,
  lastRefresh: null,
  globalError: null
};

/**
 * Verification reducer
 */
const verificationReducer = (state, action) => {
  switch (action.type) {
    case VERIFICATION_ACTIONS.SET_LOADING:
      return {
        ...state,
        [`${action.payload.type}Loading`]: action.payload.loading
      };

    case VERIFICATION_ACTIONS.SET_ERROR:
      return {
        ...state,
        [`${action.payload.type}Error`]: action.payload.error,
        [`${action.payload.type}Loading`]: false
      };

    case VERIFICATION_ACTIONS.CLEAR_ERROR:
      if (action.payload.type === 'all') {
        return {
          ...state,
          verificationError: null,
          eligibilityError: null,
          kycError: null,
          emailResendError: null,
          globalError: null
        };
      }
      return {
        ...state,
        [`${action.payload.type}Error`]: null
      };

    case VERIFICATION_ACTIONS.SET_VERIFICATION_STATUS:
      return {
        ...state,
        verificationStatus: action.payload.status,
        verificationLoading: false,
        verificationError: null,
        lastRefresh: Date.now()
      };

    case VERIFICATION_ACTIONS.SET_WITHDRAWAL_ELIGIBILITY:
      return {
        ...state,
        withdrawalEligibility: action.payload.eligibility,
        eligibilityLoading: false,
        eligibilityError: null
      };

    case VERIFICATION_ACTIONS.SET_KYC_STATUS:
      return {
        ...state,
        kycStatus: action.payload.status,
        kycLoading: false,
        kycError: null
      };

    case VERIFICATION_ACTIONS.SET_EMAIL_RESENDING:
      return {
        ...state,
        emailResending: action.payload.resending,
        emailResendError: action.payload.error || null
      };

    case VERIFICATION_ACTIONS.SET_INITIALIZED:
      return {
        ...state,
        isInitialized: action.payload.initialized
      };

    case VERIFICATION_ACTIONS.REFRESH_ALL:
      return {
        ...state,
        verificationLoading: true,
        eligibilityLoading: true,
        kycLoading: true,
        lastRefresh: Date.now()
      };

    default:
      return state;
  }
};

/**
 * Verification Context
 */
const VerificationContext = createContext();

/**
 * Verification Provider Component
 */
export const VerificationProvider = ({ children }) => {
  const [state, dispatch] = useReducer(verificationReducer, initialState);

  /**
   * Load verification status
   */
  const loadVerificationStatus = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: VERIFICATION_ACTIONS.SET_LOADING, payload: { type: 'verification', loading: true } });

      const status = await verificationService.getVerificationStatus(forceRefresh);

      dispatch({
        type: VERIFICATION_ACTIONS.SET_VERIFICATION_STATUS,
        payload: { status }
      });

      return status;
    } catch (error) {
      console.error('Error loading verification status:', error);
      dispatch({
        type: VERIFICATION_ACTIONS.SET_ERROR,
        payload: { type: 'verification', error: error.message || 'Failed to load verification status' }
      });
      throw error;
    }
  }, []);

  /**
   * Load withdrawal eligibility
   */
  const loadWithdrawalEligibility = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: VERIFICATION_ACTIONS.SET_LOADING, payload: { type: 'eligibility', loading: true } });

      const eligibility = await verificationService.getWithdrawalEligibility(forceRefresh);

      dispatch({
        type: VERIFICATION_ACTIONS.SET_WITHDRAWAL_ELIGIBILITY,
        payload: { eligibility }
      });

      return eligibility;
    } catch (error) {
      console.error('Error loading withdrawal eligibility:', error);
      dispatch({
        type: VERIFICATION_ACTIONS.SET_ERROR,
        payload: { type: 'eligibility', error: error.message || 'Failed to load withdrawal eligibility' }
      });
      throw error;
    }
  }, []);

  /**
   * Load KYC status
   */
  const loadKYCStatus = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: VERIFICATION_ACTIONS.SET_LOADING, payload: { type: 'kyc', loading: true } });

      const status = await verificationService.getKYCStatus(forceRefresh);

      dispatch({
        type: VERIFICATION_ACTIONS.SET_KYC_STATUS,
        payload: { status }
      });

      return status;
    } catch (error) {
      console.error('Error loading KYC status:', error);
      dispatch({
        type: VERIFICATION_ACTIONS.SET_ERROR,
        payload: { type: 'kyc', error: error.message || 'Failed to load KYC status' }
      });
      throw error;
    }
  }, []);

  /**
   * Resend email verification
   */
  const resendEmailVerification = useCallback(async () => {
    try {
      dispatch({ type: VERIFICATION_ACTIONS.SET_EMAIL_RESENDING, payload: { resending: true } });

      const result = await verificationService.resendEmailVerification();

      dispatch({ type: VERIFICATION_ACTIONS.SET_EMAIL_RESENDING, payload: { resending: false } });

      // Refresh verification status after resending
      setTimeout(() => {
        loadVerificationStatus(true);
      }, 1000);

      return result;
    } catch (error) {
      console.error('Error resending email verification:', error);
      dispatch({
        type: VERIFICATION_ACTIONS.SET_EMAIL_RESENDING,
        payload: { resending: false, error: error.message || 'Failed to resend email verification' }
      });
      throw error;
    }
  }, [loadVerificationStatus]);

  /**
   * Refresh all verification data
   */
  const refreshAll = useCallback(async () => {
    try {
      dispatch({ type: VERIFICATION_ACTIONS.REFRESH_ALL });

      await Promise.allSettled([
        loadVerificationStatus(true),
        loadWithdrawalEligibility(true),
        loadKYCStatus(true)
      ]);
    } catch (error) {
      console.error('Error refreshing verification data:', error);
    }
  }, [loadVerificationStatus, loadWithdrawalEligibility, loadKYCStatus]);

  /**
   * Clear specific error
   */
  const clearError = useCallback((type = 'all') => {
    dispatch({
      type: VERIFICATION_ACTIONS.CLEAR_ERROR,
      payload: { type }
    });
  }, []);

  /**
   * Clear all caches
   */
  const clearCache = useCallback(() => {
    verificationService.clearCache();
  }, []);

  /**
   * Get verification progress
   */
  const getVerificationProgress = useCallback(() => {
    if (!state.verificationStatus) {
      return {
        completedSteps: 0,
        totalSteps: 3,
        percentage: 0,
        level: 'unverified',
        nextStep: 'email_verification'
      };
    }

    const status = state.verificationStatus;
    let completedSteps = 0;

    if (status.email_verified) completedSteps++;
    if (status.ekyc_verified) completedSteps++;
    if (status.bank_accounts_count > 0) completedSteps++;

    const totalSteps = 3;
    const percentage = (completedSteps / totalSteps) * 100;

    return {
      completedSteps,
      totalSteps,
      percentage,
      level: status.verification_level,
      nextStep: status.next_verification_step?.step || 'email_verification'
    };
  }, [state.verificationStatus]);

  /**
   * Check if user can withdraw
   */
  const canWithdraw = useCallback(() => {
    return state.verificationStatus?.can_withdraw || false;
  }, [state.verificationStatus]);

  /**
   * Get missing requirements
   */
  const getMissingRequirements = useCallback(() => {
    return state.verificationStatus?.missing_requirements || [];
  }, [state.verificationStatus]);

  /**
   * Check if specific verification is completed
   */
  const isVerificationCompleted = useCallback((type) => {
    if (!state.verificationStatus) return false;

    switch (type) {
      case 'email':
        return state.verificationStatus.email_verified;
      case 'kyc':
        return state.verificationStatus.ekyc_verified;
      case 'bank':
        return state.verificationStatus.bank_accounts_count > 0;
      default:
        return false;
    }
  }, [state.verificationStatus]);

  /**
   * Initialize verification data on mount
   */
  const initializeVerification = useCallback(async () => {
    try {
      await Promise.allSettled([
        loadVerificationStatus(),
        loadWithdrawalEligibility()
      ]);

      dispatch({
        type: VERIFICATION_ACTIONS.SET_INITIALIZED,
        payload: { initialized: true }
      });
    } catch (error) {
      console.error('Error initializing verification:', error);
      dispatch({
        type: VERIFICATION_ACTIONS.SET_ERROR,
        payload: { type: 'global', error: 'Failed to initialize verification data' }
      });
    }
  }, [loadVerificationStatus, loadWithdrawalEligibility]);

  // Initialize verification data on mount
  useEffect(() => {
    if (!state.isInitialized) {
      initializeVerification();
    }
  }, [state.isInitialized, initializeVerification]);

  // Context value
  const value = {
    // State
    ...state,

    // Actions
    loadVerificationStatus,
    loadWithdrawalEligibility,
    loadKYCStatus,
    resendEmailVerification,
    refreshAll,
    clearError,
    clearCache,

    // Computed values
    getVerificationProgress,
    canWithdraw,
    getMissingRequirements,
    isVerificationCompleted,

    // Service access
    verificationService
  };

  return (
    <VerificationContext.Provider value={value}>
      {children}
    </VerificationContext.Provider>
  );
};

/**
 * Custom hook to use verification context
 */
export const useVerification = () => {
  const context = useContext(VerificationContext);
  if (!context) {
    throw new Error('useVerification must be used within a VerificationProvider');
  }
  return context;
};

export default VerificationContext;
