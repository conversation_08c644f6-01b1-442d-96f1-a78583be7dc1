/**
 * Wallet feature constants
 * 
 * This file contains all constants used throughout the wallet feature.
 * Separated from index.js to avoid circular dependencies.
 */

export const walletConstants = {
  // Transaction types
  TRANSACTION_TYPES: {
    TOPUP: 'topup',
    PURCHASE: 'purchase',
    WITHDRAWAL: 'withdrawal',
    GIFT: 'gift',
    REFUND: 'refund'
  },

  // Payment statuses
  PAYMENT_STATUS: {
    PENDING: 'pending',
    SUCCESS: 'success',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
  },

  // Error codes
  ERROR_CODES: {
    INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
    INVALID_AMOUNT: 'INVALID_AMOUNT',
    PAYMENT_ERROR: 'PAYMENT_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR'
  },

  // Cache keys
  CACHE_KEYS: {
    BALANCE: 'wallet_balance',
    TRANSACTIONS: 'wallet_transactions',
    PACKAGES: 'credit_packages'
  }
};

export default walletConstants;
