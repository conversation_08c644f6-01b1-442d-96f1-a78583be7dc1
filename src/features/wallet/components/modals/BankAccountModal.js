import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faUniversity,
  faPlus,
  faEdit,
  faShieldAlt,
  faCheckCircle,
  faInfoCircle,
  faClock,
  faCreditCard,
  faMobileAlt,
  faGlobe,
  faMapMarkerAlt,
  faSearch,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import {
  getAccountNumberPlaceholder,
  supportsDuitNow
} from '../../../banking/validation/bankAccountValidation';
import * as Yup from 'yup';
import bankAccountService from '../../../../services/bankAccountService';
import { useBankAccountsEnhanced } from '../../../banking/hooks/useBankAccountsEnhanced';

/**
 * Enhanced Bank Account Modal Component
 *
 * Malaysia-focused bank account modal with improved UI/UX design,
 * local banking context, and enhanced user experience for Malaysian users.
 */
const BankAccountModal = ({
  isOpen = false,
  mode = 'add', // 'add' or 'edit'
  account = null, // For editing existing account
  onClose,
  onSuccess,
  className = ''
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(2);
  const [selectedBankType, setSelectedBankType] = useState(null);
  const [malaysianBanks, setMalaysianBanks] = useState([]);
  const [banksLoading, setBanksLoading] = useState(false);
  const [banksError, setBanksError] = useState(null);
  const [bankSearch, setBankSearch] = useState('');
  const [selectedBank, setSelectedBank] = useState(null);
  const { loadBankAccounts } = useBankAccountsEnhanced();


  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSubmitting(false);
      setCurrentStep(2);
      setSelectedBankType(null);
      setMalaysianBanks([]);
      setBanksLoading(false);
      setBanksError(null);
      setBankSearch('');
      setSelectedBank(null);
    }
  }, [isOpen]);

  // Fetch Malaysian banks on open
  useEffect(() => {
    if (isOpen && currentStep === 2) {
      setBanksLoading(true);
      setBanksError(null);
      bankAccountService.getMalaysianBanks()
        .then(res => {
          setMalaysianBanks(res.data || []);
          setBanksLoading(false);
        })
        .catch(err => {
          setBanksError('Failed to load banks. Please try again.');
          setBanksLoading(false);
        });
    }
  }, [isOpen, currentStep]);

  // Fetch user bank accounts when modal opens
  useEffect(() => {
    if (isOpen) {
      loadBankAccounts();
    }
  }, [isOpen, loadBankAccounts]);


  // Handle form submission
  const handleFormSuccess = async (accountData) => {
    try {
      setIsSubmitting(true);
      await onSuccess(accountData);
      // Modal will be closed by parent component
    } catch (error) {
      console.error('Bank account operation failed:', error);
      // Error is handled by parent component and context
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form cancellation
  const handleFormCancel = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed inset-0 z-[9999] flex items-center justify-center p-4 bg-black/60 dark:bg-black/80 backdrop-blur-md"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.95, opacity: 0, y: 20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          onClick={(e) => e.stopPropagation()}
          className={
            `w-full max-w-4xl max-h-[95vh] overflow-y-auto
            mx-4 sm:mx-0
            bg-gradient-to-br from-white/95 via-white/90 to-white/95 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-gray-900/95
            backdrop-blur-2xl rounded-3xl
            shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] shadow-black/10
            border border-white/20 dark:border-gray-700 ring-1 ring-white/10 dark:ring-gray-800/20
            ${className}`
          }
        >
          {/* Enhanced Header */}
          <div className="sticky top-0 bg-gradient-to-r from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-900/90 backdrop-blur-xl border-b border-white/20 dark:border-gray-700 px-6 py-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, duration: 0.5, ease: "easeOut" }}
                  className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500 via-blue-500 to-blue-600 flex items-center justify-center shadow-lg shadow-blue-500/25"
                >
                  <FontAwesomeIcon
                    icon={mode === 'edit' ? faEdit : faPlus}
                    className="text-white text-2xl"
                  />
                </motion.div>
                <div>
                  <motion.h2
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3, duration: 0.4 }}
                    className="text-3xl font-bold text-left text-gray-900 dark:text-gray-100 tracking-tight"
                  >
                    {mode === 'edit' ? 'Edit Bank Account' : 'Add Bank Account'}
                  </motion.h2>
                  <motion.p
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                    className="text-gray-600 dark:text-gray-300 text-lg mt-1 font-medium"
                  >
                    {mode === 'edit'
                      ? 'Update your Malaysian bank account information'
                      : 'Connect your Malaysian bank account for secure withdrawals'
                    }
                  </motion.p>
                </div>
              </div>

              {!isSubmitting && (
                <motion.button
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3 }}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-12 h-12 rounded-2xl bg-gray-100/80 dark:bg-gray-900/80 hover:bg-gray-200/80 dark:hover:bg-gray-800/80 backdrop-blur-sm flex items-center justify-center transition-all duration-200 border border-gray-200/50 dark:border-gray-700 shadow-sm"
                  aria-label="Close modal"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600 dark:text-gray-200" />
                </motion.button>
              )}
            </div>
          </div>
          {/* Content */}
          <div className="p-6 space-y-8 overflow-y-auto max-h-[70vh]">
            {/* Step 2: Bank Selection */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8"
              >
                {/* Bank Selection */}
                <div className="flex items-center space-x-3 mb-4">
                  <FontAwesomeIcon icon={faUniversity} className="text-blue-600 text-2xl" />
                  <h3 className="text-xl font-bold text-blue-900 dark:text-blue-200">Select Your Bank</h3>
                </div>
                
                {/* Search Bar */}
                <div className="mb-4 flex items-center gap-2">
                  <div className="relative w-full">
                    <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search bank by name..."
                      value={bankSearch}
                      onChange={e => setBankSearch(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-900 dark:text-gray-100"
                    />
                  </div>
                </div>
                
                {/* Bank Cards Grid */}
                {banksLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <span className="animate-spin w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full"></span>
                  </div>
                ) : banksError ? (
                  <div className="text-red-500 text-center py-4">{banksError}</div>
                ) : (
                  <>
                    {/* Selection Counter */}
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">
                        {selectedBank ? '1 bank selected' : 'No bank selected'}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {malaysianBanks.filter(b => b.name.toLowerCase().includes(bankSearch.toLowerCase())).map(bank => (
                        <motion.button
                          key={bank.code}
                          type="button"
                          onClick={() => setSelectedBank(bank)}
                          aria-pressed={selectedBank?.code === bank.code}
                          tabIndex={0}
                          whileHover={{ scale: 1.04 }}
                          whileTap={{ scale: 0.97 }}
                          className={`relative p-5 flex flex-col items-center rounded-2xl border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 font-medium text-base cursor-pointer select-none
                            ${selectedBank?.code === bank.code
                              ? 'bg-gradient-to-br from-blue-500/90 to-indigo-500/90 text-white border-blue-600 shadow-lg shadow-blue-500/20'
                              : 'bg-white/90 dark:bg-gray-900/80 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-700 hover:border-blue-400 hover:bg-blue-50/60 dark:hover:bg-blue-900/60 hover:shadow-md'}
                          `}
                        >
                          {bank.logoUrl && (
                            <img src={bank.logoUrl} alt={bank.name} className="w-12 h-12 object-contain mb-2 rounded-xl bg-white/80 shadow-sm" />
                          )}
                          <span className="font-semibold text-center truncate w-full">{bank.name}</span>
                          {/* Animated checkmark for selected */}
                          <AnimatePresence>
                            {selectedBank?.code === bank.code && (
                              <motion.span
                                initial={{ scale: 0, opacity: 0, rotate: -90 }}
                                animate={{ scale: 1, opacity: 1, rotate: 0 }}
                                exit={{ scale: 0, opacity: 0, rotate: 90 }}
                                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                                className="absolute -top-2 -right-2 w-7 h-7 bg-green-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white"
                              >
                                <FontAwesomeIcon icon={faCheck} className="text-white text-lg" />
                              </motion.span>
                            )}
                          </AnimatePresence>
                        </motion.button>
                      ))}
                    </div>
                  </>
                )}
                
                                {/* Account Details Form */}
                {selectedBank && (
                  <motion.div
                    initial={{ opacity: 0, y: 30, scale: 0.97 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.5, type: 'spring', stiffness: 180, damping: 22 }}
                    className="relative bg-gradient-to-br from-indigo-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900 border border-indigo-100 dark:border-blue-700 rounded-2xl shadow-xl p-0 overflow-hidden"
                  >
                    {/* Section Header */}
                    <div className="flex items-center px-6 pt-6 pb-2">
                      <span className="w-1.5 h-8 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-full mr-3"></span>
                      <FontAwesomeIcon icon={faCreditCard} className="text-indigo-600 text-2xl mr-2" />
                      <h3 className="text-xl font-bold text-indigo-900 dark:text-indigo-200">Account Details</h3>
                    </div>
                    <div className="px-6 pb-6 pt-2">
                      <Formik
                        enableReinitialize
                        initialValues={{
                          malaysian_bank_id: selectedBank?.id || account?.malaysian_bank_id || '',
                          bankName: selectedBank?.name || account?.bankName || '',
                          account_number: selectedBank?.account_number || account?.account_number || '',
                          account_holder_name: selectedBank?.full_name || account?.account_holder_name || '',
                          bankCode: selectedBank?.code || account?.bankCode || '',
                          isDuitNow: account?.isDuitNow || false,
                          isFPX: account?.isFPX || false,
                          isMyDebit: account?.isMyDebit || false,
                          isActive: account?.isActive || false,
                          notes: account?.notes || '',
                        }}
                        validationSchema={Yup.object().shape({
                          malaysian_bank_id: Yup.mixed().required('Please select a bank'),
                          account_number: Yup.string().required('Account number is required'),
                          account_holder_name: Yup.string().required('Account holder name is required').min(2, 'Name must be at least 2 characters').max(100, 'Name cannot exceed 100 characters'),
                          bankName: Yup.string(),
                          bankCode: Yup.string(),
                          isDuitNow: Yup.boolean(),
                          isFPX: Yup.boolean(),
                          isMyDebit: Yup.boolean(),
                          isActive: Yup.boolean(),
                          notes: Yup.string()
                        })}
                        onSubmit={async (values, { setSubmitting, setStatus }) => {
                          try {
                            setIsSubmitting(true);
                            const payload = {
                              malaysian_bank_id: values.malaysian_bank_id,
                              account_number: values.account_number,
                              account_holder_name: values.account_holder_name,
                              is_primary: true
                            };
                            console.log('🚀 Submitting Bank payload:', payload);
                            const response = await bankAccountService.addBankAccount(payload);
                            if (onSuccess) onSuccess(response.data);
                            onClose();
                          } catch (error) {
                            console.error('❌ Failed to add bank account:', error);
                            setStatus({ apiError: error.message || 'Failed to add bank account.' });
                          } finally {
                            setIsSubmitting(false);
                            setSubmitting(false);
                          }
                        }}
                      >
                        {({ values, errors, touched, setFieldValue, status, handleSubmit, isValid, dirty }) => {
                          useEffect(() => {
                            if (selectedBank) {
                              setFieldValue('malaysian_bank_id', selectedBank.id);
                              setFieldValue('bankName', selectedBank.name);
                              setFieldValue('bankCode', selectedBank.code);
                              setFieldValue('account_number', selectedBank.account_number || '');
                              setFieldValue('account_holder_name', selectedBank.full_name || '');
                            }
                          }, [selectedBank, setFieldValue]);
                          return (
                            <Form className="space-y-8 max-w-3xl" onSubmit={handleSubmit}>
                              {/* Hidden field for malaysian_bank_id */}
                              <Field type="hidden" name="malaysian_bank_id" value={selectedBank?.id} />
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <div>
                                  <label htmlFor="account_number" className="block text-sm font-semibold text-gray-800 mb-1">Account Number</label>
                                  <Field
                                    type="text"
                                    id="account_number"
                                    name="account_number"
                                    placeholder={getAccountNumberPlaceholder(selectedBank?.code)}
                                    value={values.account_number}
                                    className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base font-medium dark:bg-gray-900 dark:text-gray-100"
                                  />
                                  <ErrorMessage name="account_number" component="div" className="text-red-500 text-xs mt-1" />
                                </div>
                                <div>
                                  <label htmlFor="account_holder_name" className="block text-sm font-semibold text-gray-800 mb-1">Account Holder Name</label>
                                  <Field
                                    type="text"
                                    id="account_holder_name"
                                    name="account_holder_name"
                                    placeholder="Enter account holder name"
                                    value={values.account_holder_name}
                                    className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base font-medium dark:bg-gray-900 dark:text-gray-100"
                                  />
                                  <ErrorMessage name="account_holder_name" component="div" className="text-red-500 text-xs mt-1" />
                                </div>
                              </div>
                              <div className="sticky bottom-0 left-0 right-0 bg-gradient-to-r from-white/95 to-blue-50 dark:from-gray-900/95 dark:to-blue-900 py-4 flex justify-end space-x-3 border-t border-gray-100 dark:border-gray-700 z-20 mt-8 shadow-lg rounded-b-2xl">
                                <motion.button
                                  type="button"
                                  onClick={handleFormCancel}
                                  disabled={isSubmitting}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.97 }}
                                  className="px-6 py-2 text-sm font-semibold text-gray-700 dark:text-gray-200 bg-gray-200 dark:bg-gray-800 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                  Cancel
                                </motion.button>
                                <motion.button
                                  type="submit"
                                  disabled={isSubmitting || !selectedBank}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.97 }}
                                  className="px-6 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-md"
                                >
                                  {isSubmitting ? 'Saving...' : 'Add Bank Account'}
                                </motion.button>
                              </div>
                              {status && status.apiError && (
                                <div className="p-3 bg-red-100 border border-red-200 rounded-xl text-red-700 text-sm mb-2">
                                  {status.apiError}
                                </div>
                              )}
                            </Form>
                          );
                        }}
                      </Formik>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BankAccountModal;