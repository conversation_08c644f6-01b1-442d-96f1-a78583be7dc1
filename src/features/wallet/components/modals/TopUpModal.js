import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faPlus,
  faCreditCard,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faFileInvoiceDollar
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { PaymentLoadingIndicator } from '../../../../components/common/LoadingIndicator';
import CreditPackageCard from '../../../../components/CreditPackageCard';
import { useTranslation } from 'react-i18next';

/**
 * Top-Up Modal Component
 * 
 * Modal-based workflow for adding credits with package selection,
 * verification checks, and payment processing.
 */
const TopUpModal = ({
  isOpen = false,
  onClose,
  onSuccess,
  className = '',
  initialStep = 'packages', // NEW PROP
  successPackage = null // NEW PROP
}) => {
  const {
    creditPackages,
    packagesLoading,
    packagesError,
    loadCreditPackages,
    processPayment,
    paymentLoading,
    paymentError,
    clearError
  } = useWallet();
  const { t } = useTranslation();

  // Local state
  const [selectedPackage, setSelectedPackage] = useState(null);
  const continueButtonRef = useRef(null);
  const parsePrice = (price) => {
    const numeric = typeof price === 'string' ? parseFloat(price) : price;
    return typeof numeric === 'number' && !isNaN(numeric) ? numeric : 0;
  };

  const formatPrice = (price) => {
    return parsePrice(price).toFixed(2);
  };
  const [step, setStep] = useState(initialStep); // 'packages', 'payment', 'redirecting', 'processing', 'success'
  // Require user to actively select a payment method
  const [paymentMethod, setPaymentMethod] = useState('');

  // Load packages when modal opens
  useEffect(() => {
    if (isOpen && (!creditPackages || creditPackages.length === 0)) {
      loadCreditPackages();
    }
  }, [isOpen, creditPackages, loadCreditPackages]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedPackage(null);
      setStep(initialStep);
      // Clear selected payment method when closing modal
      setPaymentMethod('');
      clearError('payment');
    }
  }, [isOpen, clearError, initialStep]);

  // If initialStep is 'success', set selectedPackage to successPackage
  useEffect(() => {
    if (isOpen && initialStep === 'success' && successPackage) {
      setSelectedPackage(successPackage);
      setStep('success');
    }
  }, [isOpen, initialStep, successPackage]);

  // Handle package selection
  const handlePackageSelect = (pkg) => {
    setSelectedPackage({
      ...pkg,
      price: parsePrice(pkg.price)
    });
  };

  // Handle payment processing
  const handlePayment = async () => {
    if (!selectedPackage || !paymentMethod) return;
    setStep('redirecting');
    try {
      // Simulate short delay for UX
      setTimeout(async () => {
        // Call processPayment, which should handle the redirect
        await processPayment(selectedPackage.id, {
        paymentMethod,
          onRedirect: (url) => {
            window.location.href = url;
        },
        onError: (error) => {
          setStep('payment');
        }
      });
      }, 1200);
    } catch (error) {
      console.error('Payment failed:', error);
      setStep('payment');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  // Scroll to Continue to Payment button when a package is selected
  useEffect(() => {
    if (selectedPackage && continueButtonRef.current) {
      continueButtonRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [selectedPackage]);

  if (!isOpen) return null;

  // Stepper steps
  const steps = [
    { key: 'packages', label: 'Select Package' },
    { key: 'payment', label: 'Payment' },
    { key: 'redirecting', label: 'Processing' },
    { key: 'success', label: 'Success' },
  ];
  const stepOrder = ['packages', 'payment', 'redirecting', 'success'];
  const currentIdx = stepOrder.indexOf(step);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 dark:bg-black/80 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={
            `w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-gradient-to-br from-white/95 via-white/90 to-blue-100/80 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-blue-900/80 backdrop-blur-2xl rounded-3xl shadow-2xl
            border border-white/40 dark:border-gray-700 ring-1 ring-blue-100/40 dark:ring-blue-900/40 ${className}`
          }
        >
          {/* Header */}
          <div className="sticky top-0 z-20 bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 backdrop-blur-xl border-b border-blue-100/60 dark:border-gray-700 p-8 rounded-t-3xl shadow-md flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-lg">
                <FontAwesomeIcon icon={faPlus} className="text-white text-3xl" />
              </div>
              <div>
                <h2 className="text-3xl font-extrabold text-gray-900 dark:text-gray-100 tracking-tight">Top Up Credits</h2>
                <p className="text-gray-600 dark:text-gray-300 text-left text-base font-medium">Add credits to your wallet</p>
              </div>
            </div>
            {step !== 'redirecting' && step !== 'processing' && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={handleClose}
                className="w-12 h-12 rounded-full bg-white/80 dark:bg-gray-900/80 hover:bg-gray-200 dark:hover:bg-gray-800 flex items-center justify-center transition-colors shadow border border-gray-200 dark:border-gray-700"
                aria-label="Close topup modal"
              >
                <FontAwesomeIcon icon={faTimes} className="text-gray-600 dark:text-gray-200 text-2xl" />
              </motion.button>
            )}
          </div>

          {/* Step Progress Indicator */}
          <div className="w-full px-8 pt-6 pb-2 bg-transparent">
            <div className="flex items-center justify-between gap-2">
              {steps.map((s, idx, arr) => {
                const isActive = step === s.key;
                const isCompleted = currentIdx > idx;
                return (
                  <React.Fragment key={s.key}>
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0.5 }}
                      animate={{ scale: isActive ? 1.1 : 1, opacity: isCompleted || isActive ? 1 : 0.5 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                      className={`flex flex-col items-center`}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-lg shadow-md border-2 transition-all duration-300
                        ${isCompleted ? 'bg-green-500 border-green-500 text-white' :
                          isActive ? 'bg-blue-600 border-blue-600 text-white' :
                          'bg-white/70 dark:bg-gray-900/70 border-blue-200 dark:border-blue-700 text-blue-400 dark:text-blue-200'}
                      `}>
                        {isCompleted ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="3" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                        ) : (
                          idx + 1
                        )}
                      </div>
                      <span className={`mt-2 text-xs font-medium ${isActive ? 'text-blue-700 dark:text-blue-300' : isCompleted ? 'text-green-700 dark:text-green-300' : 'text-blue-400 dark:text-blue-200'}`}>{s.label}</span>
                    </motion.div>
                    {idx < arr.length - 1 && (
                      <motion.div
                        key={`line-${s.key}`}
                        initial={{ width: 0 }}
                        animate={{ width: 36 }}
                        transition={{ duration: 0.4 }}
                        className={`h-1 mx-1 rounded-full transition-all duration-300
                          ${currentIdx > idx ? 'bg-green-400 dark:bg-green-700' : currentIdx === idx ? 'bg-blue-400 dark:bg-blue-700' : 'bg-blue-100 dark:bg-blue-900'}
                        `}
                        style={{ minWidth: 24, maxWidth: 36 }}
                      />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Package Selection Step */}
            {step === 'packages' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Select Credit Package</h3>
                  <p className="text-gray-600 dark:text-gray-300">Choose the amount of credits you want to add</p>
                </div>
                <div className="bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 rounded-2xl p-6 border border-blue-100 dark:border-blue-700 shadow-lg mb-2">
                  <div className="max-h-[340px] overflow-y-auto pr-1">
                    {packagesLoading ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[...Array(4)].map((_, i) => (
                          <div key={i} className="h-24 bg-gray-100 dark:bg-gray-800 rounded-xl animate-pulse" />
                        ))}
                      </div>
                    ) : packagesError ? (
                      <div className="text-center py-8">
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
                        <p className="text-gray-600 dark:text-gray-300">Failed to load credit packages</p>
                        <button
                          onClick={loadCreditPackages}
                          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                        >
                          Try Again
                        </button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {creditPackages?.map((pkg) => (
                          <CreditPackageCard
                            key={pkg.id}
                            package={pkg}
                            isSelected={selectedPackage?.id === pkg.id}
                            onSelect={handlePackageSelect}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                  {selectedPackage && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex justify-end pt-4 border-t border-gray-100 dark:border-gray-700"
                    >
                      <motion.button
                        ref={continueButtonRef}
                        whileHover={{ scale: 1.04 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => setStep('payment')}
                        className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg text-lg"
                      >
                        Continue to Payment
                      </motion.button>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Payment Step */}
            {step === 'payment' && selectedPackage && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Payment Details</h3>
                  <p className="text-gray-600 dark:text-gray-300">Review your order and complete payment</p>
                </div>
                {/* Order Summary */}
                <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 rounded-2xl p-6 border border-indigo-100 dark:border-blue-700 shadow-lg">
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">Order Summary</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Credits</span>
                      <span className="font-semibold">{selectedPackage.credits.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Price</span>
                      <span className="font-semibold">{selectedPackage.currency_code} {formatPrice(selectedPackage.price)}</span>
                    </div>
                    {selectedPackage.discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span className="font-semibold">-{selectedPackage.discount}%</span>
                      </div>
                    )}
                    <div className="border-t border-indigo-200 dark:border-blue-700 pt-3 flex justify-between text-lg font-bold">
                      <span>Total</span>
                      <span>{selectedPackage.currency_code} {formatPrice(selectedPackage.price)}</span>
                    </div>
                  </div>
                </div>
                {/* Payment Method */}
                <div className="bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 rounded-xl p-4 border border-blue-100 dark:border-blue-700 shadow-sm">
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">Payment Method</h4>
                  <label className="flex items-center p-4 border border-gray-200 rounded-xl hover:border-indigo-300 cursor-pointer transition-colors gap-4 bg-white/80 dark:bg-gray-900/80">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="billpliz"
                      checked={paymentMethod === 'billpliz'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="form-radio h-5 w-5 text-indigo-600 focus:ring-indigo-500"
                    />
                    <div className="flex items-center gap-2">
                      <img src="/Billplz_Blue.svg" alt="Billplz Logo" className="h-5 w-auto" />
                    </div>
                  </label>
                </div>
                {/* Security Notice */}
                <div className="items-center bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-xl p-4">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-green-600" />
                    <div>
                      <p className="font-medium text-green-900 dark:text-green-50 text-left">Secure Payment</p>
                      <p className="text-sm text-green-700 dark:text-green-200">Your payment information is encrypted and secure</p>
                    </div>
                  </div>
                </div>
                {/* Error Display */}
                {paymentError && (
                  <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                      <div>
                        <p className="font-medium text-red-900 dark:text-red-300">Payment Failed</p>
                        <p className="text-sm text-red-700 dark:text-red-200">{paymentError}</p>
                      </div>
                    </div>
                  </div>
                )}
                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4 border-t border-gray-100 dark:border-gray-700">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setStep('packages')}
                    className="flex-1 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-xl font-medium hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors bg-white/80 dark:bg-gray-900/80 shadow-sm"
                  >
                    Back
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handlePayment}
                    disabled={paymentLoading || !paymentMethod}
                    className={
                      `flex-1 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg text-lg ` +
                      ((paymentLoading || !paymentMethod) ? 'opacity-50 cursor-not-allowed' : '')
                    }
                  >
                    {paymentLoading ? 'Processing...' : `Pay ${selectedPackage.currency_code} ${formatPrice(selectedPackage.price)}`}
                  </motion.button>
                </div>
              </motion.div>
            )}

            {/* Redirecting Step */}
            {step === 'redirecting' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <PaymentLoadingIndicator text={t('wallet.redirectingToBillplz', 'Redirecting to Billplz...')} />
                <p className="text-gray-600 dark:text-gray-50 mt-4">{t('wallet.pleaseWait', "You'll be redirected to the payment page.")}</p>
              </motion.div>
            )}

            {/* Processing Step */}
            {step === 'processing' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <PaymentLoadingIndicator text="Processing your payment..." />
                <p className="text-gray-600 dark:text-gray-300 mt-4">Please don't close this window</p>
              </motion.div>
            )}

            {/* Success Step */}
            {step === 'success' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-200 to-green-100 dark:from-green-900 dark:to-green-700 rounded-full flex items-center justify-center shadow"
                >
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-3xl animate-bounce" />
                </motion.div>
                <h3 className="text-2xl font-bold text-green-900 dark:text-green-300 mb-2">{t('wallet.paymentSuccessTitle', 'Payment Successful!')}</h3>
                <p className="text-green-700 dark:text-green-200 mb-4">{t('wallet.paymentSuccessDesc', 'Credits have been added to your wallet')}</p>
                {(successPackage || selectedPackage)?.credits ? (
                  <p className="text-lg font-semibold text-green-700 dark:text-green-200">
                    +{(successPackage || selectedPackage).credits.toLocaleString()} {t('wallet.credits', 'Credits')}
                  </p>
                ) : (
                  <p className="text-lg font-semibold text-green-700 dark:text-green-200">
                    Top-up successful!
                  </p>
                )}
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TopUpModal;
