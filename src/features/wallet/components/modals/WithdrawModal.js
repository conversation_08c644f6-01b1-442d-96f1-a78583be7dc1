import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faUniversity,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faEnvelope,
  faIdCard,
  faCog,
  faArrowRight,
  faCoins,
  faChevronDown,
  faMobileAlt,
  faCreditCard
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { useBankAccounts } from '../../../banking/contexts';
import BankAccountSelector from '../../../banking/components/selectors/BankAccountSelector';
import WalletErrorHandler from '../common/WalletErrorHandler';
import ekycService from '../../../../services/ekycService';
import walletService from '../../../../services/walletService';
import bankAccountService from '../../../../services/bankAccountService';
import { useToast } from '../../../../components/common/ToastProvider';

/**
 * Withdrawal Modal Component
 *
 * Modal-based workflow for withdrawing credits with verification-first approach,
 * bank account selection, and withdrawal processing.
 */
const WithdrawModal = ({
  isOpen = false,
  onClose,
  onSuccess,
  className = '',
  onOpenSettings
}) => {
  // All hooks must be called at the top, before any return or conditional
  const { success: showSuccessToast, error: showErrorToast, info: showInfoToast } = useToast();
  const {
    balance,
    withdrawalEligibility,
    eligibilityLoading,
    loadWithdrawalEligibility,
    processWithdrawal,
    withdrawalLoading,
    withdrawalError,
    clearError
  } = useWallet();

  const {
    accounts,
    loading: accountsLoading,
    error: accountsError,
    loadBankAccounts,
    getVerifiedAccounts
  } = useBankAccounts();

  // Local state
  const [step, setStep] = useState('verification'); // 'verification', 'form', 'processing', 'success'
  const [amount, setAmount] = useState('');
  const [selectedBankAccount, setSelectedBankAccount] = useState(null);
  const [withdrawalData, setWithdrawalData] = useState(null);
  
  // Withdrawal method selection
  const [withdrawalMethod, setWithdrawalMethod] = useState('bank_account'); // 'bank_account' or 'duitnow'
  const [duitNowOptions, setDuitNowOptions] = useState([]);
  const [selectedDuitNowOption, setSelectedDuitNowOption] = useState(null);
  const [duitNowLoading, setDuitNowLoading] = useState(false);
  // Remove activeTab state
  // const [activeTab, setActiveTab] = useState('withdraw'); // 'withdraw', 'exchange', 'history'

  // Verification status state
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [verificationLoading, setVerificationLoading] = useState(true);
  const [verificationError, setVerificationError] = useState(null);

  // Withdrawal currencies and options
  const [availableCurrencies, setAvailableCurrencies] = useState(['MYR']);
  const [selectedCurrency, setSelectedCurrency] = useState('MYR');
  const [withdrawalHistory, setWithdrawalHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  // Conversion rate state
  const [conversionRate, setConversionRate] = useState(null);
  const [conversionLoading, setConversionLoading] = useState(false);
  const [conversionError, setConversionError] = useState(null);

  // Load verification status and bank accounts when modal opens
  useEffect(() => {
    if (isOpen) {
      checkVerificationStatus();
      loadBankAccounts();
    }
  }, [isOpen, loadBankAccounts]);

  // After verification succeeds, load currencies, history and conversion rate
  useEffect(() => {
    if (isOpen && verificationStatus?.canWithdraw) {
      loadAvailableCurrencies();
      loadWithdrawalHistory();
      fetchConversionRate(selectedCurrency);
      loadDuitNowOptions();
    }
  }, [isOpen, verificationStatus, selectedCurrency]);

  // Fetch conversion rate
  const fetchConversionRate = async (currency) => {
    setConversionLoading(true);
    setConversionError(null);
    try {
      const rate = await walletService.getCreditConversionRate(currency);
      setConversionRate(rate);
    } catch (error) {
      setConversionError('Failed to fetch conversion rate');
      setConversionRate(null);
    } finally {
      setConversionLoading(false);
    }
  };

  // Function to check verification status using backend APIs
  const checkVerificationStatus = async () => {
    setVerificationLoading(true);
    setVerificationError(null);

    try {
      // Check E-KYC status
      const ekycResponse = await ekycService.getVerificationStatus();
      
      // Add validation for response
      if (!ekycResponse || !ekycResponse.data) {
        throw new Error('Invalid response from E-KYC service');
      }
      
      const ekycData = ekycResponse.data;

      // Get user data to check email verification
      const userResponse = await fetch(`${process.env.REACT_APP_API_URL}/user`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();

      // Determine verification status with null checks
      const emailVerified = !!userData?.email_verified_at;
      const ekycVerified = ekycData?.is_verified === true;

      const status = {
        emailVerified,
        ekycVerified,
        canWithdraw: emailVerified && ekycVerified,
        ekycStatus: ekycData?.is_verified ? 'verified' : 'not_verified',
        submittedAt: ekycData?.verified_at,
        userEmail: userData?.email
      };

      console.log('WithdrawModal - Final determined status:', status);
      setVerificationStatus(status);

      // If verified, proceed to form step
      if (status.canWithdraw) {
        setStep('form');
      }

    } catch (error) {
      console.error('Error checking verification status:', error);
      setVerificationError(error.message || 'Failed to check verification status');
    } finally {
      setVerificationLoading(false);
    }
  };

  // Load available currencies
  const loadAvailableCurrencies = async () => {
    try {
      const response = await walletService.getWithdrawalCurrencies();
      if (response.data && response.data.currencies) {
        setAvailableCurrencies(response.data.currencies);
      }
    } catch (error) {
      console.error('Error loading currencies:', error);
      // Keep default MYR if API fails
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setStep('verification');
      setAmount('');
      setSelectedBankAccount(null);
      setWithdrawalData(null);
      setWithdrawalMethod('bank_account');
      setSelectedDuitNowOption(null);
      setDuitNowOptions([]);
      // setActiveTab('withdraw'); // Removed activeTab reset
      setVerificationStatus(null);
      setVerificationLoading(true);
      setVerificationError(null);
      setWithdrawalHistory([]);
      clearError('withdrawal');
      setConversionRate(null);
      setConversionError(null);
      setConversionLoading(false);
    }
  }, [isOpen, clearError]);

  // Handle navigation to Settings
  const handleGoToSettings = (section = 'verification') => {
    if (onOpenSettings) {
      onOpenSettings(section);
    } else {
      onClose();
      // fallback: window.location.href = `/settings?section=${section}`;
    }
  };

  // Load withdrawal history
  const loadWithdrawalHistory = async () => {
    setHistoryLoading(true);
    try {
      const response = await walletService.getWithdrawalHistory();
      if (response.data) {
        const history =
          response.data.transactions ||
          response.data.withdrawals ||
          response.data;
        if (Array.isArray(history)) {
          setWithdrawalHistory(history);
        }
      }
    } catch (error) {
      console.error('Error loading withdrawal history:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // Load DuitNow options
  const loadDuitNowOptions = async () => {
    setDuitNowLoading(true);
    try {
      const response = await bankAccountService.getDuitNowOptions();
      if (response.data && Array.isArray(response.data)) {
        setDuitNowOptions(response.data);
      }
    } catch (error) {
      console.error('Error loading DuitNow options:', error);
    } finally {
      setDuitNowLoading(false);
    }
  };

  // Handle withdrawal processing
  const handleWithdrawal = async () => {
    if (!amount) return;
    if (withdrawalMethod === 'bank_account' && !selectedBankAccount) return;
    if (withdrawalMethod === 'duitnow' && !selectedDuitNowOption) return;

    try {
      setStep('processing');

      let withdrawalRequest;
      
      if (withdrawalMethod === 'bank_account') {
        withdrawalRequest = {
          amount: parseInt(amount), // Backend expects integer
          bank_account_id: selectedBankAccount.id,
          fiat_currency: selectedCurrency,
          payment_mode_id: 1, // Bank transfer payment mode
        };
      } else {
        // DuitNow withdrawal
        withdrawalRequest = {
          amount: parseInt(amount), // Backend expects integer
          bank_account_id: selectedDuitNowOption.code, // Send DuitNow option code (MBNO, ICNO, PPNO)
          fiat_currency: selectedCurrency,
          // payment_mode_id is optional, defaults to DuitNow (2)
        };
      }

      console.log('🚀 Submitting withdrawal request:', withdrawalRequest);
      const result = await walletService.withdraw(withdrawalRequest);

      if (result.data) {
        // Show backend message if present
        if (result.data.message) {
          showSuccessToast(result.data.message, 4000);
        }
        setWithdrawalData(result.data);
        setStep('success');
        setTimeout(() => {
          onSuccess && onSuccess(result.data);
          onClose();
        }, 3000);
      } else {
        throw new Error(result.message || 'Withdrawal failed');
      }

    } catch (error) {
      console.error('Withdrawal failed:', error);
      // Show error toast if backend provides a message
      const errorMsg =
        error?.response?.data?.message ||
        error?.message ||
        'Withdrawal failed. Please try again.';
      showErrorToast(errorMsg, 4000);
      setStep('form');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  // Calculate withdrawal details (use conversion rate from backend)
  const calculateWithdrawalDetails = (creditAmount, currency) => {
    if (!creditAmount || creditAmount <= 0 || !conversionRate) {
      return {
        creditAmount: 0,
        fiatAmount: 0,
        netAmount: 0,
        conversionRate: conversionRate || 0
      };
    }
    // If currency is MYR, use the fetched conversion rate (credits * rate)
    let fiatAmount = 0;
    if (currency === 'MYR') {
      fiatAmount = creditAmount * conversionRate;
    } else {
      fiatAmount = creditAmount * conversionRate;
    }
    const netAmount = fiatAmount;
    return {
      creditAmount,
      fiatAmount,
      netAmount,
      conversionRate
    };
  };

  // Minimum withdrawal in MYR
  const MIN_WITHDRAWAL_MYR = 20;
  const withdrawalAmount = parseFloat(amount) || 0;
  const withdrawalDetails = calculateWithdrawalDetails(withdrawalAmount, selectedCurrency);
  const isBelowMinWithdrawal = selectedCurrency === 'MYR' && withdrawalDetails.fiatAmount > 0 && withdrawalDetails.fiatAmount < MIN_WITHDRAWAL_MYR;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        key="modal"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 dark:bg-black/80 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={
            `w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-gradient-to-br from-white/95 via-white/90 to-blue-100/80 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-blue-900/80 backdrop-blur-2xl rounded-3xl shadow-2xl
            border border-white/40 dark:border-gray-700 ring-1 ring-blue-100/40 dark:ring-blue-900/40 ${className}`
          }
        >
          {/* Sticky Glassy Header */}
          <div className="sticky top-0 z-20 bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 backdrop-blur-xl border-b border-blue-100/60 dark:border-gray-700 p-8 rounded-t-3xl shadow-md flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                <FontAwesomeIcon icon={faCoins} className="text-white text-3xl" />
                </div>
                <div>
                <h2 className="text-3xl font-extrabold text-gray-900 dark:text-gray-100 tracking-tight">Withdraw Credits</h2>
                <p className="text-gray-600 dark:text-gray-300 text-left text-base font-medium">Cash out your credits</p>
                </div>
              </div>
              {step !== 'processing' && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                className="w-12 h-12 rounded-full bg-white/80 dark:bg-gray-900/80 hover:bg-gray-200 dark:hover:bg-gray-800 flex items-center justify-center transition-colors shadow border border-gray-200 dark:border-gray-700"
                aria-label="Close withdrawal modal"
                >
                <FontAwesomeIcon icon={faTimes} className="text-gray-600 dark:text-gray-200 text-2xl" />
                </motion.button>
              )}
          </div>

          {/* Multi-Step Progress Indicator */}
          <div className="w-full px-8 pt-4 pb-2 bg-transparent">
            <div className="flex items-center justify-between gap-2">
              {[
                { key: 'verification', label: 'Verification' },
                { key: 'form', label: 'Withdraw' },
                { key: 'processing', label: 'Processing' },
                { key: 'success', label: 'Success' },
              ].map((s, idx, arr) => {
                const stepOrder = ['verification', 'form', 'processing', 'success'];
                const currentIdx = stepOrder.indexOf(step);
                const isActive = step === s.key;
                const isCompleted = currentIdx > idx;
                return (
                  <React.Fragment key={s.key}>
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0.5 }}
                      animate={{ scale: isActive ? 1.1 : 1, opacity: isCompleted || isActive ? 1 : 0.5 }}
                      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                      className={`flex flex-col items-center`}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-lg shadow-md border-2 transition-all duration-300
                        ${isCompleted ? 'bg-green-500 border-green-500 text-white' :
                          isActive ? 'bg-blue-600 border-blue-600 text-white' :
                          'bg-white/70 dark:bg-gray-900/70 border-blue-200 dark:border-blue-700 text-blue-400 dark:text-blue-200'}
                      `}>
                        {isCompleted ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="3" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                        ) : (
                          idx + 1
                        )}
                      </div>
                      <span className={`mt-2 text-xs font-medium ${isActive ? 'text-blue-700 dark:text-blue-300' : isCompleted ? 'text-green-700 dark:text-green-300' : 'text-blue-400 dark:text-blue-200'}`}>{s.label}</span>
                    </motion.div>
                    {idx < arr.length - 1 && (
                      <motion.div
                        key={`line-${s.key}`}
                        initial={{ width: 0 }}
                        animate={{ width: 36 }}
                        transition={{ duration: 0.4 }}
                        className={`h-1 mx-1 rounded-full transition-all duration-300
                          ${currentIdx > idx ? 'bg-green-400 dark:bg-green-700' : currentIdx === idx ? 'bg-blue-400 dark:bg-blue-700' : 'bg-blue-100 dark:bg-blue-900'}
                        `}
                        style={{ minWidth: 24, maxWidth: 36 }}
                      />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-8">
            {/* Animated Step Sections */}
            <AnimatePresence mode="wait" initial={false}>
            {step === 'verification' && (
              <motion.div
                  key="step-verification"
                  initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -40 }}
                  transition={{ duration: 0.4, type: 'spring', stiffness: 300, damping: 30 }}
                className="space-y-6"
              >
                {verificationLoading ? (
                    <div className="flex flex-col items-center justify-center py-12">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                        className="w-14 h-14 border-4 border-blue-400 border-t-transparent rounded-full mb-6"
                      />
                      <p className="text-blue-700 text-lg font-semibold mb-2">Checking verification status...</p>
                      <p className="text-blue-500 text-sm">Please wait while we verify your account</p>
                  </div>
                ) : verificationError ? (
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      className="bg-gradient-to-br from-red-50/90 to-white/80 border border-red-200 rounded-2xl p-6 shadow-lg flex flex-col items-center"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                        className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 shadow"
                      >
                        <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 text-3xl animate-bounce" />
                      </motion.div>
                      <h3 className="text-lg font-bold text-red-800 mb-2">Verification Check Failed</h3>
                      <p className="text-red-600 mb-4">{verificationError}</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={checkVerificationStatus}
                        className="px-6 py-3 bg-red-600 text-white rounded-xl font-medium hover:bg-red-700 transition-colors"
                    >
                      Try Again
                    </motion.button>
                    </motion.div>
                ) : verificationStatus ? (
                  <div>
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                        <FontAwesomeIcon icon={faShieldAlt} className="text-white text-2xl" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Account Verification</h3>
                      <p className="text-gray-600">
                        {verificationStatus.canWithdraw
                          ? 'Your account is fully verified and ready for withdrawals'
                          : 'Complete verification to withdraw credits'
                        }
                      </p>
                    </div>

                    {/* Verification Status Cards */}
                    <div className="space-y-4">
                      {/* Email Verification Status */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`
                            border rounded-xl p-4 shadow bg-gradient-to-br from-white/80 to-green-50 transition-all
                          ${verificationStatus.emailVerified
                              ? 'border-green-200'
                              : 'border-orange-200'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`
                              w-10 h-10 rounded-full flex items-center justify-center
                              ${verificationStatus.emailVerified ? 'bg-green-100' : 'bg-orange-100'}
                            `}>
                              <FontAwesomeIcon
                                icon={verificationStatus.emailVerified ? faCheckCircle : faEnvelope}
                                className={verificationStatus.emailVerified ? 'text-green-600' : 'text-orange-600'}
                              />
                            </div>
                            <div>
                              <h4 className={`font-semibold text-left ${verificationStatus.emailVerified ? 'text-green-900' : 'text-orange-900'}`}>
                                Email Verification
                              </h4>
                              <p className={`text-sm ${verificationStatus.emailVerified ? 'text-green-700' : 'text-orange-700'}`}>
                                {verificationStatus.emailVerified
                                  ? `✅ Verified (${verificationStatus.userEmail})`
                                  : `❌ Not Verified (${verificationStatus.userEmail})`
                                }
                              </p>
                            </div>
                          </div>

                          {!verificationStatus.emailVerified && (
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleGoToSettings('email')}
                              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
                            >
                              <FontAwesomeIcon icon={faCog} className="text-sm" />
                              <span>Go to Settings</span>
                              <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>

                      {/* E-KYC Verification Status */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className={`
                            border rounded-xl p-4 shadow bg-gradient-to-br from-white/80 to-blue-50 transition-all
                          ${verificationStatus.ekycVerified
                              ? 'border-green-200'
                              : 'border-blue-200'
                          }
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`
                              w-10 h-10 rounded-full flex items-center justify-center
                              ${verificationStatus.ekycVerified ? 'bg-green-100' : 'bg-blue-100'}
                            `}>
                              <FontAwesomeIcon
                                icon={verificationStatus.ekycVerified ? faCheckCircle : faIdCard}
                                className={verificationStatus.ekycVerified ? 'text-green-600' : 'text-blue-600'}
                              />
                            </div>
                            <div>
                              <h4 className={`font-semibold text-left ${verificationStatus.ekycVerified ? 'text-green-900' : 'text-blue-900'}`}>
                                Identity Verification (E-KYC)
                              </h4>
                              <p className={`text-sm ${verificationStatus.ekycVerified ? 'text-green-700' : 'text-blue-700'}`}>
                                {verificationStatus.ekycVerified
                                  ? '✅ Verified'
                                  : `❌ Not Verified (Status: ${verificationStatus.ekycStatus})`
                                }
                              </p>
                            </div>
                          </div>

                          {!verificationStatus.ekycVerified && (
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleGoToSettings('kyc')}
                              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                            >
                              <FontAwesomeIcon icon={faCog} className="text-sm" />
                              <span>Go to Settings</span>
                              <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                            </motion.button>
                          )}
                        </div>
                      </motion.div>
                    </div>

                    {/* Information Notice */}
                    <div className="mt-6 bg-indigo-50 border border-indigo-200 rounded-xl p-4">
                      <div className="flex items-start space-x-3">
                        <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-indigo-900 mb-1">Why verification is required</p>
                          <p className="text-sm text-indigo-700">
                            Both Email and E-KYC verification are required to withdraw credits. This ensures account security and compliance with financial regulations.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Progress Indicator */}
                      <div className="mt-6 bg-gradient-to-br from-white/80 to-gray-50 rounded-xl p-4 border border-gray-200 shadow">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">Verification Progress</span>
                        <span className="text-sm text-gray-600">
                          {[verificationStatus.emailVerified, verificationStatus.ekycVerified].filter(Boolean).length} / 2
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{
                            width: `${([verificationStatus.emailVerified, verificationStatus.ekycVerified].filter(Boolean).length / 2) * 100}%`
                          }}
                          transition={{ duration: 0.5 }}
                          className="bg-gradient-to-r from-indigo-500 to-blue-600 h-2 rounded-full"
                        />
                      </div>
                    </div>

                    {/* Action Button */}
                    {verificationStatus.canWithdraw && (
                      <div className="mt-6 text-center">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setStep('form')}
                          className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg"
                        >
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                          Continue to Withdrawal
                        </motion.button>
                      </div>
                    )}
                  </div>
                ) : null}
              </motion.div>
            )}

            {step === 'form' && (
              <motion.div
                  key="step-form"
                  initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -40 }}
                  transition={{ duration: 0.4, type: 'spring', stiffness: 300, damping: 30 }}
                className="space-y-6"
              >
                  {/* Withdraw Credits Form (only) */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Withdrawal Details</h3>
                      <p className="text-gray-600">Enter the amount you want to withdraw</p>
                    </div>

                    {/* Available Balance */}
                    <div className="bg-gradient-to-br from-white/95 to-blue-50/90 rounded-2xl p-6 border border-blue-100 shadow-lg mb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-600 text-left text-sm">Available Balance</p>
                          <p className="text-2xl font-bold text-gray-900">{balance?.credits?.toLocaleString() || 0} Credits</p>
                        </div>
                        <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                          <FontAwesomeIcon icon={faCoins} className="text-blue-600" />
                        </div>
                      </div>
                    </div>

                    {/* Currency Selection */}
                    <div className="relative mb-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2 transition-all duration-200">
                        Currency
                      </label>
                      <div className="relative">
                      <select
                        value={selectedCurrency}
                          onChange={(e) => {
                            setSelectedCurrency(e.target.value);
                            fetchConversionRate(e.target.value);
                          }}
                          className="w-full appearance-none px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white/95 shadow-md text-base font-semibold text-gray-900 hover:bg-white/100 cursor-pointer"
                      >
                        {availableCurrencies.map(currency => (
                          <option key={currency} value={currency}>{currency}</option>
                        ))}
                      </select>
                        <span className="pointer-events-none absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 text-lg flex items-center">
                          <FontAwesomeIcon icon={faChevronDown} />
                        </span>
                      </div>
                    </div>

                    {/* Withdrawal Amount */}
                    <div className="relative mb-2">
                      <label className={`block text-sm font-medium mb-2 transition-all duration-200 ${amount ? 'text-indigo-600' : 'text-gray-700'}`}
                        htmlFor="withdrawal-amount-input"
                      >
                        <span className="inline-flex items-center">
                        Withdrawal Amount (Credits)
                          <span className="ml-1 text-blue-500 cursor-pointer group relative">
                            <FontAwesomeIcon icon={faInfoCircle} />
                            <span className="absolute left-6 top-1/2 -translate-y-1/2 w-48 bg-white/90 text-gray-700 text-xs rounded-lg shadow-lg px-3 py-2 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity z-20 border border-blue-100">
                              Enter the number of credits you wish to withdraw. Minimum is 1, maximum is your available balance.
                            </span>
                          </span>
                        </span>
                      </label>
                      <div className="relative">
                        <input
                          id="withdrawal-amount-input"
                          type="number"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          placeholder="Enter amount (minimum 1)"
                          min="1"
                          max={balance?.credits || 0}
                          className={`w-full px-4 py-3 pr-20 border rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all shadow-sm bg-white/80
                            ${amount && (withdrawalAmount <= 0 || withdrawalAmount > (balance?.credits || 0) || isBelowMinWithdrawal) ? 'border-red-400' : 'border-gray-300'}
                          `}
                          aria-invalid={amount && (withdrawalAmount <= 0 || withdrawalAmount > (balance?.credits || 0) || isBelowMinWithdrawal)}
                        />
                        {/* Error/Success Icon - right, before Max button */}
                        {amount && (
                          <span className="absolute right-16 top-1/2 -translate-y-1/2">
                            {withdrawalAmount > 0 && withdrawalAmount <= (balance?.credits || 0) && !isBelowMinWithdrawal ? (
                              <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 text-right" />
                            ) : (
                              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-right" />
                            )}
                          </span>
                        )}
                        <button
                          onClick={() => setAmount(balance?.credits?.toString() || '0')}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-indigo-600 bg-transparent hover:bg-gray-100 text-sm font-medium hover:text-indigo-700"
                        >
                          Max
                        </button>
                      </div>
                      {/* Error Message */}
                      {amount && withdrawalAmount <= 0 && (
                        <div className="text-xs text-red-600 mt-1 flex items-center gap-1">
                          <FontAwesomeIcon icon={faExclamationTriangle} /> Enter a valid amount greater than 0.
                        </div>
                      )}
                      {amount && withdrawalAmount > (balance?.credits || 0) && (
                        <div className="text-xs text-red-600 mt-1 flex items-center gap-1">
                          <FontAwesomeIcon icon={faExclamationTriangle} /> Amount exceeds available balance.
                        </div>
                      )}
                      {amount && isBelowMinWithdrawal && (
                        <div className="text-xs text-red-600 mt-1 flex items-center gap-1">
                          <FontAwesomeIcon icon={faExclamationTriangle} /> Minimum withdrawal is MYR 20.00
                        </div>
                      )}
                    </div>

                {/* Withdrawal Method Selection */}
                <div className="relative mb-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Withdrawal Method *
                    <span className="ml-1 text-blue-500 cursor-pointer group relative">
                      <FontAwesomeIcon icon={faInfoCircle} />
                      <span className="absolute left-6 top-1/2 -translate-y-1/2 w-56 bg-white/90 text-gray-700 text-xs rounded-lg shadow-lg px-3 py-2 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity z-20 border border-blue-100">
                        Choose how you want to receive your withdrawal - via bank transfer or DuitNow.
                      </span>
                    </span>
                  </label>
                  
                  {/* Method Selection Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* Bank Account Card */}
                    <motion.button
                      type="button"
                      onClick={() => setWithdrawalMethod('bank_account')}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`relative p-6 rounded-2xl border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60
                        ${withdrawalMethod === 'bank_account'
                          ? 'bg-gradient-to-br from-blue-500/90 to-indigo-500/90 text-white border-blue-600 shadow-lg shadow-blue-500/20'
                          : 'bg-white/90 text-gray-800 border-gray-200 hover:border-blue-400 hover:bg-blue-50/60 hover:shadow-md'
                        }
                      `}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center
                          ${withdrawalMethod === 'bank_account' ? 'bg-white/20' : 'bg-blue-100'}
                        `}>
                          <FontAwesomeIcon 
                            icon={faCreditCard} 
                            className={`text-2xl ${withdrawalMethod === 'bank_account' ? 'text-white' : 'text-blue-600'}`} 
                          />
                        </div>
                        <div className="text-left">
                          <h4 className={`font-semibold text-lg ${withdrawalMethod === 'bank_account' ? 'text-white' : 'text-gray-900'}`}>
                            Bank Transfer
                          </h4>
                          <p className={`text-sm ${withdrawalMethod === 'bank_account' ? 'text-blue-100' : 'text-gray-600'}`}>
                            Transfer to your bank account
                          </p>
                        </div>
                      </div>
                      {/* Checkmark for selected */}
                      <AnimatePresence>
                        {withdrawalMethod === 'bank_account' && (
                          <motion.span
                            initial={{ scale: 0, opacity: 0, rotate: -90 }}
                            animate={{ scale: 1, opacity: 1, rotate: 0 }}
                            exit={{ scale: 0, opacity: 0, rotate: 90 }}
                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                            className="absolute -top-2 -right-2 w-7 h-7 bg-green-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white"
                          >
                            <FontAwesomeIcon icon={faCheckCircle} className="text-white text-sm" />
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </motion.button>

                    {/* DuitNow Card */}
                    <motion.button
                      type="button"
                      onClick={() => setWithdrawalMethod('duitnow')}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`relative p-6 rounded-2xl border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60
                        ${withdrawalMethod === 'duitnow'
                          ? 'bg-gradient-to-br from-pink-400/90 to-rose-400/90 text-white border-pink-500 shadow-lg shadow-pink-500/20'
                          : 'bg-white/90 text-gray-800 border-gray-200 hover:border-pink-400 hover:bg-pink-50/60 hover:shadow-md'
                        }
                      `}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center
                          ${withdrawalMethod === 'duitnow' ? 'bg-white/20' : 'bg-pink-100'}
                        `}>
                          <img 
                            src="/DuitNowLogo.png" 
                            alt="DuitNow" 
                            className="w-8 h-8 object-contain"
                          />
                        </div>
                        <div className="text-left">
                          <h4 className={`font-semibold text-lg ${withdrawalMethod === 'duitnow' ? 'text-white' : 'text-gray-900'}`}>
                            DuitNow
                          </h4>
                          <p className={`text-sm ${withdrawalMethod === 'duitnow' ? 'text-pink-100' : 'text-gray-600'}`}>
                            Instant transfer via DuitNow
                          </p>
                        </div>
                      </div>
                      {/* Checkmark for selected */}
                      <AnimatePresence>
                        {withdrawalMethod === 'duitnow' && (
                          <motion.span
                            initial={{ scale: 0, opacity: 0, rotate: -90 }}
                            animate={{ scale: 1, opacity: 1, rotate: 0 }}
                            exit={{ scale: 0, opacity: 0, rotate: 90 }}
                            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                            className="absolute -top-2 -right-2 w-7 h-7 bg-pink-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white"
                          >
                            <FontAwesomeIcon icon={faCheckCircle} className="text-white text-sm" />
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </motion.button>
                  </div>

                  {/* Method-specific selection */}
                  {withdrawalMethod === 'bank_account' && (
                    <div className="bg-gradient-to-br from-white/95 to-blue-50/90 rounded-xl p-4 border border-blue-100 shadow-sm">
                      {/* Error Handler for Bank Accounts */}
                      <WalletErrorHandler
                        context="bank_accounts"
                        onRetry={loadBankAccounts}
                        showToasts={false}
                        showInlineErrors={true}
                        autoRecovery={true}
                      />
                      {accountsLoading ? (
                        <div className="flex items-center space-x-3 animate-pulse">
                          <div className="rounded-full h-4 w-4 bg-blue-200" />
                          <span className="text-gray-500 text-sm">Loading bank accounts...</span>
                        </div>
                      ) : accounts.length === 0 ? (
                        <div className="text-center">
                          <FontAwesomeIcon icon={faUniversity} className="text-gray-400 text-2xl mb-2" />
                          <p className="text-gray-500 text-sm mb-2">No bank accounts found</p>
                          <p className="text-gray-400 text-xs">Please add a bank account first to enable withdrawals</p>
                        </div>
                      ) : getVerifiedAccounts().length === 0 ? (
                        <div className="border border-orange-300 rounded-xl p-4 bg-orange-50">
                          <div className="text-center">
                            <FontAwesomeIcon icon={faExclamationTriangle} className="text-orange-500 text-2xl mb-2" />
                            <p className="text-orange-700 text-sm mb-2">No verified bank accounts found</p>
                            <p className="text-orange-600 text-xs">Bank account verification is required for withdrawals</p>
                          </div>
                        </div>
                      ) : (
                        <BankAccountSelector
                          selectedAccountId={selectedBankAccount?.id}
                          onAccountChange={setSelectedBankAccount}
                          verifiedOnly={true}
                          showAddButton={false}
                          showSearch={false}
                          placeholder="Select bank account for withdrawal"
                          className="mb-2 bg-white/80 rounded-xl shadow-sm"
                        />
                      )}
                    </div>
                  )}

                  {withdrawalMethod === 'duitnow' && (
                    <div className="bg-gradient-to-br from-white/95 to-pink-50/90 rounded-xl p-4 border border-pink-100 shadow-sm">
                      {duitNowLoading ? (
                        <div className="flex items-center space-x-3 animate-pulse">
                          <div className="rounded-full h-4 w-4 bg-pink-200" />
                          <span className="text-gray-500 text-sm">Loading DuitNow options...</span>
                        </div>
                      ) : duitNowOptions.length === 0 ? (
                        <div className="text-center">
                          <img src="/DuitNowLogo.png" alt="DuitNow" className="w-12 h-12 mx-auto mb-2 object-contain opacity-40" />
                          <p className="text-gray-500 text-sm mb-2">No DuitNow options available</p>
                          <p className="text-gray-400 text-xs">Please try bank transfer instead</p>
                        </div>
                      ) : selectedDuitNowOption ? (
                        // Show selected DuitNow option only
                        <div className="space-y-3">
                          <div className="flex items-center justify-between mb-3">
                            <label className="block text-sm font-medium text-gray-700">
                              Selected DuitNow Option
                            </label>
                                                         <motion.button
                               type="button"
                               onClick={() => setSelectedDuitNowOption(null)}
                               whileHover={{ scale: 1.05 }}
                               whileTap={{ scale: 0.95 }}
                               className="text-sm text-pink-600 bg-transparent hover:bg-pink-50/60 hover:rounded-xl hover:text-pink-700 font-medium flex items-center space-x-1"
                             >
                               <span>Change</span>
                               <FontAwesomeIcon icon={faChevronDown} className="text-xs" />
                             </motion.button>
                          </div>
                                                     <motion.div
                             initial={{ opacity: 0, y: 10 }}
                             animate={{ opacity: 1, y: 0 }}
                             className="relative p-4 rounded-xl border-2 bg-gradient-to-br from-pink-400/90 to-rose-400/90 text-white border-pink-500 shadow-lg shadow-pink-500/20"
                           >
                             <div className="flex items-center justify-between">
                               <div className="flex items-center space-x-3">
                                 <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-white/20">
                                   <img src="/DuitNowLogo.png" alt="DuitNow" className="w-6 h-6 object-contain" />
                                 </div>
                                 <div className="text-left">
                                   <h5 className="font-semibold text-white">
                                     {selectedDuitNowOption.name || 'DuitNow Transfer'}
                                   </h5>
                                   <p className="text-sm text-pink-100">
                                     {selectedDuitNowOption.description || 'Instant transfer via DuitNow'}
                                   </p>
                                 </div>
                               </div>
                               <div className="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center shadow-lg border border-white">
                                 <FontAwesomeIcon icon={faCheckCircle} className="text-white text-xs" />
                               </div>
                             </div>
                           </motion.div>
                        </div>
                      ) : (
                        // Show DuitNow options for selection
                        <div className="space-y-3">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Select DuitNow Option
                          </label>
                          <div className="grid grid-cols-1 gap-3">
                            {duitNowOptions.map((option) => (
                              <motion.button
                                key={option.id}
                                type="button"
                                onClick={() => setSelectedDuitNowOption(option)}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className="relative p-4 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400/60 bg-white/80 text-gray-800 border-gray-200 hover:border-pink-400 hover:bg-pink-50/60 hover:shadow-md"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-pink-100">
                                      <img src="/DuitNowLogo.png" alt="DuitNow" className="w-5 h-5 object-contain" />
                                    </div>
                                    <div className="text-left">
                                      <h5 className="font-semibold text-gray-900">
                                        {option.name || 'DuitNow Transfer'}
                                      </h5>
                                      <p className="text-sm text-gray-600">
                                        {option.description || 'Instant transfer via DuitNow'}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Withdrawal Summary */}
                {withdrawalAmount > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-br from-white/95 to-blue-50/90 rounded-2xl p-6 border border-gray-200 shadow"
                  >
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mr-2" />
                      Withdrawal Summary
                    </h4>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Withdrawal Amount</span>
                        <span className="font-semibold">{withdrawalDetails.creditAmount.toLocaleString()} Credits</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Conversion Rate</span>
                        {conversionLoading ? (
                          <span className="text-blue-500 text-sm">Loading...</span>
                        ) : conversionError ? (
                          <span className="text-red-500 text-sm">{conversionError}</span>
                        ) : (
                          <span className="font-semibold text-blue-700">{conversionRate ? `1 Credit = ${conversionRate.toFixed(4)} MYR` : '-'}</span>
                        )}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Method</span>
                        <span className="font-semibold flex items-center">
                          {withdrawalMethod === 'bank_account' ? (
                            <FontAwesomeIcon icon={faCreditCard} className="mr-2 text-blue-600" />
                          ) : (
                            <img src="/DuitNowLogo.png" alt="DuitNow" className="w-5 h-5 mr-2 object-contain" />
                          )}
                          {withdrawalMethod === 'bank_account' ? 'Bank Transfer' : 'DuitNow'}
                        </span>
                      </div>
                      {withdrawalMethod === 'bank_account' && selectedBankAccount && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Bank Account</span>
                          <span className="font-semibold text-blue-700">{selectedBankAccount.bank_name} - {selectedBankAccount.account_number}</span>
                        </div>
                      )}
                      {withdrawalMethod === 'duitnow' && selectedDuitNowOption && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">DuitNow Option</span>
                          <span className="font-semibold text-pink-700">{selectedDuitNowOption.name}</span>
                        </div>
                      )}
                      <div className="border-t border-gray-300 pt-3 flex justify-between text-lg font-bold">
                        <span className="text-gray-900">You'll Receive</span>
                        <span className="text-green-600">{withdrawalDetails.netAmount.toFixed(2)} {selectedCurrency}</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Processing Time Notice */}
                <div className="bg-blue-50 items-center border border-blue-200 rounded-xl p-4">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600" />
                    <div>
                          <p className="font-medium text-left text-blue-900">Processing Time</p>
                          <p className="text-sm text-left text-blue-700">Withdrawals typically take 1-3 business days to process</p>
                    </div>
                  </div>
                </div>

                {/* Error Display */}
                {withdrawalError && (
                      <motion.div
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        className="bg-gradient-to-br from-white/95 to-red-50/90 border border-red-200 rounded-2xl p-6 shadow-lg flex items-center gap-4 mb-4"
                      >
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                          className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center shadow"
                        >
                          <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 text-xl animate-bounce" />
                        </motion.div>
                      <div>
                        <p className="font-medium text-red-900">Withdrawal Failed</p>
                        <p className="text-sm text-red-700">{withdrawalError}</p>
                      </div>
                      </motion.div>
                )}

                    {/* Action Buttons */}
                    <div className="flex space-x-4 pt-4 border-t border-gray-100">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleClose}
                        className="flex-1 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-100 transition-colors bg-white/80 shadow-sm"
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleWithdrawal}
                        disabled={
                          !amount ||
                          (withdrawalMethod === 'bank_account' && (!selectedBankAccount || !selectedBankAccount.is_verified)) ||
                          (withdrawalMethod === 'duitnow' && !selectedDuitNowOption) ||
                          withdrawalLoading ||
                          withdrawalAmount <= 0 ||
                          withdrawalAmount > (balance?.credits || 0) ||
                          isBelowMinWithdrawal
                        }
                        className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all shadow-lg disabled:opacity-50 text-lg"
                      >
                        {withdrawalLoading ? 'Processing...' :
                         withdrawalMethod === 'bank_account' && !selectedBankAccount ? 'Select Bank Account' :
                         withdrawalMethod === 'bank_account' && !selectedBankAccount?.is_verified ? 'Account Not Verified' :
                         withdrawalMethod === 'duitnow' && !selectedDuitNowOption ? 'Select DuitNow Option' :
                         'Withdraw Credits'}
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
                )}

              {step === 'processing' && (
                          <motion.div
                  key="step-processing"
                  initial={{ opacity: 0, x: 40 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -40 }}
                  transition={{ duration: 0.4, type: 'spring', stiffness: 300, damping: 30 }}
                  className="flex flex-col items-center justify-center py-16"
                >
                  {/* Animated Spinner and Progress Bar */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mb-8 shadow-lg"
                  />
                  <div className="w-full max-w-xs bg-gradient-to-r from-blue-100/80 to-indigo-100/80 rounded-full h-3 mb-6 overflow-hidden border border-blue-200 shadow">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: 'loop', ease: 'linear' }}
                      className="h-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full"
                                  />
                                </div>
                  <p className="text-blue-700 text-lg font-semibold mb-2">Processing your withdrawal...</p>
                  <p className="text-blue-500 text-sm">Please don't close this window</p>
              </motion.div>
            )}

            {step === 'success' && withdrawalData && (
              <motion.div
                  key="step-success"
                  initial={{ opacity: 0, x: 40 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -40 }}
                  transition={{ duration: 0.4, type: 'spring', stiffness: 300, damping: 30 }}
                className="text-center py-12"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-200 to-green-100 rounded-full flex items-center justify-center shadow"
                >
                    <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-3xl animate-bounce" />
                </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-gradient-to-br from-white/95 to-green-50/90 border border-green-200 rounded-2xl p-6 shadow-lg text-left max-w-md mx-auto mb-6"
                  >
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Amount:</strong> {withdrawalDetails.netAmount.toFixed(2)} {selectedCurrency}
                  </p>
                  <p className="text-sm text-green-700 mb-2">
                    <strong>Reference:</strong> {withdrawalData.reference || 'WD-' + Date.now()}
                  </p>
                  <p className="text-sm text-green-700">
                    <strong>Expected:</strong> 1-3 business days
                  </p>
                  </motion.div>
                  <h3 className="text-2xl font-bold text-green-900 mb-2">Withdrawal Submitted!</h3>
                  <p className="text-green-700 mb-4">Your withdrawal request has been processed</p>
              </motion.div>
            )}
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
      {/* Sticky Footer with Actions */}
      <div
        key="footer"
        className="sticky bottom-0 left-0 right-0 z-30 px-8 py-6 bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 backdrop-blur-xl border-t border-blue-100/60 flex flex-col sm:flex-row gap-3 sm:gap-6 items-center justify-end rounded-b-3xl shadow-lg"
      >
        {/* Step-based actions */}
        {step === 'verification' && verificationStatus?.canWithdraw && (
          <motion.button
            whileHover={{ scale: 1.04 }}
            whileTap={{ scale: 0.97 }}
            onClick={() => setStep('form')}
            className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg text-lg"
          >
            Continue to Withdrawal
          </motion.button>
        )}
        {step === 'form' && (
          <>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleClose}
              className="flex-1 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-100 transition-colors bg-white/80 shadow-sm"
            >
              Cancel
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleWithdrawal}
              disabled={
                !amount ||
                (withdrawalMethod === 'bank_account' && (!selectedBankAccount || !selectedBankAccount.is_verified)) ||
                (withdrawalMethod === 'duitnow' && !selectedDuitNowOption) ||
                withdrawalLoading ||
                withdrawalAmount <= 0 ||
                withdrawalAmount > (balance?.credits || 0) ||
                isBelowMinWithdrawal
              }
              className="flex-1 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all shadow-lg disabled:opacity-50 text-lg"
            >
              {withdrawalLoading ? 'Processing...' :
               withdrawalMethod === 'bank_account' && !selectedBankAccount ? 'Select Bank Account' :
               withdrawalMethod === 'bank_account' && !selectedBankAccount?.is_verified ? 'Account Not Verified' :
               withdrawalMethod === 'duitnow' && !selectedDuitNowOption ? 'Select DuitNow Option' :
               'Withdraw Credits'}
            </motion.button>
          </>
        )}
        {step === 'success' && (
          <motion.button
            whileHover={{ scale: 1.04 }}
            whileTap={{ scale: 0.97 }}
            onClick={handleClose}
            className="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg text-lg"
          >
            Done
          </motion.button>
        )}
      </div>
    </AnimatePresence>
  );
};

export default WithdrawModal;
