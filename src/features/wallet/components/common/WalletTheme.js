/**
 * Wallet Theme Constants
 * 
 * Centralized theme configuration for wallet components with pearl white base
 * and royal blue/indigo accents for premium trustworthy design.
 */

// Base color palette
export const WALLET_COLORS = {
  // Pearl white base colors
  base: {
    white: '#FFFFFF',
    pearl: '#FEFEFE',
    lightGray: '#F8FAFC',
    mediumGray: '#F1F5F9',
    darkGray: '#E2E8F0'
  },

  // Royal blue and indigo accents
  primary: {
    50: '#EEF2FF',
    100: '#E0E7FF',
    200: '#C7D2FE',
    300: '#A5B4FC',
    400: '#818CF8',
    500: '#6366F1', // Primary royal blue
    600: '#4F46E5', // Primary indigo
    700: '#4338CA',
    800: '#3730A3',
    900: '#312E81'
  },

  // Secondary blue palette
  secondary: {
    50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A'
  },

  // Success colors (green)
  success: {
    50: '#F0FDF4',
    100: '#DCFCE7',
    200: '#BBF7D0',
    300: '#86EFAC',
    400: '#4ADE80',
    500: '#22C55E',
    600: '#16A34A',
    700: '#15803D',
    800: '#166534',
    900: '#14532D'
  },

  // Warning colors (amber/orange)
  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F'
  },

  // Error colors (red)
  error: {
    50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D'
  }
};

// Glass-morphism effects
export const GLASS_EFFECTS = {
  // Light glass effect
  light: {
    background: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(8px)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
  },

  // Medium glass effect
  medium: {
    background: 'rgba(255, 255, 255, 0.9)',
    backdropFilter: 'blur(12px)',
    border: '1px solid rgba(255, 255, 255, 0.4)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)'
  },

  // Strong glass effect
  strong: {
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(16px)',
    border: '1px solid rgba(255, 255, 255, 0.5)',
    boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)'
  },

  // Colored glass effects
  primaryGlass: {
    background: 'rgba(99, 102, 241, 0.1)',
    backdropFilter: 'blur(8px)',
    border: '1px solid rgba(99, 102, 241, 0.2)',
    boxShadow: '0 8px 32px rgba(99, 102, 241, 0.1)'
  },

  successGlass: {
    background: 'rgba(34, 197, 94, 0.1)',
    backdropFilter: 'blur(8px)',
    border: '1px solid rgba(34, 197, 94, 0.2)',
    boxShadow: '0 8px 32px rgba(34, 197, 94, 0.1)'
  }
};

// Typography scale
export const TYPOGRAPHY = {
  // Font sizes
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem'  // 60px
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800'
  },

  // Line heights
  lineHeight: {
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2'
  }
};

// Spacing scale
export const SPACING = {
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
  '4xl': '6rem',   // 96px
  '5xl': '8rem'    // 128px
};

// Border radius
export const BORDER_RADIUS = {
  sm: '0.375rem',   // 6px
  md: '0.5rem',     // 8px
  lg: '0.75rem',    // 12px
  xl: '1rem',       // 16px
  '2xl': '1.5rem',  // 24px
  '3xl': '2rem',    // 32px
  full: '9999px'
};

// Component-specific styles
export const COMPONENT_STYLES = {
  // Card styles
  card: {
    base: {
      backgroundColor: WALLET_COLORS.base.pearl,
      borderRadius: BORDER_RADIUS['2xl'],
      padding: SPACING.xl,
      ...GLASS_EFFECTS.light
    },
    elevated: {
      backgroundColor: WALLET_COLORS.base.white,
      borderRadius: BORDER_RADIUS['2xl'],
      padding: SPACING.xl,
      ...GLASS_EFFECTS.medium
    },
    premium: {
      backgroundColor: WALLET_COLORS.base.white,
      borderRadius: BORDER_RADIUS['3xl'],
      padding: SPACING['2xl'],
      ...GLASS_EFFECTS.strong
    }
  },

  // Button styles
  button: {
    primary: {
      backgroundColor: `linear-gradient(135deg, ${WALLET_COLORS.primary[500]}, ${WALLET_COLORS.primary[600]})`,
      color: WALLET_COLORS.base.white,
      borderRadius: BORDER_RADIUS.xl,
      padding: `${SPACING.md} ${SPACING.xl}`,
      fontWeight: TYPOGRAPHY.fontWeight.semibold,
      boxShadow: `0 4px 16px rgba(99, 102, 241, 0.3)`
    },
    secondary: {
      backgroundColor: WALLET_COLORS.base.white,
      color: WALLET_COLORS.primary[600],
      border: `1px solid ${WALLET_COLORS.primary[200]}`,
      borderRadius: BORDER_RADIUS.xl,
      padding: `${SPACING.md} ${SPACING.xl}`,
      fontWeight: TYPOGRAPHY.fontWeight.medium
    },
    success: {
      backgroundColor: `linear-gradient(135deg, ${WALLET_COLORS.success[500]}, ${WALLET_COLORS.success[600]})`,
      color: WALLET_COLORS.base.white,
      borderRadius: BORDER_RADIUS.xl,
      padding: `${SPACING.md} ${SPACING.xl}`,
      fontWeight: TYPOGRAPHY.fontWeight.semibold,
      boxShadow: `0 4px 16px rgba(34, 197, 94, 0.3)`
    }
  },

  // Input styles
  input: {
    base: {
      backgroundColor: WALLET_COLORS.base.white,
      border: `1px solid ${WALLET_COLORS.base.darkGray}`,
      borderRadius: BORDER_RADIUS.xl,
      padding: `${SPACING.md} ${SPACING.lg}`,
      fontSize: TYPOGRAPHY.fontSize.base,
      color: WALLET_COLORS.primary[900]
    },
    focused: {
      borderColor: WALLET_COLORS.primary[500],
      boxShadow: `0 0 0 3px rgba(99, 102, 241, 0.1)`
    }
  },

  // Modal styles
  modal: {
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      backdropFilter: 'blur(4px)'
    },
    content: {
      backgroundColor: WALLET_COLORS.base.white,
      borderRadius: BORDER_RADIUS['3xl'],
      ...GLASS_EFFECTS.strong,
      maxWidth: '32rem',
      width: '100%'
    }
  }
};

// Animation configurations
export const ANIMATIONS = {
  // Transition durations
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms'
  },

  // Easing functions
  easing: {
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },

  // Common animations
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 }
  },

  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4 }
  },

  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.3 }
  }
};

// Utility functions
export const getGlassStyle = (variant = 'light') => {
  return GLASS_EFFECTS[variant] || GLASS_EFFECTS.light;
};

export const getButtonStyle = (variant = 'primary') => {
  return COMPONENT_STYLES.button[variant] || COMPONENT_STYLES.button.primary;
};

export const getCardStyle = (variant = 'base') => {
  return COMPONENT_STYLES.card[variant] || COMPONENT_STYLES.card.base;
};

// CSS-in-JS helper functions
export const createGlassCSS = (variant = 'light') => {
  const glass = getGlassStyle(variant);
  return {
    background: glass.background,
    backdropFilter: glass.backdropFilter,
    border: glass.border,
    boxShadow: glass.boxShadow
  };
};

export const createButtonCSS = (variant = 'primary') => {
  const button = getButtonStyle(variant);
  return {
    background: button.backgroundColor,
    color: button.color,
    border: button.border || 'none',
    borderRadius: button.borderRadius,
    padding: button.padding,
    fontWeight: button.fontWeight,
    boxShadow: button.boxShadow || 'none'
  };
};

export default {
  WALLET_COLORS,
  GLASS_EFFECTS,
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  COMPONENT_STYLES,
  ANIMATIONS,
  getGlassStyle,
  getButtonStyle,
  getCardStyle,
  createGlassCSS,
  createButtonCSS
};
