import React, { useEffect, useCallback } from 'react';
import useTranslation from '../../../../hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';
import { useWalletError } from '../../contexts/ErrorContext';
import ErrorDisplay from '../../../../components/common/ErrorDisplay';
import errorRecoveryService from '../../services/ErrorRecoveryService';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../../../../utils/errorTypes';

/**
 * Wallet Error Handler Component
 * 
 * This component provides wallet-specific error handling with automatic recovery,
 * user guidance, and integration with the wallet error context.
 */
const WalletErrorHandler = ({
  context = 'general',
  onRetry,
  onRecovery,
  showToasts = true,
  showInlineErrors = true,
  autoRecovery = true,
  className = ''
}) => {
  const { t } = useTranslation('wallet');
  const {
    getError,
    clearError,
    retryOperation,
    showErrorToast,
    dismissErrorToast,
    getAllErrors
  } = useWalletError();

  const currentError = getError(context);
  const allErrors = getAllErrors();

  /**
   * Handle retry operation
   */
  const handleRetry = useCallback(async () => {
    if (!currentError || !onRetry) return;

    try {
      // Use wallet error context retry mechanism
      const result = await retryOperation(context, onRetry);
      
      if (result.success) {
        if (onRecovery) {
          onRecovery({ success: true, context, error: currentError });
        }
      }
    } catch (error) {
      console.error('Retry failed:', error);
    }
  }, [currentError, context, onRetry, retryOperation, onRecovery]);

  /**
   * Handle automatic recovery
   */
  const handleAutoRecovery = useCallback(async () => {
    if (!currentError || !autoRecovery || !onRetry) return;

    // Check if error can be recovered automatically
    const canRecover = errorRecoveryService.canRecover(currentError, context);
    
    if (!canRecover) return;

    try {
      const recoveryResult = await errorRecoveryService.recover(
        currentError,
        onRetry,
        { context, metadata: { automatic: true } }
      );

      if (recoveryResult.success) {
        clearError(context);
        if (onRecovery) {
          onRecovery({ 
            success: true, 
            context, 
            error: currentError, 
            automatic: true,
            result: recoveryResult 
          });
        }
      } else if (recoveryResult.fallbackData !== null) {
        // Use fallback data
        if (onRecovery) {
          onRecovery({ 
            success: true, 
            context, 
            error: currentError, 
            fallback: true,
            data: recoveryResult.fallbackData 
          });
        }
      }
    } catch (error) {
      console.error('Auto-recovery failed:', error);
    }
  }, [currentError, context, autoRecovery, onRetry, clearError, onRecovery]);

  /**
   * Handle error actions
   */
  const handleAction = useCallback((action) => {
    switch (action.action) {
      case 'retry':
        handleRetry();
        break;
      
      case 'verify_email':
        // Navigate to email verification
        window.location.href = '/verify-email';
        break;
      
      case 'start_ekyc':
        // Navigate to E-KYC verification
        window.location.href = '/ekyc';
        break;
      
      case 'add_credits':
        // Navigate to credit packages
        window.location.href = '/wallet?tab=packages';
        break;
      
      case 'view_packages':
        // Navigate to credit packages
        window.location.href = '/wallet?tab=packages';
        break;
      
      case 'contact_support':
        // Navigate to support
        window.location.href = '/support';
        break;
      
      case 'check_connection':
        // Provide connection guidance
        alert(t('paymentReturn.failureMessage'));
        break;
      
      default:
        console.log('Unhandled wallet error action:', action);
    }
  }, [handleRetry]);

  /**
   * Handle error dismissal
   */
  const handleDismiss = useCallback(() => {
    clearError(context);
  }, [clearError, context]);

  // Auto-recovery effect
  useEffect(() => {
    if (currentError && autoRecovery) {
      // Delay auto-recovery to allow user to see the error
      const timer = setTimeout(handleAutoRecovery, 2000);
      return () => clearTimeout(timer);
    }
  }, [currentError, autoRecovery, handleAutoRecovery]);

  // Render inline error for current context
  const renderInlineError = () => {
    if (!currentError || !showInlineErrors) return null;

    return (
      <div className={`mb-4 ${className}`}>
        <ErrorDisplay
          error={currentError}
          onRetry={handleRetry}
          onDismiss={handleDismiss}
          onAction={handleAction}
          variant="card"
          showActions={true}
          autoHide={false}
        />
      </div>
    );
  };

  // Render error toast
  const renderErrorToast = () => {
    if (!showToasts || !showErrorToast) return null;

    // Find the most critical error to show as toast
    const criticalError = allErrors.find(error => error.severity === 'critical') ||
                         allErrors.find(error => error.severity === 'high') ||
                         allErrors[0];

    if (!criticalError) return null;

    return (
      <ErrorDisplay
        error={criticalError}
        onRetry={() => handleRetry()}
        onDismiss={dismissErrorToast}
        onAction={handleAction}
        variant="toast"
        showActions={true}
        autoHide={true}
        autoHideDelay={8000}
        compact={true}
      />
    );
  };

  // Render verification guidance for specific errors
  const renderVerificationGuidance = () => {
    if (!currentError) return null;

    const isVerificationError = [
      WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED,
      WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED
    ].includes(currentError.type);

    if (!isVerificationError) return null;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-4 p-4 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl border border-indigo-200"
      >
        <div className="flex items-start space-x-3">
          <div className="bg-indigo-100 p-2 rounded-lg">
            <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="flex-1">
            <h4 className="text-indigo-900 font-semibold mb-2">
              {currentError.type === WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED 
                ? 'Email Verification Required' 
                : 'Identity Verification Required'}
            </h4>
            <p className="text-indigo-800 text-sm mb-3">
              {currentError.type === WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED
                ? 'To ensure account security, please verify your email address before accessing wallet features.'
                : 'For security and compliance, please complete your identity verification (E-KYC) to access withdrawal features.'}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleAction({ 
                  action: currentError.type === WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED 
                    ? 'verify_email' 
                    : 'start_ekyc' 
                })}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                {currentError.type === WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED 
                  ? 'Verify Email' 
                  : 'Start Verification'}
              </button>
              <button
                onClick={handleDismiss}
                className="bg-indigo-100 text-indigo-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors"
              >
                Later
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  // Render recovery status
  const renderRecoveryStatus = () => {
    if (!currentError || !currentError.canRetry) return null;

    const isRecovering = currentError.retryCount > 0;

    if (!isRecovering) return null;

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"
      >
        <div className="flex items-center space-x-3">
          <div className="animate-spin">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <div>
            <p className="text-blue-800 text-sm font-medium">
              Attempting to recover... (Attempt {currentError.retryCount})
            </p>
            <p className="text-blue-600 text-xs">
              {t('common:loading')}
            </p>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <>
      {/* Inline Error Display */}
      {renderInlineError()}
      
      {/* Verification Guidance */}
      {renderVerificationGuidance()}
      
      {/* Recovery Status */}
      {renderRecoveryStatus()}
      
      {/* Error Toast */}
      {renderErrorToast()}
    </>
  );
};

export default WalletErrorHandler;
