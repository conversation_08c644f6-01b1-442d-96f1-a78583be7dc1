import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faShieldAlt,
  faEnvelope,
  faIdCard,
  faCheckCircle,
  faExclamationTriangle,
  faSpinner,
  faArrowRight,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { WalletLoadingIndicator } from '../../../../components/common/LoadingIndicator';

/**
 * Verification Guard Component
 * 
 * Implements verification-first approach before allowing wallet operations.
 * Checks email verification and E-KYC status with conditional API loading.
 */
const VerificationGuard = ({ 
  onVerificationSuccess,
  onVerificationError,
  showTitle = true,
  variant = 'card', // 'card', 'modal', 'inline'
  requiredVerifications = ['email', 'ekyc'], // Array of required verifications
  className = ''
}) => {
  const {
    verificationStatus,
    verificationLoading,
    verificationError,
    loadVerificationStatus,
    resendEmailVerification,
    clearError
  } = useWallet();

  // Local state
  const [checkingVerification, setCheckingVerification] = useState(true);
  const [emailResending, setEmailResending] = useState(false);

  // Check verification status on mount
  useEffect(() => {
    const checkVerification = async () => {
      try {
        setCheckingVerification(true);
        await loadVerificationStatus();
      } catch (error) {
        console.error('Verification check failed:', error);
        if (onVerificationError) {
          onVerificationError(error);
        }
      } finally {
        setCheckingVerification(false);
      }
    };

    checkVerification();
  }, [loadVerificationStatus, onVerificationError]);

  // Check if all required verifications are complete
  useEffect(() => {
    if (!checkingVerification && verificationStatus) {
      const isEmailVerified = !requiredVerifications.includes('email') || verificationStatus.email_verified;
      const isEkycVerified = !requiredVerifications.includes('ekyc') || verificationStatus.ekyc_verified;
      
      if (isEmailVerified && isEkycVerified) {
        if (onVerificationSuccess) {
          onVerificationSuccess();
        }
      }
    }
  }, [checkingVerification, verificationStatus, requiredVerifications, onVerificationSuccess]);

  // Handle email verification resend
  const handleResendEmail = async () => {
    try {
      setEmailResending(true);
      await resendEmailVerification();
      // Show success message or handle response
    } catch (error) {
      console.error('Failed to resend email:', error);
    } finally {
      setEmailResending(false);
    }
  };

  // Handle E-KYC start
  const handleStartEkyc = () => {
    window.location.href = '/profile/settings';
  };

  // Render loading state
  if (checkingVerification || verificationLoading) {
    return (
      <div className={`${variant === 'modal' ? 'py-8' : 'p-6'} ${className}`}>
        <WalletLoadingIndicator text="Checking verification status..." />
      </div>
    );
  }

  // Render error state
  if (verificationError) {
    return (
      <div className={`${variant === 'modal' ? 'py-8' : 'p-6'} ${className}`}>
        <div className="text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Verification Check Failed</h3>
          <p className="text-gray-600 mb-4">{verificationError}</p>
          <button
            onClick={() => {
              clearError('verification');
              setCheckingVerification(true);
              loadVerificationStatus().finally(() => setCheckingVerification(false));
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Check verification requirements
  const emailRequired = requiredVerifications.includes('email');
  const ekycRequired = requiredVerifications.includes('ekyc');
  const emailVerified = verificationStatus?.email_verified || false;
  const ekycVerified = verificationStatus?.ekyc_verified || false;

  // If all required verifications are complete, don't render anything
  if ((!emailRequired || emailVerified) && (!ekycRequired || ekycVerified)) {
    return null;
  }

  // Render verification requirements
  const containerClass = variant === 'modal' 
    ? 'space-y-6' 
    : variant === 'inline' 
    ? 'space-y-4' 
    : 'bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 space-y-6';

  return (
    <div className={`${containerClass} ${className}`}>
      {/* Title */}
      {showTitle && (
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
            <FontAwesomeIcon icon={faShieldAlt} className="text-white text-2xl" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Verification Required</h2>
          <p className="text-gray-600">Complete verification to access wallet features</p>
        </div>
      )}

      {/* Verification Steps */}
      <div className="space-y-4">
        {/* Email Verification */}
        {emailRequired && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`
              border rounded-xl p-4 transition-all
              ${emailVerified 
                ? 'border-green-200 bg-green-50' 
                : 'border-orange-200 bg-orange-50 hover:border-orange-300'
              }
            `}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center
                  ${emailVerified ? 'bg-green-100' : 'bg-orange-100'}
                `}>
                  <FontAwesomeIcon 
                    icon={emailVerified ? faCheckCircle : faEnvelope} 
                    className={emailVerified ? 'text-green-600' : 'text-orange-600'} 
                  />
                </div>
                <div>
                  <h3 className={`font-semibold ${emailVerified ? 'text-green-900' : 'text-orange-900'}`}>
                    Email Verification
                  </h3>
                  <p className={`text-sm ${emailVerified ? 'text-green-700' : 'text-orange-700'}`}>
                    {emailVerified ? 'Email verified successfully' : 'Verify your email address'}
                  </p>
                </div>
              </div>
              
              {!emailVerified && (
                <div className="flex space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleResendEmail}
                    disabled={emailResending}
                    className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors disabled:opacity-50"
                  >
                    {emailResending ? (
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                    ) : (
                      'Resend'
                    )}
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => window.location.href = '/verify-email'}
                    className="px-3 py-1 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-1"
                  >
                    <span>Verify</span>
                    <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                  </motion.button>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* E-KYC Verification */}
        {ekycRequired && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className={`
              border rounded-xl p-4 transition-all
              ${ekycVerified 
                ? 'border-green-200 bg-green-50' 
                : 'border-blue-200 bg-blue-50 hover:border-blue-300'
              }
            `}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center
                  ${ekycVerified ? 'bg-green-100' : 'bg-blue-100'}
                `}>
                  <FontAwesomeIcon 
                    icon={ekycVerified ? faCheckCircle : faIdCard} 
                    className={ekycVerified ? 'text-green-600' : 'text-blue-600'} 
                  />
                </div>
                <div>
                  <h3 className={`font-semibold ${ekycVerified ? 'text-green-900' : 'text-blue-900'}`}>
                    Identity Verification (E-KYC)
                  </h3>
                  <p className={`text-sm ${ekycVerified ? 'text-green-700' : 'text-blue-700'}`}>
                    {ekycVerified ? 'Verified' : 'Complete identity verification'}
                  </p>
                </div>
              </div>
              
              {!ekycVerified && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleStartEkyc}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1"
                >
                  <span>Start</span>
                  <FontAwesomeIcon icon={faArrowRight} className="text-xs" />
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </div>

      {/* Information Notice */}
      <div className="bg-indigo-50 border border-indigo-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <FontAwesomeIcon icon={faInfoCircle} className="text-indigo-600 mt-0.5" />
          <div>
            <p className="font-medium text-indigo-900 mb-1">Why verification is required</p>
            <p className="text-sm text-indigo-700">
              Verification helps us ensure the security of your account and comply with financial regulations. 
              Your information is encrypted and secure.
            </p>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      {(emailRequired || ekycRequired) && (
        <div className="bg-gray-50 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Verification Progress</span>
            <span className="text-sm text-gray-600">
              {[emailRequired && emailVerified, ekycRequired && ekycVerified].filter(Boolean).length} / {requiredVerifications.length}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ 
                width: `${([emailRequired && emailVerified, ekycRequired && ekycVerified].filter(Boolean).length / requiredVerifications.length) * 100}%` 
              }}
              transition={{ duration: 0.5 }}
              className="bg-gradient-to-r from-indigo-500 to-blue-600 h-2 rounded-full"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default VerificationGuard;
