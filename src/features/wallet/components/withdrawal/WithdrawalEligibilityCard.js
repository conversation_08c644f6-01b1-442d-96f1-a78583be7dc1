import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faExclamationTriangle,
  faSpinner,
  faRefresh,
  faShieldAlt,
  faEnvelope,
  faCreditCard,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';

/**
 * WithdrawalEligibilityCard Component
 *
 * Modern card component that displays withdrawal eligibility status with beautiful UI,
 * glass-morphism effects, and clear requirement indicators.
 */
const WithdrawalEligibilityCard = ({
  showRefreshButton = true,
  showRequirements = true,
  size = 'medium', // 'small', 'medium', 'large'
  variant = 'card', // 'card', 'inline', 'minimal'
  className = '',
  onEligibilityChange,
  autoRefresh = true,
  refreshInterval = 60000 // 1 minute
}) => {
  const {
    withdrawalEligibility,
    eligibilityLoading,
    eligibilityError,
    loadWithdrawalEligibility,
    clearError
  } = useWallet();

  // Ensure withdrawalEligibility data is safe to use
  const safeEligibility = withdrawalEligibility ? {
    ...withdrawalEligibility,
    requirements: Array.isArray(withdrawalEligibility.requirements) ? withdrawalEligibility.requirements : [],
    reasons: Array.isArray(withdrawalEligibility.reasons) ? withdrawalEligibility.reasons : []
  } : null;

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Auto-refresh eligibility
  useEffect(() => {
    if (!autoRefresh || !refreshInterval) return;

    const interval = setInterval(async () => {
      try {
        await loadWithdrawalEligibility(true);
        setLastRefresh(Date.now());
      } catch (error) {
        console.error('Auto-refresh eligibility failed:', error);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // Load eligibility on mount
  useEffect(() => {
    if (!withdrawalEligibility && !eligibilityLoading) {
      loadWithdrawalEligibility();
    }
  }, [withdrawalEligibility, eligibilityLoading]);

  // Notify parent of eligibility changes
  useEffect(() => {
    if (onEligibilityChange && safeEligibility) {
      onEligibilityChange(safeEligibility);
    }
  }, [safeEligibility, onEligibilityChange]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadWithdrawalEligibility(true);
      setLastRefresh(Date.now());
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-3',
        title: 'text-base',
        text: 'text-sm',
        icon: 'text-lg',
        button: 'p-1 text-xs'
      },
      medium: {
        container: 'p-4',
        title: 'text-lg',
        text: 'text-base',
        icon: 'text-xl',
        button: 'p-2 text-sm'
      },
      large: {
        container: 'p-6',
        title: 'text-xl',
        text: 'text-lg',
        icon: 'text-2xl',
        button: 'p-2 text-base'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const getVariantClasses = () => {
    const variants = {
      card: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-lg backdrop-blur-sm',
      inline: 'bg-transparent',
      minimal: 'bg-white border border-gray-200 rounded-lg shadow-sm'
    };
    return variants[variant] || variants.card;
  };

  const getStatusColor = (canWithdraw) => {
    return canWithdraw
      ? 'from-green-50 to-emerald-100 border-green-200'
      : 'from-yellow-50 to-orange-100 border-yellow-200';
  };

  const getRequirementIcon = (requirement) => {
    const icons = {
      'E-KYC verification': faShieldAlt,
      'Email verification': faEnvelope,
      'Bank account': faCreditCard
    };
    return icons[requirement] || faInfoCircle;
  };

  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  if (eligibilityError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`
          ${variantClasses} ${sizeClasses.container} ${className}
          border-red-200 bg-gradient-to-br from-red-50 to-red-100
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500" />
            <div>
              <p className="text-red-700 font-medium">Failed to check eligibility</p>
              <p className="text-red-600 text-sm">
                {typeof eligibilityError === 'object'
                  ? eligibilityError.message || 'Failed to check eligibility'
                  : eligibilityError
                }
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => clearError('eligibility')}
              className="text-red-600 hover:text-red-700 text-sm underline"
            >
              Dismiss
            </button>
            {showRefreshButton && (
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="text-red-600 hover:text-red-700"
              >
                <FontAwesomeIcon
                  icon={faRefresh}
                  className={isRefreshing ? 'animate-spin' : ''}
                />
              </button>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        ${variantClasses} ${sizeClasses.container} ${className}
        ${safeEligibility ? getStatusColor(safeEligibility.canWithdraw) : ''}
        relative overflow-hidden
      `}
    >
      {/* Background Pattern */}
      {variant === 'card' && (
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-300 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-300 rounded-full translate-y-12 -translate-x-12"></div>
        </div>
      )}

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon
              icon={safeEligibility?.canWithdraw ? faCheckCircle : faExclamationTriangle}
              className={`${sizeClasses.icon} ${
                safeEligibility?.canWithdraw ? 'text-green-600' : 'text-yellow-600'
              }`}
            />
            <span className={`text-gray-700 font-medium ${sizeClasses.title}`}>
              Withdrawal Eligibility
            </span>
          </div>

          {showRefreshButton && (
            <button
              onClick={handleRefresh}
              disabled={eligibilityLoading || isRefreshing}
              className={`
                text-gray-500 hover:text-gray-700 transition-colors
                disabled:opacity-50 ${sizeClasses.button}
              `}
            >
              <FontAwesomeIcon
                icon={faRefresh}
                className={(eligibilityLoading || isRefreshing) ? 'animate-spin' : ''}
              />
            </button>
          )}
        </div>

        {/* Status Display */}
        <div className="space-y-3">
          <AnimatePresence mode="wait">
            {eligibilityLoading ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center space-x-2"
              >
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                <span className={sizeClasses.text}>Checking eligibility...</span>
              </motion.div>
            ) : safeEligibility ? (
              <motion.div
                key="eligibility"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-3"
              >
                {/* Status Message */}
                <div className={`font-medium ${sizeClasses.text} ${
                  safeEligibility.canWithdraw ? 'text-green-700' : 'text-yellow-700'
                }`}>
                  {safeEligibility.canWithdraw
                    ? '✅ You can withdraw funds'
                    : '⚠️ Withdrawal requirements not met'
                  }
                </div>

                {/* Requirements */}
                {showRequirements && safeEligibility.requirements.length > 0 && (
                  <div className="space-y-2">
                    <p className={`text-gray-600 ${sizeClasses.text} text-sm`}>Requirements:</p>
                    <div className="grid grid-cols-1 gap-2">
                      {safeEligibility.requirements.map((requirement, index) => {
                        const isCompleted = safeEligibility.canWithdraw ||
                                          !safeEligibility.reasons.includes(`Missing ${requirement.toLowerCase()}`);

                        return (
                          <div key={index} className="flex items-center space-x-2">
                            <FontAwesomeIcon
                              icon={getRequirementIcon(requirement)}
                              className={`text-sm ${isCompleted ? 'text-green-500' : 'text-gray-400'}`}
                            />
                            <span className={`text-sm ${isCompleted ? 'text-green-700' : 'text-gray-600'}`}>
                              {requirement}
                            </span>
                            {isCompleted && (
                              <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 text-xs" />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Reasons for ineligibility */}
                {!safeEligibility.canWithdraw && safeEligibility.reasons.length > 0 && (
                  <div className="space-y-2">
                    <p className={`text-yellow-700 ${sizeClasses.text} text-sm font-medium`}>
                      Missing requirements:
                    </p>
                    <ul className="space-y-1">
                      {safeEligibility.reasons.map((reason, index) => (
                        <li key={index} className="flex items-center space-x-2 text-sm text-yellow-600">
                          <span className="w-1 h-1 bg-yellow-500 rounded-full"></span>
                          <span>{reason}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="no-data"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={`text-gray-500 ${sizeClasses.text}`}
              >
                No eligibility data available
              </motion.div>
            )}
          </AnimatePresence>

          {/* Last Updated */}
          {variant === 'card' && (
            <p className="text-xs text-gray-500">
              Last checked: {new Date(lastRefresh).toLocaleTimeString()}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default WithdrawalEligibilityCard;
