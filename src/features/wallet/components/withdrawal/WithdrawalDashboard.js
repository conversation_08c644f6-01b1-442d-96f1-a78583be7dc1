import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMoneyBillWave,
  faUniversity,
  faHistory,
  faShieldAlt,
  faExclamationTriangle,
  faInfoCircle,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { BankAccountManager } from '../banking/BankAccountManager';
import WithdrawalForm from './WithdrawalForm';
import WithdrawalHistoryCard from './WithdrawalHistoryCard';
import WithdrawalEligibilityCard from './WithdrawalEligibilityCard';

/**
 * WithdrawalDashboard Component
 * 
 * Comprehensive withdrawal management interface that integrates bank accounts,
 * withdrawal forms, history, and eligibility checks in a modern dashboard layout.
 */
const WithdrawalDashboard = ({
  variant = 'full', // 'full', 'compact', 'minimal'
  showBankAccounts = true,
  showWithdrawalForm = true,
  showHistory = true,
  showEligibility = true,
  className = ''
}) => {
  const {
    balance,
    withdrawalEligibility,
    loadWithdrawalEligibility,
    formatCurrency
  } = useWallet();

  const [selectedBankAccount, setSelectedBankAccount] = useState(null);
  const [activeTab, setActiveTab] = useState('withdraw'); // 'withdraw', 'accounts', 'history'
  const [loading, setLoading] = useState(true);

  // Load withdrawal eligibility on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        if (loadWithdrawalEligibility) {
          await loadWithdrawalEligibility();
        }
      } catch (error) {
        console.error('Error loading withdrawal data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [loadWithdrawalEligibility]);

  const handleBankAccountSelect = (account) => {
    setSelectedBankAccount(account);
    setActiveTab('withdraw'); // Switch to withdrawal form when account is selected
  };

  const getTabClasses = (tabName) => {
    const isActive = activeTab === tabName;
    return `
      px-4 py-2 rounded-lg font-medium transition-all
      ${isActive 
        ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md' 
        : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
      }
    `;
  };

  if (variant === 'minimal') {
    return (
      <div className={`withdrawal-dashboard-minimal ${className}`}>
        <div className="space-y-4">
          {showEligibility && (
            <WithdrawalEligibilityCard variant="minimal" />
          )}
          {showWithdrawalForm && selectedBankAccount && (
            <WithdrawalForm 
              selectedBankAccount={selectedBankAccount}
              variant="minimal"
            />
          )}
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <motion.div
        className={`
          bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl 
          border border-white/30 shadow-xl rounded-2xl p-6 ${className}
        `}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="space-y-6">
          {showEligibility && (
            <WithdrawalEligibilityCard variant="compact" />
          )}
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {showBankAccounts && (
              <BankAccountManager
                variant="card"
                size="medium"
                onAccountSelect={handleBankAccountSelect}
                selectedAccountId={selectedBankAccount?.id}
                showTitle={true}
              />
            )}
            
            {showWithdrawalForm && (
              <WithdrawalForm 
                selectedBankAccount={selectedBankAccount}
                variant="card"
                size="medium"
              />
            )}
          </div>
          
          {showHistory && (
            <WithdrawalHistoryCard variant="compact" limit={5} />
          )}
        </div>
      </motion.div>
    );
  }

  // Full dashboard variant
  return (
    <motion.div
      className={`withdrawal-dashboard ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {/* Dashboard Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Withdrawal Center
            </h1>
            <p className="text-gray-600">
              Manage your withdrawals and bank accounts
            </p>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-600 mb-1">Available Balance</p>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(balance?.available || 0)}
            </p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-2 bg-gray-100 p-1 rounded-xl">
          <button
            onClick={() => setActiveTab('withdraw')}
            className={getTabClasses('withdraw')}
          >
            <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
            Withdraw
          </button>
          <button
            onClick={() => setActiveTab('accounts')}
            className={getTabClasses('accounts')}
          >
            <FontAwesomeIcon icon={faUniversity} className="mr-2" />
            Bank Accounts
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={getTabClasses('history')}
          >
            <FontAwesomeIcon icon={faHistory} className="mr-2" />
            History
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 text-2xl mr-3" />
          <span className="text-gray-600 text-lg">Loading withdrawal data...</span>
        </div>
      )}

      {/* Dashboard Content */}
      {!loading && (
        <div className="space-y-8">
          {/* Eligibility Check - Always visible */}
          {showEligibility && (
            <WithdrawalEligibilityCard 
              variant="card" 
              size="large"
              className="mb-8"
            />
          )}

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'withdraw' && (
              <motion.div
                key="withdraw"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                  {/* Bank Account Selection */}
                  <div className="xl:col-span-1">
                    <BankAccountManager
                      variant="card"
                      size="large"
                      onAccountSelect={handleBankAccountSelect}
                      selectedAccountId={selectedBankAccount?.id}
                      showTitle={true}
                      className="h-fit"
                    />
                  </div>

                  {/* Withdrawal Form */}
                  <div className="xl:col-span-2">
                    {selectedBankAccount ? (
                      <WithdrawalForm 
                        selectedBankAccount={selectedBankAccount}
                        variant="card"
                        size="large"
                      />
                    ) : (
                      <motion.div
                        className="
                          bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl 
                          border border-white/30 shadow-xl rounded-2xl p-8
                        "
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <div className="text-center py-12">
                          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center">
                            <FontAwesomeIcon icon={faUniversity} className="text-blue-500 text-xl" />
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            Select a Bank Account
                          </h3>
                          <p className="text-gray-600 mb-6">
                            Choose a bank account from the left panel to start your withdrawal
                          </p>
                          <div className="flex items-center justify-center text-sm text-blue-600">
                            <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                            You can add new bank accounts anytime
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'accounts' && (
              <motion.div
                key="accounts"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <BankAccountManager
                  variant="card"
                  size="large"
                  onAccountSelect={handleBankAccountSelect}
                  selectedAccountId={selectedBankAccount?.id}
                  showTitle={true}
                />
              </motion.div>
            )}

            {activeTab === 'history' && (
              <motion.div
                key="history"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <WithdrawalHistoryCard 
                  variant="card" 
                  size="large"
                  showFilters={true}
                  showRefreshButton={true}
                  limit={15}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </motion.div>
  );
};

export default WithdrawalDashboard;
