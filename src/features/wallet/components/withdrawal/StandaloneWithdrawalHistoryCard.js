import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHistory,
  faSpinner,
  faExclamationTriangle,
  faRefresh,
  faArrowUp,
  faArrowDown,
  faCalendarAlt,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faQuestionCircle,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import walletService from '../../../../services/walletService';

const StandaloneWithdrawalHistoryCard = ({
  limit = 3,
  size = 'medium',
  className = ''
}) => {
  const [withdrawals, setWithdrawals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  // Always show only the number of withdrawals currently displayed
  const displayedWithdrawals = Array.isArray(withdrawals) ? withdrawals : [];

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        title: 'text-lg',
        item: 'p-3',
        text: 'text-sm'
      },
      medium: {
        container: 'p-6',
        title: 'text-xl',
        item: 'p-4',
        text: 'text-base'
      },
      large: {
        container: 'p-8',
        title: 'text-2xl',
        item: 'p-5',
        text: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };
  const sizeClasses = getSizeClasses();

  const fetchWithdrawals = async (reset = false) => {
    setLoading(true);
    setError(null);
    try {
      // Always fetch 'limit' withdrawals at a time, starting from offset
      const response = await walletService.getWithdrawalHistory(limit, reset ? 0 : offset);
      const newWithdrawals = Array.isArray(response.data?.data) ? response.data.data : [];
      setWithdrawals(reset ? newWithdrawals : [...withdrawals, ...newWithdrawals]);
      setHasMore(newWithdrawals.length === limit);
      if (reset) setOffset(limit);
      else setOffset(offset + limit);
    } catch (err) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWithdrawals(true);
    // eslint-disable-next-line
  }, []);

  const handleRefresh = () => fetchWithdrawals(true);
  const handleLoadMore = () => fetchWithdrawals(false);

  // Defensive: always use an array for rendering
  // const withdrawalsArray = Array.isArray(withdrawals) ? withdrawals : [];
  // Use displayedWithdrawals instead

  return (
    <motion.div
      className={`relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 shadow-2xl rounded-2xl overflow-hidden ${sizeClasses.container} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
    >
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-4">
              <FontAwesomeIcon icon={faHistory} className="text-white text-lg" />
            </div>
            <div>
              <h3 className={`font-bold text-gray-900 dark:text-gray-100 ${sizeClasses.title}`}>Withdrawal History</h3>
              <p className="text-gray-600 dark:text-gray-300 text-left text-sm">
                {displayedWithdrawals.length} withdrawal{displayedWithdrawals.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faRefresh} className={loading ? 'animate-spin' : ''} />
          </motion.button>
        </div>

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-xl"
          >
            <div className="flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 mr-3" />
              <div>
                <p className="text-red-800 dark:text-red-300 font-medium">{error}</p>
              </div>
            </div>
            <button
              onClick={handleRefresh}
              className="mt-2 text-red-600 dark:text-red-300 hover:text-red-700 dark:hover:text-red-400 text-sm underline"
            >
              Dismiss
            </button>
          </motion.div>
        )}

        {/* Loading State */}
        {loading && displayedWithdrawals.length === 0 && (
          <div className="space-y-4">
            {[...Array(limit)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-800 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-1/2"></div>
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Withdrawals List */}
        {!loading && displayedWithdrawals.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <FontAwesomeIcon icon={faHistory} className="text-gray-400 dark:text-gray-600 text-xl" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No withdrawals yet</h4>
            <p className="text-gray-600 dark:text-gray-300">Your withdrawal history will appear here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayedWithdrawals.map((withdrawal, index) => {
              // Status mapping
              const statusMap = {
                approved: {
                  label: 'Completed',
                  color: 'green',
                  icon: faCheckCircle,
                },
                rejected: {
                  label: 'Rejected',
                  color: 'red',
                  icon: faTimesCircle,
                },
                pending: {
                  label: 'Pending',
                  color: 'yellow',
                  icon: faClock,
                },
              };
              const status = statusMap[withdrawal.status] || {
                label: withdrawal.status ? withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1) : 'Unknown',
                color: 'gray',
                icon: faQuestionCircle,
              };
              // Masked account number
              const accountNumber = withdrawal.metadata?.modified_account_number || withdrawal.bank_account?.account_number;
              const maskedAccount = accountNumber ? `••••${accountNumber.slice(-4)}` : 'N/A';
              // Balance after
              const balanceAfter = withdrawal.metadata?.credits_balance_after;
              // Fiat amount
              const fiatAmount = withdrawal.fiat_amount && withdrawal.fiat_currency_code ? `${withdrawal.fiat_amount} ${withdrawal.fiat_currency_code}` : null;
              // Conversion rate
              const conversionRate = withdrawal.conversion_rate ? `1 Credit = ${(parseFloat(withdrawal.conversion_rate) * 1).toFixed(2)} ${withdrawal.fiat_currency_code || ''}` : null;
              // Date
              const dateStr = withdrawal.created_at ? new Date(withdrawal.created_at).toLocaleString() : 'N/A';
              return (
                <motion.div
                  key={withdrawal.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex flex-col md:flex-row md:items-center justify-between p-4 rounded-xl border transition-all bg-white/80 dark:bg-gray-900/80 border-white/30 dark:border-gray-700 gap-2"
                >
                  {/* Left: Status and Main Info */}
                  <div className="flex items-center flex-1 min-w-0 gap-4">
                    {/* Status Icon */}
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-2 bg-${status.color}-100 dark:bg-${status.color}-900`}>
                      <FontAwesomeIcon icon={status.icon} className={`text-${status.color}-600 dark:text-${status.color}-300 text-xl`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      {/* Status label and amount */}
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`font-bold text-${status.color}-700 dark:text-${status.color}-300 text-base`}>{status.label}</span>
                        {withdrawal.status === 'rejected' && (
                          <span className="ml-2 px-2 py-0.5 rounded bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 text-xs font-semibold">Rejected</span>
                        )}
                      </div>
                      {/* Amounts */}
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-gray-900 dark:text-gray-100 text-lg">{withdrawal.amount ? `- ${withdrawal.amount} Credits` : ''}</span>
                        {fiatAmount && (
                          <span className="text-gray-500 dark:text-gray-300 text-sm">({fiatAmount})</span>
                        )}
                      </div>
                      {/* Date */}
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-300 mt-1">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                        {dateStr}
                      </div>
                    </div>
                  </div>
                  {/* Right: Details */}
                  <div className="text-right flex flex-col items-end gap-1 min-w-[140px]">
                    {/* Masked Account */}
                    <div className="text-xs text-gray-500 dark:text-gray-300">Account: {maskedAccount}</div>
                    {/* Balance After */}
                    {balanceAfter !== undefined && (
                      <div className="text-xs text-gray-500 dark:text-gray-300">Balance after: {balanceAfter.toLocaleString()}</div>
                    )}
                    {/* Rejection Reason */}
                    {withdrawal.status === 'rejected' && withdrawal.rejection_reason && (
                      <div className="text-xs text-red-600 dark:text-red-300 mt-1 flex items-center gap-1">
                        <FontAwesomeIcon icon={faInfoCircle} className="text-red-400 dark:text-red-300" />
                        Reason: {withdrawal.rejection_reason}
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}

        {/* Load More Button */}
        {hasMore && !loading && (
          <div className="mt-6 text-center">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLoadMore}
              disabled={loading}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all disabled:opacity-50"
            >
              {loading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default StandaloneWithdrawalHistoryCard; 