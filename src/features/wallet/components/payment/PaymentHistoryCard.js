import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHistory,
  faSpinner,
  faExclamationTriangle,
  faRefresh,
  faFilter,
  faPlus,
  faMinus,
  faArrowUp,
  faArrowDown,
  faCalendarAlt,
  faInfoCircle,
  faCoins,
  faStar,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faQuestionCircle,
  faExchangeAlt,
  faMoneyBillWave
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import DateRangeFilter from '../common/DateRangeFilter';

/**
 * PaymentHistoryCard Component
 *
 * Modern card component that displays payment/credit and point transaction history with filtering,
 * pagination, and beautiful UI following Profile.js design patterns with glass-morphism effects.
 */
const PaymentHistoryCard = ({
  showFilters = true,
  showRefreshButton = true,
  limit = 10,
  size = 'medium', // 'small', 'medium', 'large'
  variant = 'card', // 'card', 'inline', 'minimal'
  className = '',
  onTransactionClick,
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}) => {
  const {
    transactions = [], // Default to empty array
    transactionsLoading,
    transactionsError,
    loadTransactions,
    formatCredits,
    formatCurrency,
    clearError
  } = useWallet();

  const [filters, setFilters] = useState({
    type: null, // 'add', 'deduct', null for all
    transactionType: 'all', // 'all', 'credits', 'points'
    limit: limit,
    offset: 0,
    startDate: '',
    endDate: ''
  });
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        handleRefresh();
      }, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // Load transactions on mount and filter changes
  useEffect(() => {
    loadTransactions({
      ...filters,
      replace: true,
      forceRefresh: false
    });
  }, [filters]);

  const handleRefresh = () => {
    loadTransactions({
      ...filters,
      replace: true,
      forceRefresh: true
    });
  };

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      offset: 0 // Reset to first page when filters change
    }));
  };

  const handleDateRangeChange = ({ startDate, endDate }) => {
    handleFilterChange({ startDate, endDate });
  };

  // Only show up to filters.limit transactions by default
  const displayedTransactions = transactions.slice(0, filters.limit);

  const handleLoadMore = () => {
    setFilters(prev => ({
      ...prev,
      limit: prev.limit + limit // Show more transactions
    }));
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        title: 'text-lg',
        item: 'p-3',
        text: 'text-sm'
      },
      medium: {
        container: 'p-6',
        title: 'text-xl',
        item: 'p-4',
        text: 'text-base'
      },
      large: {
        container: 'p-8',
        title: 'text-2xl',
        item: 'p-5',
        text: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const getTransactionIcon = (transaction) => {
    if (transaction.transaction_type === 'points') {
      return transaction.type === 'add' ? faStar : faMinus;
    }
    return transaction.type === 'add' ? faPlus : faMinus;
  };

  const getTransactionColor = (transaction) => {
    if (transaction.transaction_type === 'points') {
      return transaction.type === 'add'
        ? 'text-yellow-600 bg-yellow-50 border-yellow-200'
        : 'text-red-600 bg-red-50 border-red-200';
    }
    return transaction.type === 'add'
      ? 'text-green-600 bg-green-50 border-green-200'
      : 'text-red-600 bg-red-50 border-red-200';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (transaction) => {
    if (transaction.transaction_type === 'points') {
      return `${transaction.type === 'add' ? '+' : ''}${transaction.points} points`;
    }
    return `${transaction.type === 'add' ? '+' : ''}${formatCredits(transaction.credits)}`;
  };

  // Helper: Status mapping for transaction types
  const getStatusMeta = (transaction) => {
    // Withdrawal/Refund/Expense/Commission/Order/Other
    if (transaction.fee_type === 'withdrawal') {
      return {
        label: transaction.transaction_type === 'deduct' ? 'Withdrawal' : 'Withdrawal Refund',
        color: transaction.transaction_type === 'deduct' ? 'blue' : 'green',
        icon: transaction.transaction_type === 'deduct' ? faExchangeAlt : faCheckCircle,
      };
    }
    if (transaction.fee_type === 'refund') {
      return {
        label: 'Refund',
        color: 'green',
        icon: faCheckCircle,
      };
    }
    if (transaction.fee_type === 'platform_fee' || transaction.fee_type === 'commission') {
      return {
        label: 'Commission',
        color: 'purple',
        icon: faMoneyBillWave,
      };
    }
    if (transaction.fee_type === 'spent' || transaction.fee_type === 'expense') {
      return {
        label: 'Spent',
        color: 'red',
        icon: faMinus,
      };
    }
    return {
      label: transaction.transaction_type === 'add' ? 'Credit In' : 'Credit Out',
      color: transaction.transaction_type === 'add' ? 'green' : 'red',
      icon: transaction.transaction_type === 'add' ? faPlus : faMinus,
    };
  };

  const sizeClasses = getSizeClasses();

  // Ensure transactions is always an array
  const safeTransactions = Array.isArray(transactions) ? transactions : [];

  if (variant === 'minimal') {
    return (
      <div className={`payment-history-minimal ${className}`}>
        {transactionsLoading ? (
          <div className="flex items-center justify-center py-4">
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">Loading transactions...</span>
          </div>
        ) : safeTransactions.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            No transactions found
          </div>
        ) : (
          <div className="space-y-2">
            {safeTransactions.slice(0, 3).map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center">
                  <FontAwesomeIcon
                    icon={getTransactionIcon(transaction)}
                    className={`w-4 h-4 mr-3 ${transaction.transaction_type === 'points' ? (transaction.type === 'add' ? 'text-yellow-600' : 'text-red-600') : (transaction.type === 'add' ? 'text-green-600' : 'text-red-600')}`}
                  />
                  <span className="text-sm text-gray-700 dark:text-white truncate max-w-32">
                    {transaction.description}
                  </span>
                </div>
                <span className={`text-sm font-medium ${transaction.transaction_type === 'points' ? (transaction.type === 'add' ? 'text-yellow-600' : 'text-red-600') : (transaction.type === 'add' ? 'text-green-600' : 'text-red-600')}`}>
                  {formatAmount(transaction)}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <motion.div
      className={`
        relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl
        border border-white/30 dark:border-gray-700 shadow-2xl rounded-2xl overflow-hidden
        ${sizeClasses.container} ${className}
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
    >
      {/* Animated background decorations */}
      <div className="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/20 to-indigo-500/20 blur-2xl animate-pulse" />
      <div className="absolute -bottom-10 -left-10 w-24 h-24 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 blur-xl animate-pulse delay-1000" />

      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-4">
              <FontAwesomeIcon icon={faHistory} className="text-white text-lg" />
            </div>
            <div>
              <h3 className={`font-bold text-gray-900 dark:text-gray-100 ${sizeClasses.title}`}>
                Transaction History
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-left text-sm">
                {Math.min(safeTransactions.length, filters.limit)} transaction{Math.min(safeTransactions.length, filters.limit) !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {showFilters && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className={
                  `p-2 rounded-lg transition-colors ` +
                  (showFiltersPanel
                   ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200'
                   : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
                  )
                }
              >
                <FontAwesomeIcon icon={faFilter} />
              </motion.button>
            )}

            {showRefreshButton && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleRefresh}
                disabled={transactionsLoading}
               className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
              >
                <FontAwesomeIcon
                  icon={faRefresh}
                  className={transactionsLoading ? 'animate-spin' : ''}
                />
              </motion.button>
            )}
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFiltersPanel && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
             className="mb-6 p-4 bg-gray-50/80 dark:bg-gray-900/80 rounded-xl border border-gray-200/50 dark:border-gray-700"
            >
              <div className="space-y-4">
                {/* Transaction Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Transaction Type
                  </label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => handleFilterChange({ transactionType: 'all' })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.transactionType === 'all'
                          ? 'bg-blue-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      All
                    </button>
                    <button
                      onClick={() => handleFilterChange({ transactionType: 'credits' })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.transactionType === 'credits'
                          ? 'bg-green-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      Credits
                    </button>
                    <button
                      onClick={() => handleFilterChange({ transactionType: 'points' })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.transactionType === 'points'
                          ? 'bg-yellow-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      Points
                    </button>
                  </div>
                </div>

                {/* Transaction Direction Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Transaction Direction
                  </label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => handleFilterChange({ type: null })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.type === null
                          ? 'bg-blue-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      All
                    </button>
                    <button
                      onClick={() => handleFilterChange({ type: 'add' })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.type === 'add'
                          ? 'bg-green-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      Added
                    </button>
                    <button
                      onClick={() => handleFilterChange({ type: 'deduct' })}
                      className={
                        `px-3 py-1 rounded-lg text-sm font-medium transition-colors ` +
                        (filters.type === 'deduct'
                          ? 'bg-red-500 text-white'
                         : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                        )
                        }
                    >
                      Used
                    </button>
                  </div>
                </div>

                {/* Date Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <DateRangeFilter
                    startDate={filters.startDate}
                    endDate={filters.endDate}
                    onDateChange={handleDateRangeChange}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error State */}
        {transactionsError && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
           className="mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-xl"
          >
            <div className="flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 mr-3" />
              <div>
               <p className="text-red-800 dark:text-red-300 font-medium">Error loading transactions</p>
               <p className="text-red-600 dark:text-red-300 text-sm">
                  {typeof transactionsError === 'object'
                    ? transactionsError.message || 'Failed to load transactions'
                    : transactionsError
                  }
                </p>
              </div>
            </div>
            <button
              onClick={() => clearError('transactions')}
             className="mt-2 text-red-600 dark:text-red-300 hover:text-red-700 dark:hover:text-red-400 text-sm underline"
            >
              Dismiss
            </button>
          </motion.div>
        )}

        {/* Loading State */}
        {transactionsLoading && safeTransactions.length === 0 && (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                 <div className="w-10 h-10 bg-gray-200 dark:bg-gray-800 rounded-lg"></div>
                  <div className="flex-1">
                   <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-3/4 mb-2"></div>
                   <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-1/2"></div>
                  </div>
                 <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Transactions List */}
        {!transactionsLoading && safeTransactions.length === 0 ? (
          <div className="text-center py-12">
           <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
             <FontAwesomeIcon icon={faHistory} className="text-gray-400 dark:text-gray-600 text-xl" />
            </div>
           <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No transactions yet</h4>
           <p className="text-gray-600 dark:text-gray-300">Your transaction history will appear here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayedTransactions.map((transaction, index) => {
              const status = getStatusMeta(transaction);
              // Fiat info
              const fiatAmount = transaction.metadata?.fiat_amount && transaction.metadata?.fiat_currency
                ? `${transaction.metadata.fiat_amount} ${transaction.metadata.fiat_currency}`
                : null;
              // Reference
              const referenceId = transaction.reference_id || 'N/A';
              // Balance before/after
              const balanceBefore = transaction.balance_before;
              const balanceAfter = transaction.balance_after;
              // Date
              const dateStr = transaction.created_at ? formatDate(transaction.created_at) : 'N/A';
              // Description fallback
              const description = transaction.description || status.label;
              return (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => onTransactionClick && onTransactionClick(transaction)}
                 className={
                   `flex flex-col md:flex-row md:items-center justify-between p-4 rounded-xl border transition-all bg-white/80 dark:bg-gray-900/80 border-white/30 dark:border-gray-700 gap-2 ` +
                   (onTransactionClick ? 'cursor-pointer hover:shadow-md' : '')
                 }
                >
                  {/* Left: Status and Main Info */}
                  <div className="flex items-center flex-1 min-w-0 gap-4">
                    {/* Status Icon */}
                   <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-2 bg-${status.color}-100 dark:bg-${status.color}-900`}>
                     <FontAwesomeIcon icon={status.icon} className={`text-${status.color}-600 dark:text-${status.color}-300 text-xl`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      {/* Status label and description */}
                      <div className="flex items-center gap-2 mb-1">
                       <span className={`font-bold text-${status.color}-700 dark:text-${status.color}-300 text-base`}>{status.label}</span>
                      </div>
                      {/* Description */}
                     <div className="font-medium text-left text-lg text-gray-900 dark:text-gray-100 truncate">{description}</div>
                      {/* Amounts */}
                      <div className="flex items-center gap-2">
                       <span className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                          {transaction.credits > 0 ? `+${transaction.credits}` : transaction.credits} Credits
                        </span>
                        {fiatAmount && (
                         <span className="text-gray-500 dark:text-gray-300 text-sm">({fiatAmount})</span>
                        )}
                      </div>
                      {/* Date */}
                     <div className="flex items-center text-xs text-gray-500 dark:text-gray-300 mt-1">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                        {dateStr}
                      </div>
                    </div>
                  </div>
                  {/* Right: Details */}
                  <div className="text-right flex flex-col items-end gap-1 min-w-[140px]">
                   <div className="text-xs text-gray-500 dark:text-gray-300">Reference: {referenceId}</div>
                    {/* Special: Withdrawal Refund Info */}
                    {transaction.fee_type === 'refund' && transaction.metadata?.withdrawal_request_id && (
                     <div className="text-xs text-green-600 dark:text-green-300 mt-1 flex items-center gap-1">
                       <FontAwesomeIcon icon={faInfoCircle} className="text-green-400 dark:text-green-300" />
                        Refund for withdrawal #{transaction.metadata.withdrawal_request_id}
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}

        {/* Load More Button */}
        {safeTransactions.length > filters.limit && (
          <div className="mt-6 text-center">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLoadMore}
              disabled={transactionsLoading}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all disabled:opacity-50"
            >
              {transactionsLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default PaymentHistoryCard;
