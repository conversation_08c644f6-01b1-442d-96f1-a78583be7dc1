import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCheckCircle, 
  faTimesCircle, 
  faSpinner, 
  faClock, 
  faRedo,
  faInfoCircle,
  faExternalLinkAlt
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';

/**
 * PaymentStatus Component
 * 
 * Displays real-time payment status with animations and interactive elements.
 * Supports retry functionality, status tracking, and detailed information display.
 */
const PaymentStatus = ({
  transactionId,
  onRetry,
  onComplete,
  onCancel,
  showDetails = true,
  showRetryButton = true,
  autoRefresh = true,
  refreshInterval = 5000,
  className = ''
}) => {
  const {
    paymentStatus,
    paymentLoading,
    paymentError,
    checkPaymentStatus,
    retryPayment,
    formatCurrency,
    constants
  } = useWallet();

  const [localLoading, setLocalLoading] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Auto-refresh payment status
  useEffect(() => {
    if (!autoRefresh || !transactionId) return;

    const interval = setInterval(async () => {
      if (paymentStatus?.status === 'pending' || paymentStatus?.status === 'processing') {
        try {
          await checkPaymentStatus(transactionId);
        } catch (error) {
          console.error('Error checking payment status:', error);
        }
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, transactionId, paymentStatus?.status, checkPaymentStatus, refreshInterval]);

  // Handle payment completion
  useEffect(() => {
    if (paymentStatus?.status === 'success' && onComplete) {
      onComplete(paymentStatus);
    }
  }, [paymentStatus?.status, onComplete, paymentStatus]);

  const handleRetry = async () => {
    if (!transactionId || retryCount >= 3) return;

    setLocalLoading(true);
    setRetryCount(prev => prev + 1);

    try {
      const result = await retryPayment(transactionId);
      
      if (result.success && onRetry) {
        onRetry(result);
      }
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const getStatusConfig = (status) => {
    const configs = {
      pending: {
        icon: faClock,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        title: 'Payment Pending',
        description: 'Your payment is being processed...',
        showSpinner: true
      },
      processing: {
        icon: faSpinner,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        title: 'Processing Payment',
        description: 'Please wait while we process your payment...',
        showSpinner: true
      },
      success: {
        icon: faCheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        title: 'Payment Successful',
        description: 'Your payment has been completed successfully!',
        showSpinner: false
      },
      failed: {
        icon: faTimesCircle,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        title: 'Payment Failed',
        description: 'Your payment could not be processed. Please try again.',
        showSpinner: false
      },
      cancelled: {
        icon: faTimesCircle,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        title: 'Payment Cancelled',
        description: 'The payment was cancelled.',
        showSpinner: false
      }
    };

    return configs[status] || configs.pending;
  };

  const currentStatus = paymentStatus?.status || 'pending';
  const statusConfig = getStatusConfig(currentStatus);
  const isLoading = paymentLoading || localLoading;
  const canRetry = currentStatus === 'failed' && showRetryButton && retryCount < 3;

  return (
    <div className={`payment-status ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`
          rounded-lg border-2 p-6 
          ${statusConfig.bgColor} 
          ${statusConfig.borderColor}
          backdrop-blur-sm bg-opacity-80
        `}
      >
        {/* Status Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`relative ${statusConfig.color}`}>
              <FontAwesomeIcon 
                icon={statusConfig.icon} 
                className={`text-2xl ${statusConfig.showSpinner ? 'animate-spin' : ''}`}
              />
              {isLoading && (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-0"
                >
                  <FontAwesomeIcon icon={faSpinner} className="text-2xl opacity-50" />
                </motion.div>
              )}
            </div>
            
            <div>
              <h3 className={`text-lg font-semibold ${statusConfig.color}`}>
                {statusConfig.title}
              </h3>
              <p className="text-sm text-gray-600">
                {statusConfig.description}
              </p>
            </div>
          </div>

          {showDetails && paymentStatus && (
            <button
              onClick={() => setShowDetailsModal(true)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <FontAwesomeIcon icon={faInfoCircle} />
            </button>
          )}
        </div>

        {/* Payment Details */}
        {paymentStatus && (
          <div className="space-y-2 mb-4">
            {paymentStatus.amount && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">
                  {formatCurrency(paymentStatus.amount, paymentStatus.currency || 'MYR')}
                </span>
              </div>
            )}
            
            {paymentStatus.transaction_id && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Transaction ID:</span>
                <span className="font-mono text-xs">
                  {paymentStatus.transaction_id.slice(-8)}
                </span>
              </div>
            )}

            {paymentStatus.created_at && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Time:</span>
                <span>{new Date(paymentStatus.created_at).toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        )}

        {/* Error Message */}
        {paymentError && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="bg-red-50 border border-red-200 rounded-md p-3 mb-4"
          >
            <p className="text-red-700 text-sm">{paymentError}</p>
          </motion.div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          {canRetry && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleRetry}
              disabled={isLoading}
              className="
                flex items-center space-x-2 px-4 py-2 
                bg-blue-600 text-white rounded-md
                hover:bg-blue-700 disabled:opacity-50
                transition-colors
              "
            >
              <FontAwesomeIcon icon={faRedo} className={isLoading ? 'animate-spin' : ''} />
              <span>Retry Payment ({3 - retryCount} left)</span>
            </motion.button>
          )}

          {currentStatus === 'success' && paymentStatus?.redirect_url && (
            <motion.a
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              href={paymentStatus.redirect_url}
              target="_blank"
              rel="noopener noreferrer"
              className="
                flex items-center space-x-2 px-4 py-2 
                bg-green-600 text-white rounded-md
                hover:bg-green-700 transition-colors
              "
            >
              <span>View Receipt</span>
              <FontAwesomeIcon icon={faExternalLinkAlt} />
            </motion.a>
          )}

          {onCancel && (currentStatus === 'pending' || currentStatus === 'processing') && (
            <button
              onClick={onCancel}
              className="
                px-4 py-2 text-gray-600 border border-gray-300 rounded-md
                hover:bg-gray-50 transition-colors
              "
            >
              Cancel
            </button>
          )}
        </div>

        {/* Progress Indicator */}
        {(currentStatus === 'pending' || currentStatus === 'processing') && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="bg-blue-600 h-2 rounded-full"
                initial={{ width: '0%' }}
                animate={{ width: currentStatus === 'processing' ? '75%' : '25%' }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        )}
      </motion.div>

      {/* Details Modal */}
      <AnimatePresence>
        {showDetailsModal && paymentStatus && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowDetailsModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Payment Details</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium ${statusConfig.color}`}>
                    {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-mono text-sm">{paymentStatus.transaction_id}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">
                    {formatCurrency(paymentStatus.amount, paymentStatus.currency || 'MYR')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(paymentStatus.created_at).toLocaleString()}</span>
                </div>

                {paymentStatus.payment_method && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment Method:</span>
                    <span>{paymentStatus.payment_method}</span>
                  </div>
                )}
              </div>

              <button
                onClick={() => setShowDetailsModal(false)}
                className="mt-6 w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Close
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PaymentStatus;
