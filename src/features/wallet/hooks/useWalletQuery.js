import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CreditService, WithdrawalService, WalletVerificationService } from '../services';
import globalBalanceService from '../../../services/globalBalanceService';
import { walletConstants } from '../constants';
import { useAuth } from '../../../contexts/AuthContext'; // Assuming useAuth is needed for auth state in queries

// Define query keys as constants
const WALLET_QUERY_KEYS = {
    BALANCE: ['wallet', 'balance'],
    TRANSACTIONS: ['wallet', 'transactions'],
    CREDIT_PACKAGES: ['wallet', 'creditPackages'],
    VERIFICATION_STATUS: ['wallet', 'verificationStatus'],
    WITHDRAWAL_ELIGIBILITY: ['wallet', 'withdrawalEligibility'],
    WITHDRAWAL_CURRENCIES: ['wallet', 'withdrawalCurrencies'],
    WITHDRAWALS: ['wallet', 'withdrawals'],
};

/**
 * Custom hook for all wallet-related React Query operations.
 * Centralizes data fetching, caching, and invalidation logic.
 */
export const useWalletQuery = () => {
    const queryClient = useQueryClient();
    const { isAuthenticated } = useAuth(); // To enable/disable queries based on auth state

    // 1. Get Wallet Balance
    const useBalance = () => {
        return useQuery({
            queryKey: WALLET_QUERY_KEYS.BALANCE,
            queryFn: async () => {
                const balance = await globalBalanceService.getBalance();
                // Ensure balance is a valid number, default to 0 if not
                return typeof balance === 'number' && balance >= 0 ? balance : 0;
            },
            enabled: isAuthenticated, // Only fetch if authenticated
            staleTime: walletConstants.balanceRefreshInterval, // Keep data fresh for this duration
            refetchInterval: walletConstants.balanceRefreshInterval, // Refetch every X milliseconds
            gcTime: walletConstants.cacheTimeout, // Garbage collection time
            // onError: (error) => console.error('Error fetching balance:', error),
        });
    };

    // 2. Get Transaction History
    const useTransactions = (filters = {}) => {
        const { limit = 20, offset = 0, type = null } = filters;
        return useQuery({
            queryKey: [WALLET_QUERY_KEYS.TRANSACTIONS, { limit, offset, type }],
            queryFn: async () => {
                const creditTransactions = await CreditService.getTransactionHistory({ limit, offset, type });
                const safeCreditTransactions = Array.isArray(creditTransactions) ? creditTransactions : [];
                return safeCreditTransactions.sort(
                    (a, b) => new Date(b.created_at) - new Date(a.created_at)
                );
            },
            enabled: isAuthenticated,
            staleTime: walletConstants.transactionRefreshInterval,
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching transactions:', error),
        });
    };

    // 3. Get Credit Packages
    const useCreditPackages = (channel = 'default') => {
        return useQuery({
            queryKey: [WALLET_QUERY_KEYS.CREDIT_PACKAGES, channel],
            queryFn: () => CreditService.getCreditPackages(channel),
            staleTime: Infinity, // Packages don't change often, keep indefinitely stale for manual refetch
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching credit packages:', error),
        });
    };

    // 4. Process Payment for Credit Package (Mutation)
    const useProcessPayment = () => {
        return useMutation({
            mutationFn: (packageId, options) => CreditService.processPayment(packageId, options),
            onSuccess: () => {
                // Invalidate balance and transactions to refetch fresh data
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.BALANCE });
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.TRANSACTIONS });
            },
            // onError: (error) => console.error('Error processing payment:', error),
        });
    };

    // 5. Retry Failed Payment (Mutation)
    const useRetryPayment = () => {
        return useMutation({
            mutationFn: (transactionId) => CreditService.retryFailedPayment(transactionId),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.BALANCE });
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.TRANSACTIONS });
            },
            // onError: (error) => console.error('Error retrying payment:', error),
        });
    };

    // 6. Check Payment Status (Query - for polling or one-off checks)
    const useCheckPaymentStatus = (transactionId, options) => {
        return useQuery({
            queryKey: [WALLET_QUERY_KEYS.TRANSACTIONS, 'paymentStatus', transactionId],
            queryFn: () => CreditService.checkPaymentStatus(transactionId),
            enabled: !!transactionId && isAuthenticated && (options?.enabled ?? true), // Only enable if transactionId exists
            // Polling example: refetchInterval: 5000,
            staleTime: 0, // Always refetch when queried for real-time status
            gcTime: walletConstants.cacheTimeout,
            onSuccess: (data) => {
                if (data?.status === 'success') {
                    // If payment is successful, ensure balance and transactions are refetched
                    queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.BALANCE });
                    queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.TRANSACTIONS });
                }
            },
            // onError: (error) => console.error('Error checking payment status:', error),
        });
    };

    // 7. Get Wallet Verification Status
    const useWalletVerificationStatus = () => {
        return useQuery({
            queryKey: WALLET_QUERY_KEYS.VERIFICATION_STATUS,
            queryFn: () => WalletVerificationService.getVerificationStatus(),
            enabled: isAuthenticated,
            staleTime: walletConstants.cacheTimeout, // Can be less frequent if status doesn't change often
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching wallet verification status:', error),
        });
    };

    // 8. Get Withdrawal Eligibility
    const useWithdrawalEligibility = () => {
        return useQuery({
            queryKey: WALLET_QUERY_KEYS.WITHDRAWAL_ELIGIBILITY,
            queryFn: () => WithdrawalService.checkEligibility(),
            enabled: isAuthenticated,
            staleTime: walletConstants.cacheTimeout,
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching withdrawal eligibility:', error),
        });
    };

    // 9. Get Withdrawal Currencies
    const useWithdrawalCurrencies = () => {
        return useQuery({
            queryKey: WALLET_QUERY_KEYS.WITHDRAWAL_CURRENCIES,
            queryFn: () => WithdrawalService.getAvailableCurrencies(),
            enabled: isAuthenticated,
            staleTime: Infinity, // Currencies are static
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching withdrawal currencies:', error),
        });
    };

    // 10. Get Withdrawal History
    const useWithdrawals = (filters = {}) => {
        const { limit = 20, offset = 0, status = null } = filters;
        return useQuery({
            queryKey: [WALLET_QUERY_KEYS.WITHDRAWALS, { limit, offset, status }],
            queryFn: () => WithdrawalService.getWithdrawalHistory({ limit, offset, status }),
            enabled: isAuthenticated,
            staleTime: walletConstants.cacheTimeout,
            gcTime: walletConstants.cacheTimeout,
            // onError: (error) => console.error('Error fetching withdrawals:', error),
        });
    };

    // 11. Process Withdrawal (Mutation)
    const useProcessWithdrawal = () => {
        return useMutation({
            mutationFn: (withdrawalData) => WithdrawalService.processWithdrawal(withdrawalData),
            onSuccess: () => {
                // Invalidate balance and withdrawals to refetch fresh data
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.BALANCE });
                queryClient.invalidateQueries({ queryKey: WALLET_QUERY_KEYS.WITHDRAWALS });
            },
            // onError: (error) => console.error('Error processing withdrawal:', error),
        });
    };

    return {
        WALLET_QUERY_KEYS, // Export keys for external invalidation
        useBalance,
        useTransactions,
        useCreditPackages,
        useProcessPayment,
        useRetryPayment,
        useCheckPaymentStatus,
        useWalletVerificationStatus,
        useWithdrawalEligibility,
        useWithdrawalCurrencies,
        useWithdrawals,
        useProcessWithdrawal,
    };
}; 