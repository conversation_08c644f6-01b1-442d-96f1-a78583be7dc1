import api from '../../../services/api';

/**
 * Enhanced Verification Service for Wallet Features
 *
 * This service provides verification-first approach for wallet operations,
 * ensuring API calls are only made when users have appropriate verification levels.
 */
class WalletVerificationService {
  constructor() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null
    };

    // Cache duration: 5 minutes for verification status
    this.VERIFICATION_CACHE_DURATION = 5 * 60 * 1000;
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(lastUpdate, duration) {
    if (!lastUpdate) return false;
    return Date.now() - lastUpdate < duration;
  }

  /**
   * Clear verification cache
   */
  clearCache() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null
    };
  }

  /**
   * Get unified verification status for wallet operations
   */
  async getVerificationStatus(forceRefresh = false) {
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && this.isCacheValid(this.cache.lastStatusUpdate, this.VERIFICATION_CACHE_DURATION)) {
      return this.cache.verificationStatus;
    }

    try {
      const response = await api.get('/ekyc/status');
      const status = response.data;

      // Create unified verification object optimized for wallet operations
      const verificationStatus = {
        emailVerified: status.email_verified || false,
        kycVerified: status.is_verified === true, // Backend returns is_verified boolean, not status string
        kycStatus: status.is_verified ? 'verified' : 'not_verified',
        canWithdraw: this.calculateWithdrawalEligibility(status),
        missingRequirements: this.getMissingRequirements(status),
        submittedAt: status.verified_at,
        verificationLevel: this.calculateVerificationLevel(status),
        // Additional wallet-specific properties
        canAccessWithdrawalHistory: this.canAccessWithdrawalFeatures(status),
        canMakeWithdrawals: this.canMakeWithdrawals(status),
        nextVerificationStep: this.getNextVerificationStep(status)
      };

      // Update cache
      this.cache.verificationStatus = verificationStatus;
      this.cache.lastStatusUpdate = Date.now();

      return verificationStatus;
    } catch (error) {
      console.error('Error fetching verification status:', error);

      // Return cached data if available during error
      if (this.cache.verificationStatus) {
        console.warn('Using cached verification status due to API error');
        return this.cache.verificationStatus;
      }

      // Return safe default unverified status
      return this.getDefaultUnverifiedStatus();
    }
  }

  /**
   * Calculate if user can withdraw based on backend requirements
   */
  calculateWithdrawalEligibility(status) {
    return status.email_verified && status.is_verified === true;
  }

  /**
   * Check if user can access withdrawal features (history, eligibility checks)
   */
  canAccessWithdrawalFeatures(status) {
    return status.email_verified && status.is_verified === true;
  }

  /**
   * Check if user can make actual withdrawals
   */
  canMakeWithdrawals(status) {
    return status.email_verified && status.is_verified === true;
  }

  /**
   * Get missing requirements for withdrawal operations
   */
  getMissingRequirements(status) {
    const missing = [];

    if (!status.email_verified) {
      missing.push('email_verification');
    }

    if (status.is_verified !== true) {
      missing.push('kyc_verification');
    }

    return missing;
  }

  /**
   * Calculate verification level
   */
  calculateVerificationLevel(status) {
    if (status.email_verified && status.is_verified === true) {
      return 'fully_verified';
    }
    if (status.is_verified === true) {
      return 'kyc_verified';
    }
    if (status.email_verified) {
      return 'email_verified';
    }
    return 'unverified';
  }

  /**
   * Get next verification step for user guidance
   */
  getNextVerificationStep(status) {
    if (!status.email_verified) {
      return {
        step: 'email_verification',
        title: 'Verify Your Email',
        description: 'Check your email and click the verification link',
        action: 'verify_email'
      };
    }

    if (status.is_verified !== true) {
      // Since the new API only returns is_verified boolean, we can't distinguish between
      // not_submitted, pending, or rejected. We'll assume not_submitted for simplicity.
      return {
        step: 'kyc_submission',
        title: 'Complete E-KYC Verification',
        description: 'Submit your identity documents for verification',
        action: 'start_kyc'
      };
    }

    return null; // Fully verified
  }

  /**
   * Get default unverified status for error cases
   */
  getDefaultUnverifiedStatus() {
    return {
      emailVerified: false,
      kycVerified: false,
      kycStatus: 'not_submitted',
      canWithdraw: false,
      missingRequirements: ['email_verification', 'kyc_verification'],
      submittedAt: null,
      verificationLevel: 'unverified',
      canAccessWithdrawalHistory: false,
      canMakeWithdrawals: false,
      nextVerificationStep: {
        step: 'email_verification',
        title: 'Verify Your Email',
        description: 'Check your email and click the verification link',
        action: 'verify_email'
      }
    };
  }

  /**
   * Send email verification
   */
  async sendEmailVerification() {
    try {
      const response = await api.post('/email/send-verification');

      // Clear cache to force refresh on next status check
      this.clearCache();

      return {
        success: true,
        message: response.data?.message || 'Verification email sent successfully'
      };
    } catch (error) {
      console.error('Error sending email verification:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send verification email'
      };
    }
  }

  /**
   * Check if user should see withdrawal features
   */
  shouldShowWithdrawalFeatures(verificationStatus) {
    if (!verificationStatus) return false;
    return verificationStatus.canAccessWithdrawalHistory;
  }

  /**
   * Check if withdrawal API calls should be made
   */
  shouldMakeWithdrawalAPICalls(verificationStatus) {
    if (!verificationStatus) return false;
    return verificationStatus.canAccessWithdrawalHistory;
  }

  /**
   * Get verification error message for UI display
   */
  getVerificationErrorMessage(verificationStatus) {
    if (!verificationStatus) {
      return 'Unable to check verification status. Please try again.';
    }

    const missing = verificationStatus.missingRequirements;

    if (missing.includes('email_verification') && missing.includes('kyc_verification')) {
      return 'Email verification and E-KYC verification required for withdrawal operations.';
    }

    if (missing.includes('email_verification')) {
      return 'Email verification required for withdrawal operations.';
    }

    if (missing.includes('kyc_verification')) {
      return 'E-KYC verification required for withdrawal operations.';
    }

    return 'Verification requirements not met for withdrawal operations.';
  }

  /**
   * Create verification requirement object for API responses
   */
  createVerificationRequirement(verificationStatus, message = null) {
    return {
      withdrawals: [],
      requiresEmailVerification: verificationStatus?.missingRequirements?.includes('email_verification') || false,
      requiresKycVerification: verificationStatus?.missingRequirements?.includes('kyc_verification') || false,
      requiresVerification: !verificationStatus?.canWithdraw || false,
      isVerificationError: true,
      message: message || this.getVerificationErrorMessage(verificationStatus),
      nextStep: verificationStatus?.nextVerificationStep || null
    };
  }
}

// Create and export service instance
const walletVerificationService = new WalletVerificationService();

export default walletVerificationService;
