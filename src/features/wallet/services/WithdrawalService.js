import walletAPI from '../../../services/walletService';
import bankAccountService from '../../../services/bankAccountService';
import verificationService from '../../../services/verificationService';
import WalletVerificationService from './VerificationService';

/**
 * WithdrawalService - Frontend service abstraction for withdrawal operations
 *
 * This service provides a high-level interface for withdrawal-related operations,
 * wrapping the lower-level walletService with additional validation, error handling,
 * and business logic specific to withdrawal management.
 */
class WithdrawalService {
  constructor() {
    this.cache = {
      withdrawals: [],
      lastWithdrawalUpdate: null,
      currencies: [],
      lastCurrencyUpdate: null,
      eligibility: null,
      lastEligibilityCheck: null
    };

    // Cache duration in milliseconds
    this.WITHDRAWAL_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
    this.CURRENCY_CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
    this.ELIGIBILITY_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

    // Withdrawal limits and validation rules
    this.VALIDATION_RULES = {
      MIN_WITHDRAWAL_AMOUNT: 10, // Minimum 10 credits
      MAX_WITHDRAWAL_AMOUNT: 10000, // Maximum 10,000 credits per transaction
      DAILY_WITHDRAWAL_LIMIT: 50000, // Maximum 50,000 credits per day
      SUPPORTED_CURRENCIES: ['MYR', 'USD', 'SGD'],
      CONVERSION_RATES: {
        'MYR': 0.25, // 1 credit = 0.25 MYR
        'USD': 0.06, // 1 credit = 0.06 USD
        'SGD': 0.08  // 1 credit = 0.08 SGD
      }
    };
  }

  /**
   * Check withdrawal eligibility with caching
   * @param {boolean} forceRefresh - Force refresh from server
   * @returns {Promise<Object>} Eligibility status
   */
  async checkEligibility(forceRefresh = false) {
    try {
      const now = Date.now();
      const cacheValid = this.cache.lastEligibilityCheck &&
                        (now - this.cache.lastEligibilityCheck) < this.ELIGIBILITY_CACHE_DURATION;

      if (!forceRefresh && cacheValid && this.cache.eligibility) {
        return this.cache.eligibility;
      }

      // Check verification status
      const verificationResponse = await verificationService.canWithdraw();
      const eligibility = {
        canWithdraw: verificationResponse.data?.can_withdraw || false,
        reasons: verificationResponse.data?.reasons || [],
        requirements: verificationResponse.data?.requirements || [],
        isEkycVerified: verificationResponse.data?.ekyc_verified || false,
        isEmailVerified: verificationResponse.data?.email_verified || false,
        hasBankAccount: verificationResponse.data?.has_bank_account || false
      };

      // Update cache
      this.cache.eligibility = eligibility;
      this.cache.lastEligibilityCheck = now;

      return eligibility;
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error);

      // Return cached eligibility if available
      if (this.cache.eligibility) {
        return this.cache.eligibility;
      }

      // Default to not eligible if no cache and error occurred
      return {
        canWithdraw: false,
        reasons: ['Unable to verify eligibility. Please try again.'],
        requirements: ['E-KYC verification', 'Email verification', 'Bank account'],
        isEkycVerified: false,
        isEmailVerified: false,
        hasBankAccount: false
      };
    }
  }

  /**
   * Validate withdrawal request
   * @param {Object} withdrawalData - Withdrawal request data
   * @returns {Promise<Object>} Validation result
   */
  async validateWithdrawal(withdrawalData) {
    try {
      const { amount, currency, bankAccountId, currentBalance } = withdrawalData;

      // Basic validation
      const validationErrors = [];

      // Amount validation
      if (!amount || amount <= 0) {
        validationErrors.push('Amount must be greater than zero');
      } else if (amount < this.VALIDATION_RULES.MIN_WITHDRAWAL_AMOUNT) {
        validationErrors.push(`Minimum withdrawal amount is ${this.VALIDATION_RULES.MIN_WITHDRAWAL_AMOUNT} credits`);
      } else if (amount > this.VALIDATION_RULES.MAX_WITHDRAWAL_AMOUNT) {
        validationErrors.push(`Maximum withdrawal amount is ${this.VALIDATION_RULES.MAX_WITHDRAWAL_AMOUNT} credits per transaction`);
      }

      // Balance validation
      if (currentBalance !== undefined && amount > currentBalance) {
        validationErrors.push(`Insufficient balance. You have ${currentBalance} credits, but need ${amount} credits.`);
      }

      // Currency validation
      if (!currency || !this.VALIDATION_RULES.SUPPORTED_CURRENCIES.includes(currency)) {
        validationErrors.push(`Unsupported currency. Supported currencies: ${this.VALIDATION_RULES.SUPPORTED_CURRENCIES.join(', ')}`);
      }

      // Bank account validation
      if (!bankAccountId) {
        validationErrors.push('Please select a bank account for withdrawal');
      }

      // Check eligibility
      const eligibility = await this.checkEligibility();
      if (!eligibility.canWithdraw) {
        validationErrors.push(...eligibility.reasons);
      }

      // Calculate conversion
      const conversionRate = this.VALIDATION_RULES.CONVERSION_RATES[currency] || 0;
      const fiatAmount = amount * conversionRate;

      if (validationErrors.length > 0) {
        return {
          isValid: false,
          errors: validationErrors,
          code: 'VALIDATION_FAILED'
        };
      }

      return {
        isValid: true,
        creditAmount: amount,
        fiatAmount,
        conversionRate,
        currency,
        eligibility
      };
    } catch (error) {
      console.error('Error validating withdrawal:', error);
      return {
        isValid: false,
        errors: ['Unable to validate withdrawal. Please try again.'],
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Get available withdrawal currencies with caching
   * @param {boolean} forceRefresh - Force refresh from server
   * @returns {Promise<Array>} Available currencies
   */
  async getAvailableCurrencies(forceRefresh = false) {
    try {
      const now = Date.now();
      const cacheValid = this.cache.lastCurrencyUpdate &&
                        (now - this.cache.lastCurrencyUpdate) < this.CURRENCY_CACHE_DURATION;

      if (!forceRefresh && cacheValid && this.cache.currencies.length > 0) {
        return this.cache.currencies;
      }

      const response = await walletAPI.getWithdrawalCurrencies('credits');
      const currencies = response.data?.currencies || this.getDefaultCurrencies();

      // Update cache
      this.cache.currencies = currencies;
      this.cache.lastCurrencyUpdate = now;

      return currencies;
    } catch (error) {
      console.error('Error fetching withdrawal currencies:', error);

      // Return cached currencies if available
      if (this.cache.currencies.length > 0) {
        return this.cache.currencies;
      }

      // Return default currencies as fallback
      return this.getDefaultCurrencies();
    }
  }

  /**
   * Get default currencies as fallback
   * @returns {Array} Default currency list
   */
  getDefaultCurrencies() {
    return [
      {
        code: 'MYR',
        name: 'Malaysian Ringgit',
        symbol: 'RM',
        rate: this.VALIDATION_RULES.CONVERSION_RATES.MYR,
        is_default: true
      },
      {
        code: 'USD',
        name: 'US Dollar',
        symbol: '$',
        rate: this.VALIDATION_RULES.CONVERSION_RATES.USD,
        is_default: false
      },
      {
        code: 'SGD',
        name: 'Singapore Dollar',
        symbol: 'S$',
        rate: this.VALIDATION_RULES.CONVERSION_RATES.SGD,
        is_default: false
      }
    ];
  }

  /**
   * Calculate fiat amount from credits
   * @param {number} creditAmount - Amount in credits
   * @param {string} currency - Target currency
   * @returns {Object} Conversion result
   */
  calculateConversion(creditAmount, currency = 'MYR') {
    const rate = this.VALIDATION_RULES.CONVERSION_RATES[currency] || 0;
    const fiatAmount = creditAmount * rate;

    return {
      creditAmount,
      fiatAmount,
      currency,
      rate,
      formatted: this.formatCurrency(fiatAmount, currency)
    };
  }

  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted amount
   */
  formatCurrency(amount, currency = 'MYR') {
    try {
      const locale = currency === 'MYR' ? 'en-MY' : 'en-US';
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      const symbols = { MYR: 'RM', USD: '$', SGD: 'S$' };
      const symbol = symbols[currency] || currency;
      return `${symbol} ${amount.toFixed(2)}`;
    }
  }

  /**
   * Format credits amount
   * @param {number} amount - Amount in credits
   * @returns {string} Formatted credits
   */
  formatCredits(amount) {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount) + ' credits';
  }

  /**
   * Clear withdrawal cache
   */
  clearCache() {
    this.cache.withdrawals = [];
    this.cache.lastWithdrawalUpdate = null;
    this.cache.eligibility = null;
    this.cache.lastEligibilityCheck = null;
  }

  /**
   * Process withdrawal request
   * @param {Object} withdrawalData - Withdrawal request data
   * @returns {Promise<Object>} Withdrawal result
   */
  async processWithdrawal(withdrawalData) {
    try {
      // Validate withdrawal first
      const validation = await this.validateWithdrawal(withdrawalData);
      if (!validation.isValid) {
        return {
          success: false,
          errors: validation.errors,
          code: validation.code
        };
      }

      // Prepare withdrawal payload
      const payload = {
        amount: withdrawalData.amount,
        bank_account_id: withdrawalData.bankAccountId,
        fiat_currency: withdrawalData.currency,
        payment_mode_id: withdrawalData.paymentModeId || 1,
        recipient_reference: withdrawalData.recipientReference || '',
        other_payment_details: withdrawalData.otherPaymentDetails || '',
        id_validation: withdrawalData.idValidation || false,
        id_validation_type: withdrawalData.idValidationType,
        id_validation_value: withdrawalData.idValidationValue,
        transaction_type: withdrawalData.transactionType || 'withdrawal',
        purpose_of_transfer: withdrawalData.purposeOfTransfer || 'Personal withdrawal',
        email: withdrawalData.email
      };

      // Process withdrawal through wallet API
      const response = await walletAPI.withdraw(payload);

      if (response.data?.withdrawal) {
        const withdrawal = response.data.withdrawal;

        // Store withdrawal context for tracking
        this.storeWithdrawalContext({
          withdrawalId: withdrawal.id,
          transactionId: withdrawal.transaction_id,
          amount: withdrawalData.amount,
          currency: withdrawalData.currency,
          fiatAmount: validation.fiatAmount,
          status: withdrawal.status
        });

        // Invalidate caches since withdrawal was processed
        this.clearCache();

        return {
          success: true,
          withdrawal,
          validation
        };
      } else {
        throw new Error('Invalid withdrawal response from server');
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);

      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Withdrawal processing failed. Please try again.';

      return {
        success: false,
        error: errorMessage,
        code: error.response?.status || 'WITHDRAWAL_ERROR'
      };
    }
  }

  /**
   * Get withdrawal history with caching and filtering (verification-aware)
   * @param {Object} filters - Withdrawal filters
   * @returns {Promise<Array>} Withdrawal history
   */
  async getWithdrawalHistory(filters = {}) {
    try {
      const {
        limit = 20,
        offset = 0,
        status = null,
        forceRefresh = false,
        skipVerificationCheck = false
      } = filters;

      const now = Date.now();
      const cacheKey = `${limit}-${offset}-${status}`;
      const cacheValid = this.cache.lastWithdrawalUpdate &&
                        (now - this.cache.lastWithdrawalUpdate) < this.WITHDRAWAL_CACHE_DURATION;

      if (!forceRefresh && cacheValid && this.cache.withdrawals[cacheKey]) {
        return this.cache.withdrawals[cacheKey];
      }

      // Check verification status first (unless explicitly skipped)
      if (!skipVerificationCheck) {
        const verificationStatus = await WalletVerificationService.getVerificationStatus();

        if (!WalletVerificationService.shouldMakeWithdrawalAPICalls(verificationStatus)) {
          console.log('Withdrawal API call skipped - user not verified');
          return WalletVerificationService.createVerificationRequirement(
            verificationStatus,
            'Verification required to view withdrawal history'
          );
        }
      }

      const response = await walletAPI.getWithdrawals(limit, offset);

      // Handle verification requirement responses
      if (response && typeof response === 'object' && response.requiresEmailVerification) {
        // Return the verification response as-is for the UI to handle
        return response;
      }

      // Safely extract withdrawals data
      let withdrawals = [];
      if (response && response.data) {
        withdrawals = response.data.transactions || response.data.withdrawals || response.data || [];
      }

      // Ensure withdrawals is always an array
      if (!Array.isArray(withdrawals)) {
        console.warn('Withdrawal history response is not an array:', withdrawals);
        withdrawals = [];
      }

      // Filter by status if specified
      if (status && withdrawals.length > 0) {
        withdrawals = withdrawals.filter(w => w && w.status === status);
      }

      // Enhance withdrawals with formatted data
      withdrawals = withdrawals.map(withdrawal => {
        if (!withdrawal) return null;

        return {
          ...withdrawal,
          formattedAmount: this.formatCredits(withdrawal.amount || 0),
          formattedFiatAmount: this.formatCurrency(
            withdrawal.fiat_amount || 0,
            withdrawal.fiat_currency_code || 'MYR'
          ),
          conversionRate: withdrawal.conversion_rate || 1,
          statusColor: this.getStatusColor(withdrawal.status || 'pending'),
          statusText: this.getStatusText(withdrawal.status || 'pending')
        };
      }).filter(Boolean); // Remove null entries

      // Update cache
      if (!this.cache.withdrawals) this.cache.withdrawals = {};
      this.cache.withdrawals[cacheKey] = withdrawals;
      this.cache.lastWithdrawalUpdate = now;

      return withdrawals;
    } catch (error) {
      console.error('Error fetching withdrawal history:', error);

      // Handle specific error cases
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
          case 403:
            // Handle verification requirements using verification service
            console.warn('403 Forbidden - verification required for withdrawal history');

            // Get current verification status to provide accurate guidance
            try {
              const verificationStatus = await WalletVerificationService.getVerificationStatus();
              return WalletVerificationService.createVerificationRequirement(
                verificationStatus,
                data.message || 'Verification required to view withdrawal history'
              );
            } catch (verificationError) {
              console.error('Error getting verification status for 403 handling:', verificationError);

              // Fallback to parsing error message
              if (data.message?.includes('Email verification') || data.message?.includes('email')) {
                return {
                  withdrawals: [],
                  requiresEmailVerification: true,
                  requiresKycVerification: false,
                  requiresVerification: true,
                  isVerificationError: true,
                  message: 'Email verification required to view withdrawal history. Please verify your email address.'
                };
              }
              if (data.message?.includes('E-KYC') || data.message?.includes('KYC')) {
                return {
                  withdrawals: [],
                  requiresEmailVerification: false,
                  requiresKycVerification: true,
                  requiresVerification: true,
                  isVerificationError: true,
                  message: 'Identity verification (E-KYC) required to view withdrawal history. Please complete your identity verification.'
                };
              }
              // Generic verification requirement
              return {
                withdrawals: [],
                requiresEmailVerification: false,
                requiresKycVerification: false,
                requiresVerification: true,
                isVerificationError: true,
                message: 'Account verification required to view withdrawal history. Please ensure your account is properly verified.'
              };
            }

          case 401:
            throw new Error('Authentication required. Please log in again.');

          case 404:
            // Endpoint not found - return empty array instead of error
            console.warn('Withdrawal history endpoint not found, returning empty array');
            return [];

          case 500:
            throw new Error('Server error occurred. Please try again later.');

          default:
            throw new Error(data.message || 'Unable to fetch withdrawal history. Please try again.');
        }
      }

      // Return cached withdrawals if available
      const cacheKey = `${filters.limit || 20}-${filters.offset || 0}-${filters.status || null}`;
      if (this.cache.withdrawals && this.cache.withdrawals[cacheKey]) {
        console.log('Returning cached withdrawal history due to API error');
        return this.cache.withdrawals[cacheKey];
      }

      // Return empty array as fallback instead of throwing error
      console.warn('No cached withdrawal history available, returning empty array');
      return [];
    }
  }

  /**
   * Get withdrawal details by ID
   * @param {string} withdrawalId - Withdrawal ID
   * @returns {Promise<Object>} Withdrawal details
   */
  async getWithdrawalDetails(withdrawalId) {
    try {
      const response = await walletAPI.getWithdrawal(withdrawalId);
      const withdrawal = response.data?.withdrawal || response.data;

      if (!withdrawal) {
        throw new Error('Withdrawal not found');
      }

      // Enhance withdrawal with formatted data
      return {
        ...withdrawal,
        formattedAmount: this.formatCredits(withdrawal.amount),
        formattedFiatAmount: this.formatCurrency(withdrawal.fiat_amount, withdrawal.fiat_currency_code),
        conversionRate: withdrawal.conversion_rate,
        statusColor: this.getStatusColor(withdrawal.status),
        statusText: this.getStatusText(withdrawal.status)
      };
    } catch (error) {
      console.error('Error fetching withdrawal details:', error);
      throw new Error('Unable to fetch withdrawal details. Please try again.');
    }
  }

  /**
   * Get status color for UI display
   * @param {string} status - Withdrawal status
   * @returns {string} CSS color classes
   */
  getStatusColor(status) {
    const statusColors = {
      'pending': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'processing': 'text-blue-600 bg-blue-50 border-blue-200',
      'completed': 'text-green-600 bg-green-50 border-green-200',
      'failed': 'text-red-600 bg-red-50 border-red-200',
      'cancelled': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return statusColors[status] || statusColors['pending'];
  }

  /**
   * Get status text for UI display
   * @param {string} status - Withdrawal status
   * @returns {string} Human-readable status
   */
  getStatusText(status) {
    const statusTexts = {
      'pending': 'Pending Review',
      'processing': 'Processing',
      'completed': 'Completed',
      'failed': 'Failed',
      'cancelled': 'Cancelled'
    };
    return statusTexts[status] || 'Unknown';
  }

  /**
   * Store withdrawal context in localStorage
   * @param {Object} context - Withdrawal context
   */
  storeWithdrawalContext(context) {
    try {
      localStorage.setItem('withdrawalContext', JSON.stringify({
        withdrawalId: context.withdrawalId,
        transactionId: context.transactionId,
        amount: context.amount,
        currency: context.currency,
        fiatAmount: context.fiatAmount,
        status: context.status,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Unable to store withdrawal context:', error);
    }
  }

  /**
   * Clear withdrawal context from localStorage
   */
  clearWithdrawalContext() {
    try {
      localStorage.removeItem('withdrawalContext');
    } catch (error) {
      console.warn('Unable to clear withdrawal context:', error);
    }
  }

  /**
   * Get validation rules
   * @returns {Object} Current validation rules
   */
  getValidationRules() {
    return { ...this.VALIDATION_RULES };
  }
}

// Create and export singleton instance
const withdrawalService = new WithdrawalService();
export default withdrawalService;
