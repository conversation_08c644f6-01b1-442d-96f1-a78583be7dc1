/**
 * ValidationService - Enhanced validation utilities for wallet operations
 *
 * This service provides comprehensive validation for wallet and payment operations,
 * including amount validation, transaction validation, and business rule enforcement.
 */
class ValidationService {
  constructor() {
    this.rules = {
      // Amount validation rules
      amount: {
        min: 0.01,
        max: 999999.99,
        precision: 2
      },

      // Transaction validation rules
      transaction: {
        maxDailyAmount: 50000,
        maxMonthlyAmount: 200000,
        maxTransactionsPerDay: 100
      },

      // Payment validation rules
      payment: {
        minTopupAmount: 1,
        maxTopupAmount: 10000,
        allowedCurrencies: ['MYR', 'USD', 'SGD']
      }
    };
  }

  /**
   * Validate amount format and range
   * @param {number|string} amount - Amount to validate
   * @param {Object} options - Validation options
   * @returns {Object} Validation result
   */
  validateAmount(amount, options = {}) {
    const {
      min = this.rules.amount.min,
      max = this.rules.amount.max,
      precision = this.rules.amount.precision,
      required = true
    } = options;

    // Check if amount is provided when required
    if (required && (amount === null || amount === undefined || amount === '')) {
      return {
        isValid: false,
        error: 'Amount is required',
        code: 'AMOUNT_REQUIRED'
      };
    }

    // Convert to number if string
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    // Check if it's a valid number
    if (isNaN(numAmount) || !isFinite(numAmount)) {
      return {
        isValid: false,
        error: 'Amount must be a valid number',
        code: 'INVALID_NUMBER'
      };
    }

    // Check minimum amount
    if (numAmount < min) {
      return {
        isValid: false,
        error: `Amount must be at least ${min}`,
        code: 'AMOUNT_TOO_LOW',
        min
      };
    }

    // Check maximum amount
    if (numAmount > max) {
      return {
        isValid: false,
        error: `Amount cannot exceed ${max}`,
        code: 'AMOUNT_TOO_HIGH',
        max
      };
    }

    // Check decimal precision
    const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
    if (decimalPlaces > precision) {
      return {
        isValid: false,
        error: `Amount cannot have more than ${precision} decimal places`,
        code: 'INVALID_PRECISION',
        precision
      };
    }

    return {
      isValid: true,
      amount: numAmount,
      formatted: this.formatAmount(numAmount)
    };
  }

  /**
   * Validate transaction against business rules
   * @param {Object} transaction - Transaction details
   * @param {Object} userLimits - User-specific limits
   * @returns {Object} Validation result
   */
  validateTransaction(transaction, userLimits = {}) {
    const {
      amount,
      type,
      currency = 'MYR',
      userId,
      dailyTotal = 0,
      monthlyTotal = 0,
      dailyCount = 0
    } = transaction;

    // Validate amount first
    const amountValidation = this.validateAmount(amount);
    if (!amountValidation.isValid) {
      return amountValidation;
    }

    // Check transaction type specific rules
    if (type === 'topup') {
      if (amount < this.rules.payment.minTopupAmount) {
        return {
          isValid: false,
          error: `Minimum top-up amount is ${this.rules.payment.minTopupAmount}`,
          code: 'TOPUP_AMOUNT_TOO_LOW'
        };
      }

      if (amount > this.rules.payment.maxTopupAmount) {
        return {
          isValid: false,
          error: `Maximum top-up amount is ${this.rules.payment.maxTopupAmount}`,
          code: 'TOPUP_AMOUNT_TOO_HIGH'
        };
      }
    }

    // Check daily limits
    const newDailyTotal = dailyTotal + amount;
    const maxDaily = userLimits.maxDailyAmount || this.rules.transaction.maxDailyAmount;

    if (newDailyTotal > maxDaily) {
      return {
        isValid: false,
        error: `Daily transaction limit of ${maxDaily} would be exceeded`,
        code: 'DAILY_LIMIT_EXCEEDED',
        limit: maxDaily,
        current: dailyTotal,
        requested: amount
      };
    }

    // Check monthly limits
    const newMonthlyTotal = monthlyTotal + amount;
    const maxMonthly = userLimits.maxMonthlyAmount || this.rules.transaction.maxMonthlyAmount;

    if (newMonthlyTotal > maxMonthly) {
      return {
        isValid: false,
        error: `Monthly transaction limit of ${maxMonthly} would be exceeded`,
        code: 'MONTHLY_LIMIT_EXCEEDED',
        limit: maxMonthly,
        current: monthlyTotal,
        requested: amount
      };
    }

    // Check transaction count limits
    const maxDailyCount = userLimits.maxDailyCount || this.rules.transaction.maxTransactionsPerDay;

    if (dailyCount >= maxDailyCount) {
      return {
        isValid: false,
        error: `Daily transaction count limit of ${maxDailyCount} reached`,
        code: 'DAILY_COUNT_EXCEEDED',
        limit: maxDailyCount
      };
    }

    // Check currency support
    if (!this.rules.payment.allowedCurrencies.includes(currency)) {
      return {
        isValid: false,
        error: `Currency ${currency} is not supported`,
        code: 'UNSUPPORTED_CURRENCY',
        supportedCurrencies: this.rules.payment.allowedCurrencies
      };
    }

    return {
      isValid: true,
      transaction: {
        ...transaction,
        amount: amountValidation.amount,
        formattedAmount: amountValidation.formatted
      },
      limits: {
        dailyRemaining: maxDaily - newDailyTotal,
        monthlyRemaining: maxMonthly - newMonthlyTotal,
        transactionsRemaining: maxDailyCount - dailyCount - 1
      }
    };
  }

  /**
   * Validate payment package selection
   * @param {Object} packageData - Credit package details
   * @param {Object} userContext - User context
   * @returns {Object} Validation result
   */
  validatePaymentPackage(packageData, userContext = {}) {
    if (!packageData || !packageData.id) {
      return {
        isValid: false,
        error: 'Package selection is required',
        code: 'PACKAGE_REQUIRED'
      };
    }

    if (!packageData.is_active) {
      return {
        isValid: false,
        error: 'Selected package is no longer available',
        code: 'PACKAGE_INACTIVE'
      };
    }

    if (packageData.price <= 0) {
      return {
        isValid: false,
        error: 'Invalid package price',
        code: 'INVALID_PACKAGE_PRICE'
      };
    }

    // Check if user meets package requirements
    if (packageData.min_user_level && userContext.level < packageData.min_user_level) {
      return {
        isValid: false,
        error: `This package requires user level ${packageData.min_user_level} or higher`,
        code: 'INSUFFICIENT_USER_LEVEL',
        requiredLevel: packageData.min_user_level,
        currentLevel: userContext.level
      };
    }

    return {
      isValid: true,
      package: packageData
    };
  }

  /**
   * Validate withdrawal request
   * @param {Object} withdrawal - Withdrawal details
   * @param {Object} context - User and account context
   * @returns {Object} Validation result
   */
  validateWithdrawal(withdrawal, context = {}) {
    const {
      amount,
      bankAccountId,
      currency = 'MYR'
    } = withdrawal;

    const {
      balance = 0,
      bankAccounts = [],
      verificationStatus = {}
    } = context;

    // Validate amount
    const amountValidation = this.validateAmount(amount, {
      min: 10, // Minimum withdrawal amount
      max: 10000 // Maximum withdrawal amount
    });

    if (!amountValidation.isValid) {
      return amountValidation;
    }

    // Check sufficient balance
    if (amount > balance) {
      return {
        isValid: false,
        error: `Insufficient balance. You have ${balance} credits but requested ${amount}`,
        code: 'INSUFFICIENT_BALANCE',
        balance,
        requested: amount,
        shortfall: amount - balance
      };
    }

    // Validate bank account
    if (!bankAccountId) {
      return {
        isValid: false,
        error: 'Bank account selection is required',
        code: 'BANK_ACCOUNT_REQUIRED'
      };
    }

    const selectedAccount = bankAccounts.find(acc => acc.id === bankAccountId);
    if (!selectedAccount) {
      return {
        isValid: false,
        error: 'Selected bank account not found',
        code: 'BANK_ACCOUNT_NOT_FOUND'
      };
    }

    if (!selectedAccount.is_verified) {
      return {
        isValid: false,
        error: 'Bank account must be verified before withdrawal',
        code: 'BANK_ACCOUNT_NOT_VERIFIED'
      };
    }

    // Check user verification status
    if (!verificationStatus.email_verified) {
      return {
        isValid: false,
        error: 'Email verification is required for withdrawals',
        code: 'EMAIL_NOT_VERIFIED'
      };
    }

    if (!verificationStatus.kyc_verified) {
      return {
        isValid: false,
        error: 'KYC verification is required for withdrawals',
        code: 'KYC_NOT_VERIFIED'
      };
    }

    return {
      isValid: true,
      withdrawal: {
        ...withdrawal,
        amount: amountValidation.amount,
        formattedAmount: amountValidation.formatted
      },
      bankAccount: selectedAccount
    };
  }

  /**
   * Format amount for display
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted amount
   */
  formatAmount(amount, currency = 'MYR') {
    try {
      return new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      return `${currency} ${amount.toFixed(2)}`;
    }
  }

  /**
   * Format credits for display
   * @param {number} amount - Credit amount
   * @returns {string} Formatted credits
   */
  formatCredits(amount) {
    if (typeof amount !== 'number') return '0';

    return amount.toLocaleString('en-MY', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }

  /**
   * Update validation rules
   * @param {Object} newRules - New rules to merge
   */
  updateRules(newRules) {
    this.rules = {
      ...this.rules,
      ...newRules
    };
  }

  /**
   * Get current validation rules
   * @returns {Object} Current rules
   */
  getRules() {
    return { ...this.rules };
  }
}

// Create and export singleton instance
const validationService = new ValidationService();
export default validationService;
