/**
 * Banking Feature Barrel Export
 *
 * This file provides a centralized export point for all banking-related functionality.
 * It includes services, contexts, components, hooks, and utilities.
 */

// Re-export existing banking services for convenience
export { default as bankAccountAPI } from '../../services/bankAccountService';
export { default as currencyService } from '../../services/currencyService';
export { default as verificationService } from '../../services/verificationService';

// Contexts
export * from './contexts';

// Components
export * from './components';

// Note: Hooks will be added in subsequent steps
// export * from './hooks';

/**
 * Banking feature configuration
 */
export const bankingConfig = {
  // Validation settings
  validation: {
    accountNumberMinLength: 8,
    accountNumberMaxLength: 20,
    supportedBanks: [
      'Maybank', 'CIMB Bank', 'Public Bank', 'RHB Bank', 'Hong Leong Bank',
      'AmBank', 'Bank Islam', 'Bank Rakyat', 'OCBC Bank', 'Standard Chartered',
      'HSBC Bank', 'UOB Bank', 'Affin Bank', 'Alliance Bank', 'Bank Muamalat'
    ]
  },

  // Withdrawal settings
  withdrawal: {
    minAmount: 10,
    maxAmount: 10000,
    defaultCurrency: 'MYR',
    processingTime: '1-3 business days'
  },

  // UI settings
  ui: {
    maxAccountsDisplay: 5,
    defaultAccountsPageSize: 10,
    withdrawalHistoryPageSize: 20
  }
};

/**
 * Banking feature utilities
 */
export const bankingUtils = {
  /**
   * Validate Malaysian bank account number
   * @param {string} accountNumber - Account number to validate
   * @param {string} bankName - Bank name
   * @returns {Object} Validation result
   */
  validateAccountNumber: (accountNumber, bankName) => {
    if (!accountNumber || typeof accountNumber !== 'string') {
      return { isValid: false, error: 'Account number is required' };
    }

    const cleanNumber = accountNumber.replace(/\s|-/g, '');

    if (cleanNumber.length < bankingConfig.validation.accountNumberMinLength) {
      return { isValid: false, error: 'Account number is too short' };
    }

    if (cleanNumber.length > bankingConfig.validation.accountNumberMaxLength) {
      return { isValid: false, error: 'Account number is too long' };
    }

    if (!/^\d+$/.test(cleanNumber)) {
      return { isValid: false, error: 'Account number must contain only digits' };
    }

    return { isValid: true, cleanNumber };
  },

  /**
   * Format account number for display
   * @param {string} accountNumber - Account number to format
   * @returns {string} Formatted account number
   */
  formatAccountNumber: (accountNumber) => {
    if (!accountNumber) return '';

    const clean = accountNumber.replace(/\s|-/g, '');

    // Format as groups of 4 digits
    return clean.replace(/(\d{4})(?=\d)/g, '$1-');
  },

  /**
   * Mask account number for security
   * @param {string} accountNumber - Account number to mask
   * @param {number} visibleDigits - Number of digits to show at end
   * @returns {string} Masked account number
   */
  maskAccountNumber: (accountNumber, visibleDigits = 4) => {
    if (!accountNumber) return '';

    const clean = accountNumber.replace(/\s|-/g, '');

    if (clean.length <= visibleDigits) {
      return '*'.repeat(clean.length);
    }

    const masked = '*'.repeat(clean.length - visibleDigits);
    const visible = clean.slice(-visibleDigits);

    return `${masked}${visible}`;
  },

  /**
   * Validate withdrawal amount
   * @param {number} amount - Amount to validate
   * @param {number} balance - Current balance
   * @returns {Object} Validation result
   */
  validateWithdrawalAmount: (amount, balance) => {
    if (!amount || amount <= 0) {
      return { isValid: false, error: 'Amount must be greater than zero' };
    }

    if (amount < bankingConfig.withdrawal.minAmount) {
      return {
        isValid: false,
        error: `Minimum withdrawal amount is ${bankingConfig.withdrawal.minAmount} credits`
      };
    }

    if (amount > bankingConfig.withdrawal.maxAmount) {
      return {
        isValid: false,
        error: `Maximum withdrawal amount is ${bankingConfig.withdrawal.maxAmount} credits`
      };
    }

    if (amount > balance) {
      return {
        isValid: false,
        error: 'Insufficient balance for withdrawal'
      };
    }

    return { isValid: true };
  },

  /**
   * Calculate withdrawal conversion
   * @param {number} credits - Credit amount
   * @param {number} conversionRate - Conversion rate
   * @param {string} currency - Target currency
   * @returns {Object} Conversion details
   */
  calculateConversion: (credits, conversionRate, currency = 'MYR') => {
    const convertedAmount = credits * conversionRate;

    return {
      credits,
      convertedAmount,
      conversionRate,
      currency,
      formatted: new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
      }).format(convertedAmount)
    };
  },

  /**
   * Get bank logo/icon
   * @param {string} bankName - Bank name
   * @returns {string} Bank icon or emoji
   */
  getBankIcon: (bankName) => {
    const icons = {
      'Maybank': '🏦',
      'CIMB Bank': '🏛️',
      'Public Bank': '🏢',
      'RHB Bank': '🏪',
      'Hong Leong Bank': '🏬',
      'AmBank': '🏦',
      'Bank Islam': '🕌',
      'Bank Rakyat': '🏛️',
      'OCBC Bank': '🏢',
      'Standard Chartered': '🏦',
      'HSBC Bank': '🏛️',
      'UOB Bank': '🏢'
    };
    return icons[bankName] || '🏦';
  },

  /**
   * Format account number for display
   * @param {string} accountNumber - Account number
   * @returns {string} Formatted account number
   */
  formatAccountNumber: (accountNumber) => {
    if (!accountNumber) return '';
    return accountNumber.replace(/(\d{4})(?=\d)/g, '$1 ');
  },

  /**
   * Mask account number for security
   * @param {string} accountNumber - Account number
   * @returns {string} Masked account number
   */
  maskAccountNumber: (accountNumber) => {
    if (!accountNumber) return '';
    if (accountNumber.length <= 4) return accountNumber;
    const last4 = accountNumber.slice(-4);
    const masked = '*'.repeat(accountNumber.length - 4);
    return masked + last4;
  },

  /**
   * Validate withdrawal amount
   * @param {number} amount - Amount to validate
   * @param {number} balance - Available balance
   * @returns {Object} Validation result
   */
  validateWithdrawalAmount: (amount, balance) => {
    if (!amount || amount <= 0) {
      return { isValid: false, error: 'Amount must be greater than 0' };
    }

    if (amount < 10) {
      return { isValid: false, error: 'Minimum withdrawal amount is 10 credits' };
    }

    if (amount > balance) {
      return { isValid: false, error: 'Amount exceeds available balance' };
    }

    return { isValid: true };
  },

  /**
   * Calculate currency conversion
   * @param {number} amount - Amount to convert
   * @param {number} rate - Conversion rate
   * @param {string} currency - Target currency
   * @returns {Object} Conversion data
   */
  calculateConversion: (amount, rate, currency) => {
    const convertedAmount = amount * rate;
    return {
      originalAmount: amount,
      convertedAmount,
      conversionRate: rate,
      currency,
      formatted: `${convertedAmount.toFixed(2)} ${currency}`
    };
  },

  /**
   * Get withdrawal status color
   * @param {string} status - Withdrawal status
   * @returns {string} CSS color class
   */
  getWithdrawalStatusColor: (status) => {
    const colors = {
      'pending': 'text-yellow-600',
      'processing': 'text-blue-600',
      'completed': 'text-green-600',
      'failed': 'text-red-600',
      'cancelled': 'text-gray-600'
    };
    return colors[status] || 'text-gray-600';
  },

  /**
   * Get withdrawal status icon
   * @param {string} status - Withdrawal status
   * @returns {string} Icon emoji
   */
  getWithdrawalStatusIcon: (status) => {
    const icons = {
      'pending': '⏳',
      'processing': '🔄',
      'completed': '✅',
      'failed': '❌',
      'cancelled': '🚫'
    };
    return icons[status] || '📄';
  }
};

// Re-export constants
export { bankingConstants } from './constants';
