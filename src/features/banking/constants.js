/**
 * Banking feature constants
 * 
 * This file contains all constants used throughout the banking feature.
 * Separated from index.js to avoid circular dependencies.
 */

export const bankingConstants = {
  // Withdrawal statuses
  WITHDRAWAL_STATUS: {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
  },

  // Account types
  ACCOUNT_TYPES: {
    SAVINGS: 'savings',
    CURRENT: 'current'
  },

  // Supported currencies
  SUPPORTED_CURRENCIES: ['MYR', 'USD', 'SGD'],

  // Error codes
  ERROR_CODES: {
    INVALID_ACCOUNT: 'INVALID_ACCOUNT',
    INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
    WITHDRAWAL_LIMIT_EXCEEDED: 'WITHDRAWAL_LIMIT_EXCEEDED',
    VERIFICATION_REQUIRED: 'VERIFICATION_REQUIRED',
    BANK_ERROR: 'BANK_ERROR'
  },

  // Validation patterns
  PATTERNS: {
    ACCOUNT_NUMBER: /^\d{8,20}$/,
    SWIFT_CODE: /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/
  }
};

export default bankingConstants;
