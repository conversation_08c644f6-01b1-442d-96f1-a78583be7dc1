import { useState, useCallback, useEffect } from 'react';
import bankAccountService from '../../../services/bankAccountService';
import malaysianBankService from '../services/MalaysianBankService';
import { toast } from 'react-hot-toast';

/**
 * Enhanced Bank Accounts Hook
 *
 * Provides comprehensive bank account management with proper error handling,
 * loading states, and optimistic updates aligned with backend API structure.
 */
export const useBankAccountsEnhanced = () => {
  // State management
  const [state, setState] = useState({
    accounts: [],
    malaysianBanks: [],
    loading: {
      accounts: false,
      banks: false,
      operations: {}
    },
    errors: {
      accounts: null,
      banks: null,
      operations: {}
    },
    initialized: false
  });

  // Helper to update loading state
  const setLoading = useCallback((type, isLoading, operationId = null) => {
    setState(prev => ({
      ...prev,
      loading: {
        ...prev.loading,
        [type]: operationId
          ? { ...prev.loading[type], [operationId]: isLoading }
          : isLoading
      }
    }));
  }, []);

  // Helper to update error state
  const setError = useCallback((type, error, operationId = null) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [type]: operationId
          ? { ...prev.errors[type], [operationId]: error }
          : error
      }
    }));
  }, []);

  // Clear specific error
  const clearError = useCallback((type, operationId = null) => {
    setError(type, null, operationId);
  }, [setError]);

  // Load bank accounts
  const loadBankAccounts = useCallback(async () => {
    try {
      setLoading('accounts', true);
      setError('accounts', null);

      const response = await bankAccountService.getBankAccounts();
      let accounts = response.data || [];

      // Enhance accounts with bank information if missing
      if (accounts.length > 0 && state.malaysianBanks.length > 0) {
        accounts = accounts.map(account => {
          if (!account.bank && account.malaysian_bank_id) {
            console.log('🏦 Enhancing account with missing bank data:', account.id);
            const bank = state.malaysianBanks.find(b => b.id === account.malaysian_bank_id);
            if (bank) {
              return {
                ...account,
                bank: {
                  id: bank.id,
                  name: bank.name,
                  code: bank.code,
                  logo_url: bank.logo_url || `/images/banks/${bank.code.toLowerCase()}.png`
                }
              };
            }
          }
          return account;
        });
      }

      setState(prev => ({
        ...prev,
        accounts,
        loading: { ...prev.loading, accounts: false }
      }));

      return accounts;
    } catch (error) {
      console.error('🏦 Error loading bank accounts:', error);
      setError('accounts', error.message || 'Failed to load bank accounts');
      setLoading('accounts', false);
      throw error;
    }
  }, [setLoading, setError, state.malaysianBanks]);

  // Load Malaysian banks
  const loadMalaysianBanks = useCallback(async () => {
    try {
      setLoading('banks', true);
      setError('banks', null);

      const banks = await malaysianBankService.getBanks();

      setState(prev => ({
        ...prev,
        malaysianBanks: banks,
        loading: { ...prev.loading, banks: false }
      }));

      return banks;
    } catch (error) {
      console.error('Error loading Malaysian banks:', error);
      setError('banks', error.message || 'Failed to load banks');
      setLoading('banks', false);
      throw error;
    }
  }, [setLoading, setError]);

  // Add bank account
  const addBankAccount = useCallback(async (accountData) => {
    const operationId = 'add';
    try {
      setLoading('operations', true, operationId);
      setError('operations', null, operationId);

      const response = await bankAccountService.addBankAccount(accountData);

      // Extract account data from response
      let newAccount = response.data?.bank_account || response.data;

      // Validate and enhance account data with bank information
      if (newAccount && !newAccount.bank && newAccount.malaysian_bank_id) {

        // Find the bank from our loaded Malaysian banks
        const bank = state.malaysianBanks.find(b => b.id === newAccount.malaysian_bank_id);
        if (bank) {
          newAccount = {
            ...newAccount,
            bank: {
              id: bank.id,
              name: bank.name,
              code: bank.code,
              logo_url: bank.logo_url || `/images/banks/${bank.code.toLowerCase()}.png`
            }
          };
        } else {
          console.warn('🏦 Bank not found in malaysianBanks array for ID:', newAccount.malaysian_bank_id);
        }
      }

      // Final validation
      if (!newAccount.bank) {
        console.error('🏦 Account still missing bank information after processing:', newAccount);
        // Try to reload bank accounts to get complete data
        setTimeout(() => {
          loadBankAccounts();
        }, 1000);
      }
      // Optimistic update
      setState(prev => ({
        ...prev,
        accounts: [...prev.accounts, newAccount],
        loading: { ...prev.loading, operations: { ...prev.loading.operations, [operationId]: false } }
      }));

      toast.success(response.data?.message || 'Bank account added successfully');
      return newAccount;
    } catch (error) {
      console.error('🏦 Error adding bank account:', error);
      setError('operations', error.message || 'Failed to add bank account', operationId);
      setLoading('operations', false, operationId);

      // Show validation errors if available
      if (error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        Object.values(validationErrors).flat().forEach(msg => {
          toast.error(msg);
        });
      } else {
        toast.error(error.message || 'Failed to add bank account');
      }

      throw error;
    }
  }, [setLoading, setError, state.malaysianBanks, loadBankAccounts]);

  // Update bank account
  const updateBankAccount = useCallback(async (id, accountData) => {
    const operationId = `update_${id}`;
    try {
      setLoading('operations', true, operationId);
      setError('operations', null, operationId);

      const response = await bankAccountService.updateBankAccount(id, accountData);
      const updatedAccount = response.data?.bank_account || response.data;

      // Optimistic update
      setState(prev => ({
        ...prev,
        accounts: prev.accounts.map(acc =>
          acc.id === id ? { ...acc, ...updatedAccount } : acc
        ),
        loading: { ...prev.loading, operations: { ...prev.loading.operations, [operationId]: false } }
      }));

      toast.success(response.data?.message || 'Bank account updated successfully');
      return updatedAccount;
    } catch (error) {
      console.error('Error updating bank account:', error);
      setError('operations', error.message || 'Failed to update bank account', operationId);
      setLoading('operations', false, operationId);

      // Show validation errors if available
      if (error.response?.data?.errors) {
        const validationErrors = error.response.data.errors;
        Object.values(validationErrors).flat().forEach(msg => {
          toast.error(msg);
        });
      } else {
        toast.error(error.message || 'Failed to update bank account');
      }

      throw error;
    }
  }, [setLoading, setError]);

  // Delete bank account
  const deleteBankAccount = useCallback(async (id) => {
    const operationId = `delete_${id}`;
    try {
      setLoading('operations', true, operationId);
      setError('operations', null, operationId);

      const response = await bankAccountService.deleteBankAccount(id);

      // Optimistic update
      setState(prev => ({
        ...prev,
        accounts: prev.accounts.filter(acc => acc.id !== id),
        loading: { ...prev.loading, operations: { ...prev.loading.operations, [operationId]: false } }
      }));

      toast.success(response.data?.message || 'Bank account deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting bank account:', error);
      setError('operations', error.message || 'Failed to delete bank account', operationId);
      setLoading('operations', false, operationId);
      toast.error(error.message || 'Failed to delete bank account');
      throw error;
    }
  }, [setLoading, setError]);

  // Set primary account
  const setPrimaryAccount = useCallback(async (id) => {
    const operationId = `set_primary_${id}`;
    try {
      setLoading('operations', true, operationId);
      setError('operations', null, operationId);

      const response = await bankAccountService.setPrimaryBankAccount(id);

      // Optimistic update - set all accounts to non-primary, then set the selected one as primary
      setState(prev => ({
        ...prev,
        accounts: prev.accounts.map(acc => ({
          ...acc,
          is_primary: acc.id === id
        })),
        loading: { ...prev.loading, operations: { ...prev.loading.operations, [operationId]: false } }
      }));

      toast.success(response.data?.message || 'Primary account set successfully');
      return true;
    } catch (error) {
      console.error('Error setting primary account:', error);
      setError('operations', error.message || 'Failed to set primary account', operationId);
      setLoading('operations', false, operationId);
      toast.error(error.message || 'Failed to set primary account');
      throw error;
    }
  }, [setLoading, setError]);

  // Initialize data
  const initialize = useCallback(async () => {
    if (state.initialized) return;

    try {
      await Promise.allSettled([
        loadBankAccounts(),
        loadMalaysianBanks()
      ]);

      setState(prev => ({ ...prev, initialized: true }));
    } catch (error) {
      console.error('Error initializing bank accounts:', error);
    }
  }, [state.initialized, loadBankAccounts, loadMalaysianBanks]);

  // Refresh all data
  const refresh = useCallback(async () => {
    try {
      await Promise.allSettled([
        loadBankAccounts(),
        loadMalaysianBanks()
      ]);
    } catch (error) {
      console.error('Error refreshing bank accounts:', error);
    }
  }, [loadBankAccounts, loadMalaysianBanks]);

  // Auto-initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Computed values
  const verifiedAccounts = state.accounts.filter(account => account.is_verified);
  const primaryAccount = state.accounts.find(account => account.is_primary);
  const hasAccounts = state.accounts.length > 0;
  const hasVerifiedAccounts = verifiedAccounts.length > 0;

  // Check if operation is loading
  const isOperationLoading = useCallback((operation, id = null) => {
    const operationId = id ? `${operation}_${id}` : operation;
    return state.loading.operations[operationId] || false;
  }, [state.loading.operations]);

  // Get operation error
  const getOperationError = useCallback((operation, id = null) => {
    const operationId = id ? `${operation}_${id}` : operation;
    return state.errors.operations[operationId] || null;
  }, [state.errors.operations]);

  return {
    // State
    accounts: state.accounts,
    malaysianBanks: state.malaysianBanks,
    loading: state.loading,
    errors: state.errors,
    initialized: state.initialized,

    // Computed values
    verifiedAccounts,
    primaryAccount,
    hasAccounts,
    hasVerifiedAccounts,

    // Actions
    loadBankAccounts,
    loadMalaysianBanks,
    addBankAccount,
    updateBankAccount,
    deleteBankAccount,
    setPrimaryAccount,
    initialize,
    refresh,
    clearError,

    // Helpers
    isOperationLoading,
    getOperationError
  };
};

export default useBankAccountsEnhanced;
