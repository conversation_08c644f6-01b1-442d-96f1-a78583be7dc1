import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSpinner,
  faCheck,
  faExclamationTriangle,
  faTimes,
  faInfoCircle,
  faSearch,
  faCheckCircle,
  faUniversity,
  faShieldAlt,
  faCreditCard,
  faUser,
  faToggleOn,
  faToggleOff,
  faArrowLeft,
  faStar,
  faBuilding
} from '@fortawesome/free-solid-svg-icons';

import { useBankAccountsEnhanced } from '../../hooks/useBankAccountsEnhanced';
import {
  addBankAccountSchema,
  updateBankAccountSchema,
  getAccountNumberPlaceholder,
  supportsDuitNow
} from '../../validation/bankAccountValidation';

/**
 * Enhanced Bank Account Form Component
 *
 * Modern, Malaysia-focused form with improved UI/UX design,
 * better visual hierarchy, and enhanced user experience.
 * Maintains all API connections and functionality.
 */
const BankAccountFormEnhanced = ({
  account = null,
  mode = 'add', // 'add' or 'edit'
  onSuccess,
  onCancel,
  className = '',
  size = 'medium',
  selectedBankType = null // New prop for bank type filtering
}) => {
  const {
    malaysianBanks,
    addBankAccount,
    updateBankAccount,
    isOperationLoading,
    getOperationError,
    clearError
  } = useBankAccountsEnhanced();

  const [selectedBank, setSelectedBank] = useState(null);
  const [bankSearchQuery, setBankSearchQuery] = useState('');
  const [isValidatingAccount, setIsValidatingAccount] = useState(false);

  // Popular Malaysian banks (for quick selection)
  const popularBanks = [
    { name: 'Maybank', code: 'MBB', type: 'conventional' },
    { name: 'CIMB Bank', code: 'CIMB', type: 'conventional' },
    { name: 'Public Bank', code: 'PBB', type: 'conventional' },
    { name: 'RHB Bank', code: 'RHB', type: 'conventional' },
    { name: 'Bank Islam', code: 'BIMB', type: 'islamic' },
    { name: 'AmBank Islamic', code: 'AMIS', type: 'islamic' },
    { name: 'Boost', code: 'BOOST', type: 'digital' },
    { name: 'Touch n Go', code: 'TNG', type: 'digital' }
  ];

  // Filter banks by selected type
  const getFilteredBanks = () => {
    let banks = malaysianBanks;
    
    if (selectedBankType && selectedBankType !== 'all') {
      // Filter banks based on selected type
      banks = malaysianBanks.filter(bank => {
        switch (selectedBankType) {
          case 'conventional':
            return !bank.name.toLowerCase().includes('islamic') && 
                   !bank.name.toLowerCase().includes('digital') &&
                   !bank.name.toLowerCase().includes('boost') &&
                   !bank.name.toLowerCase().includes('touch n go');
          case 'islamic':
            return bank.name.toLowerCase().includes('islamic') ||
                   bank.name.toLowerCase().includes('bank islam') ||
                   bank.name.toLowerCase().includes('ambank islamic');
          case 'digital':
            return bank.name.toLowerCase().includes('digital') ||
                   bank.name.toLowerCase().includes('boost') ||
                   bank.name.toLowerCase().includes('touch n go') ||
                   bank.name.toLowerCase().includes('grabpay') ||
                   bank.name.toLowerCase().includes('bigpay');
          default:
            return true;
        }
      });
    }
    
    // Apply search filter
    return banks.filter(bank =>
      !bankSearchQuery ||
      bank.name.toLowerCase().includes(bankSearchQuery.toLowerCase()) ||
      bank.code.toLowerCase().includes(bankSearchQuery.toLowerCase())
    );
  };

  // Get initial values
  const getInitialValues = () => {
    if (mode === 'edit' && account) {
      return {
        malaysian_bank_id: account.malaysian_bank_id || '',
        account_number: account.account_number || '',
        account_holder_name: account.account_holder_name || '',
        is_primary: account.is_primary || false
      };
    }

    return {
      malaysian_bank_id: '',
      account_number: '',
      account_holder_name: '',
      is_primary: false
    };
  };

  // Set selected bank when editing
  useEffect(() => {
    if (mode === 'edit' && account && malaysianBanks.length > 0) {
      const bank = malaysianBanks.find(b => b.id === account.malaysian_bank_id);
      setSelectedBank(bank);
    }
  }, [mode, account, malaysianBanks]);

  // Get validation schema based on mode
  const getValidationSchema = () => {
    return mode === 'edit' ? updateBankAccountSchema : addBankAccountSchema;
  };

  // Handle form submission
  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      clearError('operations', mode);

      let result;
      if (mode === 'edit' && account) {
        result = await updateBankAccount(account.id, values);
      } else {
        result = await addBankAccount(values);
      }

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Form submission error:', error);

      // Handle validation errors from backend
      if (error.response?.data?.errors) {
        const backendErrors = error.response.data.errors;
        Object.keys(backendErrors).forEach(field => {
          const messages = Array.isArray(backendErrors[field])
            ? backendErrors[field]
            : [backendErrors[field]];
          setFieldError(field, messages[0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle bank selection
  const handleBankSelect = (bankId, setFieldValue) => {
    const bank = malaysianBanks.find(b => b.id === parseInt(bankId));
    setSelectedBank(bank);
    setFieldValue('malaysian_bank_id', bankId);

    // Clear account number when bank changes
    setFieldValue('account_number', '');
  };

  // Real-time account number validation
  const validateAccountNumber = async (accountNumber, setFieldError) => {
    if (!accountNumber || !selectedBank) return;

    setIsValidatingAccount(true);

    try {
      // Add your real-time validation logic here
      // This would typically call your validation service
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      // Clear any previous errors if validation passes
      setFieldError('account_number', undefined);
    } catch (error) {
      setFieldError('account_number', 'Invalid account number format');
    } finally {
      setIsValidatingAccount(false);
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        input: 'px-3 py-2 text-sm',
        button: 'px-4 py-2 text-sm',
        label: 'text-sm',
        section: 'space-y-4'
      },
      medium: {
        container: 'p-6',
        input: 'px-4 py-3 text-base',
        button: 'px-6 py-3 text-base',
        label: 'text-base',
        section: 'space-y-6'
      },
      large: {
        container: 'p-8',
        input: 'px-6 py-4 text-lg',
        button: 'px-8 py-4 text-lg',
        label: 'text-lg',
        section: 'space-y-8'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();
  const isLoading = isOperationLoading(mode, account?.id);
  const operationError = getOperationError(mode, account?.id);
  const filteredBanks = getFilteredBanks();

  return (
    <div className={`bank-account-form-enhanced ${className}`}>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={getValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, errors, touched, setFieldValue, setFieldError, isSubmitting }) => (
          <Form className={`space-y-8 ${sizeClasses.section}`}>
            {/* Malaysia-Specific Header */}
            {selectedBankType && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200/60 rounded-2xl p-6 shadow-sm backdrop-blur-sm"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                    <FontAwesomeIcon icon={faUniversity} className="text-blue-600 text-xl" />
                  </div>
                  <div className="flex-1">
                    <p className="font-semibold text-gray-900 text-lg">
                      {selectedBankType === 'conventional' && 'Conventional Banks'}
                      {selectedBankType === 'islamic' && 'Islamic Banks'}
                      {selectedBankType === 'digital' && 'Digital Banks'}
                    </p>
                    <p className="text-gray-600 text-sm mt-1">
                      {selectedBankType === 'conventional' && 'Traditional Malaysian banks with full banking services'}
                      {selectedBankType === 'islamic' && 'Shariah-compliant banking following Islamic principles'}
                      {selectedBankType === 'digital' && 'Modern digital-first banking solutions'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-green-500" />
                    <span className="text-sm text-green-600 font-medium">BNM Regulated</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Loading Overlay */}
            {(isSubmitting || isLoading) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute inset-0 bg-white/90 backdrop-blur-sm rounded-3xl flex items-center justify-center z-20"
              >
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-3xl flex items-center justify-center mb-6 mx-auto shadow-lg">
                    <FontAwesomeIcon icon={faSpinner} className="text-white text-3xl animate-spin" />
                  </div>
                  <p className="text-gray-900 font-semibold text-xl mb-2">
                    {mode === 'edit' ? 'Updating Account...' : 'Adding Account...'}
                  </p>
                  <p className="text-gray-600 text-base">
                    Please wait while we process your request securely
                  </p>
                </div>
              </motion.div>
            )}

            {/* Operation Error */}
            {operationError && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-2xl p-6 shadow-sm"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-xl bg-red-100 flex items-center justify-center flex-shrink-0">
                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                  </div>
                  <div>
                    <p className="text-red-800 font-semibold text-lg mb-1">Operation Failed</p>
                    <p className="text-red-600">{operationError}</p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Section 1: Bank Selection */}
            <section className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900 flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
                    <FontAwesomeIcon icon={faBuilding} className="text-blue-600" />
                  </div>
                  <span>Select Your Bank</span>
                </h3>
              </div>

              {/* Popular Banks Quick Selection */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/60">
                <h4 className="font-semibold text-blue-900 mb-4 flex items-center space-x-2">
                  <FontAwesomeIcon icon={faStar} className="text-yellow-500" />
                  <span>Popular Malaysian Banks</span>
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {popularBanks.map((bank) => {
                    const matchingBank = malaysianBanks.find(b => 
                      b.name.toLowerCase().includes(bank.name.toLowerCase()) ||
                      b.code === bank.code
                    );
                    
                    if (!matchingBank) return null;

                    return (
                      <button
                        key={bank.code}
                        type="button"
                        onClick={() => handleBankSelect(matchingBank.id, setFieldValue)}
                        className={`p-3 rounded-xl text-left transition-all duration-200 ${
                          values.malaysian_bank_id === matchingBank.id
                            ? 'bg-blue-500 text-white shadow-lg'
                            : 'bg-white hover:bg-blue-50 border border-blue-200/60'
                        }`}
                      >
                        <div className="font-medium text-sm">{bank.name}</div>
                        <div className={`text-xs ${values.malaysian_bank_id === matchingBank.id ? 'text-blue-100' : 'text-gray-500'}`}>
                          {bank.code}
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Bank Search and Selection */}
              <div className="space-y-4">
                <label className={`block font-semibold text-gray-900 ${sizeClasses.label}`}>
                Malaysian Bank *
              </label>

              {/* Bank Search */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
                </div>
                <input
                  type="text"
                    placeholder="Search for your bank..."
                  value={bankSearchQuery}
                  onChange={(e) => setBankSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-lg"
                />
              </div>

              {/* Bank Selection */}
              <Field name="malaysian_bank_id">
                {({ field }) => (
                  <select
                    {...field}
                    onChange={(e) => handleBankSelect(e.target.value, setFieldValue)}
                    className={`
                        w-full border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                        ${sizeClasses.input} transition-all duration-200 text-lg
                        ${errors.malaysian_bank_id && touched.malaysian_bank_id ? 'border-red-300 ring-red-200' : ''}
                    `}
                  >
                      <option value="">Select your bank from the list</option>
                    {filteredBanks.map((bank) => (
                      <option key={bank.id} value={bank.id}>
                        {bank.name} ({bank.code})
                      </option>
                    ))}
                  </select>
                )}
              </Field>

                <ErrorMessage name="malaysian_bank_id" component="div" className="text-red-600 text-sm flex items-center space-x-2">
                  <FontAwesomeIcon icon={faExclamationTriangle} />
                <ErrorMessage name="malaysian_bank_id" />
              </ErrorMessage>

              {/* Selected Bank Info */}
              {selectedBank && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                    className="bg-green-50 border-2 border-green-200 rounded-2xl p-6"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                        <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-xl" />
                    </div>
                      <div className="flex-1">
                        <p className="font-bold text-green-900 text-lg">{selectedBank.name}</p>
                        <p className="text-green-700 text-sm mb-2">
                          Bank Code: {selectedBank.code} • Format: {selectedBank.account_number_format}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                        {supportsDuitNow(selectedBank.code) && (
                            <span className="flex items-center space-x-1 text-blue-600">
                              <FontAwesomeIcon icon={faCreditCard} />
                              <span>DuitNow Supported</span>
                            </span>
                          )}
                          <span className="flex items-center space-x-1 text-green-600">
                            <FontAwesomeIcon icon={faShieldAlt} />
                            <span>BNM Regulated</span>
                          </span>
                        </div>
                      </div>
                  </div>
                </motion.div>
              )}
            </div>
            </section>

            {/* Section 2: Account Details */}
            <section className="space-y-6 max-x-5xl">
              <h3 className="text-xl font-bold text-gray-900 flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center">
                  <FontAwesomeIcon icon={faCreditCard} className="text-green-600" />
                </div>
                <span>Account Details</span>
              </h3>

            {/* Account Number */}
              <div className="space-y-4">
                <label className={`block font-semibold text-gray-900 ${sizeClasses.label}`}>
                Account Number *
              </label>
              <div className="relative">
                <Field name="account_number">
                  {({ field }) => (
                    <input
                      {...field}
                      type="text"
                      placeholder={selectedBank ? getAccountNumberPlaceholder(selectedBank.code) : "Select a bank first"}
                      disabled={!selectedBank}
                      onChange={(e) => {
                        field.onChange(e);
                        validateAccountNumber(e.target.value, setFieldError);
                      }}
                      className={`
                          w-full border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-green-500
                          ${sizeClasses.input} transition-all duration-200 text-lg
                          ${errors.account_number && touched.account_number ? 'border-red-300 ring-red-200' : ''}
                        ${!selectedBank ? 'bg-gray-100 cursor-not-allowed' : ''}
                      `}
                    />
                  )}
                </Field>
                  
                  {/* Validation Status */}
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                {isValidatingAccount && (
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 text-xl" />
                    )}
                    {!isValidatingAccount && values.account_number && !errors.account_number && (
                      <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 text-xl" />
                    )}
                    {!isValidatingAccount && errors.account_number && touched.account_number && (
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-xl" />
                    )}
                  </div>
              </div>

                <ErrorMessage name="account_number" component="div" className="text-red-600 text-sm flex items-center space-x-2">
                  <FontAwesomeIcon icon={faExclamationTriangle} />
                <ErrorMessage name="account_number" />
              </ErrorMessage>

              {selectedBank && (
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <div className="flex items-start space-x-3">
                      <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mt-1" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">Account Number Format:</p>
                        <p className="text-blue-700">{selectedBank.account_number_format}</p>
                  {supportsDuitNow(selectedBank.code) && (
                          <p className="text-blue-700 mt-1">
                            <strong>DuitNow:</strong> Start with 'D' followed by your account number
                </p>
                        )}
                      </div>
                    </div>
                  </div>
              )}
            </div>

            {/* Account Holder Name */}
              <div className="space-y-4">
                <label className={`block font-semibold text-gray-900 ${sizeClasses.label}`}>
                Account Holder Name *
              </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <FontAwesomeIcon icon={faUser} className="text-gray-400" />
                  </div>
              <Field name="account_holder_name">
                {({ field }) => (
                  <input
                    {...field}
                    type="text"
                        placeholder="Enter account holder name (as per IC)"
                    className={`
                          w-full pl-12 pr-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-green-500
                          ${sizeClasses.input} transition-all duration-200 text-lg
                          ${errors.account_holder_name && touched.account_holder_name ? 'border-red-300 ring-red-200' : ''}
                    `}
                  />
                )}
              </Field>
                </div>

                <ErrorMessage name="account_holder_name" component="div" className="text-red-600 text-sm flex items-center space-x-2">
                  <FontAwesomeIcon icon={faExclamationTriangle} />
                <ErrorMessage name="account_holder_name" />
              </ErrorMessage>

                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                  <div className="flex items-start space-x-3">
                    <FontAwesomeIcon icon={faInfoCircle} className="text-yellow-600 mt-1" />
                    <div className="text-sm text-yellow-800">
                      <p className="font-medium">Important:</p>
                      <p>Account holder name must match your Malaysian IC (Identity Card) exactly.</p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Section 3: Account Preferences */}
            <section className="space-y-6">
              <h3 className="text-xl font-bold text-gray-900 flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center">
                  <FontAwesomeIcon icon={faToggleOn} className="text-purple-600" />
            </div>
                <span>Account Preferences</span>
              </h3>

              {/* Primary Account Toggle */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200/60">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Field name="is_primary" type="checkbox" className="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" />
                      <span className="font-semibold text-gray-900 text-lg">
                        Set as Primary Account
                </span>
                    </div>
                    <p className="text-gray-600 text-sm ml-8">
                      Your primary account will be selected by default for all withdrawals and transactions.
                      You can change this setting anytime.
              </p>
            </div>
                  <div className="ml-4">
                    <FontAwesomeIcon 
                      icon={values.is_primary ? faToggleOn : faToggleOff} 
                      className={`text-3xl ${values.is_primary ? 'text-purple-600' : 'text-gray-400'}`} 
                    />
                  </div>
                </div>
              </div>
            </section>

            {/* Sticky Action Buttons */}
            <div className="sticky bottom-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 -mx-8 px-8 py-6 mt-8">
              <div className="flex space-x-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isSubmitting || isLoading}
                className={`
                    flex-1 flex items-center justify-center space-x-3
                    bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl font-semibold
                  hover:from-green-600 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed
                    transition-all duration-200 shadow-lg hover:shadow-xl
                    ${sizeClasses.button}
                `}
              >
                {isSubmitting || isLoading ? (
                  <>
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin text-xl" />
                      <span className="text-lg">{mode === 'edit' ? 'Updating...' : 'Adding...'}</span>
                  </>
                ) : (
                  <>
                      <FontAwesomeIcon icon={faCheck} className="text-xl" />
                      <span className="text-lg">{mode === 'edit' ? 'Update Account' : 'Add Account'}</span>
                  </>
                )}
              </motion.button>

              {onCancel && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={onCancel}
                  disabled={isSubmitting || isLoading}
                  className={`
                      flex items-center justify-center space-x-2
                      border-2 border-gray-300 text-gray-700 rounded-2xl font-semibold
                      hover:bg-gray-50 disabled:opacity-50 transition-all duration-200
                    ${sizeClasses.button}
                  `}
                >
                    <FontAwesomeIcon icon={faTimes} />
                    <span>Cancel</span>
                </motion.button>
              )}
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BankAccountFormEnhanced;
