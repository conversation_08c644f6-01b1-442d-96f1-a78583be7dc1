import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUniversity,
  faCheckCircle,
  faExclamationTriangle,
  faCrown,
  faShieldAlt,
  faEdit,
  faTrash,
  faStar,
  faEye,
  faEyeSlash,
  faCopy,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import { getBankAccountStatus, formatBankAccountForDisplay } from '../../utils/bankingUtils';
import { WALLET_COLORS, GLASS_EFFECTS, createGlassCSS } from '../../../wallet/components/common/WalletTheme';

/**
 * Enhanced Bank Account Card Component
 * 
 * Premium bank account display card with pearl white/royal blue theme,
 * glass-morphism effects, verification status, and interactive elements.
 */
const BankAccountCard = ({
  account,
  isSelected = false,
  showActions = true,
  showVerificationStatus = true,
  showPrimaryIndicator = true,
  showAccountNumber = true,
  variant = 'default', // 'default', 'compact', 'detailed'
  size = 'medium',
  onClick,
  onEdit,
  onDelete,
  onSetPrimary,
  onCopyAccount,
  className = ''
}) => {
  const [showFullAccount, setShowFullAccount] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  // Format account for display
  const formattedAccount = formatBankAccountForDisplay(account);
  const status = getBankAccountStatus(account);

  // Handle copy account number
  const handleCopyAccount = async () => {
    if (onCopyAccount) {
      onCopyAccount(account.account_number);
    } else {
      try {
        await navigator.clipboard.writeText(account.account_number);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch (error) {
        console.error('Failed to copy account number:', error);
      }
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        icon: 'w-8 h-8 text-sm',
        title: 'text-sm font-semibold',
        subtitle: 'text-xs',
        badge: 'text-xs px-2 py-1',
        button: 'p-1.5 text-xs'
      },
      medium: {
        container: 'p-6',
        icon: 'w-12 h-12 text-lg',
        title: 'text-base font-semibold',
        subtitle: 'text-sm',
        badge: 'text-xs px-2 py-1',
        button: 'p-2 text-sm'
      },
      large: {
        container: 'p-8',
        icon: 'w-16 h-16 text-xl',
        title: 'text-lg font-bold',
        subtitle: 'text-base',
        badge: 'text-sm px-3 py-1.5',
        button: 'p-3 text-base'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  // Get variant classes
  const getVariantClasses = () => {
    const variants = {
      default: 'rounded-2xl',
      compact: 'rounded-xl',
      detailed: 'rounded-3xl'
    };
    return variants[variant] || variants.default;
  };

  const variantClasses = getVariantClasses();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ 
        y: -4, 
        boxShadow: '0 20px 40px rgba(99, 102, 241, 0.15)' 
      }}
      transition={{ duration: 0.3 }}
      onClick={onClick}
      className={`
        relative overflow-hidden transition-all duration-300
        ${variantClasses} ${sizeClasses.container}
        ${onClick ? 'cursor-pointer' : ''}
        ${isSelected 
          ? 'ring-2 ring-blue-500 bg-blue-50/80 dark:bg-blue-900/80 border-blue-200 dark:border-blue-700' 
          : 'bg-white/80 dark:bg-gray-900/80 border-white/30 dark:border-gray-700 hover:bg-white/90 dark:hover:bg-gray-800/90'
        }
        backdrop-blur-sm border shadow-xl
        ${className}
      `}
      style={createGlassCSS('medium')}
    >
      {/* Background Decorations */}
      <div className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-gradient-to-br from-blue-500/10 to-indigo-500/10 blur-xl" />
      <div className="absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500/10 to-purple-500/10 blur-lg" />

      {/* Primary Badge */}
      {showPrimaryIndicator && account.is_primary && (
        <div className="absolute -top-2 -right-2 z-10">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full px-3 py-1 shadow-lg"
          >
            <FontAwesomeIcon icon={faCrown} className="mr-1 text-xs" />
            <span className="text-xs font-bold">Primary</span>
          </motion.div>
        </div>
      )}

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-4">
            {/* Bank Icon */}
            <div className={`
              ${sizeClasses.icon} rounded-xl flex items-center justify-center
              bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg
            `}>
              <FontAwesomeIcon icon={faUniversity} className="text-white" />
            </div>

            {/* Bank Info */}
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h3 className={`${sizeClasses.title} text-gray-900 dark:text-gray-100`}>
                  {formattedAccount.bankName}
                </h3>
                
                {/* Verification Status */}
                {showVerificationStatus && (
                  <div
                    className={
                      `flex items-center space-x-1 rounded-full ${sizeClasses.badge} ` +
                      (status.status === 'verified'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : status.status === 'pending'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200')
                    }
                  >
                    <FontAwesomeIcon 
                      icon={status.status === 'verified' ? faCheckCircle : faExclamationTriangle} 
                      className="text-xs" 
                    />
                    <span>{status.message}</span>
                  </div>
                )}
              </div>

              <p className={`${sizeClasses.subtitle} text-gray-600 dark:text-gray-300`}>
                {account.account_holder_name}
              </p>
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex items-center space-x-1">
              {!account.is_primary && onSetPrimary && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onSetPrimary(account.id);
                  }}
                  className={
                    `${sizeClasses.button} text-gray-400 hover:text-yellow-500 transition-colors rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900`
                  }
                  title="Set as primary"
                >
                  <FontAwesomeIcon icon={faStar} />
                </motion.button>
              )}

              {onEdit && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(account);
                  }}
                  className={
                    `${sizeClasses.button} text-gray-400 hover:text-blue-500 transition-colors rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900`
                  }
                  title="Edit account"
                >
                  <FontAwesomeIcon icon={faEdit} />
                </motion.button>
              )}

              {onDelete && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(account.id);
                  }}
                  className={
                    `${sizeClasses.button} text-gray-400 hover:text-red-500 transition-colors rounded-lg hover:bg-red-50 dark:hover:bg-red-900`
                  }
                  title="Delete account"
                >
                  <FontAwesomeIcon icon={faTrash} />
                </motion.button>
              )}
            </div>
          )}
        </div>

        {/* Account Number */}
        {showAccountNumber && (
          <div className="bg-gray-50/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl p-4 mb-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 mb-1">Account Number</p>
                <div className="flex items-center space-x-2">
                  <p className={`${sizeClasses.subtitle} font-mono text-gray-900 dark:text-gray-100`}>
                    {showFullAccount ? account.account_number : formattedAccount.maskedAccountNumber}
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowFullAccount(!showFullAccount);
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                    title={showFullAccount ? "Hide account number" : "Show account number"}
                  >
                    <FontAwesomeIcon icon={showFullAccount ? faEyeSlash : faEye} className="text-xs" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyAccount();
                    }}
                    className="p-1 text-gray-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors"
                    title="Copy account number"
                  >
                    <FontAwesomeIcon 
                      icon={copySuccess ? faCheck : faCopy} 
                      className={`text-xs ${copySuccess ? 'text-green-500 dark:text-green-300' : ''}`} 
                    />
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Additional Details for Detailed Variant */}
        {variant === 'detailed' && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 mb-1">Bank Code</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">{formattedAccount.bankCode}</p>
            </div>
            <div>
              <p className="text-gray-500 mb-1">Account Type</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">Savings</p>
            </div>
            {account.bank?.swift_code && (
              <div className="col-span-2">
                <p className="text-gray-500 mb-1">SWIFT Code</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">{account.bank.swift_code}</p>
              </div>
            )}
          </div>
        )}

        {/* Copy Success Message */}
        {copySuccess && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-4 right-4 bg-green-500 text-white px-3 py-1 rounded-lg text-xs"
          >
            Account number copied!
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default BankAccountCard;
