import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import bankAccountAPI from '../../../services/bankAccountService';
import currencyService from '../../../services/currencyService';
import verificationService from '../../../services/verificationService';
import malaysianBankService from '../services/MalaysianBankService';
import errorHandlingService from '../../../services/errorHandlingService';
import { formatBankAccountForDisplay } from '../utils/bankingUtils';
import { bankingConstants } from '../constants';

// Create the context
const BankAccountContext = createContext();

// Action types
const BANK_ACCOUNT_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_ACCOUNTS: 'SET_ACCOUNTS',
  ADD_ACCOUNT: 'ADD_ACCOUNT',
  UPDATE_ACCOUNT: 'UPDATE_ACCOUNT',
  DELETE_ACCOUNT: 'DELETE_ACCOUNT',
  SET_PRIMARY_ACCOUNT: 'SET_PRIMARY_ACCOUNT',
  SET_MALAYSIAN_BANKS: 'SET_MALAYSIAN_BANKS',
  SET_CURRENCIES: 'SET_CURRENCIES',
  SET_VERIFICATION_STATUS: 'SET_VERIFICATION_STATUS',
  SET_WITHDRAWAL_HISTORY: 'SET_WITHDRAWAL_HISTORY',
  ADD_WITHDRAWAL: 'ADD_WITHDRAWAL',
  UPDATE_WITHDRAWAL: 'UPDATE_WITHDRAWAL',
  SET_INITIALIZED: 'SET_INITIALIZED',
  CLEAR_ERROR: 'CLEAR_ERROR',
  RESET_STATE: 'RESET_STATE'
};

// Initial state
const initialState = {
  // Bank accounts state
  accounts: [],
  accountsLoading: false,
  accountsError: null,
  primaryAccount: null,
  lastAccountsUpdate: null,

  // Malaysian banks state
  malaysianBanks: [],
  malaysianBanksLoading: false,
  malaysianBanksError: null,

  // Currencies state
  currencies: [],
  currenciesLoading: false,
  currenciesError: null,

  // Verification state
  verificationStatus: {
    email_verified: false,
    kyc_verified: false,
    phone_verified: false
  },
  verificationLoading: false,
  verificationError: null,

  // Withdrawal state
  withdrawals: [],
  withdrawalsLoading: false,
  withdrawalsError: null,
  lastWithdrawalsUpdate: null,

  // Form state
  formLoading: false,
  formError: null,

  // Global state
  isInitialized: false,
  globalError: null
};

// Reducer function
const bankAccountReducer = (state, action) => {
  switch (action.type) {
    case BANK_ACCOUNT_ACTIONS.SET_LOADING:
      return {
        ...state,
        [`${action.payload.type}Loading`]: action.payload.loading
      };

    case BANK_ACCOUNT_ACTIONS.SET_ERROR:
      return {
        ...state,
        [`${action.payload.type}Error`]: action.payload.error,
        [`${action.payload.type}Loading`]: false
      };

    case BANK_ACCOUNT_ACTIONS.SET_ACCOUNTS:
      const primaryAccount = action.payload.accounts.find(acc => acc.is_primary) || null;
      return {
        ...state,
        accounts: action.payload.accounts,
        primaryAccount,
        accountsLoading: false,
        accountsError: null,
        lastAccountsUpdate: Date.now()
      };

    case BANK_ACCOUNT_ACTIONS.ADD_ACCOUNT:
      const newAccounts = [...state.accounts, action.payload.account];
      const newPrimary = action.payload.account.is_primary ? action.payload.account : state.primaryAccount;
      return {
        ...state,
        accounts: newAccounts,
        primaryAccount: newPrimary
      };

    case BANK_ACCOUNT_ACTIONS.UPDATE_ACCOUNT:
      const updatedAccounts = state.accounts.map(acc =>
        acc.id === action.payload.accountId
          ? { ...acc, ...action.payload.updates }
          : acc
      );
      const updatedPrimary = updatedAccounts.find(acc => acc.is_primary) || null;
      return {
        ...state,
        accounts: updatedAccounts,
        primaryAccount: updatedPrimary
      };

    case BANK_ACCOUNT_ACTIONS.DELETE_ACCOUNT:
      const filteredAccounts = state.accounts.filter(acc => acc.id !== action.payload.accountId);
      const remainingPrimary = filteredAccounts.find(acc => acc.is_primary) || null;
      return {
        ...state,
        accounts: filteredAccounts,
        primaryAccount: remainingPrimary
      };

    case BANK_ACCOUNT_ACTIONS.SET_PRIMARY_ACCOUNT:
      const accountsWithNewPrimary = state.accounts.map(acc => ({
        ...acc,
        is_primary: acc.id === action.payload.accountId
      }));
      const newPrimaryAccount = accountsWithNewPrimary.find(acc => acc.is_primary);
      return {
        ...state,
        accounts: accountsWithNewPrimary,
        primaryAccount: newPrimaryAccount
      };

    case BANK_ACCOUNT_ACTIONS.SET_MALAYSIAN_BANKS:
      return {
        ...state,
        malaysianBanks: action.payload.banks,
        malaysianBanksLoading: false,
        malaysianBanksError: null
      };

    case BANK_ACCOUNT_ACTIONS.SET_CURRENCIES:
      return {
        ...state,
        currencies: action.payload.currencies,
        currenciesLoading: false,
        currenciesError: null
      };

    case BANK_ACCOUNT_ACTIONS.SET_VERIFICATION_STATUS:
      return {
        ...state,
        verificationStatus: action.payload.status,
        verificationLoading: false,
        verificationError: null
      };

    case BANK_ACCOUNT_ACTIONS.SET_WITHDRAWAL_HISTORY:
      return {
        ...state,
        withdrawals: action.payload.replace ? action.payload.withdrawals :
                    [...state.withdrawals, ...action.payload.withdrawals],
        withdrawalsLoading: false,
        withdrawalsError: null,
        lastWithdrawalsUpdate: Date.now()
      };

    case BANK_ACCOUNT_ACTIONS.ADD_WITHDRAWAL:
      return {
        ...state,
        withdrawals: [action.payload.withdrawal, ...state.withdrawals]
      };

    case BANK_ACCOUNT_ACTIONS.UPDATE_WITHDRAWAL:
      return {
        ...state,
        withdrawals: state.withdrawals.map(withdrawal =>
          withdrawal.id === action.payload.withdrawalId
            ? { ...withdrawal, ...action.payload.updates }
            : withdrawal
        )
      };

    case BANK_ACCOUNT_ACTIONS.SET_INITIALIZED:
      return {
        ...state,
        isInitialized: action.payload.initialized
      };

    case BANK_ACCOUNT_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        [`${action.payload.type}Error`]: null
      };

    case BANK_ACCOUNT_ACTIONS.RESET_STATE:
      return {
        ...initialState,
        isInitialized: false
      };

    default:
      return state;
  }
};

/**
 * BankAccountProvider component
 *
 * Provides global bank account and withdrawal state management including
 * account CRUD operations, verification status, withdrawal processing,
 * and currency management.
 */
export const BankAccountProvider = ({ children }) => {
  const [state, dispatch] = useReducer(bankAccountReducer, initialState);

  /**
   * Load user bank accounts with enhanced error handling
   */
  const loadBankAccounts = useCallback(async () => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'accounts', loading: true } });

      const response = await bankAccountAPI.getBankAccounts();
      const accounts = response.data || [];

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ACCOUNTS,
        payload: { accounts }
      });

      return accounts;
    } catch (error) {
      console.error('Error loading bank accounts:', error);

      // Process error through error handling service
      const processedError = errorHandlingService.processError(error, 'bank_accounts', {
        operation: 'loadBankAccounts',
        timestamp: Date.now()
      });

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'accounts', error: processedError.message }
      });

      throw processedError;
    }
  }, []);

  /**
   * Load Malaysian banks with enhanced error handling
   */
  const loadMalaysianBanks = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'malaysianBanks', loading: true } });

      const banks = await malaysianBankService.getBanks(forceRefresh);

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_MALAYSIAN_BANKS,
        payload: { banks }
      });

      return banks;
    } catch (error) {
      console.error('Error loading Malaysian banks:', error);

      // Process error through error handling service
      const processedError = errorHandlingService.processError(error, 'malaysian_banks', {
        operation: 'loadMalaysianBanks',
        timestamp: Date.now()
      });

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'malaysianBanks', error: processedError.message }
      });

      throw processedError;
    }
  }, []);

  /**
   * Add new bank account
   */
  const addBankAccount = useCallback(async (accountData) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: true } });

      const response = await bankAccountAPI.addBankAccount(accountData);
      const newAccount = response.data;

      if (newAccount) {
        dispatch({
          type: BANK_ACCOUNT_ACTIONS.ADD_ACCOUNT,
          payload: { account: newAccount }
        });
      }

      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: false } });

      return newAccount;
    } catch (error) {
      console.error('Error adding bank account:', error);

      // Process error through error handling service
      const processedError = errorHandlingService.processError(error, 'add_bank_account', {
        operation: 'addBankAccount',
        accountData: { ...accountData, account_number: '***masked***' },
        timestamp: Date.now()
      });

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'form', error: processedError.message }
      });

      throw processedError;
    }
  }, []);

  /**
   * Update bank account
   */
  const updateBankAccount = useCallback(async (accountId, updates) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: true } });

      const response = await bankAccountAPI.updateBankAccount(accountId, updates);
      const updatedAccount = response.data?.bank_account;

      if (updatedAccount) {
        dispatch({
          type: BANK_ACCOUNT_ACTIONS.UPDATE_ACCOUNT,
          payload: { accountId, updates: updatedAccount }
        });
      }

      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: false } });

      return updatedAccount;
    } catch (error) {
      console.error('Error updating bank account:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'form', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Delete bank account
   */
  const deleteBankAccount = useCallback(async (accountId) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: true } });

      await bankAccountAPI.deleteBankAccount(accountId);

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.DELETE_ACCOUNT,
        payload: { accountId }
      });

      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: false } });

      return true;
    } catch (error) {
      console.error('Error deleting bank account:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'form', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Set primary bank account
   */
  const setPrimaryAccount = useCallback(async (accountId) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: true } });

      await bankAccountAPI.setPrimaryBankAccount(accountId);

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_PRIMARY_ACCOUNT,
        payload: { accountId }
      });

      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: false } });

      return true;
    } catch (error) {
      console.error('Error setting primary account:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'form', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load available currencies
   */
  const loadCurrencies = useCallback(async () => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'currencies', loading: true } });

      const response = await currencyService.getWithdrawalCurrencies();
      const currencies = response.data?.currencies || [];

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_CURRENCIES,
        payload: { currencies }
      });

      return currencies;
    } catch (error) {
      console.error('Error loading currencies:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'currencies', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load verification status
   */
  const loadVerificationStatus = useCallback(async () => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'verification', loading: true } });

      const response = await verificationService.getVerificationStatus();
      const status = response.data?.verification_status || {};

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_VERIFICATION_STATUS,
        payload: { status }
      });

      return status;
    } catch (error) {
      console.error('Error loading verification status:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'verification', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load withdrawal history
   */
  const loadWithdrawalHistory = useCallback(async (options = {}) => {
    const {
      limit = 20,
      offset = 0,
      replace = false
    } = options;

    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'withdrawals', loading: true } });

      const response = await bankAccountAPI.getWithdrawalHistory({ limit, offset });
      const withdrawals = response.data?.withdrawals || [];

      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_WITHDRAWAL_HISTORY,
        payload: { withdrawals, replace }
      });

      return withdrawals;
    } catch (error) {
      console.error('Error loading withdrawal history:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'withdrawals', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Process withdrawal
   */
  const processWithdrawal = useCallback(async (withdrawalData) => {
    try {
      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: true } });

      const response = await bankAccountAPI.createWithdrawal(withdrawalData);
      const withdrawal = response.data?.withdrawal;

      if (withdrawal) {
        dispatch({
          type: BANK_ACCOUNT_ACTIONS.ADD_WITHDRAWAL,
          payload: { withdrawal }
        });
      }

      dispatch({ type: BANK_ACCOUNT_ACTIONS.SET_LOADING, payload: { type: 'form', loading: false } });

      return withdrawal;
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'form', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Clear specific error
   */
  const clearError = useCallback((type) => {
    dispatch({
      type: BANK_ACCOUNT_ACTIONS.CLEAR_ERROR,
      payload: { type }
    });
  }, []);

  /**
   * Get formatted accounts for display
   */
  const getFormattedAccounts = useCallback(() => {
    return state.accounts.map(account => formatBankAccountForDisplay(account));
  }, [state.accounts]);

  /**
   * Get Malaysian bank by ID
   */
  const getMalaysianBankById = useCallback((bankId) => {
    return state.malaysianBanks.find(bank => bank.id === bankId) || null;
  }, [state.malaysianBanks]);

  /**
   * Refresh all bank account data
   */
  const refreshBankAccounts = useCallback(async () => {
    try {
      await Promise.allSettled([
        loadBankAccounts(),
        loadMalaysianBanks(),
        loadCurrencies(),
        loadVerificationStatus(),
        loadWithdrawalHistory({ limit: 10, offset: 0, replace: true })
      ]);
    } catch (error) {
      console.error('Error refreshing bank accounts:', error);
    }
  }, [loadBankAccounts, loadMalaysianBanks, loadCurrencies, loadVerificationStatus, loadWithdrawalHistory]);

  /**
   * Get verified accounts only
   */
  const getVerifiedAccounts = useCallback(() => {
    return state.accounts.filter(account => account.is_verified);
  }, [state.accounts]);

  /**
   * Check if user can withdraw
   */
  const canWithdraw = useCallback(() => {
    const hasVerifiedAccount = getVerifiedAccounts().length > 0;
    const isEmailVerified = state.verificationStatus.email_verified;
    const isKycVerified = state.verificationStatus.kyc_verified;

    return hasVerifiedAccount && isEmailVerified && isKycVerified;
  }, [getVerifiedAccounts, state.verificationStatus]);

  /**
   * Initialize bank account data
   */
  const initializeBankAccounts = useCallback(async () => {
    try {
      // Load essential data in parallel
      await Promise.allSettled([
        loadBankAccounts(),
        loadCurrencies(),
        loadVerificationStatus()
      ]);

      // Mark as initialized using dispatch
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_INITIALIZED,
        payload: { initialized: true }
      });
    } catch (error) {
      console.error('Error initializing bank accounts:', error);
      dispatch({
        type: BANK_ACCOUNT_ACTIONS.SET_ERROR,
        payload: { type: 'global', error: 'Failed to initialize bank account data' }
      });
    }
  }, []);

  // Initialize bank account data on mount - DISABLED to prevent infinite loops
  // TODO: Re-enable when banking components are actually used
  // useEffect(() => {
  //   if (!state.isInitialized) {
  //     initializeBankAccounts();
  //   }
  // }, [state.isInitialized, initializeBankAccounts]);

  // Context value
  const value = {
    // State
    ...state,

    // Actions
    loadBankAccounts,
    loadMalaysianBanks,
    addBankAccount,
    updateBankAccount,
    deleteBankAccount,
    setPrimaryAccount,
    loadCurrencies,
    loadVerificationStatus,
    loadWithdrawalHistory,
    processWithdrawal,
    clearError,
    refreshBankAccounts,

    // Computed values
    getFormattedAccounts,
    getMalaysianBankById,
    getVerifiedAccounts,
    canWithdraw,

    // Constants
    constants: bankingConstants
  };

  return (
    <BankAccountContext.Provider value={value}>
      {children}
    </BankAccountContext.Provider>
  );
};

/**
 * Custom hook to use bank account context
 */
export const useBankAccounts = () => {
  const context = useContext(BankAccountContext);
  if (!context) {
    throw new Error('useBankAccounts must be used within a BankAccountProvider');
  }
  return context;
};

export default BankAccountContext;
