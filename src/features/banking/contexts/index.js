/**
 * Banking Contexts Barrel Export
 *
 * This file provides a centralized export point for all banking-related contexts.
 * It includes the BankAccountContext and its associated hooks.
 */

// Context and Provider
export { default as BankAccountContext, BankAccountProvider } from './BankAccountContext';

// Custom hooks
export { useBankAccounts } from './BankAccountContext';

/**
 * Combined provider for all banking contexts
 *
 * This component wraps children with all necessary banking-related contexts.
 * Use this when you need full banking functionality in your component tree.
 *
 * Note: Import BankAccountProvider directly from './BankAccountContext' to avoid circular dependencies
 * Example: import { BankAccountProvider } from './BankAccountContext';
 */

/**
 * Context configuration and utilities
 */
export const bankingContextConfig = {
  // Auto-refresh intervals
  accountRefreshInterval: 60000, // 1 minute
  withdrawalRefreshInterval: 30000, // 30 seconds
  verificationRefreshInterval: 5 * 60 * 1000, // 5 minutes

  // Cache settings
  enableCaching: true,
  cacheTimeout: 10 * 60 * 1000, // 10 minutes

  // Error handling
  maxRetryAttempts: 3,
  retryDelay: 1000, // 1 second

  // UI settings
  showLoadingStates: true,
  showErrorMessages: true,
  autoRefreshOnFocus: true,

  // Validation settings
  enableClientValidation: true,
  validateOnChange: true,
  validateOnBlur: true
};

/**
 * Context utilities for debugging and development
 */
export const bankingContextUtils = {
  /**
   * Get current banking state (for debugging)
   * Note: This should only be used in development
   */
  getBankingState: () => {
    if (process.env.NODE_ENV === 'development') {
      console.warn('getBankingState is only available in development mode');
    }
  },

  /**
   * Reset banking context state
   * Useful for testing or when user logs out
   */
  resetBankingContext: () => {
    console.log('Banking context reset requested');
  },

  /**
   * Validate banking context integration
   * Checks if all required contexts are properly connected
   */
  validateContextIntegration: () => {
    return {
      bankAccountContext: true,
      // Add other context checks as needed
    };
  },

  /**
   * Check user eligibility for banking features
   */
  checkBankingEligibility: (verificationStatus, accounts) => {
    return {
      canAddAccount: verificationStatus.email_verified,
      canWithdraw: verificationStatus.kyc_verified && accounts.some(acc => acc.is_verified),
      canSetPrimary: accounts.length > 1,
      requiresVerification: !verificationStatus.kyc_verified
    };
  },

  /**
   * Format account number for display
   */
  formatAccountNumber: (accountNumber, maskLength = 4) => {
    if (!accountNumber) return '';

    const clean = accountNumber.replace(/\s|-/g, '');
    if (clean.length <= maskLength) {
      return '*'.repeat(clean.length);
    }

    const masked = '*'.repeat(clean.length - maskLength);
    const visible = clean.slice(-maskLength);

    return `${masked}${visible}`;
  },

  /**
   * Validate Malaysian bank account number
   */
  validateMalaysianAccount: (accountNumber, bankName) => {
    if (!accountNumber) {
      return { isValid: false, error: 'Account number is required' };
    }

    const clean = accountNumber.replace(/\s|-/g, '');

    if (clean.length < 8 || clean.length > 20) {
      return { isValid: false, error: 'Account number must be 8-20 digits' };
    }

    if (!/^\d+$/.test(clean)) {
      return { isValid: false, error: 'Account number must contain only digits' };
    }

    // Bank-specific validation could be added here
    return { isValid: true, cleanNumber: clean };
  },

  /**
   * Get withdrawal status display info
   */
  getWithdrawalStatusInfo: (status) => {
    const statusMap = {
      'pending': {
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        icon: '⏳',
        label: 'Pending'
      },
      'processing': {
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        icon: '🔄',
        label: 'Processing'
      },
      'completed': {
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: '✅',
        label: 'Completed'
      },
      'failed': {
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        icon: '❌',
        label: 'Failed'
      },
      'cancelled': {
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        icon: '🚫',
        label: 'Cancelled'
      }
    };

    return statusMap[status] || statusMap['pending'];
  }
};
