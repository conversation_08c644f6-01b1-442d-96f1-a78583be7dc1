@import url('https://api.fontshare.com/v2/css?f[]=anton@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse-light {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
  animation: slideUp 0.5s ease-out;
}

.animate-pulse-light {
  animation: pulse-light 2s infinite;
}

/* Custom styles */
.talent-card {
  transition: all 0.3s ease;
}

.talent-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom Scrollbar Styling */
@layer utilities {
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(79, 70, 229, 0.2) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(79, 70, 229, 0.2);
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(79, 70, 229, 0.4);
    border: 2px solid transparent;
    background-clip: content-box;
  }

  /* Text shadow utility */
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-md {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .font-anton {
    font-family: 'Anton', Arial, Helvetica, sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

body,
html,
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

body {
  background-color: #f9fafb;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 3D Effects and Transformations */
.perspective {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;
}

.active\:scale-98:active {
  transform: scale(0.98);
}

/* Global noise pattern */
.noise {
  position: relative;
}

.noise::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.05;
  z-index: -1;
  pointer-events: none;
}

/* Global animations */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  60% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scale-in {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.4s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out forwards;
}

.animate-pulse-light {
  animation: pulse-light 2s ease-in-out infinite;
}

@keyframes pulse-light {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
}

/* Custom scrollbar styling */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Additional hover/scale utility */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

.active\:scale-95:active {
  transform: scale(0.95);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInFromBottom {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in {
  animation: slideInFromBottom 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out forwards;
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Scale effect on hover and active states */
.scale-on-hover:hover {
  transform: scale(1.03);
  transition: transform 0.2s ease;
}

.scale-on-active:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Blur utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(12px);
}

.blur-xs {
  filter: blur(2px);
}

.blur-sm {
  filter: blur(4px);
}

.blur-md {
  filter: blur(8px);
}

.blur-lg {
  filter: blur(12px);
}

.blur-xl {
  filter: blur(16px);
}

.blur-2xl {
  filter: blur(24px);
}

.blur-3xl {
  filter: blur(64px);
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes underline-expand {
  from { width: 0; }
  to { width: 64px; }
}

.animate-underline-expand {
  animation-name: underline-expand;
}

/* Carousel Shine Effects - Phase 2 */
@keyframes shine-sweep {
  0% { 
    transform: translateX(-100%) skewX(-15deg); 
    opacity: 0;
  }
  10% { 
    opacity: 1;
  }
  50% { 
    transform: translateX(100%) skewX(-15deg); 
    opacity: 0.8;
  }
  100% { 
    transform: translateX(100%) skewX(-15deg); 
    opacity: 0;
  }
}

@keyframes shine-glow {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.6; 
    transform: scale(1.05);
  }
}

@keyframes shine-pulse {
  0%, 100% { 
    opacity: 0.2; 
    transform: scale(1) rotate(0deg);
  }
  25% { 
    opacity: 0.4; 
    transform: scale(1.1) rotate(90deg);
  }
  50% { 
    opacity: 0.6; 
    transform: scale(1.05) rotate(180deg);
  }
  75% { 
    opacity: 0.4; 
    transform: scale(1.1) rotate(270deg);
  }
}

@keyframes shine-sparkle {
  0%, 100% { 
    opacity: 0; 
    transform: scale(0) rotate(0deg);
  }
  50% { 
    opacity: 1; 
    transform: scale(1) rotate(180deg);
  }
}

.animate-shine-sweep {
  animation: shine-sweep 3s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-shine-glow {
  animation: shine-glow 4s ease-in-out infinite;
  animation-delay: 0.5s;
}

.animate-shine-pulse {
  animation: shine-pulse 6s ease-in-out infinite;
  animation-delay: 1.5s;
}

.animate-shine-sparkle {
  animation: shine-sparkle 2s ease-in-out infinite;
}

/* Shine effect utilities */
.shine-sweep {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.shine-glow {
  background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, transparent 50%);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.shine-pulse {
  background: radial-gradient(circle at 70% 70%, rgba(255,255,255,0.2) 0%, transparent 60%);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.shine-sparkle {
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  pointer-events: none;
  z-index: 15;
}

@keyframes halo-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-halo-spin {
  animation: halo-spin 6s linear infinite;
}

@keyframes wave-bar {
  0%, 100% { transform: scaleY(1); opacity: 0.7; }
  50% { transform: scaleY(1.7); opacity: 1; }
}

.wave-bar-svg {
  transform-box: fill-box;
  transform-origin: 22px 22px;
}
