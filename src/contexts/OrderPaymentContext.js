import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import orderPaymentService from '../services/orderPaymentService';
import { usePayment } from './PaymentContext';

// Create the context
const OrderPaymentContext = createContext();

/**
 * OrderPaymentProvider component
 * 
 * This provider manages order payment state across the application.
 * It handles order payment processing, status tracking, and payment history.
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const OrderPaymentProvider = ({ children }) => {
  const navigate = useNavigate();
  const { startPayment, retryPayment } = usePayment();
  
  // Order payment state
  const [orderPaymentState, setOrderPaymentState] = useState({
    isProcessing: false,
    orderId: null,
    orderType: null, // 'talent_order', 'mission_order'
    amount: null,
    transactionId: null,
    error: null,
    status: null, // 'pending', 'success', 'failed'
    paymentHistory: []
  });
  
  // Initialize order payment state from localStorage on mount
  useEffect(() => {
    const storedOrderId = localStorage.getItem('orderPaymentOrderId');
    const storedOrderType = localStorage.getItem('orderPaymentOrderType');
    const storedAmount = localStorage.getItem('orderPaymentAmount');
    const pendingPaymentId = localStorage.getItem('pendingPaymentId');
    
    if (storedOrderId || pendingPaymentId) {
      setOrderPaymentState({
        isProcessing: !!pendingPaymentId,
        orderId: storedOrderId,
        orderType: storedOrderType || 'talent_order',
        amount: storedAmount ? parseFloat(storedAmount) : null,
        transactionId: pendingPaymentId,
        error: null,
        status: pendingPaymentId ? 'pending' : null,
        paymentHistory: []
      });
    }
  }, []);
  
  /**
   * Process payment for an order
   * @param {Object} orderDetails - Order details
   * @param {number} orderDetails.orderId - The order ID
   * @param {string} orderDetails.orderType - The order type ('talent_order', 'mission_order')
   * @param {number} orderDetails.amount - The order amount
   * @param {Function} orderDetails.onSuccess - Callback on success
   * @param {Function} orderDetails.onError - Callback on error
   * @param {Function} orderDetails.onInsufficientBalance - Callback on insufficient balance
   */
  const processOrderPayment = useCallback(async (orderDetails) => {
    const { 
      orderId, 
      orderType = 'talent_order', 
      amount,
      onSuccess,
      onError,
      onInsufficientBalance
    } = orderDetails;
    
    // Store order payment context in localStorage
    localStorage.setItem('orderPaymentOrderId', orderId);
    localStorage.setItem('orderPaymentOrderType', orderType);
    localStorage.setItem('orderPaymentAmount', amount.toString());
    
    setOrderPaymentState({
      isProcessing: true,
      orderId,
      orderType,
      amount,
      transactionId: null,
      error: null,
      status: 'pending',
      paymentHistory: []
    });
    
    try {
      // Check if user has sufficient balance
      const balanceCheck = await orderPaymentService.checkSufficientBalance(amount);
      
      if (!balanceCheck.sufficient) {
        setOrderPaymentState(prev => ({
          ...prev,
          isProcessing: false,
          error: `Insufficient balance. Required: ${amount}, Available: ${balanceCheck.balance}`,
          status: 'failed'
        }));
        
        if (onInsufficientBalance) {
          onInsufficientBalance(balanceCheck);
        }
        
        return;
      }
      
      // Process the payment based on order type
      let response;
      if (orderType === 'mission_order') {
        response = await orderPaymentService.processMissionOrderPayment(orderId, amount);
      } else {
        response = await orderPaymentService.processOrderPayment(orderId, amount);
      }
      
      setOrderPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        transactionId: response.transaction_id,
        status: 'success'
      }));
      
      if (onSuccess) {
        onSuccess(response);
      }
    } catch (error) {
      console.error('Error processing order payment:', error);
      
      setOrderPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message || 'Failed to process order payment',
        status: 'failed'
      }));
      
      if (onError) {
        onError(error);
      }
    }
  }, []);
  
  /**
   * Handle top-up for insufficient balance
   * @param {Object} options - Top-up options
   * @param {number} options.amount - The amount to top up
   * @param {Function} options.onSuccess - Callback on success
   * @param {Function} options.onError - Callback on error
   */
  const handleTopUp = useCallback(async (options = {}) => {
    const { amount, onSuccess, onError } = options;
    
    try {
      // Use the payment context to start the payment
      await startPayment({
        context: 'order_top_up',
        referenceId: orderPaymentState.orderId,
        amount: amount || orderPaymentState.amount,
        returnPath: `/orders/${orderPaymentState.orderId}`,
        onSuccess,
        onError
      });
    } catch (error) {
      console.error('Error starting top-up payment:', error);
      
      if (onError) {
        onError(error);
      }
    }
  }, [startPayment, orderPaymentState.orderId, orderPaymentState.amount]);
  
  /**
   * Get order payment history
   * @param {number} orderId - The order ID
   * @returns {Promise<Array>} - The order payment history
   */
  const getOrderPaymentHistory = useCallback(async (orderId) => {
    try {
      const history = await orderPaymentService.getOrderPaymentHistory(orderId);
      
      setOrderPaymentState(prev => ({
        ...prev,
        paymentHistory: history
      }));
      
      return history;
    } catch (error) {
      console.error('Error getting order payment history:', error);
      throw error;
    }
  }, []);
  
  /**
   * Reset order payment state
   */
  const resetOrderPayment = useCallback(() => {
    setOrderPaymentState({
      isProcessing: false,
      orderId: null,
      orderType: null,
      amount: null,
      transactionId: null,
      error: null,
      status: null,
      paymentHistory: []
    });
    
    // Clear order payment context from localStorage
    localStorage.removeItem('orderPaymentOrderId');
    localStorage.removeItem('orderPaymentOrderType');
    localStorage.removeItem('orderPaymentAmount');
  }, []);
  
  // Context value
  const value = {
    orderPaymentState,
    processOrderPayment,
    handleTopUp,
    getOrderPaymentHistory,
    resetOrderPayment,
    formatCurrency: orderPaymentService.formatCurrency
  };
  
  return (
    <OrderPaymentContext.Provider value={value}>
      {children}
    </OrderPaymentContext.Provider>
  );
};

/**
 * Hook to use the order payment context
 * @returns {Object} Order payment context
 */
export const useOrderPayment = () => {
  const context = useContext(OrderPaymentContext);
  
  if (!context) {
    throw new Error('useOrderPayment must be used within an OrderPaymentProvider');
  }
  
  return context;
};

export default OrderPaymentContext;
