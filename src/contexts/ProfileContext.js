/**
 * Profile Context
 *
 * This context provides profile state and functions across the application.
 * It handles user profile data, loading states, and error handling.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import profileService from '../services/profileService';
import userServiceApi from '../services/userServiceApi';

// Create the context
const ProfileContext = createContext();

/**
 * ProfileProvider component
 *
 * This provider manages profile state across the application.
 * It handles user profile data, loading states, and error handling.
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const ProfileProvider = ({ children }) => {
  // Get auth context
  const { user: authUser, isAuthenticated } = useAuth();

  // Profile state
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [biography, setBiography] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [services, setServices] = useState([]);
  const [availability, setAvailability] = useState(null);
  const [mediaGallery, setMediaGallery] = useState([]);
  const [experienceHistory, setExperienceHistory] = useState([]);
  const [levelInfo, setLevelInfo] = useState(null);
  const [raceInfo, setRaceInfo] = useState(null);
  const [initialLoadDone, setInitialLoadDone] = useState(false);

  /**
   * Fetch user profile data
   */
  const fetchProfile = useCallback(async () => {
    if (!isAuthenticated) {
      setProfile(null);
      setInitialLoadDone(true);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getProfile();

      if (response.success) {
        setProfile(response.data);
      } else {
        setError(response.error || 'Failed to fetch profile');
      }
    } catch (err) {
      console.error('Profile fetch failed:', err);
      setError(err.message || 'Failed to fetch profile');
    } finally {
      setLoading(false);
      setInitialLoadDone(true);
    }
  }, [isAuthenticated]);

  /**
   * Fetch user biography
   */
  const fetchBiography = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getBiography();

      if (response.success) {
        setBiography(response.data);
      } else {
        setError(response.error || 'Failed to fetch biography');
      }
    } catch (err) {
      console.error('Biography fetch failed:', err);
      setError(err.message || 'Failed to fetch biography');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user statistics
   */
  const fetchStatistics = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getStatistics();

      if (response.success) {
        setStatistics(response.data);
      } else {
        setError(response.error || 'Failed to fetch statistics');
      }
    } catch (err) {
      console.error('Statistics fetch failed:', err);
      setError(err.message || 'Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user services
   */
  const fetchServices = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      // Use the new userServiceApi instead of profileService
      const response = await userServiceApi.getUserServices();

      if (response.success) {
        setServices(response.data);
      } else {
        setError(response.error || 'Failed to fetch services');
      }
    } catch (err) {
      console.error('Services fetch failed:', err);
      setError(err.message || 'Failed to fetch services');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user availability
   */
  const fetchAvailability = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getAvailability();

      if (response.success) {
        setAvailability(response.data);
      } else {
        setError(response.error || 'Failed to fetch availability');
      }
    } catch (err) {
      console.error('Availability fetch failed:', err);
      setError(err.message || 'Failed to fetch availability');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user media gallery
   */
  const fetchMediaGallery = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getMediaGallery();

      if (response.success) {
        setMediaGallery(response.data);
      } else {
        setError(response.error || 'Failed to fetch media gallery');
      }
    } catch (err) {
      console.error('Media gallery fetch failed:', err);
      setError(err.message || 'Failed to fetch media gallery');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user experience history
   */
  const fetchExperienceHistory = useCallback(async (page = 1, limit = 5) => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getExperienceHistory({ page, limit });

      if (response.success) {
        setExperienceHistory(response.data);
      } else {
        setError(response.error || 'Failed to fetch experience history');
      }
    } catch (err) {
      console.error('Experience history fetch failed:', err);
      setError(err.message || 'Failed to fetch experience history');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user level information
   */
  const fetchLevelInfo = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getLevelInfo();

      if (response.success) {
        setLevelInfo(response.data);
      } else {
        setError(response.error || 'Failed to fetch level information');
      }
    } catch (err) {
      console.error('Level info fetch failed:', err);
      setError(err.message || 'Failed to fetch level information');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Fetch user race information
   */
  const fetchRaceInfo = useCallback(async () => {
    if (!isAuthenticated || !profile) return;

    setLoading(true);
    setError(null);

    try {
      const response = await profileService.getRaceInfo();

      if (response.success) {
        setRaceInfo(response.data);
      } else {
        setError(response.error || 'Failed to fetch race information');
      }
    } catch (err) {
      console.error('Race info fetch failed:', err);
      setError(err.message || 'Failed to fetch race information');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, profile]);

  /**
   * Update user race
   *
   * @param {number} raceId - ID of the race to select
   * @returns {Promise<Object>} Update result
   */
  const updateRace = async (raceId) => {
    setLoading(true);
    setError(null);

    try {
      const response = await profileService.updateRace(raceId);

      if (response.success) {
        setRaceInfo(response.data);
        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Failed to update race');
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to update race';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update user profile
   *
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Update result
   */
  const updateProfile = async (profileData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await profileService.updateProfile(profileData);

      if (response.success) {
        setProfile(prev => ({
          ...prev,
          ...response.data
        }));
        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Failed to update profile');
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to update profile';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update user biography
   *
   * @param {Object} biographyData - Updated biography data
   * @returns {Promise<Object>} Update result
   */
  const updateBiography = async (biographyData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await profileService.updateBiography(biographyData);

      if (response.success) {
        setBiography(response.data);
        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Failed to update biography');
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to update biography';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Upload media to gallery
   *
   * @param {File} file - File to upload
   * @param {string} type - Media type (image, video)
   * @returns {Promise<Object>} Upload result
   */
  const uploadMedia = async (file, type = 'image') => {
    setLoading(true);
    setError(null);

    try {
      const response = await profileService.uploadMedia(file, type);

      if (response.success) {
        setMediaGallery(prev => [...prev, response.data]);
        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Failed to upload media');
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to upload media';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Fetch profile when auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      fetchProfile();
    } else {
      setProfile(null);
      setBiography(null);
      setStatistics(null);
      setServices([]);
      setAvailability(null);
      setMediaGallery([]);
    }
  }, [isAuthenticated, fetchProfile]);

  // Fetch additional data when profile is loaded
  useEffect(() => {
    if (profile) {
      fetchBiography();
      fetchStatistics();
      fetchServices();
      fetchAvailability();
      // Skip optional data that might not be available
      // fetchMediaGallery();
      // fetchExperienceHistory();
      // fetchLevelInfo();
      // fetchRaceInfo();
    }
  }, [profile, fetchBiography, fetchStatistics, fetchServices, fetchAvailability]);

  // Context value
  const value = {
    profile,
    biography,
    statistics,
    services,
    availability,
    mediaGallery,
    experienceHistory,
    levelInfo,
    raceInfo,
    loading,
    error,
    initialLoadDone,
    fetchProfile,
    fetchBiography,
    fetchStatistics,
    fetchServices,
    fetchAvailability,
    fetchMediaGallery,
    fetchExperienceHistory,
    fetchLevelInfo,
    fetchRaceInfo,
    updateProfile,
    updateBiography,
    uploadMedia,
    updateRace
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

/**
 * Custom hook to use the profile context
 *
 * @returns {Object} Profile context
 */
export const useProfile = () => {
  const context = useContext(ProfileContext);

  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }

  return context;
};

export default ProfileContext;
