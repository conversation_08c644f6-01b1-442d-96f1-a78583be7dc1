import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Create the Social Context
const SocialContext = createContext();

// Action types for the reducer
const SOCIAL_ACTIONS = {
    // Post actions
    UPDATE_POST_LIKE: 'UPDATE_POST_LIKE',
    UPDATE_POST_COMMENT_COUNT: 'UPDATE_POST_COMMENT_COUNT',
    ADD_POST_COMMENT: 'ADD_POST_COMMENT',
    UPDATE_POST: 'UPDATE_POST',
    
    // UI actions
    SHOW_TOAST: 'SHOW_TOAST',
    HIDE_TOAST: 'HIDE_TOAST',
    SET_LOADING: 'SET_LOADING',
    
    // Follow actions
    UPDATE_FOLLOW_STATUS: 'UPDATE_FOLLOW_STATUS',
};

// Initial state
const initialState = {
    posts: {},
    toasts: [],
    loading: {},
    followStatus: {},
};

// Reducer function
const socialReducer = (state, action) => {
    switch (action.type) {
        case SOCIAL_ACTIONS.UPDATE_POST_LIKE:
            return {
                ...state,
                posts: {
                    ...state.posts,
                    [action.payload.postId]: {
                        ...state.posts[action.payload.postId],
                        is_liked: action.payload.liked,
                        total_liked: action.payload.likeCount,
                    }
                }
            };

        case SOCIAL_ACTIONS.UPDATE_POST_COMMENT_COUNT:
            return {
                ...state,
                posts: {
                    ...state.posts,
                    [action.payload.postId]: {
                        ...state.posts[action.payload.postId],
                        total_comments: action.payload.commentCount,
                    }
                }
            };

        case SOCIAL_ACTIONS.ADD_POST_COMMENT:
            return {
                ...state,
                posts: {
                    ...state.posts,
                    [action.payload.postId]: {
                        ...state.posts[action.payload.postId],
                        comments: [
                            ...(state.posts[action.payload.postId]?.comments || []),
                            action.payload.comment
                        ],
                        total_comments: (state.posts[action.payload.postId]?.total_comments || 0) + 1,
                    }
                }
            };

        case SOCIAL_ACTIONS.UPDATE_POST:
            return {
                ...state,
                posts: {
                    ...state.posts,
                    [action.payload.id]: {
                        ...state.posts[action.payload.id],
                        ...action.payload,
                    }
                }
            };

        case SOCIAL_ACTIONS.SHOW_TOAST:
            return {
                ...state,
                toasts: [
                    ...state.toasts,
                    {
                        id: Date.now() + Math.random(),
                        ...action.payload,
                    }
                ]
            };

        case SOCIAL_ACTIONS.HIDE_TOAST:
            return {
                ...state,
                toasts: state.toasts.filter(toast => toast.id !== action.payload.id)
            };

        case SOCIAL_ACTIONS.SET_LOADING:
            return {
                ...state,
                loading: {
                    ...state.loading,
                    [action.payload.key]: action.payload.loading,
                }
            };

        case SOCIAL_ACTIONS.UPDATE_FOLLOW_STATUS:
            return {
                ...state,
                followStatus: {
                    ...state.followStatus,
                    [action.payload.userId]: action.payload.isFollowing,
                }
            };

        default:
            return state;
    }
};

// Toast component
const Toast = ({ toast, onClose }) => {
    const getToastStyles = () => {
        switch (toast.type) {
            case 'success':
                return 'bg-green-500 text-white';
            case 'error':
                return 'bg-red-500 text-white';
            case 'warning':
                return 'bg-yellow-500 text-white';
            case 'info':
            default:
                return 'bg-blue-500 text-white';
        }
    };

    const getToastIcon = () => {
        switch (toast.type) {
            case 'success':
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                );
            case 'error':
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                );
            case 'warning':
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                );
            case 'info':
            default:
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                );
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            className={`flex items-center p-4 rounded-2xl shadow-lg backdrop-blur-sm ${getToastStyles()} min-w-[300px] max-w-md`}
        >
            <div className="flex-shrink-0 mr-3">
                {getToastIcon()}
            </div>
            <div className="flex-1">
                {toast.title && (
                    <p className="font-semibold text-sm">{toast.title}</p>
                )}
                <p className="text-sm opacity-90">{toast.message}</p>
            </div>
            <motion.button
                onClick={() => onClose(toast.id)}
                className="ml-3 p-1 rounded-full hover:bg-white/20 transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
            >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </motion.button>
        </motion.div>
    );
};

// Social Provider component
export const SocialProvider = ({ children }) => {
    const [state, dispatch] = useReducer(socialReducer, initialState);

    // Action creators
    const updatePostLike = useCallback((postId, liked, likeCount) => {
        dispatch({
            type: SOCIAL_ACTIONS.UPDATE_POST_LIKE,
            payload: { postId, liked, likeCount }
        });
    }, []);

    const updatePostCommentCount = useCallback((postId, commentCount) => {
        dispatch({
            type: SOCIAL_ACTIONS.UPDATE_POST_COMMENT_COUNT,
            payload: { postId, commentCount }
        });
    }, []);

    const addPostComment = useCallback((postId, comment) => {
        dispatch({
            type: SOCIAL_ACTIONS.ADD_POST_COMMENT,
            payload: { postId, comment }
        });
    }, []);

    const updatePost = useCallback((post) => {
        dispatch({
            type: SOCIAL_ACTIONS.UPDATE_POST,
            payload: post
        });
    }, []);

    const showToast = useCallback((toast) => {
        const toastId = dispatch({
            type: SOCIAL_ACTIONS.SHOW_TOAST,
            payload: toast
        });

        // Auto-hide toast after duration
        setTimeout(() => {
            dispatch({
                type: SOCIAL_ACTIONS.HIDE_TOAST,
                payload: { id: toastId }
            });
        }, toast.duration || 5000);

        return toastId;
    }, []);

    const hideToast = useCallback((id) => {
        dispatch({
            type: SOCIAL_ACTIONS.HIDE_TOAST,
            payload: { id }
        });
    }, []);

    const setLoading = useCallback((key, loading) => {
        dispatch({
            type: SOCIAL_ACTIONS.SET_LOADING,
            payload: { key, loading }
        });
    }, []);

    const updateFollowStatus = useCallback((userId, isFollowing) => {
        dispatch({
            type: SOCIAL_ACTIONS.UPDATE_FOLLOW_STATUS,
            payload: { userId, isFollowing }
        });
    }, []);

    const value = {
        // State
        posts: state.posts,
        toasts: state.toasts,
        loading: state.loading,
        followStatus: state.followStatus,

        // Actions
        updatePostLike,
        updatePostCommentCount,
        addPostComment,
        updatePost,
        showToast,
        hideToast,
        setLoading,
        updateFollowStatus,
    };

    return (
        <SocialContext.Provider value={value}>
            {children}
            
            {/* Toast Container */}
            <div className="fixed top-4 right-4 z-[9999] space-y-2">
                <AnimatePresence>
                    {state.toasts.map((toast) => (
                        <Toast
                            key={toast.id}
                            toast={toast}
                            onClose={hideToast}
                        />
                    ))}
                </AnimatePresence>
            </div>
        </SocialContext.Provider>
    );
};

// Custom hook to use the Social Context
export const useSocial = () => {
    const context = useContext(SocialContext);
    if (!context) {
        throw new Error('useSocial must be used within a SocialProvider');
    }
    return context;
};

export default SocialContext;
