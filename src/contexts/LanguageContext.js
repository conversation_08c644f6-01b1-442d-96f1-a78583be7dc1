import React, { createContext, useState, useEffect, useCallback, useContext } from 'react';
import i18n from 'i18next';
import { supportedLanguages } from '../translations';

// Create the language context
const LanguageContext = createContext();

/**
 * Language Provider Component
 * 
 * This provider manages language state across the application.
 * It handles language detection, switching, and persistence.
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const LanguageProvider = ({ children }) => {
  // State for current language
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');
  
  // State for language loading
  const [isLanguageLoading, setIsLanguageLoading] = useState(false);
  
  // State for language detection source
  const [detectionSource, setDetectionSource] = useState(null);
  
  // Effect to sync with i18n language changes
  useEffect(() => {
    const handleLanguageChanged = (lng) => {
      setCurrentLanguage(lng);
      setIsLanguageLoading(false);
    };
    
    // Listen for language changes
    i18n.on('languageChanged', handleLanguageChanged);
    
    // Clean up listener
    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, []);
  
  // Effect to detect initial language source
  useEffect(() => {
    // Check if language is from localStorage
    const storedLanguage = localStorage.getItem('userLanguage');
    if (storedLanguage) {
      setDetectionSource('localStorage');
      return;
    }
    
    // Check if language is from browser
    const browserLanguage = navigator.language.split('-')[0];
    const isBrowserLanguageSupported = supportedLanguages.some(
      lang => lang.code === browserLanguage
    );
    
    if (isBrowserLanguageSupported) {
      setDetectionSource('browser');
      return;
    }
    
    // Default to 'default' source
    setDetectionSource('default');
  }, []);
  
  /**
   * Change the application language
   * @param {string} languageCode - The language code to change to
   * @param {Object} options - Additional options
   * @param {boolean} options.persist - Whether to persist the language choice
   * @param {string} options.source - Source of the language change
   * @returns {Promise<void>}
   */
  const changeLanguage = useCallback(async (languageCode, options = {}) => {
    const { persist = true, source = 'user' } = options;
    
    // Validate language code
    const isValidLanguage = supportedLanguages.some(lang => lang.code === languageCode);
    if (!isValidLanguage) {
      console.warn(`Invalid language code: ${languageCode}`);
      return;
    }
    
    try {
      // Start loading
      setIsLanguageLoading(true);
      
      // Change language in i18n
      await i18n.changeLanguage(languageCode);
      
      // Persist language choice if requested
      if (persist) {
        localStorage.setItem('userLanguage', languageCode);
      }
      
      // Update document language attribute
      document.documentElement.setAttribute('lang', languageCode);
      
      // Update detection source if it's a user choice
      if (source === 'user') {
        setDetectionSource('user');
      }
      
      // Dispatch custom event for other parts of the app
      const event = new CustomEvent('languageChanged', { 
        detail: { language: languageCode, source } 
      });
      window.dispatchEvent(event);
      
      console.log(`Language changed to ${languageCode} (source: ${source})`);
    } catch (error) {
      console.error('Error changing language:', error);
      setIsLanguageLoading(false);
    }
  }, []);
  
  /**
   * Get the display name of a language
   * @param {string} languageCode - The language code
   * @returns {string} The display name of the language
   */
  const getLanguageDisplayName = useCallback((languageCode) => {
    const language = supportedLanguages.find(lang => lang.code === languageCode);
    return language ? language.nativeName : languageCode;
  }, []);
  
  /**
   * Get the text direction for the current language
   * @returns {string} 'rtl' for right-to-left languages, 'ltr' otherwise
   */
  const getTextDirection = useCallback(() => {
    return i18n.dir();
  }, []);
  
  // Create context value
  const contextValue = {
    currentLanguage,
    isLanguageLoading,
    detectionSource,
    supportedLanguages,
    changeLanguage,
    getLanguageDisplayName,
    getTextDirection,
    dir: getTextDirection()
  };
  
  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

/**
 * Custom hook to use the language context
 * @returns {Object} The language context
 */
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;
