import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import walletAPI from '../services/walletService';

// Create the context
const PaymentContext = createContext();

/**
 * PaymentProvider component
 * 
 * This provider manages payment state across the application.
 * It handles payment context tracking, payment processing, and payment status.
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const PaymentProvider = ({ children }) => {
  const navigate = useNavigate();
  
  // Payment state
  const [paymentState, setPaymentState] = useState({
    isProcessing: false,
    context: null, // 'wallet-topup', 'order-payment', 'mission-creation', etc.
    referenceId: null, // orderId, missionId, etc.
    transactionId: null,
    amount: null,
    returnPath: '/wallet',
    error: null,
    status: null // 'pending', 'success', 'failed'
  });
  
  // Initialize payment state from localStorage on mount
  useEffect(() => {
    const storedContext = localStorage.getItem('paymentContext');
    const storedReferenceId = localStorage.getItem('paymentReferenceId');
    const storedAmount = localStorage.getItem('paymentAmount');
    const storedReturnPath = localStorage.getItem('paymentReturnPath');
    const pendingPaymentId = localStorage.getItem('pendingPaymentId');
    
    if (storedContext || pendingPaymentId) {
      setPaymentState({
        isProcessing: !!pendingPaymentId,
        context: storedContext || 'unknown',
        referenceId: storedReferenceId,
        transactionId: pendingPaymentId,
        amount: storedAmount ? parseFloat(storedAmount) : null,
        returnPath: storedReturnPath || '/wallet',
        error: null,
        status: pendingPaymentId ? 'pending' : null
      });
    }
  }, []);
  
  /**
   * Start a payment process
   * @param {Object} paymentDetails - Payment details
   * @param {string} paymentDetails.context - Payment context (e.g., 'wallet-topup')
   * @param {string} paymentDetails.referenceId - Reference ID (e.g., orderId)
   * @param {number} paymentDetails.amount - Payment amount
   * @param {string} paymentDetails.returnPath - Path to return to after payment
   * @param {Function} paymentDetails.onSuccess - Callback on success
   * @param {Function} paymentDetails.onError - Callback on error
   */
  const startPayment = useCallback(async (paymentDetails) => {
    const { 
      context, 
      referenceId, 
      amount, 
      returnPath = '/wallet',
      creditPackageId,
      onSuccess,
      onError
    } = paymentDetails;
    
    // Store payment context in localStorage for persistence across redirects
    localStorage.setItem('paymentContext', context);
    if (referenceId) localStorage.setItem('paymentReferenceId', referenceId);
    if (amount) localStorage.setItem('paymentAmount', amount.toString());
    localStorage.setItem('paymentReturnPath', returnPath);
    
    setPaymentState({
      isProcessing: true,
      context,
      referenceId,
      amount,
      returnPath,
      error: null,
      status: 'pending'
    });
    
    try {
      // Get current URL for redirect
      const redirectUrl = `${window.location.origin}/payment-return`;

      // Create payment and automatically redirect to the gateway
      const response = await walletAPI.createPayment(
        creditPackageId,
        redirectUrl,
        true
      );
      
      if (response.data && response.data.payment) {
        // Store the transaction ID in local storage to check on return
        localStorage.setItem('pendingPaymentId', response.data.payment.transaction_id);
        
        setPaymentState(prev => ({
          ...prev,
          transactionId: response.data.payment.transaction_id
        }));
        
        if (onSuccess) {
          onSuccess(response.data);
        }
      } else {
        throw new Error('Invalid payment response');
      }
    } catch (err) {
      console.error('Error creating payment:', err);
      
      setPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        error: err.message || 'Failed to process payment',
        status: 'failed'
      }));
      
      if (onError) {
        onError(err);
      }
    }
  }, []);
  
  /**
   * Retry a failed payment
   * @param {Object} options - Retry options
   * @param {string} options.transactionId - Transaction ID to retry
   * @param {Function} options.onSuccess - Callback on success
   * @param {Function} options.onError - Callback on error
   */
  const retryPayment = useCallback(async ({ transactionId, onSuccess, onError }) => {
    if (!transactionId) {
      const error = new Error('Invalid transaction ID');
      if (onError) onError(error);
      return;
    }
    
    setPaymentState(prev => ({
      ...prev,
      isProcessing: true,
      error: null,
      status: 'pending'
    }));
    
    try {
      const response = await walletAPI.retryPayment(transactionId);
      
      if (response.data && response.data.payment) {
        // Store the transaction ID in local storage to check on return
        localStorage.setItem('pendingPaymentId', response.data.payment.transaction_id);
        
        setPaymentState(prev => ({
          ...prev,
          transactionId: response.data.payment.transaction_id
        }));
        
        // Redirect to payment gateway
        if (response.data.payment.redirect_url) {
          window.location.href = response.data.payment.redirect_url;
        } else if (onSuccess) {
          onSuccess(response.data);
        }
      } else {
        throw new Error('Invalid payment response');
      }
    } catch (err) {
      console.error('Error retrying payment:', err);
      
      setPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        error: err.message || 'Failed to retry payment',
        status: 'failed'
      }));
      
      if (onError) {
        onError(err);
      }
    }
  }, []);
  
  /**
   * Complete a payment process
   * @param {string} transactionId - Transaction ID
   * @param {string} status - Payment status
   */
  const completePayment = useCallback((transactionId, status) => {
    setPaymentState(prev => ({
      ...prev,
      isProcessing: false,
      transactionId,
      status
    }));
    
    // Clear payment context from localStorage
    localStorage.removeItem('paymentContext');
    localStorage.removeItem('paymentReferenceId');
    localStorage.removeItem('paymentAmount');
    localStorage.removeItem('paymentReturnPath');
    localStorage.removeItem('pendingPaymentId');
  }, []);
  
  /**
   * Set payment error
   * @param {Error} error - Error object
   */
  const setPaymentError = useCallback((error) => {
    setPaymentState(prev => ({
      ...prev,
      isProcessing: false,
      error,
      status: 'failed'
    }));
  }, []);
  
  /**
   * Reset payment state
   */
  const resetPayment = useCallback(() => {
    setPaymentState({
      isProcessing: false,
      context: null,
      referenceId: null,
      transactionId: null,
      amount: null,
      returnPath: '/wallet',
      error: null,
      status: null
    });
    
    // Clear payment context from localStorage
    localStorage.removeItem('paymentContext');
    localStorage.removeItem('paymentReferenceId');
    localStorage.removeItem('paymentAmount');
    localStorage.removeItem('paymentReturnPath');
    localStorage.removeItem('pendingPaymentId');
  }, []);
  
  // Context value
  const value = {
    paymentState,
    startPayment,
    retryPayment,
    completePayment,
    setPaymentError,
    resetPayment
  };
  
  return (
    <PaymentContext.Provider value={value}>
      {children}
    </PaymentContext.Provider>
  );
};

/**
 * Hook to use the payment context
 * @returns {Object} Payment context
 */
export const usePayment = () => {
  const context = useContext(PaymentContext);
  
  if (!context) {
    throw new Error('usePayment must be used within a PaymentProvider');
  }
  
  return context;
};

export default PaymentContext;
