{"common": {"wallet": "Wallet", "balance": "Baki", "available": "Tersedia", "pending": "Tertunda", "total": "<PERSON><PERSON><PERSON>", "currency": "<PERSON>", "amount": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "status": "Status", "description": "Penerangan", "reference": "Rujukan", "type": "<PERSON><PERSON>"}, "dashboard": {"title": "Wallet X", "subtitle": "<PERSON><PERSON> dana anda", "currentBalance": "<PERSON><PERSON>", "pendingBalance": "Baki Tertunda", "totalBalance": "<PERSON><PERSON><PERSON>", "quickActions": "<PERSON><PERSON><PERSON>", "recentTransactions": "<PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON>", "noTransactions": "Tiada transaksi lagi"}, "transactions": {"title": "Transaksi", "subtitle": "<PERSON><PERSON> se<PERSON>ah <PERSON> anda", "filter": {"title": "<PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "topUp": "Tambah Nilai", "withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pembayaran", "refund": "Bayaran Balik", "reward": "Ganjaran", "dateRange": "Julat Tarikh", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON>", "clear": "<PERSON>"}, "status": {"completed": "Se<PERSON><PERSON>", "pending": "Tertunda", "failed": "Gaga<PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "refunded": "Dibayar <PERSON>"}, "types": {"topUp": "Tambah Nilai", "withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pembayaran", "refund": "Bayaran Balik", "reward": "Ganjaran", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "missionPayout": "<PERSON><PERSON><PERSON><PERSON>", "serviceFee": "<PERSON><PERSON>"}, "details": {"title": "<PERSON><PERSON><PERSON>", "id": "ID Transaksi", "date": "Tarikh & Masa", "amount": "<PERSON><PERSON><PERSON>", "status": "Status", "type": "<PERSON><PERSON>", "description": "Penerangan", "reference": "Rujukan", "paymentMethod": "<PERSON><PERSON><PERSON>", "recipient": "<PERSON><PERSON><PERSON>", "sender": "Penghantar", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>"}, "empty": "Tiada transaksi dijumpai", "loadMore": "<PERSON><PERSON>"}, "topUp": {"title": "Tambah Nilai", "subtitle": "<PERSON><PERSON> dana ke Wallet anda", "amount": "<PERSON><PERSON><PERSON>", "selectAmount": "<PERSON><PERSON><PERSON>", "customAmount": "<PERSON><PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON>", "addPaymentMethod": "<PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON>", "subtotal": "<PERSON><PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "button": "Tambah Ni<PERSON>", "success": "<PERSON><PERSON> nilai berjaya!", "error": "<PERSON><PERSON> tambah nilai.", "processing": "Sedang memproses pembayaran..."}, "withdraw": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> dana dari <PERSON> anda", "amount": "<PERSON><PERSON><PERSON>", "availableBalance": "Baki <PERSON>", "withdrawalMethod": "<PERSON><PERSON><PERSON>", "addWithdrawalMethod": "<PERSON><PERSON>", "bankAccount": "Akaun Bank", "addBankAccount": "Tambah Akaun Bank", "summary": "<PERSON><PERSON><PERSON>", "subtotal": "<PERSON><PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON> pengeluaran berjaya di<PERSON>!", "error": "Pengel<PERSON><PERSON> gagal. Sila cuba lagi.", "processing": "Sedang memproses pengeluaran...", "minimumAmount": "Jumlah minimum untuk pengeluaran ialah {{amount}}", "maximumAmount": "<PERSON><PERSON><PERSON> maksimum untuk pengeluaran ialah {{amount}}"}, "bankAccounts": {"title": "Akaun Bank", "subtitle": "Urus akaun bank anda", "addAccount": "Tambah Akaun Bank", "editAccount": "<PERSON><PERSON><PERSON>", "deleteAccount": "<PERSON><PERSON>", "setDefault": "<PERSON><PERSON><PERSON>", "fields": {"accountName": "<PERSON><PERSON>", "accountNumber": "<PERSON><PERSON><PERSON>", "bankName": "Nama Bank", "branchCode": "<PERSON><PERSON>", "swiftCode": "Kod SWIFT", "country": "Negara"}, "placeholders": {"accountName": "<PERSON><PERSON>", "accountNumber": "Masukkan nombor akaun", "bankName": "Nama Bank", "branchCode": "Masukkan kod cawangan", "swiftCode": "Masukkan kod SWIFT", "country": "<PERSON><PERSON><PERSON> negara"}, "success": {"add": "Akaun bank berjaya ditambah!", "edit": "Akaun bank berjaya dikemas kini!", "delete": "Akaun bank berjaya dipadam!", "default": "A<PERSON>un utama berjaya ditetapkan!"}, "error": {"add": "Gagal menambah akaun bank", "edit": "Gagal mengemaskini akaun bank", "delete": "Gagal padam akaun bank", "default": "<PERSON>l tetapkan akaun utama."}, "info": {"securityTitle": "Keselamatan Akaun Bank", "securityDesc": "Butiran akaun bank anda disimpan dengan selamat. <PERSON>mi tidak akan berkongsi maklumat ini dengan pengguna lain atau pihak ketiga tanpa kebenaran anda.", "primaryDesc": "<PERSON><PERSON><PERSON> utama akan digunakan secara automatik untuk pengeluaran."}, "confirmDelete": "<PERSON><PERSON>h anda pasti ingin memadam akaun bank ini?", "noAccounts": "Tiada akaun bank ditambah lagi"}, "page": {"title": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON> kredit dan trans<PERSON>i anda"}, "paymentReturn": {"verifying": "Mengesah<PERSON>", "processingMessage": "Sila tunggu sementara kami mengesahkan status pembayaran anda...", "successTitle": "Pembayaran Berjaya!", "successMessage": "Pembayaran anda telah diproses dan kredit telah ditambah ke Wallet anda.", "redirecting": "<PERSON><PERSON><PERSON><PERSON> anda semula...", "failureTitle": "Pembayaran Gagal", "tryAgain": "Cuba Lagi", "goWallet": "<PERSON><PERSON> ke <PERSON>et", "failureMessage": "Terdapat masalah memproses pembayaran anda.", "returnWallet": "Ke<PERSON>li ke Wallet", "transactionId": "ID Transaksi:", "creditsAdded": "Kredit Ditambah:", "newBalance": "Baki Baru:", "date": "Tarikh:", "processingTitle": "Memp<PERSON><PERSON>", "needHelp": "Perlukan <PERSON>?", "pageTitle": "Status Pembayaran", "footerCopyright": "Hak cipta terpeli<PERSON>.", "footerReturnWallet": "Kembali ke Dompet", "footerNeedHelp": "Perlukan <PERSON>?", "footerHelpLink": "Hubungi <PERSON>"}}