{"common": {"profile": "Profile", "edit": "Edit Profile", "save": "Save Changes", "cancel": "Cancel", "uploadPhoto": "Upload Photo", "removePhoto": "Remove Photo", "changePhoto": "Change Photo"}, "view": {"title": "Profile", "personalInfo": "Personal Information", "bio": "Bio", "skills": "Skills", "games": "Games", "achievements": "Achievements", "reviews": "Reviews", "services": "Services", "stats": {"title": "Stats", "missions": "Missions", "completed": "Completed", "rating": "Rating", "joined": "Joined"}, "contact": "Contact", "report": "Report User", "share": "Share Profile", "loadDemoData": "Load Demo Data", "noBiography": "No biography available yet.", "verified": "Verified", "errorLoadingProfile": "Could not load profile data.", "quickSections": "Quick Sections", "bookmarkedMissions": "Bookmarked Missions", "likedPosts": "Liked Posts", "noBookmarkedMissions": "No Bookmarked Missions", "noLikedPosts": "No Liked Posts", "viewAll": "View All", "quick": {"myClients": "My Clients", "noMissions": "No Missions", "missionCount": "{{count}} Mission", "missionCount_plural": "{{count}} Missions", "myOrders": "My Orders", "pending": "{{count}} Pending", "myMissions": "My Missions", "activeCount": "{{count}} Active", "disputes": " My Disputes", "noneActive": "None Active", "activeLabel": "Active", "totalLabel": "Total", "myReviews": "My Reviews", "reviewsDesc": "View & Filter", "myGifts": "My Gifts", "giftsDesc": "View your gifts", "inviteFriends": "My Points & Referral", "referrals": "{{count}} Referrals", "voiceNote": "My Voice Note", "playing": "Playing...", "clickToPlay": "Click to play", "notRecorded": "Not recorded yet", "setAvailability": "My Availability", "manageSchedule": "Manage schedule"}, "profileDetails": {"title": "Profile Details", "experience": "Experience", "level": "Level", "xpToLevel": "{{xp}} XP to Level {{level}}", "birthday": "Birthday", "gender": "Gender", "height": "Height", "weight": "Weight", "race": "Race", "languages": "Languages", "personalities": "Personalities", "constellation": "Constellation", "totalFollowers": "Total Followers"}, "inviteCard": {"description": "Share your referral code and earn 500 credits for each friend who joins!", "yourCode": "Your Referral Code", "copy": "Copy Referral Code", "rewardPerReferral": "Reward per referral", "whenFriendsSignUp": "When friends sign up", "credits": "Credits"}}, "edit": {"title": "Edit Profile", "sections": {"personal": "Personal Information", "bio": "Bio & Description", "skills": "Skills & Expertise", "games": "Games & Platforms", "services": "Services & Pricing", "social": "Social Media", "basicInfo": "Basic Info", "media": "Media", "preferences": "Preferences"}, "fields": {"name": "Full Name", "username": "Username", "email": "Email", "phone": "Phone Number", "country": "Country", "language": "Language", "timezone": "Timezone", "bio": "Bio", "shortBio": "Short Bio", "skills": "Skills", "addSkill": "<PERSON><PERSON>", "games": "Games", "addGame": "Add Game", "platforms": "Platforms", "addPlatform": "Add Platform", "services": "Services", "addService": "Add Service", "pricing": "Pricing", "availability": "Availability", "social": {"discord": "Discord", "twitter": "Twitter", "twitch": "Twitch", "youtube": "YouTube", "instagram": "Instagram"}}, "placeholders": {"name": "Enter your full name", "username": "Choose a username", "email": "Enter your email", "phone": "Enter your phone number", "bio": "Tell others about yourself...", "shortBio": "Brief introduction (150 characters max)", "addSkill": "Type to add a skill", "addGame": "Search for a game", "addPlatform": "Select platforms", "serviceName": "Service name", "serviceDescription": "Describe your service", "price": "Set your price"}, "success": "Profile updated successfully!", "error": "Failed to update profile"}, "setup": {"title": "Profile Setup", "subtitle": "Let's set up your profile", "steps": {"personal": "Personal Info", "bio": "Bio", "personality": "Personality", "services": "Services", "complete": "Complete"}, "personalInfo": {"title": "Personal Information", "subtitle": "Tell us about yourself", "fields": {"name": "Full Name", "username": "Username", "country": "Country", "language": "Language"}}, "bio": {"title": "Your Bio", "subtitle": "Tell others about yourself", "fields": {"bio": "Bio", "shortBio": "Short Bio"}}, "personality": {"title": "Your Personality", "subtitle": "Select traits that describe you", "traits": {"friendly": "Friendly", "competitive": "Competitive", "strategic": "Strategic", "teamPlayer": "Team Player", "creative": "Creative", "patient": "Patient", "leader": "Leader", "adaptable": "Adaptable", "communicative": "Communicative", "analytical": "Analytical"}}, "services": {"title": "Your Services", "subtitle": "What services do you offer?", "fields": {"serviceName": "Service Name", "description": "Description", "price": "Price", "addService": "Add Service"}}, "complete": {"title": "Setup Complete!", "subtitle": "Your profile is ready to go", "message": "You've successfully set up your profile. You can now start exploring missions and connecting with other gamers.", "viewProfile": "View Profile", "exploreMissions": "Explore Missions"}, "buttons": {"back": "Back", "next": "Next", "skip": "<PERSON><PERSON>", "finish": "Finish"}}, "modals": {"myClients": {"title": "My Clients", "description": "Manage your service orders and client interactions", "tabs": {"toAccept": "To Accept", "accepted": "Accepted", "inProgress": "In Progress", "rejected": "Rejected", "cancelled": "Cancelled", "completed": "Completed", "expired": "Expired"}, "loading": "Loading orders...", "empty": "No {{status}} orders", "emptyDesc": "You don't have any {{status}} orders at the moment.", "acceptOffer": "Accept Offer", "rejectOffer": "Reject", "accepting": "Accepting...", "rejecting": "Rejecting...", "viewAcceptedOrders": "View Accepted Orders", "continue": "Continue", "dateTime": "Date & Time", "creditsToReceive": "Credits to Receive", "earnings": "Earnings:", "whatNext": "What happens next?", "steps": {"notified": "Client has been notified of your acceptance", "creditsHeld": "Credits are held securely until completion", "startService": "You can now start the service"}, "notSet": "Not set"}, "myOrders": {"title": "My Orders", "description": "View and manage your service orders", "tabs": {"toaccept": "To Accept", "accepted": "Accepted", "rejected": "Rejected", "inprogress": "In Progress", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "loading": "Loading orders...", "empty": "No {{status}} orders", "emptyDesc": "You don't have any {{status}} orders at the moment."}, "myReviews": {"title": "My Reviews", "description": "View and manage reviews you've written for others", "loading": "Loading your reviews...", "empty": "No reviews yet", "emptyDesc": "Complete orders and missions to start receiving reviews!", "retry": "Retry"}, "availability": {"title": "Availability", "available": "Available", "notAvailable": "Not Available", "noData": "No availability data found.", "remarks": "Remarks"}, "disputes": {"title": "Dispute Management", "description": "View and manage your order disputes", "status": {"all": "All", "submitted": "Submitted", "in_review": "In Review", "resolved": "Resolved", "rejected": "Rejected"}, "loading": "Loading disputes...", "noDisputes": "No disputes found", "noDisputesDesc": "You haven't created any disputes yet.", "noDisputesStatus": "No {{status}} disputes", "noDisputesStatusDesc": "Try changing the filter to see other disputes.", "retry": "Retry", "details": "Dispute Details", "statusMessages": {"submitted": "Your dispute has been submitted and is awaiting review.", "in_review": "Your dispute is currently under review.", "resolved": "Your dispute has been resolved."}}}, "availabilityPage": {"title": "Availability", "description": "Manage when you're available for bookings", "status": {"available": "Available", "unavailable": "Unavailable"}, "setOverride": "Set Override", "tabs": {"calendar": "Calendar View", "manage": "Manage Settings"}, "error": "Failed to load user data", "tryAgain": "Try Again", "goBack": "Go Back"}}