import React from 'react';

// Lazy load page components and prefetch important bundles
export const LazyHome = React.lazy(() =>
  import(/* webpackPrefetch: true */ './components/Home')
);
// Allow manual preloading of the home page
LazyHome.preload = () => import('./components/Home');
export const LazyAllGames = React.lazy(() => import('./pages/AllGames'));
export const LazyTalentProfile = React.lazy(() => import('./components/TalentProfile'));
export const LazyOrderManagement = React.lazy(() => import('./components/orders/OrderManagement'));
export const LazyOrderDetails = React.lazy(() => import('./components/orders/OrderDetails'));
export const LazyProfile = React.lazy(() =>
  import(/* webpackPrefetch: true */ './components/Profile')
);
LazyProfile.preload = () => import('./components/Profile');
// Prefetch chat page bundle to reduce navigation latency
export const LazyChat = React.lazy(() =>
  import(/* webpackPrefetch: true */ './pages/Chat')
);
// Allow manual preloading
LazyChat.preload = () => import('./pages/Chat');
export const LazyProfileSetup = React.lazy(() => import('./components/ProfileSetup'));
// Prefetch talent page bundle to reduce navigation latency
export const LazyTalent = React.lazy(() =>
  import(/* webpackPrefetch: true */ './pages/TalentPage')
);
// Allow manual preloading
LazyTalent.preload = () => import('./pages/TalentPage');
// Prefetch explore page bundle
export const LazyExplore = React.lazy(() =>
  import(/* webpackPrefetch: true */ './pages/Explore')
);
LazyExplore.preload = () => import('./pages/Explore');
export const LazyWallet = React.lazy(() => import('./pages/Wallet'));
export const LazyPaymentReturn = React.lazy(() => import('./pages/PaymentReturn'));
export const LazyPaymentReturnPage = React.lazy(() => import('./pages/PaymentReturnPage'));
export const LazyBankAccountsPage = React.lazy(() => import('./pages/BankAccountsPage'));
export const LazyMissionPage = React.lazy(() => import('./pages/MissionPage'));
export const LazyMissionDetailPage = React.lazy(() => import('./pages/MissionDetailPage'));
export const LazyMyMissionsPage = React.lazy(() => import('./pages/MyMissionsPage'));
export const LazyMissionApplicantsPage = React.lazy(() => import('./pages/MissionApplicantsPage'));
export const LazyMissionCreatePage = React.lazy(() => import('./pages/MissionCreatePage'));
export const LazyMissionEditPage = React.lazy(() => import('./pages/MissionEditPage'));
export const LazyMissionExecutionPage = React.lazy(() => import('./pages/MissionExecutionPage'));

// Add missing components
export const LazyAvailabilityPage = React.lazy(() => import('./pages/AvailabilityPage'));
export const LazyMissionDetailPageHost = React.lazy(() => import('./pages/MissionDetailPageHost'));
