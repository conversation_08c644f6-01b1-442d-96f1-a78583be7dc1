import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';
import orderAPI from '../services/orderService';
import OrderPaymentStatus from './OrderPaymentStatus';
import OrderPaymentHistory from './OrderPaymentHistory';
import InsufficientBalanceModal from './InsufficientBalanceModal';

/**
 * OrderDetails component
 *
 * This component displays the details of an order and its payment status.
 */
const OrderDetails = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();

  const [order, setOrder] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showTopUpModal, setShowTopUpModal] = useState(false);

  // Fetch order details on mount and when orderId changes
  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    }
  }, [orderId]);

  // Fetch order details
  const fetchOrderDetails = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await orderAPI.getOrderDetails(orderId);
      setOrder(response.data);
    } catch (err) {
      console.error('Error fetching order details:', err);
      setError('Failed to load order details');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle payment success
  const handlePaymentSuccess = () => {
    // Refresh order details to get updated payment status
    fetchOrderDetails();
  };

  // Handle top-up click
  const handleTopUpClick = () => {
    setShowTopUpModal(true);
  };

  // Handle top-up success
  const handleTopUpSuccess = () => {
    setShowTopUpModal(false);
    // Refresh order details after top-up
    fetchOrderDetails();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: {
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        label: 'Pending'
      },
      accepted: {
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        label: 'Accepted'
      },
      rejected: {
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        label: 'Rejected'
      },
      completed: {
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        label: 'Completed'
      },
      cancelled: {
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        label: 'Cancelled'
      }
    };

    const config = statusConfig[status] || {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      label: status
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
        {config.label}
      </span>
    );
  };

  // If loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600">Loading order details...</span>
      </div>
    );
  }

  // If error
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
        <div className="bg-white rounded-lg shadow-sm p-6 max-w-md w-full">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={fetchOrderDetails}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Try Again
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/orders')}
                  className="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Back to Orders
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If no order
  if (!order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
        <div className="bg-white rounded-lg shadow-sm p-6 max-w-md w-full">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-800">Order Not Found</h3>
              <div className="mt-2 text-sm text-gray-600">
                <p>The order you're looking for could not be found.</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => navigate('/orders')}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Back to Orders
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8"
    >
      <div className="mb-6">
        <button
          type="button"
          onClick={() => navigate('/orders')}
          className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800"
        >
          <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Orders
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-bold text-gray-900">
              Order #{order.id}
            </h1>
            <div>
              {getStatusBadge(order.status)}
            </div>
          </div>
        </div>

        <div className="px-6 py-5">
          {/* Payment Status */}
          <OrderPaymentStatus
            order={order}
            onPaymentSuccess={handlePaymentSuccess}
            onTopUpClick={handleTopUpClick}
          />

          {/* Order Details */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Order Details</h2>
            </div>

            <div className="px-6 py-4">
              <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Service</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.service_name || 'N/A'}</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Price</dt>
                  <dd className="mt-1 text-sm text-gray-900 font-semibold">{order.price || order.amount || 0} Credits</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Created At</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(order.created_at)}</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Updated At</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(order.updated_at)}</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Customer</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.customer?.name || 'N/A'}</dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Talent</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.talent?.name || 'N/A'}</dd>
                </div>

                {order.notes && (
                  <div className="col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Notes</dt>
                    <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{order.notes}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Payment History */}
          <OrderPaymentHistory
            orderId={order.id}
            orderType="talent_order"
          />
        </div>
      </div>

      {/* Insufficient Balance Modal */}
      <InsufficientBalanceModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        requiredAmount={order.price || order.amount || 0}
        currentBalance={0} // This will be fetched by the modal
        onTopUpSuccess={handleTopUpSuccess}
      />
    </motion.div>
  );
};

export default OrderDetails;
