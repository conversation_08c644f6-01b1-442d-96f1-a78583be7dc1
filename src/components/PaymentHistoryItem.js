import React from 'react';
import PaymentRetryButton from './PaymentRetryButton';

/**
 * PaymentHistoryItem component
 * 
 * Displays a payment transaction in the payment history list
 * with retry functionality for failed payments
 * 
 * @param {Object} props
 * @param {Object} props.transaction - The transaction object
 * @param {Function} props.onRetrySuccess - Function to call when payment retry is successful
 */
const PaymentHistoryItem = ({ transaction, onRetrySuccess }) => {
  if (!transaction) return null;
  
  // Determine transaction type styling
  const getTypeConfig = (type) => {
    switch (type) {
      case 'add':
        return {
          bgColor: 'bg-green-50',
          textColor: 'text-green-700',
          icon: (
            <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          ),
          label: 'Added'
        };
      case 'deduct':
        return {
          bgColor: 'bg-red-50',
          textColor: 'text-red-700',
          icon: (
            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4" />
            </svg>
          ),
          label: 'Deducted'
        };
      case 'hold':
        return {
          bgColor: 'bg-yellow-50',
          textColor: 'text-yellow-700',
          icon: (
            <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          ),
          label: 'On Hold'
        };
      case 'release':
        return {
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          icon: (
            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
            </svg>
          ),
          label: 'Released'
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          icon: (
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          label: 'Transaction'
        };
    }
  };
  
  // Get transaction status
  const getTransactionStatus = (transaction) => {
    // Check metadata for payment status
    if (transaction.metadata && transaction.metadata.payment_status) {
      return transaction.metadata.payment_status;
    }
    
    // Check if this is a failed payment
    if (transaction.metadata && transaction.metadata.payment_error) {
      return 'failed';
    }
    
    // Default to success for completed transactions
    return 'success';
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Format credits amount with sign
  const formatCredits = (credits) => {
    if (credits > 0) {
      return `+${credits}`;
    } else {
      return credits.toString();
    }
  };
  
  const typeConfig = getTypeConfig(transaction.transaction_type);
  const status = getTransactionStatus(transaction);
  const isFailedPayment = status === 'failed' && transaction.metadata && transaction.metadata.payment_transaction_id;
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-4">
      <div className="flex justify-between items-start">
        <div className="flex items-start">
          <div className={`${typeConfig.bgColor} p-2 rounded-full mr-3`}>
            {typeConfig.icon}
          </div>
          
          <div>
            <div className="flex items-center">
              <h3 className="font-medium text-gray-900">{transaction.description}</h3>
              {isFailedPayment && (
                <span className="ml-2 bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full">
                  Failed
                </span>
              )}
            </div>
            
            <p className="text-xs text-gray-500 mt-1">
              {formatDate(transaction.created_at)}
            </p>
            
            {transaction.metadata && transaction.metadata.payment_transaction_id && (
              <p className="text-xs text-gray-500 mt-1">
                Transaction ID: {transaction.metadata.payment_transaction_id}
              </p>
            )}
          </div>
        </div>
        
        <div className="text-right">
          <div className={`font-bold ${transaction.credits > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {formatCredits(transaction.credits)}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Balance: {transaction.balance_after}
          </div>
        </div>
      </div>
      
      {/* Show retry button for failed payments */}
      {isFailedPayment && (
        <div className="mt-3 pt-3 border-t border-gray-100 flex justify-end">
          <PaymentRetryButton
            transactionId={transaction.metadata.payment_transaction_id}
            buttonText="Retry Payment"
            buttonClassName="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onSuccess={onRetrySuccess}
          />
        </div>
      )}
    </div>
  );
};

export default PaymentHistoryItem;
