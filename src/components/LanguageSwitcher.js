import React from 'react';
import EnhancedLanguageSwitcher from './common/EnhancedLanguageSwitcher';

/**
 * Main language switcher component for the application
 * Uses the EnhancedLanguageSwitcher component with application-specific styling
 *
 * @param {Object} props - Component props
 * @param {string} props.position - Position of the language switcher ('fixed' or 'inline')
 * @param {string} props.variant - Variant of the language switcher ('minimal', 'compact', 'full')
 * @param {boolean} props.showFlags - Whether to show language flags
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.dropDirection - Direction for the dropdown ('up' or 'down')
 */
const LanguageSwitcher = ({
  position = 'fixed',
  variant = 'full',
  showFlags = true,
  className = '',
  dropDirection = 'auto', // 'auto', 'up', or 'down'
}) => {
  // Determine container classes based on position
  let containerClasses = '';

  if (position === 'fixed') {
    containerClasses = 'fixed bottom-4 left-4 z-50';
  } else if (position === 'header') {
    containerClasses = 'relative';
  } else {
    containerClasses = 'relative';
  }

  // For fixed bottom position, default to 'up' direction
  const finalDropDirection =
    dropDirection === 'auto' && position === 'fixed' ? 'up' : dropDirection;

  return (
    <div className={`${containerClasses} ${className}`} data-drop-direction={finalDropDirection}>
      <EnhancedLanguageSwitcher
        variant={variant}
        showFlags={showFlags}
        className={position === 'fixed' ? 'shadow-lg' : ''}
        dropDirection={finalDropDirection}
      />
    </div>
  );
};

export default LanguageSwitcher;
