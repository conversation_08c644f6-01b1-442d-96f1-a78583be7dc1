import React from 'react';

const tabs = [
  { id: 'about', label: 'About' },
  { id: 'review', label: 'Reviews' },
  { id: 'skills', label: 'Skills' },
  { id: 'post', label: 'Posts' },
  { id: 'mission', label: 'Missions' },
];

const TabNavigation = ({ activeTab, onTabChange }) => {
  return (
    <nav className="w-full border-b border-gray-200 bg-white">
      <ul className="flex space-x-2 px-4">
        {tabs.map((tab) => (
          <li key={tab.id}>
            <button
              className={`px-4 py-2 text-sm font-semibold rounded-t-md focus:outline-none transition-colors duration-150
                ${activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 hover:bg-blue-200'
                  : 'text-gray-600 hover:text-blue-600 bg-transparent hover:bg-blue-50'}`}
              onClick={() => onTabChange(tab.id)}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.label}
            </button>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default TabNavigation;
