import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

const UserAvailabilityModal = ({ isOpen, onClose, availabilityData, availabilityRemarks }) => {
  const { t } = useTranslation('profile');
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 dark:bg-black/70"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative bg-white dark:bg-gray-900 rounded-2xl shadow-xl dark:shadow-indigo-900/30 w-full max-w-md sm:max-w-md max-w-full mx-2 p-0 overflow-y-auto max-h-[90vh] border border-gray-100 dark:border-gray-800"
          initial={{ scale: 0.97, y: 20, opacity: 0 }}
          animate={{ scale: 1, y: 0, opacity: 1 }}
          exit={{ scale: 0.97, y: 20, opacity: 0 }}
          onClick={e => e.stopPropagation()}
        >
          <motion.button
            onClick={onClose}
            className="absolute top-2 right-2 sm:top-4 sm:right-4 p-2 sm:p-3 bg-white/20 dark:bg-gray-800/40 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 shadow-sm hover:shadow-md z-20"
            whileHover={{ scale: 1.05, rotate: 90, backgroundColor: 'rgba(243,244,246,0.7)' }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.2 }}
            aria-label="Close availability modal"
          >
            <svg className="w-5 h-5 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </motion.button>
          <div className="p-4 sm:p-8 pt-10 sm:pt-12">
            <h2 className="text-xl sm:text-2xl font-semibold text-gray-900 dark:text-yellow-400 mb-6 text-center tracking-tight">{t('modals.availability.title')}</h2>
          {availabilityData && Array.isArray(availabilityData) && availabilityData.length > 0 ? (
              <div className="divide-y divide-gray-100 dark:divide-gray-800 rounded-xl overflow-hidden bg-white dark:bg-gray-900">
                    {availabilityData.map(day => {
                      const isAvailable = day.isAvailable ?? day.is_available;
                      const periods = Array.isArray(day.periods) ? day.periods : [];
                      return (
                  <div key={day.day} className="flex flex-col sm:flex-row items-start sm:items-center justify-between py-3 sm:py-4 px-1 sm:px-2 gap-2 sm:gap-0">
                    <span className="font-medium text-gray-700 dark:text-gray-200 w-full sm:w-1/4 text-sm sm:text-base">{day.day}</span>
                    <span className={`px-3 py-1 rounded-full text-xs font-semibold w-fit sm:w-1/4 text-center ${isAvailable ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500'}`}>
                    {isAvailable ? t('modals.availability.available') : t('modals.availability.notAvailable')}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-300 w-full sm:w-2/4 text-left sm:text-right break-words">
                      {isAvailable && periods.length > 0
                        ? periods.map(p => `${p.start_time || p.startTime}–${p.end_time || p.endTime}`).join(', ')
                        : '-'}
                                </span>
                  </div>
                      );
                              })}
                            </div>
                          ) : (
              <div className="text-gray-500 dark:text-gray-300 text-center py-12 text-base sm:text-lg font-semibold">{t('modals.availability.noData')}</div>
            )}
              {availabilityRemarks && (
              <div className="mt-6 sm:mt-8 p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg text-gray-700 dark:text-gray-200 text-sm border border-gray-100 dark:border-gray-700">
                <span className="block font-semibold text-gray-600 dark:text-yellow-400 mb-1">{t('modals.availability.remarks')}</span>
                {availabilityRemarks}
                </div>
              )}
            </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default UserAvailabilityModal; 