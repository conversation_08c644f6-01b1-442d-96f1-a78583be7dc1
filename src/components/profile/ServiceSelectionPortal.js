import ReactDOM from 'react-dom';

const ServiceSelectionPortal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return ReactDOM.createPortal(
    <div className="relative">
      {/* Animated gradient blobs for vibrancy (yellow to pink) */}
      <div className="absolute -top-12 -right-16 w-40 h-40 bg-gradient-to-br from-yellow-400/30 to-pink-400/30 rounded-full blur-2xl animate-pulse z-0 pointer-events-none" />
      <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-yellow-400/30 to-pink-400/30 rounded-full blur-xl animate-pulse delay-1000 z-0 pointer-events-none" />
      {/* Backdrop and modal content */}
      {children}
    </div>,
    document.body
  );
};

export default ServiceSelectionPortal; 