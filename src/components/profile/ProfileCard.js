import React from 'react';
import { FaGamepad } from 'react-icons/fa';

const getStatusColor = (status) => {
  if (!status) return null;
  // Hex color provided by backend e.g. "#FFFF00"
  if (typeof status === 'string' && status.startsWith('#')) {
    return status;
  }
  const map = {
    Red: '#ef4444',
    Green: '#22c55e',
    Yellow: '#facc15'
  };
  return map[status] || null;
};

const getStatusBadgeProps = (hex) => {
  switch (hex?.toUpperCase()) {
    case '#00FF00':
      return {
        color: 'bg-green-500',
        icon: <FaGamepad className="w-4 h-4 text-white" />,
        tooltip: 'Available',
      };
    case '#FFFF00':
      return {
        color: 'bg-yellow-400',
        icon: (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        ),
        tooltip: 'Busy/In Game',
      };
    case '#FF0000':
      return {
        color: 'bg-red-500',
        icon: (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 12.79A9 9 0 1111.21 3a7 7 0 009.79 9.79z" />
          </svg>
        ),
        tooltip: 'Offline',
      };
    default:
      return {
        color: 'bg-gray-400',
        icon: (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <text x="12" y="16" textAnchor="middle" fontSize="12" fill="white">?</text>
          </svg>
        ),
        tooltip: 'Unknown',
      };
  }
};

const ProfileCard = ({
  profileImage,
  displayName,
  handle,
  bio,
  levelNumber,
  gender,
  isOnline,
  followers,
  missionsCompleted,
  rating,
  reviewCount,
  isFollowing,
  onFollow,
  onMessage,
  onOrder,
  availabilityData,
  availabilityRemarks,
  availabilityIsAvailable,
  availabilityStatus,
  onShowAvailability
}) => {
  const statusColor = getStatusColor(availabilityStatus);
  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6 w-full sm:w-[340px] min-h-lg max-h-[800px] overflow-y-auto flex flex-col items-center -mt-24 border border-gray-100">
      <div className="relative">
        <img
          src={profileImage}
          alt={displayName}
          className="w-32 h-32 rounded-full border-4 border-white shadow-md object-cover object-center"
        />
        {/* Enhanced Status Indicator Badge - bottom right, based on backend hex code */}
        {availabilityStatus && (() => {
          const { color, icon, tooltip } = getStatusBadgeProps(availabilityStatus);
          return (
            <div className="absolute bottom-2 right-2 group">
              <span
                className={`w-7 h-7 rounded-full border-2 border-white flex items-center justify-center ${color} cursor-pointer`}
              >
                {icon}
              </span>
              {/* Tooltip on hover */}
              <div className="absolute bottom-9 right-1/2 translate-x-1/2 z-20 hidden group-hover:flex flex-col items-center">
                <div className="px-3 py-1 rounded bg-gray-900 text-white text-xs shadow-lg whitespace-nowrap">
                  {tooltip}
                </div>
                <div className="w-2 h-2 bg-gray-900 rotate-45 mt-[-4px]"></div>
              </div>
            </div>
          );
        })()}
      </div>
      <h2 className="mt-4 text-2xl font-bold text-gray-900 text-center">{displayName}</h2>
      <div className="flex items-center justify-around gap-2 mt-1">
        <span className={`text-lg ${gender === 'male' ? 'text-blue-600' : gender === 'female' ? 'text-pink-500' : 'text-purple-600'}`}>{gender === 'male' ? '♂' : gender === 'female' ? '♀' : '⚧'}</span>
        <span className="bg-blue-100 text-blue-700 text-xs font-semibold px-2 py-0.5 rounded-full ml-2 flex items-center gap-1">
          LV {levelNumber}
        </span>
        <span className="">
          <button
            className="ml-2 px-2 py-0.5 text-xs bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 transition"
            onClick={onShowAvailability}
            type="button"
          >
            View Availability
          </button>
        </span>
      </div>
      <p className="mt-3 text-gray-600 text-center text-sm">{bio || 'No bio available.'}</p>
      <div className="grid grid-cols-2 gap-4 mt-6 w-full">
        <div className="flex flex-col items-center">
          <span className="font-bold text-gray-900">{followers}</span>
          <span className="text-xs text-gray-500">Followers</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="font-bold text-gray-900">{missionsCompleted}</span>
          <span className="text-xs text-gray-500">Missions</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="font-bold text-gray-900">{rating}</span>
          <span className="text-xs text-gray-500">Rating</span>
        </div>
        <div className="flex flex-col items-center">
          <span className="font-bold text-gray-900">{reviewCount}</span>
          <span className="text-xs text-gray-500">Reviews</span>
        </div>
      </div>
      <div className="flex flex-col gap-3 mt-8 w-full">
        <button
          className="w-full px-6 py-3 text-base bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full font-semibold shadow-md hover:scale-105 active:scale-95 transition-all duration-200 focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none"
          onClick={onOrder}
          aria-label="Order a service from this talent"
          title="Order a service from this talent"
        >
          Order
        </button>
        <button
          className={`w-full px-6 py-3 text-base rounded-full font-semibold border shadow-sm transition-all duration-200 focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none hover:scale-105 active:scale-95 ${isFollowing ? 'bg-white text-blue-600 border-blue-600 hover:bg-blue-50' : 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'}`}
          onClick={() => onFollow(!isFollowing)}
          aria-label={isFollowing ? 'Unfollow this talent' : 'Follow this talent'}
          title={isFollowing ? 'Unfollow this talent' : 'Follow this talent'}
        >
          {isFollowing ? 'Unfollow' : 'Follow'}
        </button>
        <button
          className="w-full px-6 py-3 text-base bg-gradient-to-r from-gray-200 via-indigo-100 to-blue-100 text-gray-700 rounded-full font-semibold shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none"
          onClick={onMessage}
          aria-label="Send a message to this talent"
          title="Send a message to this talent"
        >
          Message
        </button>
      </div>
    </div>
  );
};

export default ProfileCard; 