import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ServiceSelectionModal from './ServiceSelectionModal';
import DateTimeSelectionModal from './DateTimeSelectionModal';
import PlaceOrderPage from './PlaceOrderPage';
import ServiceSelectionPortal from './ServiceSelectionPortal';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * SkillsTab component displays the talent's skills and services
 */
const SkillsTab = ({ talent, onOrder, onViewOrders, onOpenServiceModal, isOwnProfile }) => {
  const [showAllSkills, setShowAllSkills] = useState(false);
  const [selectedTier, setSelectedTier] = useState(null);
  const [selectedDateTime, setSelectedDateTime] = useState(null);
  const [showDateTimeModal, setShowDateTimeModal] = useState(false);
  const [showPlaceOrderPage, setShowPlaceOrderPage] = useState(false);
  // Local state for service modal logic
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [showServiceModal, setShowServiceModal] = useState(false);
  // 3D tilt and expand/flip effect for skill cards
  const [hoveredSkillId, setHoveredSkillId] = useState(null);

  // Helper to transform a skill into the expected format with a 'tiers' array
  function toSkillWithTiers(skill) {
    return {
      ...skill,
      tiers: skill.rates
        ? skill.rates.map((rate, idx) => ({
            id: skill.styles && skill.styles[idx] ? skill.styles[idx].id : idx + 1,
            name: rate.type,
            price: rate.price,
            unit: rate.unit,
            description: rate.description,
            service_style_id: skill.styles && skill.styles[idx] ? skill.styles[idx].id : undefined,
            pricing_option_type_id: rate.pricing_option_type_id !== undefined ? rate.pricing_option_type_id : undefined,
          }))
        : [],
      service_category_id: skill.service_category_id,
      pricing_option_type_id: skill.pricing_option_type_id,
      talent_id: skill.talent_id || talent?.id || talent?.talent_id,
    };
  }

  // Handle the case when a skill is passed from the parent component (via onOrder)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const autoOpenOrder = urlParams.get('order');

    if (autoOpenOrder === 'true' && talent && talent.skills && talent.skills.length > 0) {
      const firstSkill = talent.skills[0];
      const skillWithTiers = {
        ...firstSkill,
        tiers: firstSkill.rates
          ? firstSkill.rates.map((rate, index) => ({
              id: index + 1,
              name: rate.type,
              price: rate.price,
              unit: rate.unit,
              description: rate.description,
              service_style_id:
                firstSkill.styles && firstSkill.styles[index]
                  ? firstSkill.styles[index].id
                  : undefined,
              pricing_option_type_id:
                rate.pricing_option_type_id || firstSkill.pricing_option_type_id,
            }))
          : [],
        service_category_id: firstSkill.service_category_id,
        pricing_option_type_id: firstSkill.pricing_option_type_id,
        talent_id: talent.id || talent.talent_id,
      };

      setSelectedSkill(skillWithTiers);
      if (onOpenServiceModal) {
        onOpenServiceModal(skillWithTiers);
      }

      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('order');
      window.history.replaceState({}, '', newUrl);
    }
  }, [talent, onOpenServiceModal]);

  // Default values in case talent data is incomplete
  const { skills = [] } = talent || {};

  // Deduplicate skills by service type id to avoid counting
  // multiple tiers of the same service as individual services
  const uniqueSkills = React.useMemo(() => {
    const map = new Map();
    skills.forEach(skill => {
      const key =
        skill.service_type_id ??
        skill.serviceTypeId ??
        skill.service_type?.id ??
        skill.id;
      if (!map.has(key)) {
        map.set(key, skill);
      }
    });
    return Array.from(map.values());
  }, [skills]);

  // Determine if we need to show "See All" button
  const hasMoreSkills = uniqueSkills.length > 3;

  // Get skills to display (all or just first 3)
  const displayedSkills = showAllSkills ? uniqueSkills : uniqueSkills.slice(0, 3);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  // Handle order button click for a specific skill and rate
  const handleOrderClick = (skill, rate, rateIndex) => {
    const matchingStyle = skill.styles && skill.styles[rateIndex];
    const skillWithTiers = {
      ...skill,
      tiers: skill.rates
        ? skill.rates.map((rate, idx) => ({
            id: skill.styles && skill.styles[idx] ? skill.styles[idx].id : idx + 1,
            name: rate.type,
            price: rate.price,
            unit: rate.unit,
            description: rate.description,
            service_style_id: skill.styles && skill.styles[idx] ? skill.styles[idx].id : undefined,
            pricing_option_type_id:
              rate.pricing_option_type_id || skill.pricing_option_type_id,
          }))
        : [],
      service_style_id:
        (matchingStyle && matchingStyle.id) ||
        rate?.service_style_id ||
        rate?.id,
      service_category_id: skill.service_category_id,
      pricing_option_type_id: skill.pricing_option_type_id,
      talent_id: skill.talent_id || talent.talent_id || talent.id,
    };
    if (onOpenServiceModal) {
      onOpenServiceModal(skillWithTiers);
    }
  };

  // Handle service selection
  const handleServiceSelect = (skill, tier) => {
    setSelectedTier(tier);
    setShowDateTimeModal(true);
  };

  // Handle "Order For Now" click
  const handleOrderNow = (skill, tier) => {
    setSelectedTier(tier);
    setSelectedDateTime(null);
    setShowDateTimeModal(false);
    setShowPlaceOrderPage(true);
  };

  // Handle date/time selection
  const handleDateTimeSelect = (skill, tier, dateTime) => {
    setSelectedDateTime(dateTime);
    setShowDateTimeModal(false);
    setShowPlaceOrderPage(true);
  };

  // Handle back button click in place order page
  const handlePlaceOrderBack = () => {
    setShowPlaceOrderPage(false);
    if (selectedDateTime) {
      setShowDateTimeModal(true);
    } else {
      // If no date/time selected, maybe open the service modal again or do nothing
      // For now, we'll just close the place order page
    }
  };

  // Handle order placed
  const handleOrderPlaced = (order) => {
    setSelectedSkill(null);
    setSelectedTier(null);
    setSelectedDateTime(null);
    setShowPlaceOrderPage(false);
    if (onOrder) {
      onOrder(order);
    }
  };

  // If showing place order page, render it
  if (showPlaceOrderPage) {
    return (
      <PlaceOrderPage
        talent={talent}
        skill={selectedSkill}
        tier={selectedTier}
        dateTime={selectedDateTime}
        onBack={handlePlaceOrderBack}
        onOrderPlaced={handleOrderPlaced}
      />
    );
  }

  // Remove rates/tiers from main view, show only service types as cards
  return (
    <motion.div
      className="animate-fade-in bg-transparent"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header with skills count */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent dark:from-yellow-400 dark:to-pink-400">
            MY SERVICES
          </h2>
          <p className="text-sm text-left text-gray-500 dark:text-gray-300 mt-1">
            {uniqueSkills.length} {uniqueSkills.length === 1 ? 'Service Type' : 'Service Types'} Available
          </p>
        </div>
        {hasMoreSkills && (
          <motion.button
            onClick={() => setShowAllSkills(!showAllSkills)}
            className="text-indigo-600 dark:text-yellow-400 text-sm bg-transparent hover:bg-transparent dark:hover:bg-gray-800 hover:rounded-2xl font-medium hover:text-indigo-800 dark:hover:text-yellow-300 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {showAllSkills ? 'See Less' : 'See All'}
          </motion.button>
        )}
      </div>

      {/* Skills grid - box cards with maximized icons */}
      <AnimatePresence>
        <motion.div className="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-5 gap-6">
          {displayedSkills.map((skill) => {
            // Determine background image or fallback gradient
            const backgroundImage = skill.image
              ? `url(${getCdnUrl(skill.image)})`
              : skill.service_category?.icon_path
                ? `url(${getCdnUrl(skill.service_category.icon_path)})`
                : 'linear-gradient(135deg, #7F00FF 0%, #E100FF 100%)';
            const isHovered = hoveredSkillId === skill.id;
            return (
              <motion.div
                key={skill.id}
                className="relative group rounded-2xl shadow-lg dark:shadow-indigo-900/30 hover:shadow-2xl dark:hover:shadow-indigo-900/40 overflow-hidden min-w-[150px] max-w-[170px] h-[200px] bg-cover bg-center flex flex-col justify-end cursor-pointer transition-all duration-300 dark:border dark:border-gray-800"
                style={{ backgroundImage, perspective: 800 }}
                variants={itemVariants}
                whileHover={{ y: -4, scale: 1.07 }}
                onMouseEnter={() => setHoveredSkillId(skill.id)}
                onMouseLeave={() => setHoveredSkillId(null)}
                animate={isHovered ? { rotateX: 8, rotateY: -8, z: 20, scale: 1.09 } : { rotateX: 0, rotateY: 0, z: 0, scale: 1 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                onClick={() => {
                  if (isOwnProfile) return;
                  setSelectedSkill(toSkillWithTiers(skill));
                  setShowServiceModal(true);
                }}
                aria-disabled={isOwnProfile}
                title={isOwnProfile ? 'You cannot book your own service' : undefined}
              >
                {/* Animated gradient blob decorations */}
                <div className="absolute -top-8 -right-8 w-16 h-16 bg-gradient-to-br from-blue-400/30 to-indigo-400/30 dark:from-yellow-400/30 dark:to-pink-400/30 rounded-full blur-2xl animate-pulse z-0" />
                <div className="absolute -bottom-8 -left-8 w-12 h-12 bg-gradient-to-br from-pink-400/30 to-blue-400/30 dark:from-pink-400/30 dark:to-yellow-400/30 rounded-full blur-xl animate-pulse delay-1000 z-0" />
                {/* Glassmorphism overlay for text area */}
                <div className="relative z-10 p-2 flex flex-col items-center justify-end h-full transition-all duration-300">
                  <div className="w-full rounded-xl bg-white/30 dark:bg-gray-900/60 backdrop-blur-md px-2 py-2 shadow-lg dark:shadow-indigo-900/30">
                    <h3 className="font-extrabold text-base bg-gradient-to-r from-indigo-200 via-white to-blue-200 dark:from-yellow-200 dark:via-pink-200 dark:to-indigo-200 bg-clip-text text-transparent drop-shadow-lg uppercase text-center">
                      {skill.service_category?.slug?.toLowerCase() === 'others'
                        ? skill.service_type_title || skill.title || skill.name
                        : skill.title || skill.name}
                    </h3>
                  </div>
                  {/* Reveal/expand on hover */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={isHovered ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                    className="absolute left-0 right-0 bottom-0 px-3 py-4 bg-white/80 dark:bg-gray-900/90 backdrop-blur-lg rounded-b-2xl shadow-lg dark:shadow-indigo-900/30 flex flex-col items-center gap-2"
                    style={{ pointerEvents: isHovered ? 'auto' : 'none' }}
                  >
                    {skill.description && (
                      <p className="text-xs text-gray-700 dark:text-gray-200 text-center mb-1 line-clamp-3">{skill.description}</p>
                    )}
                    {skill.tiers && skill.tiers.length > 0 && (
                      <span className="text-xs font-semibold text-indigo-600 dark:text-yellow-400">
                        From {skill.tiers[0].price} {skill.tiers[0].unit}
                      </span>
                    )}
                    <button
                      className="mt-2 px-4 py-1.5 bg-gradient-to-r from-indigo-500 to-blue-500 dark:from-yellow-500 dark:to-pink-500 text-white dark:text-gray-900 rounded-full font-bold shadow dark:shadow-yellow-400/30 hover:from-indigo-600 hover:to-blue-600 dark:hover:from-yellow-400 dark:hover:to-pink-400 hover:scale-105 active:scale-95 transition-all text-xs"
                      onClick={e => {
                        e.stopPropagation();
                        if (isOwnProfile) return;
                        setSelectedSkill(toSkillWithTiers(skill));
                        setShowServiceModal(true);
                      }}
                      disabled={isOwnProfile}
                      aria-disabled={isOwnProfile}
                      title={isOwnProfile ? 'You cannot book your own service' : 'Book Now'}
                    >
                      Book Now
                    </button>
                  </motion.div>
                </div>
                {/* Glow border on hover */}
                <div className="absolute inset-0 rounded-2xl pointer-events-none group-hover:ring-2 group-hover:ring-blue-400/60 dark:group-hover:ring-yellow-400/60 transition-all duration-300" />
              </motion.div>
            );
          })}
        </motion.div>
      </AnimatePresence>

      {/* Empty state */}
      {skills.length === 0 && (
        <motion.div
          className="text-center py-12 bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-white/30 dark:border-gray-800"
          variants={itemVariants}
        >
          <svg
            className="w-16 h-16 text-gray-300 dark:text-gray-700 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-yellow-400 mb-2">No Skills Available</h3>
          <p className="text-gray-500 dark:text-gray-300">This talent hasn't listed any skills yet.</p>
        </motion.div>
      )}

      {/* ServiceSelectionPortal and ServiceSelectionModal */}
      <ServiceSelectionPortal isOpen={showServiceModal && !!selectedSkill} onClose={() => { setShowServiceModal(false); setSelectedSkill(null); }}>
        <ServiceSelectionModal
          isOpen={showServiceModal && !!selectedSkill}
          onClose={() => { setShowServiceModal(false); setSelectedSkill(null); }}
          skill={selectedSkill}
          availabilityData={talent?.availabilityData}
          onOrderPlaced={(order) => {
            setShowServiceModal(false);
            setSelectedSkill(null);
            if (typeof onOrderPlaced === 'function') {
              onOrderPlaced(order);
            }
          }}
          onViewOrders={() => { setShowServiceModal(false); setSelectedSkill(null); }}
        />
      </ServiceSelectionPortal>
    </motion.div>
  );
};

export default SkillsTab;
