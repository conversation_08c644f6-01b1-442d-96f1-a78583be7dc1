import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * ReviewTab component displays reviews for a talent
 */
const ReviewTab = ({ talent }) => {
  const [showAllReviews, setShowAllReviews] = useState(false);
  
  // Default values in case talent data is incomplete
  const {
    reviews = [],
    rating,
    averageRating,
    average_rating,
    reviewCount,
    review_count,
    reviews_count,
    user = {},
  } = talent || {};

  const userAverageRating =
    user.averageRating ?? user.average_rating ?? undefined;
  const userReviewsCount =
    user.reviewsCount ?? user.reviews_count ?? user.review_count;

  const parsedRating =
    typeof rating === 'number' ? rating : parseFloat(rating) || 0;
  const parsedAverageRating =
    typeof averageRating === 'number'
      ? averageRating
      : parseFloat(averageRating);
  const parsedSnakeAverageRating =
    typeof average_rating === 'number'
      ? average_rating
      : parseFloat(average_rating);
  const parsedUserAverageRating =
    typeof userAverageRating === 'number'
      ? userAverageRating
      : parseFloat(userAverageRating);
  const parsedReviewCount =
    typeof reviewCount === 'number' ? reviewCount : parseInt(reviewCount, 10) || 0;
  const parsedReview_count =
    typeof review_count === 'number' ? review_count : parseInt(review_count, 10);
  const parsedReviews_count =
    typeof reviews_count === 'number'
      ? reviews_count
      : parseInt(reviews_count, 10);
  const parsedUserReviewsCount =
    typeof userReviewsCount === 'number'
      ? userReviewsCount
      : parseInt(userReviewsCount, 10);

  const overallRating =
    parsedSnakeAverageRating ||
    parsedAverageRating ||
    parsedUserAverageRating ||
    parsedRating;

  const totalReviews =
    typeof parsedReviews_count === 'number' && !Number.isNaN(parsedReviews_count)
      ? parsedReviews_count
      : typeof parsedReview_count === 'number' && !Number.isNaN(parsedReview_count)
        ? parsedReview_count
        : typeof parsedUserReviewsCount === 'number' && !Number.isNaN(parsedUserReviewsCount)
          ? parsedUserReviewsCount
          : parsedReviewCount;
  
  // Determine if we need to show "See All" button
  const hasMoreReviews = reviews.length > 3;
  
  // Get reviews to display (all or just first 3)
  const displayedReviews = showAllReviews ? reviews : reviews.slice(0, 3);
  
  // Toggle showing all reviews
  const toggleShowAllReviews = () => {
    setShowAllReviews(!showAllReviews);
  };
  
  // Generate star rating display
  const renderStars = (rating) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`w-4 h-4 ${i < rating ? 'text-yellow-500' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Rating Summary Card */}
      <motion.div
        className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg dark:shadow-indigo-900/30 border border-white/30 dark:border-gray-800"
        variants={itemVariants}
      >
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-center md:text-left">
            <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent">
              OVERALL RATING
            </h2>
            <div className="flex items-center justify-center md:justify-start gap-2">
              <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">{
                Number.isFinite(overallRating)
                  ? overallRating.toFixed(1)
                  : '0.0'
              }</span>
              <div className="flex">
                {renderStars(overallRating)}
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{totalReviews} reviews</p>
          </div>
          <div className="w-full md:w-auto">
            <div className="grid grid-cols-5 gap-2">
              {[5, 4, 3, 2, 1].map((star) => {
                const count = reviews.filter(
                  (review) => Math.round(review.rating) === star
                ).length;
                const percentage = totalReviews
                  ? (count / totalReviews) * 100
                  : 0;
                return (
                  <div key={star} className="text-center">
                    <div className="text-sm font-medium text-gray-600 dark:text-gray-300">{star}</div>
                    <div className="h-2 bg-gray-200 dark:bg-gray-800 rounded-full mt-1 overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-yellow-400 to-yellow-500 dark:from-yellow-500 dark:to-yellow-400"
                        initial={{ width: 0 }}
                        animate={{ width: `${percentage}%` }}
                        transition={{ duration: 1, delay: 0.2 }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{count}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Reviews List - Single Column Layout */}
      <AnimatePresence mode="wait">
        <motion.div
          className="flex flex-col gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {reviews.length > 0 ? (
            <>
              {displayedReviews.map((review, index) => {
                const reviewer = review.user || review.reviewer || {};
                const profileImage = getCdnUrl(
                  reviewer.profile_image ||
                    reviewer.profile_picture ||
                    reviewer.profileImage ||
                    ''
                );
                const createdAt = review.created_at || review.createdAt;
                const comment = review.comment ?? review.review_text ?? '';
                return (
                  <motion.div
                    key={review.id || index}
                  className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-lg rounded-2xl p-6 shadow-xl dark:shadow-indigo-900/30 border border-white/30 dark:border-gray-800 hover:shadow-2xl dark:hover:shadow-indigo-900/40 transition-all duration-300 flex flex-col gap-4 relative group"
                  variants={itemVariants}
                  whileHover={{ y: -4, scale: 1.03 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {/* Top Review Badge */}
                  <span className="absolute top-4 right-4 z-20">
                    <span className="inline-flex items-center px-4 py-1.5 rounded-full bg-gradient-to-r from-yellow-400 via-pink-400 to-indigo-400 text-white font-bold text-xs shadow-lg uppercase tracking-wide border-2 border-white/40 backdrop-blur-md dark:border-gray-800">
                      <svg className="w-4 h-4 mr-1 text-white opacity-90" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 17.75L18.2 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.44 4.73L5.8 21z" />
                      </svg>
                      Top Review
                    </span>
                  </span>
                  <div className="flex items-center gap-4 mb-2">
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-indigo-100 to-blue-100 dark:from-indigo-900 dark:to-blue-900 border-2 border-indigo-200 dark:border-indigo-700 shadow-md">
                        {profileImage ? (
                          <img
                            src={profileImage}
                            alt={reviewer.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-indigo-500 to-blue-500 dark:from-indigo-900 dark:to-blue-900 text-white font-bold text-2xl">
                            {reviewer?.name?.charAt(0).toUpperCase() || '?'}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col justify-center items-start text-left">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-lg mb-0.5">{reviewer?.name || 'Anonymous'}</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {createdAt ? new Date(createdAt).toLocaleDateString() : 'Unknown date'}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        {renderStars(review.rating)}
                        <span className="ml-1 text-xs text-yellow-500 font-bold">{review.rating.toFixed(1)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-indigo-50 via-white to-blue-50 dark:from-gray-800 dark:via-gray-900 dark:to-blue-900 rounded-xl p-4 text-gray-700 dark:text-gray-200 text-sm leading-relaxed shadow-inner border border-indigo-50 dark:border-gray-800">
                    {comment}
                  </div>
                </motion.div>
                );
              })}
            </>
          ) : (
            <motion.div
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg rounded-2xl p-8 text-center border border-white/30 dark:border-gray-800"
              variants={itemVariants}
            >
              <div className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-700">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Reviews Yet</h3>
              <p className="text-gray-500 dark:text-gray-300">Be the first to review this talent!</p>
            </motion.div>
          )}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
};

export default ReviewTab;
