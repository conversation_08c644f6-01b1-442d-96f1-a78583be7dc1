import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * MissionTab component displays the talent's missions
 */
const MissionTab = ({ talent }) => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [showAllMissions, setShowAllMissions] = useState(false);
  
  // Default values in case talent data is incomplete
  const {
    missions = [],
    hostedMissions = []
  } = talent || {};
  
  // Combine all missions
  const allMissions = [...(missions || []), ...(hostedMissions || [])];
  
  // Filter missions based on active filter
  const filteredMissions = activeFilter === 'all' 
    ? allMissions
    : activeFilter === 'hosted' 
      ? hostedMissions
      : missions;

  // Determine if we need to show "See More" button
  const hasMoreMissions = filteredMissions.length > 3;
  // Get missions to display (all or just first 3)
  const displayedMissions = showAllMissions ? filteredMissions : filteredMissions.slice(0, 3);

  // Timeline for completed missions
  const completedMissions = filteredMissions.filter(m => m.status === 'completed');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  // Handle filter change
  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  // Get status color and icon
  const getStatusInfo = (status) => {
    switch (status) {
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          )
        };
      case 'in_progress':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        };
    }
  };

  // Get mission type color and icon
  const getMissionTypeInfo = (isHosted) => {
    return isHosted
      ? {
          color: 'bg-purple-100 text-purple-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          )
        }
      : {
          color: 'bg-blue-100 text-blue-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          )
        };
  };
  
  // Render a single mission
  const renderMission = (mission) => {
    const statusInfo = getStatusInfo(mission.status);
    const missionTypeInfo = getMissionTypeInfo(mission.isHosted);
    
    return (
      <motion.div
        key={mission.id}
        variants={itemVariants}
        className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-4 hover:shadow-md transition-all duration-300"
      >
        {/* Mission header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${missionTypeInfo.color}`}>
                {missionTypeInfo.icon}
                <span className="ml-1">{mission.isHosted ? 'Hosted' : 'Participated'}</span>
              </span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}>
                {statusInfo.icon}
                <span className="ml-1">
                  {mission.status === 'completed' 
                    ? 'Completed' 
                    : mission.status === 'in_progress'
                      ? 'In Progress'
                      : 'Pending'}
                </span>
              </span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{mission.title}</h3>
            <p className="text-sm text-gray-500">{mission.date}</p>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-green-600">{mission.reward} Credits</div>
            <div className="text-sm text-gray-500">{mission.duration}</div>
          </div>
        </div>
        
        {/* Mission description */}
        <p className="text-gray-700 mb-4 line-clamp-2">{mission.description}</p>
        
        {/* Mission progress */}
        {mission.status === 'in_progress' && mission.progress && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progress</span>
              <span>{mission.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${mission.progress}%` }}
              />
            </div>
          </div>
        )}
        
        {/* Mission footer */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-100">
          <div className="flex items-center">
            <div className="flex -space-x-2">
              {mission.participants && mission.participants.slice(0, 3).map((participant, index) => (
                <motion.div
                  key={index}
                  className="w-8 h-8 rounded-full border-2 border-white overflow-hidden"
                  whileHover={{ scale: 1.1, zIndex: 1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <img 
                    src={participant.image} 
                    alt={participant.name} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'https://via.placeholder.com/32?text=User';
                    }}
                  />
                </motion.div>
              ))}
              {mission.participants && mission.participants.length > 3 && (
                <div className="w-8 h-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs text-gray-600">
                  +{mission.participants.length - 3}
                </div>
              )}
            </div>
            <span className="text-sm text-gray-500 ml-2">
              {mission.participants ? mission.participants.length : 0} Participants
            </span>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors"
          >
            View Details
          </motion.button>
        </div>
      </motion.div>
    );
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="animate-fade-in bg-transparent"
    >
      {/* Header with filter tabs */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent dark:from-yellow-400 dark:to-pink-400">MY MISSIONS</h2>
          <div className="text-sm text-gray-500 dark:text-gray-300">
            {filteredMissions.length} {filteredMissions.length === 1 ? 'Mission' : 'Missions'}
          </div>
        </div>
        
        <div className="bg-gray-100 dark:bg-gray-900 p-1 rounded-xl">
          <div className="flex">
            {['all', 'hosted', 'participated'].map((filter) => (
              <motion.button
                key={filter}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`flex-1 py-2 px-4 text-sm bg-transparent dark:bg-transparent hover:bg-gray-200 dark:hover:bg-gray-800 font-medium rounded-lg transition-all duration-200 ${
                  activeFilter === filter 
                    ? 'bg-white dark:bg-gray-800 shadow-sm text-blue-600 dark:text-yellow-400' 
                    : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white'
                }`}
                onClick={() => handleFilterChange(filter)}
              >
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
              </motion.button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Missions list */}
      <AnimatePresence mode="wait">
        {filteredMissions.length > 0 ? (
          <>
            {/* Timeline for completed missions */}
            {completedMissions.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-bold text-indigo-700 dark:text-yellow-400 mb-2 ml-2">Completed Missions Timeline</h3>
                <div className="relative pl-8">
                  {/* Vertical line */}
                  <div className="absolute left-3 top-0 bottom-0 w-1 bg-gradient-to-b from-indigo-400 via-blue-300 to-pink-300 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 rounded-full" style={{ minHeight: 40 * completedMissions.length }} />
                  {completedMissions.map((mission, idx) => (
                    <div key={mission.id} className="relative mb-8 flex items-center group">
                      {/* Timeline dot */}
                      <span className="absolute left-0 w-6 h-6 rounded-full bg-gradient-to-tr from-indigo-400 via-blue-400 to-pink-400 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 border-2 border-white dark:border-gray-900 shadow-lg flex items-center justify-center z-10">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" /></svg>
                      </span>
                      <div className="ml-8 flex-1">
                        <motion.div
                          className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-white/30 dark:border-gray-800 p-4 flex flex-col gap-2 hover:shadow-2xl dark:hover:shadow-indigo-900/40 transition-all duration-300 relative"
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 30 }}
                          whileHover={{ scale: 1.03, boxShadow: '0 8px 32px 0 rgba(99,102,241,0.12)' }}
                          transition={{ type: 'spring', stiffness: 200, damping: 20, delay: idx * 0.08 }}
                        >
                          <div className="flex items-center gap-2">
                            <span className="font-bold text-indigo-700 dark:text-yellow-400">{mission.title}</span>
                            <span className="text-xs text-gray-400 dark:text-gray-300">{mission.date}</span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-200 line-clamp-2">{mission.description}</p>
                        </motion.div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {/* Animated mission cards */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8"
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={{
                hidden: {},
                visible: {
                  transition: { staggerChildren: 0.09 }
                }
              }}
            >
              {displayedMissions.map((mission) => (
                <motion.div
                  key={mission.id}
                  className="group relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/20 dark:border-gray-800 rounded-2xl overflow-hidden shadow-lg dark:shadow-indigo-900/30 hover:shadow-2xl dark:hover:shadow-indigo-900/40 transition-all duration-500 transform hover:scale-105 cursor-pointer w-full h-full flex flex-col items-center justify-center min-h-[320px] p-8"
                  variants={itemVariants}
                  initial="hidden"
                  animate="visible"
                  exit={{ opacity: 0, y: -20 }}
                  layout
                  whileHover={{ scale: 1.04, boxShadow: '0 8px 32px 0 rgba(99,102,241,0.12)' }}
                >
                  {/* Hover overlay */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-indigo-100/30 to-pink-100/30 dark:from-indigo-900/30 dark:to-pink-900/30 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10 pointer-events-none"
                  />
                  {/* Mission Image */}
                  <div className="w-full flex justify-center mb-6">
                    {mission.image ? (
                      <img
                        src={mission.image}
                        alt={mission.title}
                        className="w-40 h-40 object-cover rounded-xl shadow-md border border-gray-200 dark:border-gray-800 bg-gray-100 dark:bg-gray-900"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = 'https://via.placeholder.com/160?text=Mission';
                        }}
                      />
                    ) : (
                      <div className="w-40 h-40 flex items-center justify-center rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 text-indigo-400 dark:text-yellow-400 text-5xl font-bold shadow-md border border-gray-200 dark:border-gray-800">
                        <svg className="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  {/* Mission Title */}
                  <h3 className="text-xl font-medium text-center text-gray-900 dark:text-yellow-400 line-clamp-2 mb-2">{mission.title}</h3>
                  {/* Progress bar for in-progress missions */}
                  {mission.status === 'in_progress' && mission.progress && (
                    <div className="w-full mt-2">
                      <div className="flex justify-between text-xs text-gray-600 dark:text-gray-300 mb-1">
                        <span>Progress</span>
                        <span>{mission.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2 overflow-hidden">
                        <motion.div
                          className="h-2 rounded-full bg-gradient-to-r from-yellow-400 via-pink-400 to-indigo-400 dark:from-yellow-500 dark:via-pink-500 dark:to-indigo-500"
                          initial={{ width: 0 }}
                          animate={{ width: `${mission.progress}%` }}
                          transition={{ duration: 0.7, ease: 'easeInOut' }}
                        />
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </motion.div>
            {hasMoreMissions && (
              <div className="flex justify-center mt-6">
                <motion.button
                  onClick={() => setShowAllMissions(!showAllMissions)}
                  className="text-indigo-600 dark:text-yellow-400 text-sm bg-transparent hover:bg-transparent hover:rounded-2xl font-medium hover:text-indigo-800 dark:hover:text-yellow-300 transition-colors px-6 py-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {showAllMissions ? 'See Less' : 'See More'}
                </motion.button>
              </div>
            )}
          </>
        ) : (
          <motion.div
            key={`empty-${activeFilter}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="text-center py-12 bg-gray-50 dark:bg-gray-900 rounded-xl"
          >
            <svg 
              className="w-16 h-16 text-gray-300 dark:text-gray-700 mx-auto mb-4" 
              fill="none" stroke="currentColor" viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <p className="text-gray-500 dark:text-gray-300 text-lg font-medium mb-2">No missions found</p>
            <p className="text-gray-400 dark:text-gray-400 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-sm max-w-md mx-auto">
              {activeFilter === 'all' 
                ? 'This talent has not participated in or hosted any missions yet' 
                : activeFilter === 'hosted'
                  ? 'This talent has not hosted any missions yet'
                  : 'This talent has not participated in any missions yet'}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default MissionTab;
