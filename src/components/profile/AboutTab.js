import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * AboutTab component displays the talent's personalities, and other personal information
 */
const AboutTab = ({ talent }) => {
  const [isPlayingVoiceNote, setIsPlayingVoiceNote] = useState(false);
  const [voiceNoteError, setVoiceNoteError] = useState(null);
  const audioRef = useRef(null);
  
  // Default values in case talent data is incomplete
  const {
    personalities = [],
    languages = [],
    race = '',
    constellation = '',
    height = 0,
    weight = 0,
    experience = 0,
    dateOfBirth = null,
    voiceNoteUrl = null
  } = talent || {};

  // Voice note handlers
  const handlePlayVoiceNote = () => {
    if (!voiceNoteUrl) {
      setVoiceNoteError('No voice note available');
      return;
    }

    // If already playing, stop current audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlayingVoiceNote(false);
      return;
    }

    setIsPlayingVoiceNote(true);
    setVoiceNoteError(null);

    // Create audio element and play
    const audio = new Audio(voiceNoteUrl);
    audioRef.current = audio;

    audio.onloadstart = () => {
      // Voice note starts loading
    };

    audio.oncanplay = () => {
      // Voice note ready to play
    };

    audio.onended = () => {
      setIsPlayingVoiceNote(false);
      audioRef.current = null;
    };

    audio.onerror = (e) => {
      console.error('Voice note playback error:', e);
      setIsPlayingVoiceNote(false);
      audioRef.current = null;
      setVoiceNoteError('Failed to play voice note');
    };

    audio.onpause = () => {
      setIsPlayingVoiceNote(false);
    };

    audio.play().catch((error) => {
      console.error('Error playing voice note:', error);
      setIsPlayingVoiceNote(false);
      audioRef.current = null;
      setVoiceNoteError('Failed to play voice note. Please check your internet connection.');
    });
  };

  const handleStopVoiceNote = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }
    setIsPlayingVoiceNote(false);
    setVoiceNoteError(null);
  };

  // Cleanup audio on component unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  // Calculate age from date of birth
  const calculateAge = (dob) => {
    if (!dob) return null;
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Voice Note Section */}
      {voiceNoteUrl && (
        <motion.div
          className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/30"
          variants={itemVariants}
        >
          <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-emerald-600 to-teal-500 bg-clip-text text-transparent">
            Voice Introduction
          </h2>
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={isPlayingVoiceNote ? handleStopVoiceNote : handlePlayVoiceNote}
              className={`p-4 rounded-full shadow-lg transition-all duration-300 ${
                isPlayingVoiceNote 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-emerald-500 hover:bg-emerald-600 text-white'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={voiceNoteError}
            >
              {isPlayingVoiceNote ? (
                <motion.svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 0.5, repeat: Infinity }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </motion.svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
            </motion.button>
            <div className="flex-1">
              <p className="text-gray-700 text-right font-medium">
                {isPlayingVoiceNote ? 'Playing voice note...' : 'Listen to voice introduction'}
              </p>
              <p className="text-gray-500 text-right text-sm">
                {voiceNoteError ? (
                  <span className="text-red-500">{voiceNoteError}</span>
                ) : (
                  'Click to play the talent\'s voice introduction'
                )}
              </p>
            </div>
            {isPlayingVoiceNote && (
              <motion.div
                className="flex space-x-1"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                <div className="w-2 h-2 bg-emerald-500 rounded-full" />
              </motion.div>
            )}
          </div>
        </motion.div>
      )}
      
      {/* Personalities Section */}
      <motion.div
        className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/30"
        variants={itemVariants}
      >
        <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-pink-600 to-rose-500 bg-clip-text text-transparent">
          Personality Traits
        </h2>
        {personalities.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {personalities.map((trait, index) => (
              <motion.span
                key={index}
                className="bg-gradient-to-r from-pink-100 to-rose-100 text-pink-800 text-xs px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {trait}
              </motion.span>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No personality traits listed</p>
        )}
      </motion.div>

      {/* Languages Section */}
      <motion.div
        className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/30"
        variants={itemVariants}
      >
        <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-indigo-500 bg-clip-text text-transparent">
          Languages
        </h2>
        {languages.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {languages.map((language, index) => (
              <motion.span
                key={index}
                className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-xs px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {language}
              </motion.span>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No languages listed</p>
        )}
      </motion.div>

      {/* Personal Information Section */}
      <motion.div
        className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/30"
        variants={itemVariants}
      >
        <h2 className="text-xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-indigo-500 bg-clip-text text-transparent">
          Personal Information
        </h2>
        <div className="grid grid-cols-2 gap-4">
          {race && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Race</p>
              <p className="text-gray-900 font-medium">{race}</p>
            </motion.div>
          )}
          {constellation && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Constellation</p>
              <p className="text-gray-900 font-medium">{constellation}</p>
            </motion.div>
          )}
          {dateOfBirth && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Age</p>
              <p className="text-gray-900 font-medium">{calculateAge(dateOfBirth)} years old</p>
            </motion.div>
          )}
          {height > 0 && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Height</p>
              <p className="text-gray-900 font-medium">{height} cm</p>
            </motion.div>
          )}
          {weight > 0 && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Weight</p>
              <p className="text-gray-900 font-medium">{weight} kg</p>
            </motion.div>
          )}
          {experience > 0 && (
            <motion.div
              className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-500 text-sm">Experience</p>
              <p className="text-gray-900 font-medium">{experience} hours</p>
            </motion.div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AboutTab;
