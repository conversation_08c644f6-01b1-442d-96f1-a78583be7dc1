import React from 'react';

/**
 * ReviewsTab component displays the talent's reviews and ratings
 */
const ReviewsTab = ({ talent }) => {
  // Extract values and ensure numeric types
  const {
    rating,
    averageRating,
    reviewCount,
    reviews = []
  } = talent || {};

  const parsedRating = Number(averageRating ?? rating);
  let overallRating = Number.isFinite(parsedRating) ? parsedRating : 0;

  // Fallback to calculating from reviews if rating missing
  if (!overallRating && reviews.length) {
    overallRating =
      reviews.reduce((sum, r) => sum + Number(r.rating || 0), 0) / reviews.length;
  }

  const totalReviews =
    Number.isFinite(Number(reviewCount)) && Number(reviewCount) > 0
      ? Number(reviewCount)
      : reviews.length;

  // Calculate rating percentage for each star using totalReviews
  const ratingPercentages = {
    5: (reviews.filter((r) => r.rating === 5).length / totalReviews) * 100 || 0,
    4: (reviews.filter((r) => r.rating === 4).length / totalReviews) * 100 || 0,
    3: (reviews.filter((r) => r.rating === 3).length / totalReviews) * 100 || 0,
    2: (reviews.filter((r) => r.rating === 2).length / totalReviews) * 100 || 0,
    1: (reviews.filter((r) => r.rating === 1).length / totalReviews) * 100 || 0,
  };

  // Format date to relative time
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 604800)}w ago`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}mo ago`;
    return `${Math.floor(diffInSeconds / 31536000)}y ago`;
  };

  return (
    <div className="animate-fade-in">
      {/* Rating summary */}
      <div className="bg-white rounded-xl p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="text-4xl font-bold text-gray-900 mr-4">{overallRating.toFixed(1)}</div>
            <div>
              <div className="flex items-center mb-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className={`w-5 h-5 ${
                      star <= Math.round(overallRating) ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-sm text-gray-600">{totalReviews} reviews</p>
            </div>
          </div>
        </div>

        {/* Rating breakdown */}
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((star) => (
            <div key={star} className="flex items-center">
              <span className="text-sm text-gray-600 w-8">{star} stars</span>
              <div className="flex-1 h-2 mx-4 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-yellow-400 rounded-full"
                  style={{ width: `${ratingPercentages[star]}%` }}
                />
              </div>
              <span className="text-sm text-gray-600 w-12 text-right">
                {Math.round(ratingPercentages[star])}%
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Reviews list */}
      <div className="space-y-6">
        {reviews.length > 0 ? (
          reviews.map((review) => {
            const reviewer = review.user || review.reviewer || {};
            const createdAt = review.created_at || review.createdAt;
            const comment = review.comment ?? review.review_text ?? '';
            return (
            <div key={review.id} className="bg-white rounded-xl p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <img
                    src={reviewer.profile_image || 'https://via.placeholder.com/40'}
                    alt={reviewer.name}
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  <div>
                    <h3 className="font-medium">{reviewer.name}</h3>
                    <div className="flex items-center">
                      <div className="flex mr-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`w-4 h-4 ${
                              star <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">
                        {formatDate(createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-gray-700">{comment}</p>
            </div>
            );
          })
        ) : (
          <p className="text-gray-500 text-center py-8">No reviews yet</p>
        )}
      </div>
    </div>
  );
};

export default ReviewsTab; 