import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const CoverImage = ({ coverMedia = [], alt, children }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [coverOffset, setCoverOffset] = useState(0);

  // Process cover media array
  const processedCoverMedia = coverMedia && coverMedia.length > 0 ? coverMedia : [{
    type: 'image',
    url: null,
    order: 1
  }];

  // Handle scroll for parallax effect
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.pageYOffset;
      setCoverOffset(offset * 0.5); // Parallax effect
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-advance carousel
  useEffect(() => {
    if (processedCoverMedia.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % processedCoverMedia.length);
      }, 5000); // Change every 5 seconds

      return () => clearInterval(interval);
    }
  }, [processedCoverMedia.length]);

  // Navigation functions
  const nextCoverMedia = () => {
    if (processedCoverMedia.length > 1) {
      setCurrentIndex((prev) => (prev + 1) % processedCoverMedia.length);
    }
  };

  const prevCoverMedia = () => {
    if (processedCoverMedia.length > 1) {
      setCurrentIndex((prev) => (prev - 1 + processedCoverMedia.length) % processedCoverMedia.length);
    }
  };

  return (
    <div className="relative h-[60vh] overflow-hidden">
      {/* Cover Photo Background with Parallax */}
      <motion.div
        className="absolute inset-0"
        style={{ y: coverOffset }}
      >
        {processedCoverMedia[currentIndex] ? (
          processedCoverMedia[currentIndex].type === 'video' ? (
            <video
              src={`${processedCoverMedia[currentIndex].url}?quality=high`}
              className="w-full h-full object-cover"
              autoPlay
              muted
              loop
              playsInline
              preload="auto"
              poster={
                processedCoverMedia[currentIndex].thumbnail
                  ? `${processedCoverMedia[currentIndex].thumbnail}?quality=high&w=1920&h=1080&fit=crop&auto=format`
                  : `${processedCoverMedia[currentIndex].url}?quality=high`
              }
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
          ) : (
            <img
              src={`${processedCoverMedia[currentIndex].url}?quality=high&w=1920&h=1080&fit=crop&auto=format`}
              alt={alt}
              className="w-full h-full object-cover"
              loading="eager"
              decoding="async"
              fetchpriority="high"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
          )
        ) : null}
        
        {/* Fallback gradient background */}
        <div
          className={`w-full h-full bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-800 ${processedCoverMedia[currentIndex]?.url ? 'hidden' : 'block'}`}
          style={{ display: processedCoverMedia[currentIndex]?.url ? 'none' : 'block' }}
        />

        {/* Simple overlay for text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/60" />

        {/* Bottom gradient for blending into TalentProfile background */}
        <div
          className="pointer-events-none absolute bottom-0 left-0 w-full h-32 z-0 dark:hidden"
          style={{ background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(236,233,255,0.7) 80%, #f5f3ff 100%)' }}
        />
        <div
          className="pointer-events-none absolute bottom-0 left-0 w-full h-32 z-0 hidden dark:block"
          style={{ background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(24,24,32,0.85) 80%, #09090b 100%)' }}
        />

        {/* Carousel Navigation Controls */}
        {processedCoverMedia.length > 1 && (
          <>
            {/* Previous Button */}
            <button
              onClick={prevCoverMedia}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-300 hover:scale-110"
              aria-label="Previous cover media"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Next Button */}
            <button
              onClick={nextCoverMedia}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-300 hover:scale-110"
              aria-label="Next cover media"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Media Counter */}
            <div className="absolute top-4 right-4 z-10 px-3 py-1 bg-black/50 text-white text-sm rounded-full">
              {currentIndex + 1} / {processedCoverMedia.length}
            </div>
          </>
        )}
      </motion.div>

      {/* Floating particles animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -120],
              opacity: [0, 1, 0],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Overlay children (e.g., ProfileCard) */}
      <div className="absolute left-8 bottom-0 z-10">
        {children}
      </div>
    </div>
  );
};

export default CoverImage; 