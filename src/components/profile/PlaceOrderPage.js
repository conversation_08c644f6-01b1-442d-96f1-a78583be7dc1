import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import walletAPI from '../../services/walletService';
import orderAPI from '../../services/orderService';
import InsufficientBalanceModal from '../InsufficientBalanceModal';

/**
 * PlaceOrderPage component
 * 
 * This page allows users to finalize order details and place an order.
 * 
 * @param {Object} props
 * @param {Object} props.talent - The talent providing the service
 * @param {Object} props.skill - The skill/service being ordered
 * @param {Object} props.tier - The selected service tier
 * @param {Date} props.dateTime - The selected date and time for the service
 * @param {Function} props.onBack - Function to call when the back button is clicked
 * @param {Function} props.onOrderPlaced - Function to call when the order is successfully placed
 */
const PlaceOrderPage = ({ 
  talent, 
  skill, 
  tier, 
  dateTime, 
  onBack, 
  onOrderPlaced 
}) => {
  const navigate = useNavigate();
  
  const [quantity, setQuantity] = useState(1);
  const [remarks, setRemarks] = useState('');
  const [walletBalance, setWalletBalance] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  
  // Calculate total price
  const totalPrice = tier ? tier.price * quantity : 0;
  
  // Check if user has sufficient balance
  const hasSufficientBalance = walletBalance >= totalPrice;
  
  // Fetch wallet balance on mount
  useEffect(() => {
    fetchWalletBalance();
  }, []);
  
  // Fetch wallet balance
  const fetchWalletBalance = async () => {
    try {
      const response = await walletAPI.getBalance();
      setWalletBalance(response.data.credits_balance);
    } catch (err) {
      console.error('Error fetching wallet balance:', err);
      setError('Failed to fetch wallet balance');
    }
  };
  
  // Handle quantity change
  const handleQuantityChange = (newQuantity) => {
    // Ensure quantity is at least 1
    const validQuantity = Math.max(1, newQuantity);
    setQuantity(validQuantity);
  };
  
  // Handle remarks change
  const handleRemarksChange = (e) => {
    // Limit remarks to 100 words
    const value = e.target.value;
    setRemarks(value);
  };
  
  // Handle top up click
  const handleTopUpClick = () => {
    setShowTopUpModal(true);
  };
  
  // Handle top up success
  const handleTopUpSuccess = () => {
    setShowTopUpModal(false);
    fetchWalletBalance();
  };
  
  // Handle place order click
  const handlePlaceOrder = async () => {
    if (!hasSufficientBalance) {
      setShowTopUpModal(true);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Prepare order data
      let pricingId =
        tier?.pricing_option_type_id ??
        skill?.pricing_option_type_id ??
        (Array.isArray(skill?.pricing_options)
          ? skill.pricing_options[0]?.id ??
            skill.pricing_options[0]?.pricing_option_type_id
          : skill?.pricing_options?.id ??
            skill?.pricing_options?.pricing_option_type_id);

      pricingId = Number(pricingId);

      const orderData = {
        talent_id: talent.id,
        user_service_id: skill.id,
        pricing_option_type_id: pricingId,
        service_category_id: skill.service_category_id,
        quantity,
        remarks: remarks.trim() || null
      };
      if (skill.service_category?.slug?.toLowerCase() !== 'others') {
        orderData.service_style_id = tier.service_style_id;
        orderData.service_type_id =
          skill.service_type_id ||
          skill.service_type?.id ||
          null;
      }

      if (dateTime) {
        orderData.scheduled_start_time = dateTime.toISOString();
      }

      // Use unified endpoint regardless of order type
      const response = await orderAPI.createOrder(orderData);

      // Call onOrderPlaced callback with the order data
      if (onOrderPlaced) {
        onOrderPlaced(response.data);
      }
      
      // Navigate to order details page
      navigate(`/orders/${response.data.id}`);
    } catch (err) {
      console.error('Error placing order:', err);
      
      // Check if error is due to insufficient balance
      if (err.response && err.response.status === 402) {
        setError('Insufficient balance. Please top up your wallet.');
        setShowTopUpModal(true);
      } else {
        setError(err.message || 'Failed to place order');
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Format date for display
  const formatDate = (date) => {
    if (!date) return 'As soon as possible';
    
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    };
    
    return date.toLocaleDateString(undefined, options);
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-2xl mx-auto py-8 px-4 sm:px-6 lg:px-8"
    >
      {/* Header */}
      <div className="mb-6">
        <button
          type="button"
          onClick={onBack}
          className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800"
        >
          <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back
        </button>
        <h1 className="mt-2 text-2xl font-bold text-gray-900">Place Order</h1>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Order details */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Order Details</h2>
        </div>
        
        <div className="px-4 py-5 sm:p-6">
          {/* Service details */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500">Service Details</h3>
            <div className="mt-2 flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                  {skill && skill.icon ? (
                    <img src={skill.icon} alt={skill.name} className="h-6 w-6" />
                  ) : (
                    <svg className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                </div>
              </div>
              <div className="ml-3">
                <div className="text-sm font-medium text-gray-900">
                  {skill ? skill.name : 'Service'} - {tier ? tier.name : 'Tier'}
                </div>
                <div className="text-sm text-gray-500">
                  {tier ? `${tier.price} credits / ${tier.unit || 'round'}` : ''}
                </div>
              </div>
              <div className="ml-auto">
                <button
                  type="button"
                  onClick={onBack}
                  className="text-sm text-indigo-600 hover:text-indigo-800"
                >
                  Change
                </button>
              </div>
            </div>
            
            {/* Quantity selector */}
            <div className="mt-4">
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <button
                  type="button"
                  onClick={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 1}
                  className="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm rounded-l-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4" />
                  </svg>
                </button>
                <input
                  type="number"
                  name="quantity"
                  id="quantity"
                  min="1"
                  value={quantity}
                  onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                  className="flex-1 min-w-0 block w-full px-3 py-2 border border-gray-300 text-center sm:text-sm"
                />
                <button
                  type="button"
                  onClick={() => handleQuantityChange(quantity + 1)}
                  className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm rounded-r-md hover:bg-gray-100"
                >
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          {/* Service theme and type */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Service Theme</h3>
              <div className="mt-2 text-sm text-gray-900">
                {skill && skill.type ? skill.type : 'Game'}
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Service Type</h3>
              <div className="mt-2 text-sm text-gray-900">Online</div>
            </div>
          </div>
          
          {/* Service date */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500">Service Start Date</h3>
            <div className="mt-2 flex items-center">
              <div className="text-sm text-gray-900">
                {formatDate(dateTime)}
              </div>
              <div className="ml-auto">
                <button
                  type="button"
                  onClick={onBack}
                  className="text-sm text-indigo-600 hover:text-indigo-800"
                >
                  Change
                </button>
              </div>
            </div>
          </div>
          
          {/* Remarks */}
          <div className="mb-6">
            <label htmlFor="remarks" className="block text-sm font-medium text-gray-700">
              Remarks (Optional)
            </label>
            <div className="mt-1">
              <textarea
                id="remarks"
                name="remarks"
                rows="3"
                value={remarks}
                onChange={handleRemarksChange}
                placeholder="Enter your remarks"
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              ></textarea>
              <p className="mt-1 text-xs text-gray-500">
                {remarks.length}/100 words
              </p>
            </div>
          </div>
          
          {/* Payment method */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500">Payment Method</h3>
            <div className="mt-2">
              <div className="flex items-center justify-between p-3 border border-gray-300 rounded-md">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                    <svg className="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">MissionX Wallet</div>
                    <div className="text-sm text-gray-500">
                      Balance: {walletBalance} credits
                      {!hasSufficientBalance && (
                        <span className="ml-2 text-red-600">Insufficient balance</span>
                      )}
                    </div>
                  </div>
                </div>
                {!hasSufficientBalance && (
                  <button
                    type="button"
                    onClick={handleTopUpClick}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Top Up
                  </button>
                )}
              </div>
            </div>
          </div>
          
          {/* Total price */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex justify-between items-center">
              <h3 className="text-base font-medium text-gray-900">Total Price</h3>
              <div className="text-xl font-bold text-indigo-600">{totalPrice} credits</div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onBack}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back
        </button>
        <button
          type="button"
          onClick={handlePlaceOrder}
          disabled={isLoading || !hasSufficientBalance}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Processing...' : 'Place Order'}
        </button>
      </div>
      
      {/* Insufficient balance modal */}
      <InsufficientBalanceModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        requiredAmount={totalPrice}
        currentBalance={walletBalance}
        onTopUpSuccess={handleTopUpSuccess}
      />
    </motion.div>
  );
};

export default PlaceOrderPage;
