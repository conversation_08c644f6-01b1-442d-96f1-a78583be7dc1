import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
const shortDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const ScheduleDateTimePicker = ({ availabilityData = [], specialDates = [], onSelect, minDate }) => {
  const [currentMonth, setCurrentMonth] = useState(() => {
    const d = minDate ? new Date(minDate) : new Date();
    d.setDate(1);
    d.setHours(0, 0, 0, 0);
    return d;
  });
  const [calendarDays, setCalendarDays] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [hoveredDate, setHoveredDate] = useState(null);
  const [hoveredSlot, setHoveredSlot] = useState(null);

  useEffect(() => {
    generateCalendarDays(currentMonth);
  }, [currentMonth, availabilityData, specialDates, minDate]);

  useEffect(() => {
    if (selectedDate) {
      generateTimeSlots(selectedDate);
    } else {
      setTimeSlots([]);
      setSelectedSlot(null);
    }
  }, [selectedDate, availabilityData, specialDates]);

  // Generate calendar days for the current month
  const generateCalendarDays = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayOfWeek = firstDay.getDay();
    const daysFromPrevMonth = firstDayOfWeek;
    const totalDays = 42; // 6 rows of 7 days
    const days = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    // Add days from previous month
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();
    for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
      days.push({
        date: new Date(year, month - 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    // Add days from current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      date.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();
      const isSelectable = (!minDate || date.getTime() >= new Date(minDate).setHours(0,0,0,0)) && date.getTime() >= today.getTime();
      const hasAvailability = checkDateAvailability(date);
      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelectable: isSelectable && hasAvailability,
        hasAvailability
      });
    }
    // Add days from next month
    const remainingDays = totalDays - days.length;
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    setCalendarDays(days);
  };

  // Check if a date has availability
  const checkDateAvailability = (date) => {
    // Format date as YYYY-MM-DD
    const formattedDate = date.toISOString().split('T')[0];
    // Check special dates
    const specialDate = specialDates.find(d => d.date === formattedDate);
    if (specialDate && specialDate.periods && specialDate.periods.length > 0) {
      return true;
    }
    // Check weekly availability
    const dayOfWeek = daysOfWeek[date.getDay()];
    const dayAvailability = availabilityData.find(d => d.day === dayOfWeek);
    return dayAvailability && dayAvailability.periods && dayAvailability.periods.length > 0;
  };

  // Generate time slots for the selected date
  const generateTimeSlots = (date) => {
    const slots = [];
    const dayOfWeek = daysOfWeek[date.getDay()];
    const formattedDate = date.toISOString().split('T')[0];
    // Check special dates
    const specialDate = specialDates.find(d => d.date === formattedDate);
    const periods = specialDate && specialDate.periods && specialDate.periods.length > 0
      ? specialDate.periods
      : (availabilityData.find(d => d.day === dayOfWeek)?.periods || []);
    periods.forEach(period => {
      const [startHour, startMinute] = period.start_time.split(':').map(Number);
      const [endHour, endMinute] = period.end_time.split(':').map(Number);
      for (let hour = startHour; hour <= endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          if ((hour === startHour && minute < startMinute) || (hour === endHour && minute > endMinute)) {
            continue;
          }
          slots.push({
            time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            hour,
            minute,
            isAvailable: true
          });
        }
      }
    });
    setTimeSlots(slots);
  };

  // Handle date selection
  const handleDateSelect = (day) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
      setSelectedSlot(null);
    }
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (slot) => {
    setSelectedSlot(slot);
    if (onSelect && selectedDate && slot) {
      const dateTime = new Date(selectedDate);
      dateTime.setHours(slot.hour, slot.minute, 0, 0);
      onSelect(dateTime);
    }
  };

  // Handle previous/next month
  const handlePrevMonth = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    setCurrentMonth(prevMonth);
  };
  const handleNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };

  // Format month for display
  const formatMonth = (date) => {
    if (!date) return '';
    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString(undefined, options);
  };
  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/40 dark:from-gray-900 dark:via-blue-900/10 dark:to-indigo-900/15 rounded-3xl shadow-2xl border border-white/50 dark:border-blue-900/30 overflow-hidden backdrop-blur-sm mb-6"
    >
      {/* Calendar header */}
      <div className="px-6 py-5 bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700 dark:from-blue-800 dark:via-indigo-800 dark:to-blue-900">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-xl font-bold text-white mb-1">
              Select Date & Time
            </h3>
            <p className="text-blue-100 dark:text-blue-200 text-sm">
              Choose your preferred appointment slot
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <motion.button 
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              type="button" 
              onClick={handlePrevMonth} 
              className="p-2.5 rounded-2xl bg-white/20 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 group"
            >
              <svg className="w-5 h-5 text-white group-hover:text-blue-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>
            
            <div className="px-5 py-2.5 bg-white/20 backdrop-blur-sm rounded-2xl">
              <span className="text-lg font-bold text-white">{formatMonth(currentMonth)}</span>
            </div>
            
            <motion.button 
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              type="button" 
              onClick={handleNextMonth} 
              className="p-2.5 rounded-2xl bg-white/20 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 group"
            >
              <svg className="w-5 h-5 text-white group-hover:text-blue-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Calendar content */}
      <div className="p-6">
        {/* Day names */}
        <div className="grid grid-cols-7 gap-2 mb-4">
          {shortDays.map((day, index) => (
            <motion.div 
              key={day}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="text-center py-2.5 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50"
            >
              <span className="text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                {day}
              </span>
            </motion.div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-2 mb-6">
          {calendarDays.map((day, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.008 }}
              whileHover={day.isSelectable ? { scale: 1.05, y: -2 } : {}}
              whileTap={day.isSelectable ? { scale: 0.95 } : {}}
              type="button"
              onClick={() => handleDateSelect(day)}
              onMouseEnter={() => day.isSelectable && setHoveredDate(day.date)}
              onMouseLeave={() => setHoveredDate(null)}
              disabled={!day.isSelectable}
              className={`
                h-12 rounded-2xl flex items-center justify-center text-sm font-semibold relative overflow-hidden transition-all duration-300 transform-gpu
                ${!day.isCurrentMonth 
                  ? 'text-gray-300 dark:text-gray-600 bg-gray-50/50 dark:bg-gray-800/20' 
                  : day.isToday
                    ? 'bg-gradient-to-br from-amber-400 to-orange-500 text-white shadow-lg shadow-amber-500/30 ring-2 ring-amber-300 dark:ring-amber-600'
                    : selectedDate && day.date.getTime() === selectedDate.getTime()
                      ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-xl shadow-blue-500/40 ring-2 ring-blue-300 dark:ring-blue-600'
                      : day.isSelectable
                        ? hoveredDate && day.date.getTime() === hoveredDate.getTime()
                          ? 'bg-gradient-to-br from-blue-100 to-indigo-100/60 dark:from-blue-800/50 dark:to-indigo-800/30 text-blue-700 dark:text-blue-200 shadow-lg'
                          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50/40 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/20 shadow-sm hover:shadow-md border border-gray-200/50 dark:border-gray-700/50'
                        : 'bg-gray-100/50 dark:bg-gray-800/20 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                }
              `}
            >
              <span className="relative z-10">{day.day}</span>
              
              {/* Availability indicator */}
              {day.hasAvailability && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-emerald-400 dark:bg-emerald-300 shadow-sm"
                >
                  <div className="absolute inset-0 w-2 h-2 rounded-full bg-emerald-400 dark:bg-emerald-300 animate-ping opacity-40"></div>
                </motion.div>
              )}
              
              {/* Selection highlight effect */}
              {selectedDate && day.date.getTime() === selectedDate.getTime() && (
                <motion.div
                  layoutId="selectedDatePicker"
                  className="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          ))}
        </div>

        {/* Time slots */}
        <AnimatePresence mode="wait">
          {selectedDate && (
            <motion.div
              initial={{ opacity: 0, y: 20, height: 0 }}
              animate={{ opacity: 1, y: 0, height: "auto" }}
              exit={{ opacity: 0, y: -20, height: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="overflow-hidden"
            >
              <div className="bg-gradient-to-r from-blue-50 via-indigo-50/50 to-blue-50 dark:from-blue-900/20 dark:via-indigo-900/10 dark:to-blue-900/20 rounded-3xl p-6 border border-blue-200/30 dark:border-blue-800/30">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center space-x-3 mb-5"
                >
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-800 dark:text-gray-100">
                      Available Times
                    </h4>
                    <p className="text-blue-600 dark:text-blue-300 text-sm font-medium">
                      {formatDate(selectedDate)}
                    </p>
                  </div>
                </motion.div>

                {timeSlots.length > 0 ? (
                  <motion.div 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 max-h-64 overflow-y-auto custom-scrollbar"
                  >
                    {timeSlots.map((slot, idx) => (
                      <motion.button
                        key={idx}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: idx * 0.02 }}
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        type="button"
                        onClick={() => handleTimeSlotSelect(slot)}
                        onMouseEnter={() => setHoveredSlot(slot)}
                        onMouseLeave={() => setHoveredSlot(null)}
                        className={`
                          py-3 px-4 rounded-2xl text-sm font-semibold transition-all duration-300 transform-gpu relative overflow-hidden
                          ${selectedSlot === slot
                            ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-xl shadow-blue-500/40 ring-2 ring-blue-300 dark:ring-blue-600'
                            : hoveredSlot === slot
                              ? 'bg-gradient-to-br from-blue-100 to-indigo-100/60 dark:from-blue-800/50 dark:to-indigo-800/30 text-blue-700 dark:text-blue-200 shadow-lg'
                              : 'bg-gradient-to-br from-emerald-400 to-teal-500 text-white shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-500/30 border border-emerald-300/50'
                          }
                        `}
                      >
                        <span className="relative z-10">{slot.time}</span>
                        
                        {/* Selection highlight effect */}
                        {selectedSlot === slot && (
                          <motion.div
                            layoutId="selectedTimeSlot"
                            className="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl"
                            initial={false}
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                          />
                        )}
                        
                        {/* Hover shine effect */}
                        {selectedSlot !== slot && (
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-700 ease-out" />
                        )}
                      </motion.button>
                    ))}
                  </motion.div>
                ) : (
                  <motion.div 
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 }}
                    className="text-center py-8"
                  >
                    <div className="w-14 h-14 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                      <svg className="w-7 h-7 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h5 className="text-lg font-semibold text-gray-600 dark:text-gray-300 mb-1">
                      No Available Times
                    </h5>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      No time slots available for this date. Please select another date.
                    </p>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #3b82f6, #6366f1);
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #2563eb, #4f46e5);
        }
      `}</style>
    </motion.div>
  );
};

export default ScheduleDateTimePicker;