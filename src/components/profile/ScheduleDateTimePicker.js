import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
const shortDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const ScheduleDateTimePicker = ({ availabilityData = [], specialDates = [], onSelect, minDate }) => {
  const [currentMonth, setCurrentMonth] = useState(() => {
    const d = minDate ? new Date(minDate) : new Date();
    d.setDate(1);
    d.setHours(0, 0, 0, 0);
    return d;
  });
  const [calendarDays, setCalendarDays] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);

  useEffect(() => {
    generateCalendarDays(currentMonth);
  }, [currentMonth, availabilityData, specialDates, minDate]);

  useEffect(() => {
    if (selectedDate) {
      generateTimeSlots(selectedDate);
    } else {
      setTimeSlots([]);
      setSelectedSlot(null);
    }
  }, [selectedDate, availabilityData, specialDates]);

  // Generate calendar days for the current month
  const generateCalendarDays = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayOfWeek = firstDay.getDay();
    const daysFromPrevMonth = firstDayOfWeek;
    const totalDays = 42; // 6 rows of 7 days
    const days = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    // Add days from previous month
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();
    for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
      days.push({
        date: new Date(year, month - 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    // Add days from current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      date.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();
      const isSelectable = (!minDate || date.getTime() >= new Date(minDate).setHours(0,0,0,0)) && date.getTime() >= today.getTime();
      const hasAvailability = checkDateAvailability(date);
      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelectable: isSelectable && hasAvailability,
        hasAvailability
      });
    }
    // Add days from next month
    const remainingDays = totalDays - days.length;
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    setCalendarDays(days);
  };

  // Check if a date has availability
  const checkDateAvailability = (date) => {
    // Format date as YYYY-MM-DD
    const formattedDate = date.toISOString().split('T')[0];
    // Check special dates
    const specialDate = specialDates.find(d => d.date === formattedDate);
    if (specialDate && specialDate.periods && specialDate.periods.length > 0) {
      return true;
    }
    // Check weekly availability
    const dayOfWeek = daysOfWeek[date.getDay()];
    const dayAvailability = availabilityData.find(d => d.day === dayOfWeek);
    return dayAvailability && dayAvailability.periods && dayAvailability.periods.length > 0;
  };

  // Generate time slots for the selected date
  const generateTimeSlots = (date) => {
    const slots = [];
    const dayOfWeek = daysOfWeek[date.getDay()];
    const formattedDate = date.toISOString().split('T')[0];
    // Check special dates
    const specialDate = specialDates.find(d => d.date === formattedDate);
    const periods = specialDate && specialDate.periods && specialDate.periods.length > 0
      ? specialDate.periods
      : (availabilityData.find(d => d.day === dayOfWeek)?.periods || []);
    periods.forEach(period => {
      const [startHour, startMinute] = period.start_time.split(':').map(Number);
      const [endHour, endMinute] = period.end_time.split(':').map(Number);
      for (let hour = startHour; hour <= endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          if ((hour === startHour && minute < startMinute) || (hour === endHour && minute > endMinute)) {
            continue;
          }
          slots.push({
            time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            hour,
            minute,
            isAvailable: true
          });
        }
      }
    });
    setTimeSlots(slots);
  };

  // Handle date selection
  const handleDateSelect = (day) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
      setSelectedSlot(null);
    }
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (slot) => {
    setSelectedSlot(slot);
    if (onSelect && selectedDate && slot) {
      const dateTime = new Date(selectedDate);
      dateTime.setHours(slot.hour, slot.minute, 0, 0);
      onSelect(dateTime);
    }
  };

  // Handle previous/next month
  const handlePrevMonth = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    setCurrentMonth(prevMonth);
  };
  const handleNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };

  // Format month for display
  const formatMonth = (date) => {
    if (!date) return '';
    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString(undefined, options);
  };
  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white/80 rounded-2xl shadow-inner border border-blue-100/40 p-4 mb-4"
    >
      {/* Calendar header */}
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-base font-semibold text-blue-900">Select Date</h3>
        <div className="flex items-center space-x-2">
          <button type="button" onClick={handlePrevMonth} className="p-1 rounded-full bg-indigo-50 hover:bg-gray-100">
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span className="text-sm font-medium">{formatMonth(currentMonth)}</span>
          <button type="button" onClick={handleNextMonth} className="p-1 rounded-full bg-indigo-50 hover:bg-gray-100">
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
      {/* Day names */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {shortDays.map((day) => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">
            {day}
          </div>
        ))}
      </div>
      {/* Calendar days */}
      <div className="grid grid-cols-7 gap-1 mb-4">
        {calendarDays.map((day, index) => (
          <button
            key={index}
            type="button"
            onClick={() => handleDateSelect(day)}
            disabled={!day.isSelectable}
            className={`h-10 rounded-md flex items-center justify-center text-sm relative
              ${day.isCurrentMonth ? 'font-medium' : 'text-gray-400'}
              ${
                selectedDate && day.date.getTime() === selectedDate.getTime()
                  ? 'bg-indigo-600 text-white'
                  : day.isToday
                  ? 'bg-indigo-100 text-indigo-700'
                  : day.isCurrentMonth
                  ? 'bg-indigo-700 text-white'
                  : 'bg-white'
              }
              ${
                day.isSelectable && !(selectedDate && day.date.getTime() === selectedDate.getTime())
                  ? 'hover:bg-gray-500 hover:text-white'
                  : ''
              }
              ${!day.isSelectable ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            {day.day}
            {day.hasAvailability && (
              <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-green-500"></span>
            )}
          </button>
        ))}
      </div>
      {/* Time slots */}
      {selectedDate && (
        <div>
          <h4 className="text-sm font-medium text-blue-900 mb-2">Available Times for {formatDate(selectedDate)}</h4>
          {timeSlots.length > 0 ? (
            <div className="grid grid-cols-3 sm:grid-cols-5 gap-2 max-h-32 overflow-y-auto">
              {timeSlots.map((slot, idx) => (
                <button
                  key={idx}
                  type="button"
                  onClick={() => handleTimeSlotSelect(slot)}
                  className={`py-2 px-3 rounded-lg text-xs font-semibold transition-all duration-200
                    ${selectedSlot === slot ? 'bg-indigo-600 text-white' : 'bg-blue-100 text-blue-700 hover:bg-indigo-100 hover:text-indigo-700'}
                  `}
                >
                  {slot.time}
                </button>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">No available time slots for this date</div>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default ScheduleDateTimePicker; 