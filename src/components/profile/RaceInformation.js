/**
 * Race Information Component
 * 
 * This component displays the user's race information and allows them to change their race.
 */

import React, { useState } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * RaceInformation component
 * 
 * @param {Object} props
 * @param {boolean} props.isCurrentUser - Whether the profile belongs to the current user
 */
const RaceInformation = ({ isCurrentUser = true }) => {
  // State for race selection modal
  const [showRaceModal, setShowRaceModal] = useState(false);
  const [selectedRaceId, setSelectedRaceId] = useState(null);
  const [isChanging, setIsChanging] = useState(false);
  
  // Get profile context
  const { raceInfo, loading, error, updateRace } = useProfile();
  
  // Use the translation hook
  const { t } = useTranslation('profile');
  
  // Handle race change
  const handleRaceChange = async () => {
    if (!selectedRaceId) return;
    
    setIsChanging(true);
    try {
      const result = await updateRace(selectedRaceId);
      if (result.success) {
        setShowRaceModal(false);
      }
    } catch (err) {
      console.error('Error changing race:', err);
    } finally {
      setIsChanging(false);
    }
  };
  
  // If loading, show skeleton
  if (loading && !raceInfo) {
    return (
      <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6 animate-pulse">
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-24 bg-gray-200 rounded-lg mb-4"></div>
        <div className="h-6 bg-gray-300 rounded w-1/3 mb-2"></div>
        <div className="grid grid-cols-3 gap-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="h-16 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.race.error.title', 'Error loading race information')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }
  
  // If no race info, show empty state
  if (!raceInfo) {
    return (
      <div className="w-full bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
        <p className="font-medium">{t('profile.race.empty.title', 'No race information available')}</p>
        <p className="text-sm mt-1">
          {t('profile.race.empty.message', 'Race information will appear here once it is set.')}
        </p>
        {isCurrentUser && (
          <button
            onClick={() => setShowRaceModal(true)}
            className="mt-3 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
          >
            {t('profile.race.select', 'Select Your Race')}
          </button>
        )}
      </div>
    );
  }
  
  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {t('profile.race.title', 'Race Information')}
        </h2>
        
        {isCurrentUser && (
          <button
            onClick={() => setShowRaceModal(true)}
            className="px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center"
          >
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            {t('profile.race.change', 'Change Race')}
          </button>
        )}
      </div>
      
      {/* Race Information */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6">
        <div className="flex items-center mb-3">
          <span className="text-4xl mr-3">{getRaceIcon(raceInfo.race)}</span>
          <h3 className="text-xl font-bold text-indigo-900">{raceInfo.race}</h3>
        </div>
        <p className="text-indigo-800 mb-4">{raceInfo.race_description}</p>
      </div>
      
      {/* Race Abilities */}
      <div>
        <h3 className="text-md font-medium text-gray-800 mb-3">
          {t('profile.race.abilities', 'Race Abilities')}
        </h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {raceInfo.race_abilities.map((ability, index) => (
            <div key={index} className="bg-white border border-indigo-100 rounded-xl p-4 shadow-sm">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-2">{ability.icon}</span>
                <h4 className="font-semibold text-indigo-800">{ability.name}</h4>
              </div>
              <p className="text-sm text-gray-600">{ability.description}</p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Race Selection Modal */}
      <AnimatePresence>
        {showRaceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <motion.div
              className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    {t('profile.race.selectTitle', 'Select Your Race')}
                  </h2>
                  <button
                    onClick={() => setShowRaceModal(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                  {raceInfo.available_races.map((race) => (
                    <div
                      key={race.id}
                      className={`border rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                        selectedRaceId === race.id
                          ? 'border-indigo-500 bg-indigo-50 shadow-md'
                          : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50'
                      }`}
                      onClick={() => setSelectedRaceId(race.id)}
                    >
                      <div className="flex items-center mb-2">
                        <span className="text-3xl mr-3">{race.icon}</span>
                        <h3 className="text-lg font-semibold">{race.name}</h3>
                      </div>
                      <p className="text-sm text-gray-600">{race.description}</p>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowRaceModal(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    {t('profile.race.cancel', 'Cancel')}
                  </button>
                  <button
                    onClick={handleRaceChange}
                    disabled={!selectedRaceId || isChanging}
                    className={`px-4 py-2 bg-indigo-600 text-white rounded-lg transition-colors ${
                      !selectedRaceId || isChanging
                        ? 'opacity-50 cursor-not-allowed'
                        : 'hover:bg-indigo-700'
                    }`}
                  >
                    {isChanging ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('profile.race.changing', 'Changing...')}
                      </span>
                    ) : (
                      t('profile.race.confirm', 'Confirm Selection')
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

/**
 * Get race icon based on race name
 * 
 * @param {string} raceName - Name of the race
 * @returns {string} Race icon
 */
const getRaceIcon = (raceName) => {
  switch (raceName) {
    case 'Human':
      return '👤';
    case 'Elf':
      return '🧝';
    case 'Dwarf':
      return '🧔';
    case 'Orc':
      return '👹';
    case 'Cyborg':
      return '🤖';
    default:
      return '👤';
  }
};

export default RaceInformation;
