import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

/**
 * ServiceDetailModal component
 * 
 * A modal for viewing service details.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function called when modal is closed
 * @param {Object} props.service - Service to display
 * @param {Function} props.onEdit - Function called when Edit button is clicked
 * @param {Function} props.onDelete - Function called when Delete button is clicked
 * @param {Function} props.onToggleActive - Function called when Toggle Active button is clicked
 * @param {boolean} props.isLoading - Whether any action is in progress
 */
const ServiceDetailModal = ({
  isOpen,
  onClose,
  service,
  onEdit,
  onDelete,
  onToggleActive,
  isLoading = false
}) => {
  const { t } = useTranslation('profile');
  const [isClosing, setIsClosing] = useState(false);
  
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape' && isOpen && !isLoading) {
        handleClose();
      }
    };
    
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, isLoading]);
  
  // Handle close with animation
  const handleClose = () => {
    if (isLoading) return;
    
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };
  
  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };
  
  if (!isOpen && !isClosing || !service) return null;
  
  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={backdropVariants}
        onClick={handleClose}
      >
        <motion.div
          className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col"
          variants={modalVariants}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              {t('services.detail.title', 'Service Details')}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              disabled={isLoading}
              aria-label={t('services.detail.close', 'Close')}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Service Header */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className="text-lg font-bold text-gray-900">{service.title}</h3>
                
                <div className="flex flex-wrap gap-2 mt-2">
                  {/* Service Category */}
                  {service.service_category && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {service.service_category.name}
                    </span>
                  )}
                  
                  {/* Service Type */}
                  {service.service_type && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {service.service_type.name}
                    </span>
                  )}
                  
                  {/* Status Badge */}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    service.status === 'approved' 
                      ? 'bg-green-100 text-green-800' 
                      : service.status === 'pending' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                  </span>
                  
                  {/* Active/Inactive Badge */}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    service.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {service.is_active 
                      ? t('services.detail.active', 'Active') 
                      : t('services.detail.inactive', 'Inactive')}
                  </span>
                </div>
              </div>
              
              {/* Price */}
              {(service.price || (service.service_styles && service.service_styles.length > 0)) && (
                <div className="text-right">
                  {service.price && (
                    <div className="text-xl font-bold text-blue-600">
                      {formatCurrency(service.price)}<span className="text-sm font-normal text-gray-600">/hr</span>
                    </div>
                  )}
                  
                  {/* Pricing Option */}
                  {service.pricing_options && service.pricing_options.length > 0 && (
                    <div className="text-sm text-gray-500 mt-1">
                      {service.pricing_options.find(option => option.is_active)?.pricing_option_type?.name || 
                        t('services.detail.hourlyRate', 'Hourly Rate')}
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {/* Description */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {t('services.detail.description', 'Description')}
              </h4>
              <p className="text-gray-600 whitespace-pre-line">{service.description}</p>
            </div>
            
            {/* Service Elements */}
            {service.service_elements && service.service_elements.length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  {t('services.detail.elements', 'Service Elements')}
                </h4>
                <ul className="list-disc list-inside text-gray-600 space-y-1">
                  {service.service_elements.map((element, index) => (
                    <li key={index}>{element}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Service Styles */}
            {service.service_styles && service.service_styles.length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  {t('services.detail.styles', 'Service Styles')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {service.service_styles.map((style) => (
                    <div key={style.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">{style.name}</span>
                        <span className="text-blue-600 font-medium">
                          {formatCurrency(style.price)}<span className="text-xs text-gray-500">/hr</span>
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Custom Service Type (for Talent category) */}
            {service.service_category_id === 2 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  {t('services.detail.customType', 'Custom Service Type')}
                </h4>
                <div className="border border-gray-200 rounded-lg p-4">
                  <h5 className="font-medium text-gray-900">{service.service_type_title}</h5>
                  <p className="text-gray-600 mt-2">{service.service_type_description}</p>
                </div>
              </div>
            )}
            
            {/* Created/Updated Info */}
            <div className="text-xs text-gray-500 flex justify-between">
              {service.created_at && (
                <span>
                  {t('services.detail.created', 'Created')}: {new Date(service.created_at).toLocaleDateString()}
                </span>
              )}
              {service.updated_at && (
                <span>
                  {t('services.detail.updated', 'Updated')}: {new Date(service.updated_at).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
          
          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
            <button
              type="button"
              onClick={() => onDelete(service)}
              className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              disabled={isLoading}
            >
              {t('services.detail.delete', 'Delete')}
            </button>
            
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => onToggleActive(service.id)}
                className={`px-4 py-2 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                  service.is_active
                    ? 'border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500'
                    : 'border-green-300 text-green-700 hover:bg-green-50 focus:ring-green-500'
                }`}
                disabled={isLoading || service.status !== 'approved'}
              >
                {service.is_active
                  ? t('services.detail.deactivate', 'Deactivate')
                  : t('services.detail.activate', 'Activate')}
              </button>
              
              <button
                type="button"
                onClick={() => onEdit(service)}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                disabled={isLoading}
              >
                {t('services.detail.edit', 'Edit')}
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ServiceDetailModal;
