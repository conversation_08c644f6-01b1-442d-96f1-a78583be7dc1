import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

/**
 * OrderConfirmationPage component
 * 
 * This page is shown after an order is successfully placed.
 * 
 * @param {Object} props
 * @param {Object} props.order - The placed order
 * @param {Object} props.talent - The talent providing the service
 * @param {Function} props.onViewOrder - Function to call when "View Order" is clicked
 * @param {Function} props.onBackToProfile - Function to call when "Back to Profile" is clicked
 */
const OrderConfirmationPage = ({ 
  order, 
  talent, 
  onViewOrder, 
  onBackToProfile 
}) => {
  const navigate = useNavigate();
  
  // Handle view order click
  const handleViewOrder = () => {
    if (onViewOrder) {
      onViewOrder(order);
    } else if (order && order.id) {
      navigate(`/orders/${order.id}`);
    }
  };
  
  // Handle back to profile click
  const handleBackToProfile = () => {
    if (onBackToProfile) {
      onBackToProfile(talent);
    } else if (talent && talent.id) {
      navigate(`/talent/${talent.id}`);
    }
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'As soon as possible';
    
    const date = new Date(dateString);
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    };
    
    return date.toLocaleDateString(undefined, options);
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-2xl mx-auto py-12 px-4 sm:px-6 lg:px-8 text-center"
    >
      {/* Success icon */}
      <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
        <svg className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      
      {/* Success message */}
      <h1 className="text-3xl font-extrabold text-gray-900 mb-2">Order Placed Successfully!</h1>
      <p className="text-lg text-gray-600 mb-8">
        Your order has been placed and is awaiting confirmation from the talent.
      </p>
      
      {/* Order summary */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 text-left">
          <h2 className="text-lg font-medium text-gray-900">Order Summary</h2>
        </div>
        
        <div className="px-4 py-5 sm:p-6 text-left">
          {/* Order ID */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500">Order ID</h3>
            <div className="mt-1 text-sm text-gray-900">#{order ? order.id : 'N/A'}</div>
          </div>
          
          {/* Service details */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500">Service</h3>
            <div className="mt-1 text-sm text-gray-900">
              {order ? order.service_name : 'N/A'}
              {order && order.tier_name && ` - ${order.tier_name}`}
            </div>
          </div>
          
          {/* Talent */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500">Talent</h3>
            <div className="mt-1 text-sm text-gray-900">
              {talent ? talent.name : 'N/A'}
            </div>
          </div>
          
          {/* Scheduled time */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500">Scheduled Time</h3>
            <div className="mt-1 text-sm text-gray-900">
              {order ? formatDate(order.scheduled_at) : 'N/A'}
            </div>
          </div>
          
          {/* Quantity */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-500">Quantity</h3>
            <div className="mt-1 text-sm text-gray-900">
              {order ? order.quantity : '1'} {order && order.unit ? order.unit : 'round(s)'}
            </div>
          </div>
          
          {/* Total price */}
          <div className="border-t border-gray-200 pt-4 mt-4">
            <div className="flex justify-between">
              <h3 className="text-base font-medium text-gray-900">Total Price</h3>
              <div className="text-base font-medium text-indigo-600">
                {order ? order.price : '0'} credits
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Next steps */}
      <div className="bg-blue-50 rounded-lg p-6 mb-8 text-left">
        <h3 className="text-lg font-medium text-blue-800 mb-2">What's Next?</h3>
        <ul className="list-disc list-inside text-sm text-blue-700 space-y-2">
          <li>The talent will review your order and confirm or suggest changes.</li>
          <li>Once confirmed, you'll receive a notification.</li>
          <li>You can chat with the talent to discuss details.</li>
          <li>After the service is completed, you can leave a review.</li>
        </ul>
      </div>
      
      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
        <button
          type="button"
          onClick={handleViewOrder}
          className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          View Order
        </button>
        <button
          type="button"
          onClick={handleBackToProfile}
          className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Profile
        </button>
      </div>
    </motion.div>
  );
};

export default OrderConfirmationPage;
