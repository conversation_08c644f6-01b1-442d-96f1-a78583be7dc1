import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * ProfileHeader component displays the top section of a talent's profile
 * including cover image, profile picture, name, level, and basic information
 */
const ProfileHeader = ({ talent }) => {
  const [imageError, setImageError] = useState(false);

  // Default values for all properties
  const {
    displayName = '',
    levelNumber = 0,
    profileImage = '',
    coverImage = '',
    bio = '',
    gender = '',
    lastActive = null,
    isOnline = false
  } = talent || {};

  const formatLastActive = (lastActive) => {
    if (!lastActive) return '';
    const date = new Date(lastActive);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="relative">
      {/* Cover Image */}
      <div className="h-48 md:h-64 bg-gradient-to-r from-indigo-500 to-blue-500">
        {coverImage && !imageError && (
          <img
            src={coverImage}
            alt="Cover"
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        )}
      </div>

      {/* Profile Content */}
      <div className="px-4 md:px-6 -mt-16 relative z-10">
        <div className="flex flex-col md:flex-row items-center md:items-end gap-4">
          {/* Profile Image */}
          <div className="relative">
            <div className="w-32 h-32 rounded-full border-4 border-white bg-white shadow-lg overflow-hidden">
              <img
                src={profileImage || 'https://via.placeholder.com/150?text=No+Image'}
                alt={displayName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://via.placeholder.com/150?text=No+Image';
                }}
              />
            </div>
            {isOnline && (
              <div className="absolute bottom-2 right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1 text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start gap-2 mb-1">
              <h1 className="text-2xl font-bold text-gray-900">{displayName}</h1>
              <span className={`text-lg font-bold ${
                gender === 'male' ? 'text-blue-600' :
                gender === 'female' ? 'text-pink-500' :
                'text-purple-600'
              }`}>
                {gender === 'male' ? '♂' : gender === 'female' ? '♀' : '⚧'}
              </span>
            </div>
            <div className="flex items-center justify-center md:justify-start gap-2 text-sm text-gray-600">
              <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                LV{levelNumber}
              </span>
              {lastActive && (
                <span className="text-gray-500">
                  Last active {formatLastActive(lastActive)}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Bio */}
        {bio && (
          <div className="mt-4 text-center md:text-left">
            <p className="text-gray-600">{bio}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileHeader;

