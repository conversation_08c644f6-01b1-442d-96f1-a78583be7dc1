import React from 'react';
import AboutTab from './AboutTab';
import ReviewTab from './ReviewTab';
import SkillsTab from './SkillsTab';
import PostTab from './PostTab';
import MissionTab from './MissionTab';

/**
 * TabContent component serves as a container for the active tab content
 * It provides a consistent layout and styling for all tab content
 * with smooth transitions between tabs
 */
const TabContent = ({ activeTab, talent, onOrder, onViewOrders }) => {
  switch (activeTab) {
    case 'about':
      return <AboutTab talent={talent} />;
    case 'review':
      return <ReviewTab talent={talent} />;
    case 'skills':
      return <SkillsTab talent={talent} onOpenServiceModal={onOrder} onViewOrders={onViewOrders} />;
    case 'post':
      return <PostTab talent={talent} />;
    case 'mission':
      return <MissionTab talent={talent} />;
    default:
      return null;
  }
};

export default TabContent;
