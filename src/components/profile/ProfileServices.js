/**
 * Profile Services Component
 *
 * This component displays the user's services they offer and provides
 * functionality to manage services (create, view, edit, delete).
 */

import React, { useState, useEffect } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';
import ServiceFormModal from './ServiceFormModal';
import ServiceDetailModal from './ServiceDetailModal';
import ServiceDeleteConfirmationModal from './ServiceDeleteConfirmationModal';
import userServiceApi from '../../services/userServiceApi';
import { toast } from 'react-hot-toast';

/**
 * ProfileServices component
 *
 * @param {Object} props
 * @param {boolean} props.isCurrentUser - Whether the profile belongs to the current user
 */
const ProfileServices = ({ isCurrentUser = true }) => {
  // Get profile context
  const { services, loading, error, fetchServices } = useProfile();

  // Use the translation hook
  const { t } = useTranslation('profile');

  // State for modals
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [formInitialValues, setFormInitialValues] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState(null);

  // State for service configuration
  const [serviceCategories, setServiceCategories] = useState([
    { id: 1, name: 'Gaming', description: 'Gaming related services' },
    { id: 2, name: 'Talent', description: 'Talent services' }
  ]);
  const [serviceTypes, setServiceTypes] = useState([
    { id: 1, name: 'Coaching', description: 'Coaching services', service_category_id: 1 },
    { id: 2, name: 'Duo Partner', description: 'Play together as a duo', service_category_id: 1 },
    { id: 3, name: 'Team Player', description: 'Play as a team member', service_category_id: 1 }
  ]);
  const [serviceStyles, setServiceStyles] = useState([
    { id: 1, name: 'Casual', description: 'Casual gameplay', service_type_id: 1, preset_price: 50 },
    { id: 2, name: 'Competitive', description: 'Competitive gameplay', service_type_id: 1, preset_price: 75 },
    { id: 3, name: 'Casual', description: 'Casual gameplay', service_type_id: 2, preset_price: 35 },
    { id: 4, name: 'Competitive', description: 'Competitive gameplay', service_type_id: 2, preset_price: 60 },
    { id: 5, name: 'Casual', description: 'Casual gameplay', service_type_id: 3, preset_price: 30 },
    { id: 6, name: 'Competitive', description: 'Competitive gameplay', service_type_id: 3, preset_price: 55 }
  ]);
  const [pricingOptionTypes, setPricingOptionTypes] = useState([
    { id: 1, name: 'Hourly', description: 'Charged per hour' },
    { id: 2, name: 'Fixed', description: 'Fixed price per session' },
    { id: 3, name: 'Package', description: 'Package pricing' }
  ]);

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return '';

    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Fetch service configuration
  useEffect(() => {
    const fetchServiceConfiguration = async () => {
      try {
        // In a real implementation, we would fetch these from the API
        // For now, we'll use the mock data defined above
      } catch (error) {
        console.error('Error fetching service configuration:', error);
      }
    };

    fetchServiceConfiguration();
  }, []);

  // Handle add service
  const handleAddService = () => {
    setFormInitialValues({});
    setFormError(null);
    setIsFormModalOpen(true);
  };

  // Handle edit service
  const handleEditService = (service) => {
    // Format service data for the form
    const formattedService = {
      id: service.id,
      service_category_id: service.service_category_id,
      service_type_id: service.service_type_id,
      pricing_option_id: service.pricing_options?.[0]?.pricing_option_type_id || 1,
      title: service.title,
      description: service.description,
      service_type_title: service.service_type_title,
      service_type_description: service.service_type_description,
      price: service.price,
      service_elements: service.service_elements || [],
      service_style: service.service_styles?.map(style => ({
        service_style_id: style.id,
        is_active: true,
        price: style.price
      })) || []
    };

    setFormInitialValues(formattedService);
    setFormError(null);
    setIsFormModalOpen(true);
  };

  // Handle view service details
  const handleViewServiceDetails = (service) => {
    setSelectedService(service);
    setIsDetailModalOpen(true);
  };

  // Handle delete service
  const handleDeleteService = (service) => {
    setSelectedService(service);
    setIsDetailModalOpen(false);
    setIsDeleteModalOpen(true);
  };

  // Handle toggle service active status
  const handleToggleActive = async (serviceId) => {
    setIsSubmitting(true);

    try {
      const response = await userServiceApi.toggleServiceActive(serviceId);

      if (response.success) {
        // Update the service in the UI
        await fetchServices();

        // Show success message
        toast.success(response.data.message);

        // Close the detail modal
        setIsDetailModalOpen(false);
      } else {
        toast.error(response.error || 'Failed to toggle service status');
      }
    } catch (error) {
      console.error('Error toggling service status:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleFormSubmit = async (values) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      let response;

      if (values.id) {
        // Update existing service
        response = await userServiceApi.updateService(values);
      } else {
        // Create new service
        response = await userServiceApi.createService(values);
      }

      if (response.success) {
        // Refresh services
        await fetchServices();

        // Show success message
        toast.success(response.data.message);

        // Close the form modal
        setIsFormModalOpen(false);
      } else {
        setFormError(response.error || 'Failed to save service');
      }
    } catch (error) {
      console.error('Error saving service:', error);
      setFormError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async (serviceId) => {
    setIsSubmitting(true);

    try {
      const response = await userServiceApi.deleteService(serviceId);

      if (response.success) {
        // Refresh services
        await fetchServices();

        // Show success message
        toast.success(response.data.message);

        // Close the delete modal
        setIsDeleteModalOpen(false);
      } else {
        toast.error(response.error || 'Failed to delete service');
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-4 animate-pulse">
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 bg-gray-300 rounded w-1/4"></div>
          <div className="h-8 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(2)].map((_, index) => (
            <div key={index} className="h-48 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.error.title', 'Error loading services')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {t('profile.services.title', 'Services')}
        </h2>

        {isCurrentUser && (
          <button
            onClick={handleAddService}
            className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center"
          >
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
            </svg>
            {t('profile.services.addService', 'Add Service')}
          </button>
        )}
      </div>

      {/* No services message */}
      {(!services || services.length === 0) && (
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
          <p className="font-medium">{t('profile.services.empty.title', 'No services available')}</p>
          <p className="text-sm mt-1">
            {isCurrentUser
              ? t('profile.services.empty.currentUser', 'Add services to let others know what you offer.')
              : t('profile.services.empty.otherUser', 'This user has not added any services yet.')}
          </p>

          {isCurrentUser && (
            <button
              onClick={handleAddService}
              className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
            >
              {t('profile.services.addFirstService', 'Add Your First Service')}
            </button>
          )}
        </div>
      )}

      {/* Services grid */}
      {services && services.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {services.map((service) => (
            <div
              key={service.id}
              className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
              onClick={() => handleViewServiceDetails(service)}
            >
              <div className="relative">
                {/* Status badge */}
                <div className={`absolute top-2 left-2 text-white text-xs font-bold px-2 py-1 rounded-full ${
                  service.status === 'approved'
                    ? 'bg-green-500'
                    : service.status === 'pending'
                      ? 'bg-yellow-500'
                      : 'bg-red-500'
                }`}>
                  {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                </div>

                {/* Active/Inactive badge */}
                <div className={`absolute top-2 right-2 text-white text-xs font-bold px-2 py-1 rounded-full ${
                  service.is_active ? 'bg-blue-500' : 'bg-gray-500'
                }`}>
                  {service.is_active
                    ? t('profile.services.active', 'Active')
                    : t('profile.services.inactive', 'Inactive')}
                </div>

                {/* Service type badge */}
                {service.service_type && (
                  <div className="absolute bottom-2 right-2 bg-indigo-600 text-white text-xs font-bold px-2 py-1 rounded-lg">
                    {service.service_type.name}
                  </div>
                )}

                {/* Service image or placeholder */}
                <div className="h-40 bg-gray-200">
                  {service.image_url ? (
                    <img
                      src={service.image_url}
                      alt={service.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-indigo-500 text-white">
                      <div className="text-center p-4">
                        <svg className="w-10 h-10 mx-auto mb-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span className="text-sm font-medium">
                          {service.service_category?.name || 'Service'}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">
                  {service.title}
                </h3>

                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {service.description}
                </p>

                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-blue-600">
                    {formatCurrency(service.price || (service.service_styles && service.service_styles[0]?.price))}
                    <span className="text-sm font-normal text-gray-600">/hr</span>
                  </span>

                  {isCurrentUser ? (
                    <button
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditService(service);
                      }}
                    >
                      {t('profile.services.edit', 'Edit')}
                    </button>
                  ) : (
                    <button
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {t('profile.services.book', 'Book Now')}
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Service Form Modal */}
      <ServiceFormModal
        isOpen={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
        initialValues={formInitialValues}
        onSubmit={handleFormSubmit}
        serviceCategories={serviceCategories}
        serviceTypes={serviceTypes}
        serviceStyles={serviceStyles}
        pricingOptionTypes={pricingOptionTypes}
        isLoading={isSubmitting}
        error={formError}
      />

      {/* Service Detail Modal */}
      <ServiceDetailModal
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        service={selectedService}
        onEdit={handleEditService}
        onDelete={handleDeleteService}
        onToggleActive={handleToggleActive}
        isLoading={isSubmitting}
      />

      {/* Service Delete Confirmation Modal */}
      <ServiceDeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        service={selectedService}
        onConfirm={handleDeleteConfirm}
        isLoading={isSubmitting}
      />
    </div>
  );
};

export default ProfileServices;
