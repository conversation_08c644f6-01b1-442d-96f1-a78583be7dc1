/**
 * Profile Voice Note Component
 * 
 * This component displays the user's voice note with playback controls.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';
import profileService from '../../services/profileService';

/**
 * ProfileVoiceNote component
 * 
 * @param {Object} props
 * @param {boolean} props.isCurrentUser - Whether the profile belongs to the current user
 */
const ProfileVoiceNote = ({ isCurrentUser = true }) => {
  // State for voice note
  const [voiceNote, setVoiceNote] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // Audio ref
  const audioRef = useRef(new Audio());
  
  // Use the translation hook
  const { t } = useTranslation('profile');
  
  // Fetch voice note on component mount
  useEffect(() => {
    const fetchVoiceNote = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await profileService.getVoiceNote();
        if (response.success) {
          setVoiceNote(response.data);
        }
      } catch (err) {
        console.error('Error fetching voice note:', err);
        setError(t('profile.voiceNote.error', 'Failed to load voice note'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchVoiceNote();
    
    return () => {
      // Clean up audio on unmount
      audioRef.current.pause();
      audioRef.current.src = '';
    };
  }, [t]);
  
  // Set up audio event listeners
  useEffect(() => {
    if (voiceNote?.url) {
      audioRef.current.src = voiceNote.url;
      
      const handleEnded = () => setIsPlaying(false);
      audioRef.current.addEventListener('ended', handleEnded);
      
      return () => {
        audioRef.current.removeEventListener('ended', handleEnded);
      };
    }
  }, [voiceNote]);
  
  // Toggle playback
  const togglePlayback = () => {
    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };
  
  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6 animate-pulse">
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-12 bg-gray-200 rounded"></div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.voiceNote.error', 'Error loading voice note')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }
  
  // If no voice note, show empty state
  if (!voiceNote || !voiceNote.url) {
    return (
      <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {t('profile.voiceNote.title', 'Voice Note')}
          </h2>
        </div>
        
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
          <p className="font-medium">{t('profile.voiceNote.empty.title', 'No voice note available')}</p>
          <p className="text-sm mt-1">
            {isCurrentUser
              ? t('profile.voiceNote.empty.currentUser', 'Record a voice note to introduce yourself to potential clients.')
              : t('profile.voiceNote.empty.otherUser', 'This user has not recorded a voice note yet.')}
          </p>
          
          {isCurrentUser && (
            <button
              onClick={() => window.location.href = '/edit-profile'}
              className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
            >
              {t('profile.voiceNote.record', 'Record Voice Note')}
            </button>
          )}
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {t('profile.voiceNote.title', 'Voice Note')}
        </h2>
      </div>
      
      <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={togglePlayback}
              className="p-3 bg-indigo-100 rounded-full text-indigo-600 hover:bg-indigo-200 transition-colors mr-4"
            >
              {isPlaying ? (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {t('profile.voiceNote.introduction', 'Voice Introduction')}
              </p>
              <p className="text-xs text-gray-500">
                {voiceNote.duration 
                  ? t('profile.voiceNote.duration', '{{duration}} seconds', { duration: voiceNote.duration }) 
                  : t('profile.voiceNote.tapToPlay', 'Tap to play')}
              </p>
            </div>
          </div>
          
          {isCurrentUser && (
            <button
              onClick={() => window.location.href = '/edit-profile'}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              {t('profile.actions.change', 'Change')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileVoiceNote;
