import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCdnUrl } from '../../utils/cdnUtils';
import ShareModal from '../modals/ShareModal';
import { generateShareUrl, generateShareTitle, generateShareDescription, trackShareAction } from '../../utils/shareUtils';
import socialPostService from '../../services/socialPostService';
import { useToast } from '../common/ToastProvider';

/**
 * PostTab component displays the talent's social media posts
 */
const PostTab = ({ talent, isOwnProfile }) => {
  const [showAllPosts, setShowAllPosts] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [currentSharePost, setCurrentSharePost] = useState(null);
  const [posts, setPosts] = useState(talent?.posts || []);
  const [likedPosts, setLikedPosts] = useState(new Set());
  const [likeLoading, setLikeLoading] = useState(new Set());
  const { success: showSuccessToast, error: showErrorToast } = useToast();
  
  // Default values in case talent data is incomplete
  const {
    posts: talentPosts = []
  } = talent || {};

  // Confetti burst state for like button
  const [confettiPostId, setConfettiPostId] = useState(null);

  // Initialize posts state when talent data changes
  React.useEffect(() => {
    if (talentPosts.length > 0) {
      setPosts(talentPosts);
      // Initialize liked posts from the data
      const likedSet = new Set();
      talentPosts.forEach(post => {
        if (post.is_liked) {
          likedSet.add(post.id);
        }
      });
      setLikedPosts(likedSet);
    }
  }, [talentPosts]);

  // Determine if we need to show "See All" button
  const hasMorePosts = posts.length > 3;
  
  // Get posts to display (all or just first 3)
  const displayedPosts = showAllPosts ? posts : posts.slice(0, 3);

  // Toggle showing all posts
  const toggleShowAllPosts = () => {
    setShowAllPosts(!showAllPosts);
  };

  // Handle post click for full view
  const handlePostClick = (post) => {
    setSelectedPost(post);
  };

  // Handle closing full view
  const handleCloseFullView = () => {
    setSelectedPost(null);
  };

  // Handle like/unlike post
  const handleLikePost = async (post, event) => {
    event.stopPropagation(); // Prevent triggering post click
    
    if (likeLoading.has(post.id)) return; // Prevent multiple clicks
    
    try {
      setLikeLoading(prev => new Set(prev).add(post.id));
      
      // Call the API to toggle like
      await socialPostService.toggleLike(post.id);
      
      // Update local state
      setPosts(prevPosts => 
        prevPosts.map(p => {
          if (p.id === post.id) {
            const isLiked = likedPosts.has(post.id);
            return {
              ...p,
              likes_count: isLiked ? (p.likes_count || 1) - 1 : (p.likes_count || 0) + 1,
              is_liked: !isLiked
            };
          }
          return p;
        })
      );
      
      // Update liked posts set
      setLikedPosts(prev => {
        const newSet = new Set(prev);
        if (newSet.has(post.id)) {
          newSet.delete(post.id);
        } else {
          newSet.add(post.id);
        }
        return newSet;
      });
      
      // Show success message
      const isLiked = likedPosts.has(post.id);
      showSuccessToast(isLiked ? 'Post unliked' : 'Post liked!');
      
      // Trigger confetti burst if liking
      if (!isLiked) {
        setConfettiPostId(post.id);
        setTimeout(() => setConfettiPostId(null), 900);
      }
      
    } catch (error) {
      console.error('Error toggling like:', error);
      showErrorToast('Failed to update like. Please try again.');
    } finally {
      setLikeLoading(prev => {
        const newSet = new Set(prev);
        newSet.delete(post.id);
        return newSet;
      });
    }
  };

  // Handle comment click (for now, just show a message)
  const handleCommentClick = (post, event) => {
    event.stopPropagation(); // Prevent triggering post click
    showSuccessToast('Comment functionality coming soon!');
  };

  // Handle share post with enhanced functionality
  const handleSharePost = (post, event) => {
    event.stopPropagation(); // Prevent triggering post click
    setCurrentSharePost(post);
    setIsShareModalOpen(true);
  };

  // Handle closing share modal
  const handleCloseShareModal = () => {
    setIsShareModalOpen(false);
    setCurrentSharePost(null);
  };

  // Handle share action with analytics tracking
  const handleShareAction = (platform, shareData) => {
    // Track share action for analytics
    trackShareAction(platform, {
      type: 'post',
      id: currentSharePost?.id,
      author: currentSharePost?.author,
      content: currentSharePost?.content
    });

    // You can add additional tracking or logging here
    console.log(`Post shared via ${platform}:`, shareData);
  };

  // Generate share URL for a post using utility function
  const generatePostShareUrl = (post) => {
    return generateShareUrl.post(post.id);
  };

  // Generate share title for a post using utility function
  const generatePostShareTitle = (post) => {
    return generateShareTitle.post(post);
  };

  // Generate share description for a post using utility function
  const generatePostShareDescription = (post) => {
    return generateShareDescription.post(post);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  // Render a single post
  const renderPost = (post) => {
    const isLiked = likedPosts.has(post.id);
    const isLikeLoading = likeLoading.has(post.id);
    const hasImage = !!post.image;
    // For future: support multiple images/media
    // const mediaFiles = post.media_files || (post.image ? [{ url: post.image, type: 'image' }] : []);
    return (
      <motion.div
        key={post.id}
        className="group relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/20 dark:border-gray-800 rounded-2xl overflow-hidden shadow-lg dark:shadow-indigo-900/30 hover:shadow-2xl dark:hover:shadow-indigo-900/40 transition-all duration-500 transform hover:scale-105 cursor-pointer w-full h-full flex flex-col justify-end"
        variants={itemVariants}
        onClick={() => { if (!isOwnProfile) handlePostClick(post); }}
        aria-disabled={isOwnProfile}
        title={isOwnProfile ? 'You cannot interact with your own posts here' : undefined}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        layout
      >
        {/* Confetti burst on like */}
        {confettiPostId === post.id && (
          <motion.svg
            className="absolute left-1/2 top-8 -translate-x-1/2 z-30 pointer-events-none"
            width="60" height="40" viewBox="0 0 60 40" fill="none"
            initial={{ opacity: 0, scale: 0.7 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.7 }}
            transition={{ duration: 0.7 }}
          >
            {/* 8 confetti lines */}
            {[...Array(8)].map((_, i) => {
              const angle = (i * 45) * (Math.PI / 180);
              const x2 = 30 + 18 * Math.cos(angle);
              const y2 = 20 + 12 * Math.sin(angle);
              return (
                <line
                  key={i}
                  x1="30" y1="20" x2={x2} y2={y2}
                  stroke={['#fbbf24','#f472b6','#60a5fa','#34d399','#f87171','#a78bfa','#f472b6','#facc15'][i]}
                  strokeWidth="4"
                  strokeLinecap="round"
                />
              );
            })}
          </motion.svg>
        )}
        {/* Media Section - Preserves Aspect Ratio */}
        <div className="relative w-full h-52 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-900 dark:to-gray-800">
          {hasImage ? (
            <img
              src={getCdnUrl(post.image)}
              alt="Post"
              className="w-full h-full object-cover bg-gray-100 dark:bg-gray-900"
              loading="lazy"
              style={{ aspectRatio: '1 / 1' }}
              onError={(e) => {
                if (!e.target.src.includes('via.placeholder.com')) {
                  e.target.src = 'https://via.placeholder.com/600x400?text=Post+Image';
                }
                e.target.onerror = null;
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900 dark:to-purple-900">
              <svg
                className="w-16 h-16 text-indigo-300 dark:text-yellow-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}
          {/* Multiple Media Indicator (future) */}
          {/* {mediaFiles.length > 1 && (
            <div className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full p-2">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </div>
          )} */}
          {/* Gradient Overlay for readability */}
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black/80 to-black/40 to-transparent dark:from-black/90 dark:to-black/60 pointer-events-none" />
        </div>
        {/* Content Overlay - Positioned at bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-4 z-10">
          {/* User info and content */}
          <div className="flex items-center mb-2">
            
          </div>
          {/* Post content */}
          <p className="text-sm font-bold text-left text-white dark:text-yellow-400 mb-2 line-clamp-2 leading-relaxed">
            {post.content}
          </p>
          {/* Interaction stats */}
          <div className="flex items-center justify-between pt-2 border-t border-white/20">
            <div className="flex items-center space-x-3">
              <button
                className={`flex items-center space-x-1 text-xs font-medium transition-all duration-200 rounded-2xl p-1.5 ${
                  isLiked
                    ? 'text-red-400 dark:text-yellow-400 bg-red-50 dark:bg-gray-800 hover:bg-red-100 dark:hover:bg-gray-700'
                    : 'text-gray-200 dark:text-gray-300 bg-transparent hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-red-500 dark:hover:text-yellow-400'
                } ${isLikeLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                onClick={(e) => { if (!isOwnProfile) handleLikePost(post, e); }}
                disabled={isLikeLoading || isOwnProfile}
                aria-label={isLiked ? 'Unlike post' : 'Like post'}
                aria-disabled={isOwnProfile}
                title={isOwnProfile ? 'You cannot like your own post here' : (isLiked ? 'Unlike post' : 'Like post')}
              >
                <svg
                  className={`w-4 h-4 transition-all duration-200 ${isLiked ? 'fill-current' : 'fill-none'}`}
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span>{post.likes_count || 0}</span>
                {isLikeLoading && (
                  <div className="w-3 h-3 border-2 border-red-500 border-t-transparent rounded-full animate-spin ml-1" />
                )}
              </button>
            </div>
            <button
              className="text-xs text-white dark:text-yellow-400 bg-white/20 dark:bg-gray-800/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-2xl font-medium hover:bg-white/30 dark:hover:bg-gray-700 transition-colors px-3 py-1"
              onClick={(e) => { if (!isOwnProfile) handleSharePost(post, e); }}
              disabled={isOwnProfile}
              aria-label="Share post"
              aria-disabled={isOwnProfile}
              title={isOwnProfile ? 'You cannot share your own post here' : 'Share'}
            >
              Share
            </button>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <motion.div
      className="animate-fade-in bg-transparent"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-500 bg-clip-text text-transparent dark:from-yellow-400 dark:to-pink-400">
          MY POSTS ({posts.length})
        </h2>
        {hasMorePosts && (
          <button
            onClick={toggleShowAllPosts}
            className="text-sm font-medium text-indigo-600 dark:text-yellow-400 hover:bg-indigo-20 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 hover:rounded-2xl hover:text-indigo-700 dark:hover:text-yellow-300 transition-colors"
          >
            {showAllPosts ? 'Show Less' : 'See All'}
          </button>
        )}
      </div>
      {/* Posts grid with animated entry */}
      <AnimatePresence>
        {posts.length > 0 ? (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={{
              hidden: {},
              visible: {
                transition: { staggerChildren: 0.09 }
              }
            }}
          >
            {displayedPosts.map((post) => renderPost(post))}
          </motion.div>
        ) : (
          <motion.div
            className="text-center py-12 bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-white/30 dark:border-gray-800"
            variants={itemVariants}
          >
            <svg
              className="w-16 h-16 text-gray-300 dark:text-gray-700 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <p className="text-gray-900 dark:text-yellow-400 font-medium mb-2">No posts yet</p>
            <p className="text-gray-500 dark:text-gray-300 text-sm">Check back later for updates from this talent</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Full view modal */}
      <AnimatePresence>
        {selectedPost && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={handleCloseFullView}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-900 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={e => e.stopPropagation()}
            >
              <div className="p-6">
                {renderPost(selectedPost)}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Enhanced Share Modal with utilities */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={handleCloseShareModal}
        shareUrl={currentSharePost ? generatePostShareUrl(currentSharePost) : ''}
        title={currentSharePost ? generatePostShareTitle(currentSharePost) : ''}
        description={currentSharePost ? generatePostShareDescription(currentSharePost) : ''}
        modalTitle="Share this post"
        modalDescription="Share this post with your friends and followers"
        showQRCode={true}
        onShare={handleShareAction}
      />
    </motion.div>
  );
};

export default PostTab;
