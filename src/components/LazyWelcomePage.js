import React, { lazy, Suspense } from 'react';
import useTranslation from '../hooks/useTranslation';

// Lazy load the WelcomePage component
const WelcomePage = lazy(() => import('./WelcomePage'));

/**
 * LazyWelcomePage component that uses React.lazy for code splitting
 * This improves initial load performance by only loading the WelcomePage
 * component when it's needed
 */
const LazyWelcomePage = () => {
  const { t } = useTranslation('welcome');
  
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
          <div className="relative">
            <div className="absolute inset-0 rounded-full bg-blue-400/20 blur-xl transform scale-125"></div>
            <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-4 border-t-blue-600 relative"></div>
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
              <span className="text-2xl" role="img" aria-label="Gaming controller emoji">🎮</span>
            </div>
          </div>
          <p className="mt-6 text-blue-700 font-medium text-lg">
            {t('loading', 'Preparing your welcome...')}
          </p>
          <p className="mt-2 text-blue-500 text-sm">
            {t('loadingHint', 'This will only take a moment')}
          </p>
        </div>
      }
    >
      <WelcomePage />
    </Suspense>
  );
};

export default LazyWelcomePage;
