import React, { useState } from 'react';
import { useNotifications } from '../context/NotificationContext';

const NotificationCenter = () => {
  const { notifications, unreadCount, markAsRead, markAllAsRead, removeNotification, clearAllNotifications } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);

  const toggleNotificationCenter = () => {
    setIsOpen(!isOpen);
    if (!isOpen && unreadCount > 0) {
      markAllAsRead();
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleNotificationClick = (notification) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    // Process based on notification type
    if (notification.data?.type === 'order_response') {
      // Handle order response notification
      const orderId = notification.data?.order_id;
      const status = notification.data?.status;
      
      // Navigate to order details or take appropriate action
      console.log(`Order ${orderId} ${status}`);
      
      // Example: redirect to order page
      // window.location.href = `/orders/${orderId}`;
    }
  };

  return (
    <div className="notification-center">
      {/* Notification Bell Icon */}
      <div 
        className="notification-bell" 
        onClick={toggleNotificationCenter}
        style={{
          position: 'relative',
          cursor: 'pointer',
          padding: '8px',
        }}
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.36 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.63 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16Z" fill="currentColor"/>
        </svg>
        
        {/* Notification Badge */}
        {unreadCount > 0 && (
          <div 
            style={{
              position: 'absolute',
              top: '0',
              right: '0',
              backgroundColor: 'red',
              color: 'white',
              borderRadius: '50%',
              width: '18px',
              height: '18px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              fontSize: '12px',
            }}
          >
            {unreadCount}
          </div>
        )}
      </div>
      
      {/* Notification Panel */}
      {isOpen && (
        <div 
          style={{
            position: 'absolute',
            top: '60px',
            right: '20px',
            width: '320px',
            maxHeight: '500px',
            backgroundColor: 'white',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            borderRadius: '8px',
            zIndex: 1000,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* Header */}
          <div 
            style={{
              padding: '12px 16px',
              borderBottom: '1px solid #eee',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <h3 style={{ margin: 0, fontSize: '16px' }}>Notifications</h3>
            <button 
              onClick={clearAllNotifications}
              style={{
                background: 'none',
                border: 'none',
                color: '#666',
                cursor: 'pointer',
                fontSize: '14px',
              }}
            >
              Clear All
            </button>
          </div>
          
          {/* Notification List */}
          <div 
            style={{
              overflowY: 'auto',
              maxHeight: '400px',
            }}
          >
            {notifications.length === 0 ? (
              <div 
                style={{
                  padding: '20px',
                  textAlign: 'center',
                  color: '#666',
                }}
              >
                No notifications
              </div>
            ) : (
              notifications.map(notification => (
                <div 
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  style={{
                    padding: '12px 16px',
                    borderBottom: '1px solid #eee',
                    backgroundColor: notification.read ? 'white' : '#f0f7ff',
                    cursor: 'pointer',
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <h4 style={{ margin: 0, fontSize: '14px' }}>{notification.title}</h4>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeNotification(notification.id);
                      }}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: '#999',
                        cursor: 'pointer',
                        fontSize: '14px',
                        padding: '0',
                      }}
                    >
                      ×
                    </button>
                  </div>
                  <p style={{ margin: '4px 0', fontSize: '14px' }}>{notification.body}</p>
                  <span style={{ fontSize: '12px', color: '#999' }}>{formatDate(notification.timestamp)}</span>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
