import React, { useState, useEffect, useRef, forwardRef } from 'react';
import socialPostService from '../services/socialPostService';
import Avatar from './Avatar';

const MentionsInput = forwardRef(({ 
    value, 
    onChange, 
    placeholder = 'Write a comment...', 
    className = '', 
    disabled = false 
}, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [mentionQuery, setMentionQuery] = useState('');
    const [mentionResults, setMentionResults] = useState([]);
    const [showMentions, setShowMentions] = useState(false);
    const [cursorPosition, setCursorPosition] = useState(null);
    const [mentionSearchLoading, setMentionSearchLoading] = useState(false);
    
    const inputRef = useRef(null);
    const mentionDropdownRef = useRef(null);
    const searchTimeoutRef = useRef(null);
    
    // Combine refs (from forwardRef and local ref)
    const handleRef = (el) => {
        inputRef.current = el;
        if (ref) {
            if (typeof ref === 'function') {
                ref(el);
            } else {
                ref.current = el;
            }
        }
    };
    
    // Detect @ characters and handle mention search
    const handleInputChange = (e) => {
        const newValue = e.target.value;
        onChange(newValue);
        
        // Get cursor position
        const cursorPos = e.target.selectionStart;
        setCursorPosition(cursorPos);
        
        // Check if we're typing a mention
        const textBeforeCursor = newValue.substring(0, cursorPos);
        const mentionMatch = textBeforeCursor.match(/@(\w*)$/);
        
        if (mentionMatch) {
            const query = mentionMatch[1];
            setMentionQuery(query);
            
            if (query.length > 0) {
                // Clear previous timeout
                if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                }
                
                // Debounce search
                setMentionSearchLoading(true);
                searchTimeoutRef.current = setTimeout(() => {
                    searchUsers(query);
                }, 300);
            } else {
                setMentionResults([]);
            }
            
            setShowMentions(true);
        } else {
            setShowMentions(false);
        }
    };
    
    // Search for users matching the query
    const searchUsers = async (query) => {
        try {
            const results = await socialPostService.searchUsers(query);
            setMentionResults(results);
        } catch (error) {
            console.error('Error searching for users:', error);
            setMentionResults([]);
        } finally {
            setMentionSearchLoading(false);
        }
    };
    
    // Handle selecting a user from the dropdown
    const selectMention = (username) => {
        // Replace the @query with @username
        const textBeforeMention = value.substring(0, cursorPosition - mentionQuery.length - 1);
        const textAfterMention = value.substring(cursorPosition);
        const newText = `${textBeforeMention}@${username}${textAfterMention}`;
        
        onChange(newText);
        setShowMentions(false);
        
        // Move cursor to the end of the inserted mention
        setTimeout(() => {
            if (inputRef.current) {
                const newPosition = textBeforeMention.length + username.length + 1;
                inputRef.current.focus();
                inputRef.current.setSelectionRange(newPosition, newPosition);
            }
        }, 0);
    };
    
    // Handle click outside of dropdown to close it
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (mentionDropdownRef.current && !mentionDropdownRef.current.contains(event.target)) {
                setShowMentions(false);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    
    // Clear timeout on unmount
    useEffect(() => {
        return () => {
            if (searchTimeoutRef.current) {
                clearTimeout(searchTimeoutRef.current);
            }
        };
    }, []);
    
    return (
        <div className="relative">
            <input
                ref={handleRef}
                type="text"
                value={value}
                onChange={handleInputChange}
                placeholder={placeholder}
                className={`w-full ${className}`}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setTimeout(() => setIsFocused(false), 100)}
                disabled={disabled}
            />
            
            {/* User mention dropdown */}
            {showMentions && isFocused && (
                <div 
                    ref={mentionDropdownRef}
                    className="absolute z-10 mt-1 w-64 bg-white rounded-md shadow-lg max-h-60 overflow-y-auto"
                >
                    {mentionSearchLoading ? (
                        <div className="flex items-center justify-center p-4">
                            <div className="w-5 h-5 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                            <span className="ml-2 text-sm text-gray-500">Searching...</span>
                        </div>
                    ) : mentionResults.length > 0 ? (
                        <ul className="py-1">
                            {mentionResults.map(user => (
                                <li 
                                    key={user.id}
                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={() => selectMention(user.username)}
                                >
                                    <div className="flex items-center">
                                        <Avatar user={user} size="sm" />
                                        <div className="ml-2">
                                            <p className="text-sm font-medium text-gray-900">{user.username}</p>
                                            <p className="text-xs text-gray-500">{user.name}</p>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    ) : mentionQuery.length > 0 ? (
                        <div className="p-4 text-center text-sm text-gray-500">
                            No users found matching "@{mentionQuery}"
                        </div>
                    ) : (
                        <div className="p-4 text-center text-sm text-gray-500">
                            Start typing a username...
                        </div>
                    )}
                </div>
            )}
        </div>
    );
});

export default MentionsInput; 