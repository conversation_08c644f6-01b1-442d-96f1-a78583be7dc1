import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import io from 'socket.io-client';
import OrderNotification from './OrderNotification';

const socket = io(process.env.REACT_APP_SOCKET_URL_OUT, {
    reconnection: true,
    reconnectionAttempts: 5,
});

const Orders = () => {
    const navigate = useNavigate();
    const [orderData, setOrderData] = useState([]);
    const [notifications, setNotifications] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [talentId, setTalentId] = useState(null);

    // Fetch initial orders and talent ID
    useEffect(() => {
        const fetchOrders = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Not logged in.');
                return;
            }

            try {
                // Get user profile
                const userRes = await axios.get(`${process.env.REACT_APP_API_URL}/user`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    },
                });

                const fetchedTalentId = userRes.data?.data?.id ?? userRes.data?.id ?? null;

                if (!fetchedTalentId) {
                    throw new Error('Talent ID not found in profile');
                }

                setTalentId(fetchedTalentId);

                // Fetch orders
                const orderRes = await axios.get(`${process.env.REACT_APP_API_URL}/orders`, {
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`,
                    },
                });

                const orders = orderRes.data?.data?.data ?? [];
                setOrderData(orders);
            } catch (err) {
                setError('Error fetching data: ' + err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchOrders();
    }, []);

    // Listen for real-time orders
    useEffect(() => {
        if (!talentId) return;

        socket.emit('join', `talent:${talentId}`);
        console.log('Socket.IO: Joined channel', `talent:${talentId}`);

        socket.on('order.placed', (data) => {
            console.log('Socket.IO: Received order.placed', data);
            if (data.talent_id === talentId) {
                setNotifications((prev) => [
                    ...prev.filter((n) => n.order_id !== data.order_id),
                    {
                        order_id: data.order_id,
                        talent_id: data.talent_id,
                        customer_id: data.customer_id,
                        expires_at: data.expires_at,
                        title: data.title,
                        customer_name: data.customer_name,
                        credit_amount: data.credit_amount,
                        status: 'pending',
                    },
                ]);
            }
        });

        socket.on('connect_error', (err) => {
            console.error('Socket.IO: Connection error', err);
        });

        return () => {
            socket.off('order.placed');
            socket.off('connect_error');
        };
    }, [talentId]);

    // Handle notification response
    const handleResponded = (orderId, newStatus) => {
        setNotifications((prev) =>
            prev.map((n) =>
                n.order_id === orderId ? { ...n, status: newStatus } : n
            )
        );
        // Update orderData for table
        setOrderData((prev) =>
            prev.map((o) =>
                o.id === orderId ? { ...o, status: newStatus } : o
            )
        );
    };

    // Handle row click
    const handleRowClick = (orderId) => {
        navigate(`/orders/${orderId}`);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
                <span className="ml-2 text-gray-600">Loading orders...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
                <div className="bg-white rounded-lg shadow-sm p-6 max-w-md w-full">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-lg font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">
                                <p>{error}</p>
                            </div>
                            <div className="mt-4">
                                <button
                                    type="button"
                                    onClick={() => window.location.reload()}
                                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                    Try Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-6">Orders</h1>
            <p className="mb-4">Your Talent ID: {talentId || 'Not loaded'}</p>
            
            {/* Real-time notifications */}
            {notifications.length > 0 && (
                <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6">
                    <h2 className="px-4 py-4 border-b border-gray-200 text-lg font-medium text-gray-900">New Orders</h2>
                    <ul className="divide-y divide-gray-200">
                        {notifications.map((order) => (
                            <li key={order.order_id}>
                                <OrderNotification
                                    order={order}
                                    onResponded={handleResponded}
                                />
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6">
                <ul className="divide-y divide-gray-200">
                    {orderData.map((order) => (
                        <li key={order.id}>
                            <div
                                className="block hover:bg-gray-50 cursor-pointer transition-colors"
                                onClick={() => handleRowClick(order.id)}
                            >
                                <div className="px-4 py-4 sm:px-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <p className="text-sm font-medium text-indigo-600 truncate">
                                                Order #{order.id}
                                            </p>
                                            <div className="ml-2 flex-shrink-0 flex">
                                                <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                    ${order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                    order.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                    order.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                                                    'bg-blue-100 text-blue-800'}`}
                                                >
                                                    {order.status}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="ml-2 flex-shrink-0 flex">
                                            <p className="text-sm text-gray-500">
                                                {order.credit_amount} Credits
                                            </p>
                                        </div>
                                    </div>
                                    <div className="mt-2 sm:flex sm:justify-between">
                                        <div className="sm:flex">
                                            <p className="flex items-center text-sm text-gray-500">
                                                Customer: {order.customer?.name || 'N/A'}
                                            </p>
                                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                Talent: {order.talent?.name || 'N/A'}
                                            </p>
                                        </div>
                                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                            <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <p>
                                                {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>

            {orderData.length === 0 && (
                <div className="text-center py-12">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No orders</h3>
                    <p className="mt-1 text-sm text-gray-500">You don't have any orders yet.</p>
                </div>
            )}
        </div>
    );
};

export default Orders;
