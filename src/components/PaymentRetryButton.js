import React, { useState } from 'react';
import PaymentRetryModal from './PaymentRetryModal';
import { usePayment } from '../contexts/PaymentContext';

/**
 * PaymentRetryButton component
 *
 * A button that opens the PaymentRetryModal when clicked
 *
 * @param {Object} props
 * @param {string} props.transactionId - The ID of the transaction to retry
 * @param {string} props.buttonText - The text to display on the button (default: "Retry Payment")
 * @param {string} props.buttonClassName - Additional CSS classes for the button
 * @param {Function} props.onSuccess - Function to call when the payment is successfully retried
 */
const PaymentRetryButton = ({
  transactionId,
  buttonText = "Retry Payment",
  buttonClassName = "",
  onSuccess
}) => {
  const [showModal, setShowModal] = useState(false);

  // Open the modal
  const handleOpenModal = () => {
    setShowModal(true);
  };

  // Close the modal
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Handle successful payment retry
  const handleSuccess = (data) => {
    if (onSuccess) {
      onSuccess(data);
    }
  };

  // Default button classes
  const defaultButtonClasses = "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500";

  return (
    <>
      <button
        type="button"
        className={buttonClassName || defaultButtonClasses}
        onClick={handleOpenModal}
      >
        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        {buttonText}
      </button>

      <PaymentRetryModal
        isOpen={showModal}
        onClose={handleCloseModal}
        transactionId={transactionId}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default PaymentRetryButton;
