import React from 'react';
import { motion } from 'framer-motion';
import TransactionItem from './TransactionItem';

const TransactionList = ({
  transactions,
  isLoading,
  onTransactionClick,
  onRetrySuccess
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="animate-pulse bg-gray-100 h-24 rounded-xl" />
        ))}
      </div>
    );
  }

  if (!transactions || transactions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <svg className="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p className="text-gray-500">No transactions yet</p>
        <p className="text-gray-400 text-sm mt-2">Top up your wallet to get started</p>
      </div>
    );
  }

  // Group transactions by date
  const groupedTransactions = transactions.reduce((groups, transaction) => {
    const date = new Date(transaction.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    if (!groups[date]) {
      groups[date] = [];
    }

    groups[date].push(transaction);
    return groups;
  }, {});

  // Animation variants for list and items
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <motion.div
      className="space-y-6"
      variants={container}
      initial="hidden"
      animate="show"
    >
      {Object.entries(groupedTransactions).map(([date, items]) => (
        <div key={date} className="space-y-3">
          <h3 className="text-gray-500 text-sm font-medium sticky top-0 bg-white/90 backdrop-blur-sm py-2 z-10">
            {date}
          </h3>
          <div className="space-y-3">
            {items.map(transaction => (
              <TransactionItem
                key={transaction.id}
                transaction={transaction}
                onClick={() => onTransactionClick && onTransactionClick(transaction)}
                onRetrySuccess={onRetrySuccess}
              />
            ))}
          </div>
        </div>
      ))}
    </motion.div>
  );
};

export default TransactionList;