import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useOrderPayment } from '../contexts/OrderPaymentContext';
import orderPaymentService from '../services/orderPaymentService';

/**
 * OrderPaymentStatus component
 * 
 * This component displays the payment status for an order and provides
 * options to top up if the balance is insufficient.
 * 
 * @param {Object} props
 * @param {Object} props.order - The order object
 * @param {Function} props.onPaymentSuccess - Callback when payment is successful
 * @param {Function} props.onTopUpClick - Callback when top-up button is clicked
 */
const OrderPaymentStatus = ({ order, onPaymentSuccess, onTopUpClick }) => {
  const [balance, setBalance] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const { orderPaymentState, processOrderPayment, handleTopUp, formatCurrency } = useOrderPayment();
  
  // Fetch wallet balance on mount
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const balance = await orderPaymentService.getWalletBalance();
        setBalance(balance);
      } catch (err) {
        console.error('Error fetching wallet balance:', err);
        setError('Failed to fetch wallet balance');
      }
    };
    
    fetchBalance();
  }, []);
  
  // Handle payment processing
  const handleProcessPayment = async () => {
    if (!order || !order.id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await processOrderPayment({
        orderId: order.id,
        orderType: order.type || 'talent_order',
        amount: order.amount || order.price,
        onSuccess: (data) => {
          setIsLoading(false);
          if (onPaymentSuccess) {
            onPaymentSuccess(data);
          }
        },
        onError: (err) => {
          setIsLoading(false);
          setError(err.message || 'Failed to process payment');
        },
        onInsufficientBalance: (balanceData) => {
          setIsLoading(false);
          setError(`Insufficient balance. Required: ${formatCurrency(order.amount || order.price)}, Available: ${formatCurrency(balanceData.balance)}`);
        }
      });
    } catch (err) {
      console.error('Error processing payment:', err);
      setIsLoading(false);
      setError(err.message || 'Failed to process payment');
    }
  };
  
  // Handle top-up click
  const handleTopUpClick = () => {
    if (onTopUpClick) {
      onTopUpClick();
    } else {
      handleTopUp({
        amount: order.amount || order.price,
        onSuccess: () => {
          // Refresh balance after top-up
          orderPaymentService.getWalletBalance().then(setBalance);
        }
      });
    }
  };
  
  // If no order, don't render anything
  if (!order) return null;
  
  // Get payment status
  const getPaymentStatus = () => {
    if (order.payment_status === 'paid' || order.is_paid) {
      return 'paid';
    } else if (order.payment_status === 'pending') {
      return 'pending';
    } else if (order.payment_status === 'failed') {
      return 'failed';
    } else {
      return 'unpaid';
    }
  };
  
  const paymentStatus = getPaymentStatus();
  
  // Determine if balance is sufficient
  const isBalanceSufficient = balance !== null && balance >= (order.amount || order.price);
  
  // Render different content based on payment status
  const renderContent = () => {
    switch (paymentStatus) {
      case 'paid':
        return (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Payment Completed</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>Your payment for this order has been processed successfully.</p>
                </div>
                <div className="mt-4">
                  <div className="text-sm font-medium text-gray-500">
                    Amount Paid: <span className="font-bold text-gray-900">{formatCurrency(order.amount || order.price)}</span>
                  </div>
                  {order.payment_date && (
                    <div className="text-sm font-medium text-gray-500 mt-1">
                      Payment Date: <span className="font-medium text-gray-700">
                        {new Date(order.payment_date).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'pending':
        return (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Payment Pending</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>Your payment for this order is being processed. This may take a moment.</p>
                </div>
                <div className="mt-4">
                  <div className="text-sm font-medium text-gray-500">
                    Amount: <span className="font-bold text-gray-900">{formatCurrency(order.amount || order.price)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'failed':
        return (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Payment Failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error || 'There was an issue processing your payment. Please try again.'}</p>
                </div>
                <div className="mt-4 flex">
                  <button
                    type="button"
                    onClick={handleProcessPayment}
                    disabled={isLoading || !isBalanceSufficient}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {isLoading ? 'Processing...' : 'Try Again'}
                  </button>
                  
                  {!isBalanceSufficient && (
                    <button
                      type="button"
                      onClick={handleTopUpClick}
                      className="ml-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Top Up Wallet
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'unpaid':
      default:
        return (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-800">Payment Required</h3>
                <div className="mt-2 text-sm text-gray-600">
                  <p>Please complete the payment to proceed with this order.</p>
                </div>
                <div className="mt-2">
                  <div className="text-sm font-medium text-gray-500">
                    Amount: <span className="font-bold text-gray-900">{formatCurrency(order.amount || order.price)}</span>
                  </div>
                  {balance !== null && (
                    <div className="text-sm font-medium text-gray-500 mt-1">
                      Your Balance: <span className={`font-medium ${isBalanceSufficient ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(balance)}
                      </span>
                    </div>
                  )}
                  {!isBalanceSufficient && balance !== null && (
                    <div className="text-sm font-medium text-red-600 mt-1">
                      Shortfall: {formatCurrency((order.amount || order.price) - balance)}
                    </div>
                  )}
                </div>
                <div className="mt-4 flex">
                  <button
                    type="button"
                    onClick={handleProcessPayment}
                    disabled={isLoading || !isBalanceSufficient}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {isLoading ? 'Processing...' : 'Pay Now'}
                  </button>
                  
                  {!isBalanceSufficient && (
                    <button
                      type="button"
                      onClick={handleTopUpClick}
                      className="ml-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Top Up Wallet
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mb-6"
    >
      {renderContent()}
    </motion.div>
  );
};

export default OrderPaymentStatus;
