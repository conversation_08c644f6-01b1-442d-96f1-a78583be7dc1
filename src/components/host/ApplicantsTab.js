import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const ApplicantsTab = ({ applicants = [] }) => {
  const navigate = useNavigate();
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  // Format relative time
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return formatDate(dateString);
    }
  };
  
  // Get status badge color and text
  const getStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          text: 'Pending'
        };
      case 'accepted':
        return {
          color: 'bg-green-100 text-green-800',
          text: 'Accepted'
        };
      case 'rejected':
        return {
          color: 'bg-red-100 text-red-800',
          text: 'Rejected'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          text: status.charAt(0).toUpperCase() + status.slice(1)
        };
    }
  };
  
  // Filter applicants
  const filteredApplicants = applicants.filter(applicant => {
    // Filter by status
    if (filter !== 'all' && applicant.status !== filter) {
      return false;
    }
    
    // Filter by search query
    if (searchQuery && !applicant.user.name.toLowerCase().includes(searchQuery.toLowerCase()) && !applicant.mission.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    return true;
  });
  
  // Handle accept applicant
  const handleAccept = (e, applicantId) => {
    e.stopPropagation();
    // In a real app, this would be an API call
    console.log('Applicant accepted:', applicantId);
  };
  
  // Handle reject applicant
  const handleReject = (e, applicantId) => {
    e.stopPropagation();
    // In a real app, this would be an API call
    console.log('Applicant rejected:', applicantId);
  };

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Applicants</h2>
        <p className="text-gray-600">
          Review and manage applicants for your missions.
        </p>
      </div>
      
      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        {/* Search */}
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search applicants or missions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-lg pl-10 pr-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex items-center space-x-2">
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('pending')}
          >
            Pending
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('accepted')}
          >
            Accepted
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('rejected')}
          >
            Rejected
          </button>
        </div>
      </div>
      
      {/* Applicants List */}
      {filteredApplicants.length > 0 ? (
        <div className="space-y-4">
          {filteredApplicants.map(applicant => (
            <motion.div
              key={applicant.id}
              className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-100"
              whileHover={{ y: -5 }}
            >
              <div className="p-6">
                <div className="flex flex-col md:flex-row md:items-start gap-4">
                  {/* Applicant Info */}
                  <div className="flex items-start">
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-4 bg-gray-200 flex-shrink-0">
                      <img 
                        src={applicant.user.avatar || '/images/default-avatar.jpg'} 
                        alt={applicant.user.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-800 mb-1">{applicant.user.name}</h3>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <span className="mr-2">LV{applicant.user.level}</span>
                        {applicant.user.rating && (
                          <span className="flex items-center">
                            <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            {applicant.user.rating}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusInfo(applicant.status).color}`}>
                          {getStatusInfo(applicant.status).text}
                        </span>
                        <span className="mx-2 text-gray-300">•</span>
                        <span className="text-xs text-gray-500">
                          Applied {formatRelativeTime(applicant.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Mission Info */}
                  <div className="flex-1 bg-gray-50 p-3 rounded-lg">
                    <div 
                      className="font-medium text-gray-800 mb-1 cursor-pointer hover:text-indigo-600 transition-colors"
                      onClick={() => navigate(`/missions/${applicant.mission.id}`)}
                    >
                      {applicant.mission.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatDate(applicant.mission.date)}
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      className="px-3 py-1 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                      onClick={() => navigate(`/talents/${applicant.user.id}`)}
                    >
                      View Profile
                    </button>
                    
                    {applicant.status === 'pending' && (
                      <>
                        <button
                          className="px-3 py-1 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
                          onClick={(e) => handleAccept(e, applicant.id)}
                        >
                          Accept
                        </button>
                        <button
                          className="px-3 py-1 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                          onClick={(e) => handleReject(e, applicant.id)}
                        >
                          Reject
                        </button>
                      </>
                    )}
                    
                    {applicant.status === 'accepted' && (
                      <button
                        className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors"
                        onClick={() => navigate('/chat')}
                      >
                        Message
                      </button>
                    )}
                  </div>
                </div>
                
                {/* Application Message */}
                {applicant.message && (
                  <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                    <p className="text-gray-700 text-sm">{applicant.message}</p>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm p-8 text-center">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No applicants found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? `No applicants match "${searchQuery}"` : filter !== 'all' ? `You don't have any ${filter} applicants.` : "You don't have any applicants yet."}
          </p>
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
            onClick={() => navigate('/missions/create')}
          >
            Create a Mission
          </button>
        </div>
      )}
    </div>
  );
};

export default ApplicantsTab;
