import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const MissionsTab = ({ missions }) => {
  const navigate = useNavigate();
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  // Get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-red-100 text-red-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Filter missions
  const filteredMissions = missions.filter(mission => {
    // Filter by status
    if (filter !== 'all' && mission.status !== filter) {
      return false;
    }
    
    // Filter by search query
    if (searchQuery && !mission.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    return true;
  });
  
  // Handle mission cancellation
  const handleCancel = (e, missionId) => {
    e.stopPropagation();
    // In a real app, this would be an API call
    if (window.confirm('Are you sure you want to cancel this mission? This action cannot be undone.')) {
      console.log('Mission cancelled:', missionId);
    }
  };
  
  // Handle mission edit
  const handleEdit = (e, missionId) => {
    e.stopPropagation();
    navigate(`/missions/edit/${missionId}`);
  };
  
  // Handle view applicants
  const handleViewApplicants = (e, missionId) => {
    e.stopPropagation();
    navigate(`/missions/${missionId}/applicants`);
  };

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">My Missions</h2>
        <p className="text-gray-600">
          Manage all your missions, review applicants, and track progress.
        </p>
      </div>
      
      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        {/* Search */}
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search missions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full border border-gray-300 rounded-lg pl-10 pr-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex items-center space-x-2">
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'open' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('open')}
          >
            Open
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('in_progress')}
          >
            In Progress
          </button>
          <button
            className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'completed' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
            onClick={() => setFilter('completed')}
          >
            Completed
          </button>
        </div>
      </div>
      
      {/* Missions List */}
      {filteredMissions.length > 0 ? (
        <div className="space-y-4">
          {filteredMissions.map(mission => (
            <motion.div
              key={mission.id}
              className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-100 cursor-pointer"
              whileHover={{ y: -5 }}
              onClick={() => navigate(`/missions/${mission.id}`)}
            >
              <div className="p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  {/* Mission Info */}
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-bold text-gray-800 mr-3">{mission.title}</h3>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(mission.status)}`}>
                        {mission.status === 'open' ? 'Recruiting' : mission.status.replace('_', ' ').charAt(0).toUpperCase() + mission.status.replace('_', ' ').slice(1)}
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {formatDate(mission.date)}
                      </div>
                      
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        {mission.slots_filled}/{mission.slots_total} slots filled
                      </div>
                      
                      {mission.applicants > 0 && (
                        <div className="flex items-center text-indigo-600 font-medium">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          {mission.applicants} {mission.applicants === 1 ? 'applicant' : 'applicants'}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {mission.status === 'open' && (
                      <>
                        <button
                          className="px-3 py-1 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                          onClick={(e) => handleEdit(e, mission.id)}
                        >
                          Edit
                        </button>
                        
                        {mission.applicants > 0 && (
                          <button
                            className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors"
                            onClick={(e) => handleViewApplicants(e, mission.id)}
                          >
                            View Applicants
                          </button>
                        )}
                        
                        <button
                          className="px-3 py-1 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                          onClick={(e) => handleCancel(e, mission.id)}
                        >
                          Cancel
                        </button>
                      </>
                    )}
                    
                    {mission.status === 'in_progress' && (
                      <button
                        className="px-3 py-1 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/missions/${mission.id}/manage`);
                        }}
                      >
                        Manage
                      </button>
                    )}
                    
                    {mission.status === 'completed' && (
                      <button
                        className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('/missions/create', { state: { template: mission.id } });
                        }}
                      >
                        Recreate
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm p-8 text-center">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No missions found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? `No missions match "${searchQuery}"` : filter !== 'all' ? `You don't have any ${filter.replace('_', ' ')} missions.` : "You haven't created any missions yet."}
          </p>
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
            onClick={() => navigate('/missions/create')}
          >
            Create a Mission
          </button>
        </div>
      )}
    </div>
  );
};

export default MissionsTab;
