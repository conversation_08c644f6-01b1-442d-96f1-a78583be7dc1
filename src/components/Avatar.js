import React from 'react';
import { getCdnUrl } from '../utils/cdnUtils';

const Avatar = ({ user, size = 'md', className = '' }) => {
    // Size classes
    const sizeClasses = {
        sm: 'w-8 h-8 text-xs',
        md: 'w-10 h-10 text-sm',
        lg: 'w-14 h-14 text-base',
        xl: 'w-20 h-20 text-lg'
    };
    
    // Get user initial(s) for fallback
    const getInitials = () => {
        if (!user) return '?';
        
        const name = user.name || user.username || '';
        if (!name) return '?';
        
        const parts = name.split(' ');
        if (parts.length >= 2) {
            return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
        }
        return name[0].toUpperCase();
    };
    
    return (
        <div className={`${sizeClasses[size] || sizeClasses.md} rounded-full overflow-hidden flex-shrink-0 ${className}`}>
            {user?.profile_picture ? (
                <img
                    src={getCdnUrl(user.profile_picture)}
                    alt={user.name || user.username || 'User'}
                    className="w-full h-full object-cover"
                />
            ) : (
                <div className="w-full h-full flex items-center justify-center bg-indigo-100 text-indigo-600 font-medium">
                    {getInitials()}
                </div>
            )}
        </div>
    );
};

export default Avatar; 