import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import walletAPI from '../services/walletService';
import { motion } from 'framer-motion';
import useTranslation from '../hooks/useTranslation';

/**
 * PaymentReturnHandler component
 * 
 * This component handles the return from payment gateways and processes the payment status.
 * It can be used on any page that needs to handle payment returns.
 * 
 * @param {Object} props
 * @param {Function} props.onSuccess - Function to call when payment is successful
 * @param {Function} props.onError - Function to call when payment fails
 * @param {boolean} props.showFeedback - Whether to show visual feedback (default: true)
 * @param {boolean} props.autoRedirect - Whether to automatically redirect after processing (default: true)
 */
const PaymentReturnHandler = ({
  onSuccess, 
  onError, 
  showFeedback = true,
  autoRedirect = true
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation(['wallet', 'common']);
  const [isProcessing, setIsProcessing] = useState(true);
  const [status, setStatus] = useState(null);
  const [message, setMessage] = useState('');
  const [error, setError] = useState(null);
  
  useEffect(() => {
    const handlePaymentReturn = async () => {
      // Check if we have payment return parameters
      const hasPaymentParams = 
        searchParams.has('id') || 
        searchParams.has('transaction_id') || 
        searchParams.has('billplz[id]') || 
        localStorage.getItem('pendingPaymentId');
      
      if (!hasPaymentParams) {
        setIsProcessing(false);
        return;
      }
      
      try {
        // Get transaction ID from URL or localStorage
        // Different payment gateways might use different parameter names
        const transactionId = 
          searchParams.get('id') || 
          searchParams.get('transaction_id') || 
          searchParams.get('billplz[id]') || 
          localStorage.getItem('pendingPaymentId');
        
        if (!transactionId) {
          throw new Error('No transaction ID found');
        }
        
        // Get payment context from localStorage
        const paymentContext = localStorage.getItem('paymentContext') || 'wallet-topup';
        const paymentReferenceId = localStorage.getItem('paymentReferenceId');
        const paymentReturnPath = localStorage.getItem('paymentReturnPath') || '/wallet';
        
        // Build params object from the search parameters
        const paramsObj = {};
        for (const [key, value] of searchParams.entries()) {
          paramsObj[key] = value;
        }

        // Send return parameters to backend if present
        let response;
        if (Object.keys(paramsObj).length > 0) {
          response = await walletAPI.handlePaymentReturn(paramsObj);
        } else {
          // Fallback to checking payment status by transaction ID
          response = await walletAPI.checkPaymentStatus(transactionId);
        }

        const paymentStatus = response.data.status;
        setStatus(paymentStatus);
        
        // Handle success
        if (paymentStatus === 'success' || paymentStatus === 'completed') {
          setMessage('Payment processed successfully!');
          
          if (onSuccess) {
            onSuccess({
              transactionId,
              status: paymentStatus,
              context: paymentContext,
              referenceId: paymentReferenceId,
              data: response.data
            });
          }
          
          // Auto redirect after success
          if (autoRedirect) {
            setTimeout(() => {
              navigate(paymentReturnPath);
            }, 2000);
          }
        } 
        // Handle pending
        else if (paymentStatus === 'pending') {
          setMessage('Your payment is still being processed. We\'ll update you once it\'s complete.');
          
          if (onError) {
            onError({
              transactionId,
              status: paymentStatus,
              message: 'Payment is still pending',
              context: paymentContext,
              referenceId: paymentReferenceId
            });
          }
        } 
        // Handle failure
        else {
          setError('Payment was not successful. Please try again.');
          
          if (onError) {
            onError({
              transactionId,
              status: paymentStatus,
              message: response.data.message || 'Payment failed',
              context: paymentContext,
              referenceId: paymentReferenceId
            });
          }
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
        setError('Failed to verify payment status. Please contact support if your credits are not updated.');
        
        if (onError) {
          onError({
            error,
            message: 'Failed to verify payment status'
          });
        }
      } finally {
        setIsProcessing(false);
        // Clear payment context from localStorage
        localStorage.removeItem('pendingPaymentId');
        localStorage.removeItem('paymentContext');
        localStorage.removeItem('paymentReferenceId');
        localStorage.removeItem('paymentReturnPath');
      }
    };
    
    handlePaymentReturn();
  }, [searchParams, onSuccess, onError, navigate, autoRedirect]);
  
  // If we're not showing feedback, just return null
  if (!showFeedback) {
    return null;
  }
  
  // Render loading state
  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{t('paymentReturn.processingTitle')}</h1>
        <div className="w-16 h-16 mb-4">
          <svg className="animate-spin w-full h-full text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">{t('paymentReturn.verifying')}</h3>
        <p className="text-gray-600 text-center">
          {t('paymentReturn.processingMessage')}
        </p>
      </div>
    );
  }
  
  // Render success state
  if (status === 'success' || status === 'completed') {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-8"
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">{t('paymentReturn.successTitle')}</h3>
        <p className="text-gray-600 text-center mb-4">
          {message || t('paymentReturn.successMessage')}
        </p>
        <p className="text-sm text-gray-500">
          {t('paymentReturn.redirecting')}
        </p>
      </motion.div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-8"
      >
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">{t('paymentReturn.failureTitle')}</h3>
        <p className="text-red-600 text-center mb-4">
          {error}
        </p>
        <div className="flex space-x-4">
          <button
            onClick={() => navigate('/wallet')}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-gray-800 transition-colors"
          >
            {t('paymentReturn.goWallet')}
          </button>
          <button
            onClick={() => navigate('/wallet?action=topup')}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white transition-colors"
          >
            {t('paymentReturn.tryAgain')}
          </button>
        </div>
      </motion.div>
    );
  }
  
  // Render pending state
  return (
    <motion.div 
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="flex flex-col items-center justify-center p-8"
    >
      <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
        <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 className="text-xl font-medium text-gray-900 mb-2">{t('paymentReturn.verifying')}</h3>
      <p className="text-gray-600 text-center mb-4">
        {message || t('paymentReturn.processingMessage')}
      </p>
      <button
        onClick={() => navigate('/wallet')}
        className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white transition-colors"
      >
        {t('wallet.paymentReturn.goWallet')}
      </button>
    </motion.div>
  );
};

export default PaymentReturnHandler;
