import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import bankAccountService from '../services/bankAccountService';

const BankAccountSelector = ({ 
  selectedAccountId = null, 
  onSelectAccount, 
  showManageLink = true,
  canAddNew = true,
  className = ''
}) => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch bank accounts on component mount
  useEffect(() => {
    fetchBankAccounts();
  }, []);

  // Fetch user's bank accounts
  const fetchBankAccounts = async () => {
    setIsLoading(true);
    try {
      const response = await bankAccountService.getBankAccounts();
      const accounts = response.data.bank_accounts;
      setBankAccounts(accounts);
      
      // If no account is selected and we have accounts, select the primary one or the first one
      if (!selectedAccountId && accounts.length > 0 && onSelectAccount) {
        const primaryAccount = accounts.find(account => account.is_primary);
        onSelectAccount(primaryAccount ? primaryAccount.id : accounts[0].id);
      }
      
      // Update bank accounts count in localStorage for development environment
      localStorage.setItem('bank_accounts_count', accounts.length.toString());
    } catch (err) {
      console.error('Error fetching bank accounts:', err);
      setError('Failed to load bank accounts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle account selection
  const handleSelect = (accountId) => {
    if (onSelectAccount) {
      onSelectAccount(accountId);
    }
  };

  // Show loading skeleton
  if (isLoading) {
    return (
      <div className={`animate-pulse space-y-3 ${className}`}>
        {[...Array(2)].map((_, i) => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    );
  }

  // Show empty state
  if (bankAccounts.length === 0) {
    return (
      <div className={`text-center py-6 ${className}`}>
        <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
        <p className="text-gray-500 mb-4">You don't have any bank accounts yet</p>
        {canAddNew && (
          <Link 
            to="/bank-accounts"
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors inline-flex items-center"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Bank Account
          </Link>
        )}
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={`p-4 bg-red-50 rounded-lg ${className}`}>
        <p className="text-red-600">{error}</p>
        <button 
          onClick={fetchBankAccounts}
          className="mt-2 text-sm text-red-600 hover:underline"
        >
          Try again
        </button>
      </div>
    );
  }

  // Show bank accounts list
  return (
    <div className={className}>
      <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
        {bankAccounts.map(account => (
          <motion.div
            key={account.id}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            className={`border rounded-lg p-3 cursor-pointer transition-colors ${
              selectedAccountId === account.id
                ? 'border-indigo-500 bg-indigo-50'
                : 'border-gray-200 hover:border-indigo-300'
            }`}
            onClick={() => handleSelect(account.id)}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">{account.bank_name}</div>
                <div className="text-gray-500 text-sm">
                  {account.account_number.replace(/^(.{4})(.*)(.{4})$/, '$1••••$3')}
                </div>
                <div className="text-gray-500 text-sm">{account.account_holder_name}</div>
                {account.is_primary && (
                  <div className="mt-1">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Primary
                    </span>
                  </div>
                )}
              </div>
              {selectedAccountId === account.id && (
                <div className="h-5 w-5 bg-indigo-500 rounded-full flex items-center justify-center">
                  <svg className="h-3 w-3 text-white" viewBox="0 0 12 12" fill="none">
                    <path d="M3.5 6.5L5 8L8.5 4.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
      
      {showManageLink && (
        <div className="flex justify-between items-center mt-4">
          <Link
            to="/bank-accounts"
            className="text-indigo-600 text-sm hover:underline flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Manage Bank Accounts
          </Link>
          
          {canAddNew && (
            <Link
              to="/bank-accounts"
              className="text-indigo-600 text-sm hover:underline flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add New Account
            </Link>
          )}
        </div>
      )}
    </div>
  );
};

export default BankAccountSelector; 