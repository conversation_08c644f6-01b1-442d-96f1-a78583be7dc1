import React from 'react';
import { motion } from 'framer-motion';
import { formatDate, formatCredits, formatCurrency, formatTransactionStatus } from '../utils/formatters';

const TransactionDetails = ({ transaction, onClose }) => {
  if (!transaction) return null;

  // Extract metadata
  const metadata = transaction.metadata || {};
  const isWithdrawal = 
    transaction.transaction_type === 'deduct' && 
    metadata.withdrawal_method;
  
  // For withdrawals
  const fiatCurrency = metadata.fiat_currency || 'MYR';
  const fiatAmount = metadata.fiat_amount || 0;
  const processingFee = metadata.processing_fee || 0;
  const conversionRate = metadata.conversion_rate || 0;
  const bankAccount = transaction.bankAccount || null;
  
  // Format dates
  const createdDate = new Date(transaction.created_at);
  const updatedDate = transaction.updated_at ? new Date(transaction.updated_at) : null;
  
  // Helper to get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Get transaction type icon
  const getTransactionIcon = () => {
    if (isWithdrawal) {
      return (
        <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-7a2 2 0 012-2h2m3-4H9a2 2 0 00-2 2v7a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-1m-1 4l-3 3m0 0l-3-3m3 3V3" />
        </svg>
      );
    } else if (transaction.transaction_type === 'add') {
      if (metadata.payment_gateway) {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      } else if (metadata.refund_for) {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
          </svg>
        );
      } else {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      }
    } else {
      return (
        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 12H6" />
        </svg>
      );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm"
    >
      <div className="flex items-center justify-between p-5 border-b border-gray-100">
        <div className="flex items-center">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-4 ${
            transaction.transaction_type === 'add' 
              ? 'bg-green-100' 
              : 'bg-red-100'
          }`}>
            {getTransactionIcon()}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{transaction.description}</h3>
            <p className="text-sm text-gray-500">
              {formatDate(transaction.created_at, 'full')}
            </p>
          </div>
        </div>
        {transaction.status && (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
            {formatTransactionStatus(transaction.status)}
          </span>
        )}
      </div>

      <div className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Transaction ID</p>
              <p className="mt-1 text-sm text-gray-900">{transaction.id}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Amount</p>
              <p className="mt-1 text-sm text-gray-900 font-semibold">
                <span className={transaction.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'}>
                  {transaction.transaction_type === 'add' ? '+' : '-'} {formatCredits(Math.abs(transaction.credits))}
                </span>
              </p>
            </div>
            
            {isWithdrawal && (
              <>
                <div>
                  <p className="text-sm font-medium text-gray-500">Processing Fee</p>
                  <p className="mt-1 text-sm text-gray-900">
                    {processingFee} Credits
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500">Net Amount</p>
                  <p className="mt-1 text-sm text-gray-900">
                    {Math.abs(transaction.credits) - processingFee} Credits
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500">Fiat Amount</p>
                  <p className="mt-1 text-sm text-gray-900 font-semibold">
                    {formatCurrency(fiatAmount, fiatCurrency)}
                  </p>
                  <p className="mt-0.5 text-xs text-gray-500">
                    Rate: 1 Credit = {conversionRate} {fiatCurrency}
                  </p>
                </div>
              </>
            )}
            
            <div>
              <p className="text-sm font-medium text-gray-500">Balance Before</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatCredits(transaction.balance_before)}
              </p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Balance After</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatCredits(transaction.balance_after)}
              </p>
            </div>
          </div>
          
          <div className="space-y-4">
            {isWithdrawal && bankAccount && (
              <div>
                <p className="text-sm font-medium text-gray-500">Bank Account</p>
                <div className="mt-1 text-sm text-gray-900">
                  <p className="font-medium">{bankAccount.bank_name}</p>
                  <p>{bankAccount.account_number.replace(/^(.{4})(.*)(.{4})$/, '$1••••$3')}</p>
                  <p>{bankAccount.account_holder_name}</p>
                </div>
              </div>
            )}
            
            {metadata.payment_gateway && (
              <div>
                <p className="text-sm font-medium text-gray-500">Payment Method</p>
                <p className="mt-1 text-sm text-gray-900">
                  {metadata.payment_gateway.charAt(0).toUpperCase() + metadata.payment_gateway.slice(1)}
                </p>
                {metadata.payment_transaction_id && (
                  <p className="mt-0.5 text-xs text-gray-500">
                    Ref: {metadata.payment_transaction_id}
                  </p>
                )}
              </div>
            )}
            
            {metadata.refund_for && (
              <div>
                <p className="text-sm font-medium text-gray-500">Refund Reference</p>
                <p className="mt-1 text-sm text-gray-900">
                  {metadata.refund_for}
                </p>
                {metadata.reason && (
                  <p className="mt-0.5 text-xs text-gray-500">
                    Reason: {metadata.reason === 'user_cancelled' ? 'Cancelled by user' : metadata.reason}
                  </p>
                )}
              </div>
            )}
            
            {transaction.status && (
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className="mt-1 text-sm text-gray-900">
                  {transaction.status === 'pending' && (
                    <span className="text-yellow-600">
                      Pending {isWithdrawal && `(estimated ${metadata.estimated_days})`}
                    </span>
                  )}
                  {transaction.status === 'completed' && (
                    <span className="text-green-600">
                      Completed
                    </span>
                  )}
                  {transaction.status === 'failed' && (
                    <span className="text-red-600">
                      Failed: {metadata.failure_reason || 'Unknown error'}
                    </span>
                  )}
                  {transaction.status === 'cancelled' && (
                    <span className="text-gray-600">
                      Cancelled
                    </span>
                  )}
                </p>
              </div>
            )}
            
            <div>
              <p className="text-sm font-medium text-gray-500">Created Date</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatDate(transaction.created_at, 'full')}
              </p>
            </div>
            
            {updatedDate && (
              <div>
                <p className="text-sm font-medium text-gray-500">Last Updated</p>
                <p className="mt-1 text-sm text-gray-900">
                  {formatDate(transaction.updated_at, 'full')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TransactionDetails; 