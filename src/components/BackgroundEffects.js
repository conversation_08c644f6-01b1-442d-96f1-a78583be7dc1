import React from 'react';
import { motion, useViewportScroll, useTransform } from 'framer-motion';

// Responsive configurations
const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

// Generate shining elements
const shineElements = Array.from({ length: isMobile ? 12 : 20 }).map((_, i) => {
  const size = Math.random() * (isMobile ? 100 : 200) + (isMobile ? 50 : 100);
  const x = Math.random() * 100;
  const y = Math.random() * 100;
  const delay = Math.random() * 8;
  const duration = Math.random() * 15 + 20;
  const intensity = Math.random() * 0.6 + 0.2;
  
  return { size, x, y, delay, duration, intensity, key: i };
});

// Generate sparkle particles
const sparkles = Array.from({ length: isMobile ? 15 : 30 }).map((_, i) => ({
  key: i,
  x: Math.random() * 100,
  y: Math.random() * 100,
  size: Math.random() * 4 + 2,
  delay: Math.random() * 10,
  duration: Math.random() * 8 + 6,
}));

export default function BackgroundEffects() {
  const { scrollY } = useViewportScroll();
  const yParallax1 = useTransform(scrollY, [0, 1000], [0, 100]);
  const yParallax2 = useTransform(scrollY, [0, 1000], [0, 150]);

  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden pointer-events-none z-0">
      {/* Main Shining Background Gradient */}
      <motion.div
        className="absolute inset-0 w-full h-full"
        style={{
          background: 'linear-gradient(45deg, rgba(255,255,255,0.03) 0%, rgba(255,255,255,0.08) 50%, rgba(255,255,255,0.03) 100%)',
        }}
        animate={{
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      {/* Large Shining Orbs */}
      {shineElements.map((shine) => (
        <motion.div
          key={shine.key}
          className="absolute rounded-full"
          style={{
            left: `${shine.x}%`,
            top: `${shine.y}%`,
            width: shine.size,
            height: shine.size,
            background: `radial-gradient(circle, rgba(255,255,255,${shine.intensity}) 0%, rgba(255,255,255,0.1) 30%, transparent 70%)`,
            filter: 'blur(20px)',
          }}
          animate={{
            scale: [0.8, 1.2, 0.8],
            opacity: [0.3, 0.8, 0.3],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: shine.duration,
            repeat: Infinity,
            delay: shine.delay,
            ease: 'easeInOut',
          }}
        />
      ))}

      {/* Sparkle Particles */}
      {sparkles.map((sparkle) => (
        <motion.div
          key={sparkle.key}
          className="absolute"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`,
            width: sparkle.size,
            height: sparkle.size,
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            rotate: [0, 360],
          }}
          transition={{
            duration: sparkle.duration,
            repeat: Infinity,
            delay: sparkle.delay,
            ease: 'easeOut',
          }}
        >
          <div
            style={{
              width: '100%',
              height: '100%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.4) 50%, transparent 100%)',
              borderRadius: '50%',
              filter: 'blur(1px)',
            }}
          />
        </motion.div>
      ))}

      {/* Animated Shine Sweep */}
      <motion.div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.15) 50%, transparent 100%)',
          transform: 'translateX(-100%)',
        }}
        animate={{
          transform: ['translateX(-100%)', 'translateX(100%)'],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'easeInOut',
          repeatDelay: 3,
        }}
      />

      {/* Diagonal Shine Sweep */}
      <motion.div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          background: 'linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%)',
          transform: 'translate(-100%, -100%)',
        }}
        animate={{
          transform: ['translate(-100%, -100%)', 'translate(100%, 100%)'],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: 'easeInOut',
          repeatDelay: 5,
          delay: 2,
        }}
      />

      {/* Radial Shine Pulses */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
          filter: 'blur(40px)',
          y: yParallax1,
        }}
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.3, 0.7, 0.3],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <motion.div
        className="absolute top-3/4 right-1/4 w-80 h-80"
        style={{
          background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%)',
          filter: 'blur(50px)',
          y: yParallax2,
        }}
        animate={{
          scale: [1.2, 0.8, 1.2],
          opacity: [0.4, 0.8, 0.4],
        }}
        transition={{
          duration: 14,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 3,
        }}
      />

      {/* Center Glow */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-[600px] h-[600px]"
        style={{
          transform: 'translate(-50%, -50%)',
          background: 'radial-gradient(circle, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 40%, transparent 70%)',
          filter: 'blur(60px)',
        }}
        animate={{
          scale: [0.9, 1.1, 0.9],
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: 16,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      {/* Shimmer Lines */}
      <svg
        className="absolute inset-0 w-full h-full opacity-20"
        viewBox="0 0 1440 800"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <motion.path
          d="M0,400 Q360,300 720,400 T1440,400"
          stroke="url(#shimmerGradient1)"
          strokeWidth="2"
          fill="none"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ 
            pathLength: [0, 1, 0],
            opacity: [0, 0.8, 0]
          }}
          transition={{ 
            duration: 8, 
            repeat: Infinity,
            ease: 'easeInOut',
            repeatDelay: 2
          }}
        />
        <motion.path
          d="M0,300 Q360,500 720,300 T1440,300"
          stroke="url(#shimmerGradient2)"
          strokeWidth="1.5"
          fill="none"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ 
            pathLength: [0, 1, 0],
            opacity: [0, 0.6, 0]
          }}
          transition={{ 
            duration: 10, 
            repeat: Infinity,
            ease: 'easeInOut',
            repeatDelay: 3,
            delay: 2
          }}
        />
        <defs>
          <linearGradient id="shimmerGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ffffff" stopOpacity="0" />
            <stop offset="50%" stopColor="#ffffff" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#ffffff" stopOpacity="0" />
          </linearGradient>
          <linearGradient id="shimmerGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#ffffff" stopOpacity="0" />
            <stop offset="50%" stopColor="#ffffff" stopOpacity="0.6" />
            <stop offset="100%" stopColor="#ffffff" stopOpacity="0" />
          </linearGradient>
        </defs>
      </svg>

      {/* Floating Light Particles */}
      {Array.from({ length: isMobile ? 8 : 16 }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            background: 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.2) 100%)',
            filter: 'blur(2px)',
          }}
          animate={{
            y: [0, -150, 0],
            x: [0, Math.random() * 100 - 50, 0],
            opacity: [0, 1, 0],
            scale: [0.5, 1.2, 0.5],
          }}
          transition={{
            duration: 12 + Math.random() * 8,
            repeat: Infinity,
            delay: Math.random() * 10,
            ease: 'easeOut',
          }}
        />
      ))}

      {/* Subtle Texture Overlay */}
      <div
        className="absolute inset-0 w-full h-full z-10"
        style={{
          backgroundImage:
            'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' fill=\'none\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Crect width=\'60\' height=\'60\' fill=\'transparent\'/%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'1\' fill=\'%23ffffff\' fill-opacity=\'0.05\'/%3E%3C/svg%3E")',
          opacity: 0.3,
          pointerEvents: 'none',
        }}
      />

      {/* Enhanced Vignette */}
      <div
        className="absolute inset-0 w-full h-full z-20 pointer-events-none"
        style={{
          background: 'radial-gradient(ellipse at center, rgba(255,255,255,0) 60%, rgba(0,0,0,0.05) 100%)',
        }}
      />

      {/* CSS for additional shine effects */}
      <style jsx>{`
        @keyframes shine {
          0% { transform: translateX(-100%) skewX(-15deg); }
          100% { transform: translateX(200vw) skewX(-15deg); }
        }
        
        @keyframes shimmer {
          0%, 100% { opacity: 0.2; }
          50% { opacity: 0.8; }
        }
      `}</style>
    </div>
  );
}