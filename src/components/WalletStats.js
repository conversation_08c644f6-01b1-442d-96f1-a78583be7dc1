import React from 'react';
import { motion } from 'framer-motion';

const WalletStats = ({ transactions }) => {
  // Early return if no transactions
  if (!transactions || transactions.length === 0) {
    return (
      <div className="bg-white rounded-xl p-6 text-center">
        <p className="text-gray-500">No transaction data available yet.</p>
      </div>
    );
  }

  // Calculate statistics
  const getTotalTopUps = () => {
    return transactions
      .filter(t => t.transaction_type === 'add')
      .reduce((total, t) => total + t.credits, 0);
  };

  const getTotalSpent = () => {
    return Math.abs(
      transactions
        .filter(t => t.transaction_type === 'deduct')
        .reduce((total, t) => total + t.credits, 0)
    );
  };

  const getMostCommonType = () => {
    const typeCount = transactions.reduce((acc, t) => {
      // Extract transaction type from description
      const desc = t.description.toLowerCase();
      let type = 'Other';

      if (desc.includes('top up')) type = 'Top Up';
      else if (desc.includes('platform fee')) type = 'Platform Fee';
      else if (desc.includes('buy gift')) type = 'Gifts';
      else if (desc.includes('mission income')) type = 'Mission Income';
      else if (desc.includes('expense')) type = 'Expense';

      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    // Find the type with the highest count
    return Object.entries(typeCount).sort((a, b) => b[1] - a[1])[0][0];
  };

  const totalTopUps = getTotalTopUps();
  const totalSpent = getTotalSpent();
  const mostCommonType = getMostCommonType();

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h2 className="text-xl font-bold text-gray-900 mb-6">Wallet Statistics</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Credits Added */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200"
        >
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-800">Total Added</h3>
          </div>
          <p className="text-2xl font-bold text-green-600">+{totalTopUps} credits</p>
          <p className="text-sm text-gray-500 mt-1">All time total</p>
        </motion.div>
        
        {/* Total Credits Spent */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200"
        >
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-800">Total Spent</h3>
          </div>
          <p className="text-2xl font-bold text-red-600">-{totalSpent} credits</p>
          <p className="text-sm text-gray-500 mt-1">All time total</p>
        </motion.div>
        
        {/* Most Common Transaction */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-4 border border-indigo-200"
        >
          <div className="flex items-center mb-3">
            <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-800">Most Common</h3>
          </div>
          <p className="text-xl font-bold text-indigo-600">{mostCommonType}</p>
          <p className="text-sm text-gray-500 mt-1">Transaction type</p>
        </motion.div>
      </div>
      
      {/* Visual Comparison */}
      <div className="mt-6 pt-6 border-t border-gray-100">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Balance Flow</h3>
        <div className="h-4 bg-gray-100 rounded-full overflow-hidden flex">
          <motion.div 
            initial={{ width: 0 }} 
            animate={{ width: `${(totalTopUps / (totalTopUps + totalSpent)) * 100}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="h-full bg-green-500"
          />
          <motion.div 
            initial={{ width: 0 }} 
            animate={{ width: `${(totalSpent / (totalTopUps + totalSpent)) * 100}%` }}
            transition={{ duration: 1, ease: "easeOut", delay: 0.3 }}
            className="h-full bg-red-500"
          />
        </div>
        <div className="flex justify-between mt-2 text-xs">
          <span className="text-green-600">Credits In ({Math.round((totalTopUps / (totalTopUps + totalSpent)) * 100)}%)</span>
          <span className="text-red-600">Credits Out ({Math.round((totalSpent / (totalTopUps + totalSpent)) * 100)}%)</span>
        </div>
      </div>
    </div>
  );
};

export default WalletStats; 