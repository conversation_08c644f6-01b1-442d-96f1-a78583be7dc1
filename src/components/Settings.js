import React, { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import api from '../services/api';
import useTranslation from '../hooks/useTranslation';
import profileService from '../services/profileService';
import ekycService from '../services/ekycService';
import authService from '../services/authService';
import { FiLogOut } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import LogoutConfirmation from './Logout';
import { useAuth } from '../contexts/AuthContext';
import EmergencyContactModal from './modals/EmergencyContactModal';
import FormInput from './common/FormInput';

// Modal Components
const SettingsModal = ({ title, children, onClose }) => (
    <motion.div
        className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
    >
        <motion.div
            className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl w-full max-w-xl mx-4 shadow-2xl border border-gray-100 dark:border-gray-700"
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
        >
            <div className="p-6">
                <div className="flex justify-between items-center mb-6 border-b border-gray-100 pb-4 dark:border-gray-700">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">{title}</h3>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-red-100 dark:hover:bg-red-900 bg-white dark:bg-gray-900 rounded-full transition-all duration-200 hover:rotate-90"
                    >
                        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div className="text-gray-800 dark:text-gray-200">
                    {children}
                </div>
            </div>
        </motion.div>
    </motion.div>
);

// Email Verification Modal Component
const EmailVerificationModal = ({ onClose }) => {
    const { t } = useTranslation(['settings', 'common']);
    const { user, updateUser, checkAuth } = useAuth();
    const [step, setStep] = useState('initial'); // 'initial', 'loading', 'success', 'error'
    const [error, setError] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    // Check if email is already verified
    const isEmailVerified = user?.email_verified_at || user?.email_verified;

    const handleSendVerification = async () => {
        if (!user?.email) {
            setError('No email address found. Please update your profile with an email address first.');
            setStep('error');
            return;
        }

        setIsLoading(true);
        setError(null);
        setStep('loading');

        try {
            const token = localStorage.getItem('token');
            const apiBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

            const response = await axios.post(
                `${apiBaseUrl}/email/send-verification`,
                { email: user.email },
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                }
            );

            if (response.data.message) {
                setStep('success');
            } else {
                throw new Error('Unexpected response format');
            }
        } catch (err) {
            console.error('Email verification error:', err);

            let errorMessage = 'Failed to send verification email. Please try again.';

            if (err.response?.status === 422) {
                const validationErrors = err.response.data.errors;
                if (validationErrors?.email) {
                    errorMessage = Array.isArray(validationErrors.email)
                        ? validationErrors.email[0]
                        : validationErrors.email;
                } else {
                    errorMessage = err.response.data.message || errorMessage;
                }
            } else if (err.response?.status === 500) {
                errorMessage = err.response.data.message || 'Server error occurred. Please try again later.';
            } else if (err.response?.data?.message) {
                errorMessage = err.response.data.message;
            }

            setError(errorMessage);
            setStep('error');
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        if (!isLoading) {
            onClose();
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape' && !isLoading) {
            onClose();
        }
    };

    useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isLoading]);

    // Check for email verification status updates periodically
    useEffect(() => {
        let intervalId;

        // Only check if we're in success state and email is not yet verified
        if (step === 'success' && !isEmailVerified) {
            intervalId = setInterval(async () => {
                try {
                    // Refresh user data to check if email was verified
                    await checkAuth();
                } catch (error) {
                    console.error('Error checking email verification status:', error);
                }
            }, 5000); // Check every 5 seconds
        }

        return () => {
            if (intervalId) {
                clearInterval(intervalId);
            }
        };
    }, [step, isEmailVerified, checkAuth]);

    // Show success message when email becomes verified
    useEffect(() => {
        if (isEmailVerified && step === 'success') {
            // Email was verified! Update the step to show verified state
            setTimeout(() => {
                setStep('verified');
            }, 1000);
        }
    }, [isEmailVerified, step]);

    return (
        <motion.div
            className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => e.target === e.currentTarget && handleClose()}
        >
            <motion.div
                className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl w-full max-w-md mx-4 shadow-2xl border border-gray-100 dark:border-gray-700 overflow-hidden"
                initial={{ scale: 0.95, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.95, opacity: 0, y: 20 }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-900 dark:to-purple-900 p-6 text-white">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold">{t('account.emailVerification.title')}</h3>
                            </div>
                            <button
                                onClick={handleClose}
                                disabled={isLoading}
                                className="p-2 hover:bg-white/20 dark:hover:bg-gray-800 rounded-full transition-all duration-200 hover:rotate-90 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="p-6 text-gray-800 dark:text-gray-200">
                    {isEmailVerified ? (
                        // Already Verified State
                        <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="text-center space-y-4"
                        >
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div>
                                <h4 className="text-lg font-semibold text-gray-900 dark:text-indigo-100 mb-2">Email Already Verified</h4>
                                <p className="text-gray-600 dark:text-indigo-100">
                                    Your email address <span className="font-medium text-indigo-600">{user?.email}</span> is already verified.
                                </p>
                            </div>
                            <button
                                onClick={handleClose}
                                className="w-full bg-indigo-600 dark:bg-indigo-700 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                            >
                                Close
                            </button>
                        </motion.div>
                    ) : (
                        // Verification Flow
                        <div className="space-y-6">
                            {step === 'initial' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Verify Your Email</h4>
                                        <p className="text-gray-600 mb-4">
                                            We'll send a verification link to <span className="font-medium text-indigo-600">{user?.email || 'your email address'}</span>
                                        </p>
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p>Click the link in the email to complete verification. The link will expire in 24 hours.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={handleSendVerification}
                                        disabled={!user?.email}
                                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Send Verification Email
                                    </button>
                                    {!user?.email && (
                                        <p className="text-sm text-red-600">
                                            Please add an email address to your profile first.
                                        </p>
                                    )}
                                </motion.div>
                            )}

                            {step === 'loading' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-800 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-indigo-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Sending Email...</h4>
                                        <p className="text-gray-600">
                                            Please wait while we send the verification email.
                                        </p>
                                    </div>
                                </motion.div>
                            )}

                            {step === 'success' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Email Sent!</h4>
                                        <p className="text-gray-600 mb-4">
                                            We've sent a verification link to <span className="font-medium text-indigo-600">{user?.email}</span>
                                        </p>
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium">Check your email</p>
                                                    <p>Click the verification link to complete the process. Don't forget to check your spam folder!</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={async () => {
                                            // Refresh user data to get updated verification status
                                            await checkAuth();
                                            handleClose();
                                        }}
                                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                                    >
                                        Close
                                    </button>
                                </motion.div>
                            )}

                            {step === 'error' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Error Occurred</h4>
                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800 mb-4">
                                            {error}
                                        </div>
                                    </div>
                                    <div className="flex space-x-3">
                                        <button
                                            onClick={() => setStep('initial')}
                                            className="flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                                        >
                                            Try Again
                                        </button>
                                        <button
                                            onClick={handleClose}
                                            className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 transition-colors font-medium"
                                        >
                                            Close
                                        </button>
                                    </div>
                                </motion.div>
                            )}

                            {step === 'verified' && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-center space-y-4"
                                >
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Email Verified Successfully!</h4>
                                        <p className="text-gray-600 mb-4">
                                            Your email address <span className="font-medium text-indigo-600">{user?.email}</span> has been verified.
                                        </p>
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-800">
                                            <div className="flex items-start space-x-2">
                                                <svg className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <div>
                                                    <p className="font-medium">Verification Complete</p>
                                                    <p>You can now access all email-related features and receive important notifications.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={handleClose}
                                        className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
                                    >
                                        Continue
                                    </button>
                                </motion.div>
                            )}
                        </div>
                    )}
                </div>
            </motion.div>
        </motion.div>
    );
};

const Settings = ({ onClose }) => {
    const { t, tns, changeLanguage, currentLanguage, supportedLanguages } = useTranslation(['settings', 'common']);
    const { user, logout } = useAuth();
    const [activeModal, setActiveModal] = useState(null);

    // Check if email is verified from user data
    const isVerified = user?.email_verified_at || user?.email_verified;
    const [allow3rdPartyAccess, setAllow3rdPartyAccess] = useState(false);
    const [isLoading3rdPartyAccess, setIsLoading3rdPartyAccess] = useState(false);
    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const navigate = useNavigate();

    // KYC-specific states
    const [kycStatus, setKycStatus] = useState('not_started'); // not_started, in_progress, pending_review, verified, rejected
    const [kycStep, setKycStep] = useState(1); // 1: intro, 2: nationality, 3: document_upload, 4: selfie, 5: review
    const [selectedNationality, setSelectedNationality] = useState(null); // 'malaysian' or 'foreigner'
    const [uploadedDocuments, setUploadedDocuments] = useState([]);
    const [selfieImage, setSelfieImage] = useState(null);
    const [kycError, setKycError] = useState(null);
    const [isSubmittingKyc, setIsSubmittingKyc] = useState(false);

    // Foreigner-specific states
    const [foreignerInfo, setForeignerInfo] = useState({
        full_name: '',
        passport_number: '',
        country: ''
    });

    // Malaysian-specific states
    const [malaysianInfo, setMalaysianInfo] = useState({
        full_name: '',
        ic_number: ''
    });

    // Selfie capture states
    const [cameraStream, setCameraStream] = useState(null);
    const [isCameraActive, setIsCameraActive] = useState(false);
    const [cameraError, setCameraError] = useState(null);
    const [isCapturing, setIsCapturing] = useState(false);
    const [captureMode, setCaptureMode] = useState('camera'); // 'camera', 'upload'
    const [cameraSupported, setCameraSupported] = useState(true);

    // Static content states
    const [termsContent, setTermsContent] = useState('');
    const [privacyContent, setPrivacyContent] = useState('');
    const [aboutUsContent, setAboutUsContent] = useState('');
    const [contactUsContent, setContactUsContent] = useState('');

    // Loading states for static content
    const [isLoadingTerms, setIsLoadingTerms] = useState(false);
    const [isLoadingPrivacy, setIsLoadingPrivacy] = useState(false);
    const [isLoadingAboutUs, setIsLoadingAboutUs] = useState(false);
    const [isLoadingContactUs, setIsLoadingContactUs] = useState(false);

    // Error states for static content
    const [termsError, setTermsError] = useState(null);
    const [privacyError, setPrivacyError] = useState(null);
    const [aboutUsError, setAboutUsError] = useState(null);
    const [contactUsError, setContactUsError] = useState(null);

    // KYC Enhancement states for final polish
    const [kycProgress, setKycProgress] = useState(0);
    const [isKycModalClosing, setIsKycModalClosing] = useState(false);

    // State for connected applications
    const [connectedApps, setConnectedApps] = useState([]);

    // Password change states
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmNewPassword, setConfirmNewPassword] = useState('');
    const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
    const [passwordError, setPasswordError] = useState(null);

    // Feedback modal states
    const [feedbackTopic, setFeedbackTopic] = useState('');
    const [feedbackTopics, setFeedbackTopics] = useState([]);
    const [feedbackDescription, setFeedbackDescription] = useState('');
    const [feedbackAttachments, setFeedbackAttachments] = useState([]); // Array of files, max 5
    const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
    const [feedbackError, setFeedbackError] = useState(null);
    const [feedbackSuccess, setFeedbackSuccess] = useState(null);
    const [isLoadingFeedbackTopics, setIsLoadingFeedbackTopics] = useState(false);
    const [feedbackFileError, setFeedbackFileError] = useState(null);
    // Accessibility: feedback modal focus trap
    const feedbackFirstInputRef = React.useRef(null);
    // Add ref to track if KYC status has been requested
    const kycStatusRequested = useRef(false);
    useEffect(() => {
        if (activeModal === 'feedback' && feedbackFirstInputRef.current) {
            feedbackFirstInputRef.current.focus();
        }
        // Keyboard navigation: ESC closes modal
        const handleKeyDown = (e) => {
            if (activeModal === 'feedback') {
                if (e.key === 'Escape') {
                    setActiveModal(null);
                } else if (e.key === 'Enter' && document.activeElement?.form) {
                    // Let form handle submit
                } else if (e.key === 'Tab') {
                    // Focus trap: cycle focus within modal
                    const focusableEls = Array.from(document.querySelectorAll('[data-feedback-modal] input, [data-feedback-modal] select, [data-feedback-modal] textarea, [data-feedback-modal] button'));
                    if (focusableEls.length > 0) {
                        const firstEl = focusableEls[0];
                        const lastEl = focusableEls[focusableEls.length - 1];
                        if (!e.shiftKey && document.activeElement === lastEl) {
                            e.preventDefault();
                            firstEl.focus();
                        } else if (e.shiftKey && document.activeElement === firstEl) {
                            e.preventDefault();
                            lastEl.focus();
                        }
                    }
                }
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [activeModal]);

    const {
        thirdPartyAccess,
        kycStatus: authKycStatus,
        fetchThirdPartyAccess,
        fetchKycStatus
    } = useAuth();

    // Load settings from context or fetch if missing
    useEffect(() => {
        if (thirdPartyAccess) {
            setAllow3rdPartyAccess(thirdPartyAccess.enabled);
            setConnectedApps(thirdPartyAccess.connected_apps || []);
        } else {
            setIsLoading3rdPartyAccess(true);
            fetchThirdPartyAccess().finally(() => setIsLoading3rdPartyAccess(false));
        }

        if (authKycStatus) {
            setKycStatus(authKycStatus);
            setKycStep(authKycStatus === 'verified' || authKycStatus === 'pending_review' ? 5 : 1);
        } else {
            fetchKycStatus();
        }
    }, [thirdPartyAccess, authKycStatus, fetchThirdPartyAccess, fetchKycStatus]);

    // Memoize the loadKycStatus function to prevent unnecessary recreations
    const loadKycStatus = useCallback(async (forceRefresh = false) => {
        if (kycStatusRequested.current && !forceRefresh) {
            return;
        }
        kycStatusRequested.current = true;
        await fetchKycStatus();
    }, [fetchKycStatus]);

    // Load KYC status if not already available
    useEffect(() => {
        if (!authKycStatus) {
            loadKycStatus();
        }
    }, [authKycStatus, loadKycStatus]);

    // Manual function to clear all KYC cache (for testing/debugging)
    const clearAllKycCache = () => {
        console.log('Manually clearing all KYC cache...');

        // Clear all KYC-related localStorage items
        const kycKeys = [
            'kyc_progress',
            'kyc_submission',
            'kyc_verified',
            'kyc_status',
            'kyc_error',
            'email_verified',
            'bank_accounts_count'
        ];

        kycKeys.forEach(key => {
            localStorage.removeItem(key);
            console.log(`Cleared localStorage key: ${key}`);
        });

        // Reset component state
        setKycStatus('not_started');
        setKycStep(1);
        setKycError(null);
        setSelectedNationality(null);
        setUploadedDocuments([]);
        setSelfieImage(null);
        setKycProgress(0);
        setForeignerInfo({
            full_name: '',
            passport_number: '',
            country: ''
        });
        setMalaysianInfo({
            full_name: '',
            ic_number: ''
        });

        console.log('KYC cache cleared and state reset');

        // Reload status from backend
        loadKycStatus();
    };

    const handleModalOpen = (modalName) => {
        setActiveModal(modalName);

        // Fetch content based on the modal being opened
        switch (modalName) {
            case 'termsConditions':
                fetchTermsAndConditions();
                break;
            case 'privacyPolicy':
                fetchPrivacyPolicy();
                break;
            case 'aboutUs':
                fetchAboutUs();
                break;
            case 'contactUs':
                fetchContactUs();
                break;
            default:
                break;
        }
    };

    // Function to fetch Terms and Conditions
    const fetchTermsAndConditions = async () => {
        // Always fetch fresh content when the modal is opened
        setIsLoadingTerms(true);
        setTermsError(null);

        try {
            // Add cache-busting parameter to prevent browser caching
            const timestamp = new Date().getTime();
            const response = await axios.get(`${process.env.REACT_APP_API_URL}/static-content/terms-conditions?_=${timestamp}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if response.data has the expected structure
            if (response.data && response.data.content !== undefined) {
                setTermsContent(response.data.content);
                console.log('Terms content loaded:', response.data.content.substring(0, 100) + '...');
            } else {
                console.error('Unexpected API response format:', response.data);
                setTermsError('Received invalid data format from server');
            }
        } catch (error) {
            console.error('Error fetching terms and conditions:', error);
            if (error.response && error.response.status === 404) {
                setTermsError('Terms and conditions not found');
            } else {
                setTermsError('Failed to load terms and conditions');
            }
        } finally {
            setIsLoadingTerms(false);
        }
    };

    // Function to fetch Privacy Policy
    const fetchPrivacyPolicy = async () => {
        // Always fetch fresh content when the modal is opened
        setIsLoadingPrivacy(true);
        setPrivacyError(null);

        try {
            // Add cache-busting parameter to prevent browser caching
            const timestamp = new Date().getTime();
            const response = await axios.get(`${process.env.REACT_APP_API_URL}/static-content/privacy-policy?_=${timestamp}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if response.data has the expected structure
            if (response.data && response.data.content !== undefined) {
                setPrivacyContent(response.data.content);
                console.log('Privacy content loaded:', response.data.content.substring(0, 100) + '...');
            } else {
                console.error('Unexpected API response format:', response.data);
                setPrivacyError('Received invalid data format from server');
            }
        } catch (error) {
            console.error('Error fetching privacy policy:', error);
            if (error.response && error.response.status === 404) {
                setPrivacyError('Privacy policy not found');
            } else {
                setPrivacyError('Failed to load privacy policy');
            }
        } finally {
            setIsLoadingPrivacy(false);
        }
    };

    // Function to fetch About Us
    const fetchAboutUs = async () => {
        // Always fetch fresh content when the modal is opened
        setIsLoadingAboutUs(true);
        setAboutUsError(null);

        try {
            // Add cache-busting parameter to prevent browser caching
            const timestamp = new Date().getTime();
            const response = await axios.get(`${process.env.REACT_APP_API_URL}/static-content/about-us?_=${timestamp}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if response.data has the expected structure
            if (response.data && response.data.content !== undefined) {
                setAboutUsContent(response.data.content);
                console.log('About Us content loaded:', response.data.content.substring(0, 100) + '...');
            } else {
                console.error('Unexpected API response format:', response.data);
                setAboutUsError('Received invalid data format from server');
            }
        } catch (error) {
            console.error('Error fetching about us:', error);
            if (error.response && error.response.status === 404) {
                setAboutUsError('About us information not found');
            } else {
                setAboutUsError('Failed to load about us information');
            }
        } finally {
            setIsLoadingAboutUs(false);
        }
    };

    // Function to fetch Contact Us
    const fetchContactUs = async () => {
        // Always fetch fresh content when the modal is opened
        setIsLoadingContactUs(true);
        setContactUsError(null);

        try {
            // Add cache-busting parameter to prevent browser caching
            const timestamp = new Date().getTime();
            const response = await axios.get(`${process.env.REACT_APP_API_URL}/static-content/contact-us?_=${timestamp}`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if response.data has the expected structure
            if (response.data && response.data.content !== undefined) {
                setContactUsContent(response.data.content);
                console.log('Contact Us content loaded:', response.data.content.substring(0, 100) + '...');
            } else {
                console.error('Unexpected API response format:', response.data);
                setContactUsError('Received invalid data format from server');
            }
        } catch (error) {
            console.error('Error fetching contact us:', error);
            if (error.response && error.response.status === 404) {
                setContactUsError('Contact information not found');
            } else {
                setContactUsError('Failed to load contact information');
            }
        } finally {
            setIsLoadingContactUs(false);
        }
    };

    const handleLanguageChange = (languageCode) => {
        changeLanguage(languageCode);
    };

    const handleLogout = async () => {
        try {
            setShowLogoutModal(false);
            await logout();
        } catch (error) {
            console.error('Logout process failed:', error);
        } finally {
            navigate('/');
        }
    };

    // Toggle 3rd party access setting
    const handle3rdPartyAccessToggle = async () => {
        setIsLoading3rdPartyAccess(true);
        try {
            // Get the new value (opposite of current value)
            const newValue = !allow3rdPartyAccess;

            // Optimistically update the UI
            setAllow3rdPartyAccess(newValue);

            // Call the API to update the setting
            const response = await profileService.updateThirdPartyAccess(newValue);

            if (response.success) {
                // Show success message
                console.log(`Third-party access ${newValue ? 'enabled' : 'disabled'}`);

                // If the setting was enabled, refresh the connected apps list
                if (newValue) {
                    const accessResponse = await profileService.getThirdPartyAccess();
                    if (accessResponse.success) {
                        setConnectedApps(accessResponse.data.connected_apps || []);
                    }
                }
                await fetchThirdPartyAccess();
            } else {
                // Show error message
                console.error(response.error || 'Failed to update setting');

                // Revert the change if the API call fails
                setAllow3rdPartyAccess(!newValue);
            }
        } catch (error) {
            console.error('Error updating 3rd party access setting:', error);

            // Show error message
            console.error('Failed to update setting. Please try again.');

            // Revert the change if the API call fails
            setAllow3rdPartyAccess(!allow3rdPartyAccess);
        } finally {
            setIsLoading3rdPartyAccess(false);
        }
    };

    const handlePasswordChange = async (e) => {
        e.preventDefault();
        setPasswordError(null);
        if (newPassword !== confirmNewPassword) {
            setPasswordError('Passwords do not match');
            toast.error('Passwords do not match');
            return;
        }
        setIsUpdatingPassword(true);
        try {
            const response = await authService.changePassword(
                currentPassword,
                newPassword,
                confirmNewPassword
            );
            if (response.success) {
                toast.success(response.message || 'Password updated');
                setActiveModal(null);
                setCurrentPassword('');
                setNewPassword('');
                setConfirmNewPassword('');
            } else {
                toast.error(response.error || 'Failed to update password');
                setPasswordError(response.error || 'Failed to update password');
            }
        } catch (error) {
            console.error('Error updating password:', error);
            toast.error('Failed to update password');
            setPasswordError('Failed to update password');
        } finally {
            setIsUpdatingPassword(false);
        }
    };

    // Enhanced KYC Functions for Final Polish
    React.useEffect(() => {
        // Update progress based on current step and completion
        const calculateProgress = () => {
            let progress = (kycStep - 1) * 20; // Base progress per step

            // Add completion bonuses
            if (selectedNationality) progress += 5;

            // Different document requirements for Malaysian vs Foreigner
            if (selectedNationality === 'malaysian') {
                if (uploadedDocuments.length >= 2) progress += 10; // IC front + back
                if (malaysianInfo.full_name && malaysianInfo.ic_number) progress += 5; // Malaysian info
            } else if (selectedNationality === 'foreigner') {
                if (uploadedDocuments.length >= 1) progress += 10; // Passport only
                if (foreignerInfo.full_name && foreignerInfo.passport_number && foreignerInfo.country) progress += 5;
            }

            if (selfieImage) progress += 5;
            if (kycStatus === 'pending_review') progress = 100;

            return Math.min(progress, 100);
        };

        setKycProgress(calculateProgress());
    }, [kycStep, selectedNationality, uploadedDocuments.length, selfieImage, kycStatus, foreignerInfo, malaysianInfo]);

    // Enhanced modal close with cleanup
    const handleKycModalClose = () => {
        setIsKycModalClosing(true);

        // Cleanup camera if active
        if (isCameraActive) {
            stopCamera();
        }

        // Delay close to allow animations
        setTimeout(() => {
            setActiveModal(null);
            setIsKycModalClosing(false);
        }, 300);
    };

    // Keyboard navigation handler
    const handleKycKeyDown = (e) => {
        if (e.key === 'Escape') {
            handleKycModalClose();
        } else if (e.key === 'ArrowLeft' && kycStep > 1) {
            setKycStep(kycStep - 1);
        } else if (e.key === 'ArrowRight' && kycStep < 5) {
            // Only allow forward navigation if current step is complete
            const canProceed =
                (kycStep === 1) ||
                (kycStep === 2 && selectedNationality) ||
                (kycStep === 3 && (
                    (selectedNationality === 'malaysian' && uploadedDocuments.length >= 2 &&
                     malaysianInfo.full_name && malaysianInfo.ic_number) ||
                    (selectedNationality === 'foreigner' && uploadedDocuments.length >= 1 &&
                     foreignerInfo.full_name && foreignerInfo.passport_number && foreignerInfo.country)
                )) ||
                (kycStep === 4 && selfieImage);

            if (canProceed) {
                setKycStep(kycStep + 1);
            }
        }
    };

    // Auto-save progress to localStorage
    React.useEffect(() => {
        if (kycStep > 1 || selectedNationality || uploadedDocuments.length > 0 || selfieImage) {
            const kycData = {
                step: kycStep,
                nationality: selectedNationality,
                hasDocuments: selectedNationality === 'malaysian' ? uploadedDocuments.length >= 2 : uploadedDocuments.length >= 1,
                hasSelfie: !!selfieImage,
                status: kycStatus,
                foreignerInfo: selectedNationality === 'foreigner' ? foreignerInfo : null,
                malaysianInfo: selectedNationality === 'malaysian' ? malaysianInfo : null,
                timestamp: Date.now()
            };
            localStorage.setItem('kyc_progress', JSON.stringify(kycData));
        }
    }, [kycStep, selectedNationality, uploadedDocuments.length, selfieImage, kycStatus, foreignerInfo, malaysianInfo]);

    // Load saved progress on mount - DISABLED for real API integration
    React.useEffect(() => {
        // Note: Disabled localStorage progress loading since we now use real backend API
        // The loadKycStatus() function will get the real status from backend
        console.log('localStorage progress loading disabled - using real API status');

        // Clean up any remaining cached data
        localStorage.removeItem('kyc_progress');
        localStorage.removeItem('kyc_submission');
    }, []);

    // KYC Helper Functions
    const handleDocumentUpload = (file, side) => {
        try {
            // Log upload attempt for debugging
            console.log(`Starting document upload for ${side} side:`, file?.name);

            // Validate file input
            if (!file || !side) {
                setKycError('Invalid file or document side specified.');
                return;
            }

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            if (!allowedTypes.includes(file.type)) {
                setKycError('Please upload a valid image file (JPG, PNG) or PDF.');
                return;
            }

            // Validate file size (10MB limit)
            const maxSize = 10 * 1024 * 1024; // 10MB in bytes
            if (file.size > maxSize) {
                setKycError('File size must be under 10MB. Please compress your image and try again.');
                return;
            }

            // Create preview URL
            const previewUrl = URL.createObjectURL(file);

            // Create document object
            const document = {
                id: Date.now() + Math.random(),
                file,
                side,
                previewUrl,
                name: file.name,
                size: file.size,
                type: file.type
            };

            // Update uploaded documents
            setUploadedDocuments(prev => {
                // Remove existing document for this side
                const filtered = prev.filter(doc => doc.side !== side);
                return [...filtered, document];
            });

            // Clear any existing errors
            setKycError(null);

            console.log(`Document uploaded successfully for ${side} side:`, file.name);
        } catch (error) {
            console.error('Error handling document upload:', error);
            setKycError('Failed to upload document. Please try again.');
        }
    };

    const removeDocument = (side) => {
        setUploadedDocuments(prev => {
            const docToRemove = prev.find(doc => doc.side === side);
            if (docToRemove && docToRemove.previewUrl) {
                URL.revokeObjectURL(docToRemove.previewUrl);
            }
            return prev.filter(doc => doc.side !== side);
        });
    };

    // Camera Helper Functions
    const checkCameraSupport = async () => {
        try {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                setCameraSupported(false);
                setCaptureMode('upload');
                return false;
            }
            return true;
        } catch (error) {
            console.error('Camera support check failed:', error);
            setCameraSupported(false);
            setCaptureMode('upload');
            return false;
        }
    };

    const startCamera = async () => {
        try {
            setCameraError(null);
            setIsCapturing(true);

            const constraints = {
                video: {
                    facingMode: 'user', // Front camera
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                },
                audio: false
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            setCameraStream(stream);
            setIsCameraActive(true);
            setIsCapturing(false);
            return stream;
        } catch (error) {
            console.error('Camera access failed:', error);
            setIsCapturing(false);

            let errorMessage = 'Unable to access camera. ';
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Please allow camera access and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No camera found on this device.';
            } else {
                errorMessage += 'Please check your camera settings.';
            }

            setCameraError(errorMessage);
            setCameraSupported(false);
            setCaptureMode('upload');
            return null;
        }
    };

    const stopCamera = () => {
        if (cameraStream) {
            cameraStream.getTracks().forEach(track => track.stop());
            setCameraStream(null);
            setIsCameraActive(false);
        }
    };

    const capturePhoto = (videoElement) => {
        try {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            // Draw the video frame to canvas
            context.drawImage(videoElement, 0, 0);

            // Convert to blob
            canvas.toBlob((blob) => {
                if (blob) {
                    const file = new File([blob], 'selfie.jpg', { type: 'image/jpeg' });
                    const previewUrl = URL.createObjectURL(blob);

                    setSelfieImage({
                        file,
                        previewUrl,
                        name: 'selfie.jpg',
                        size: blob.size,
                        type: 'image/jpeg'
                    });

                    stopCamera();
                    setKycError(null);
                }
            }, 'image/jpeg', 0.8);
        } catch (error) {
            console.error('Photo capture failed:', error);
            setKycError('Failed to capture photo. Please try again.');
        }
    };

    const handleSelfieUpload = (file) => {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            setKycError('Please upload a valid image file (JPG, PNG).');
            return;
        }

        // Validate file size (5MB limit for selfies)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            setKycError('File size must be under 5MB. Please compress your image and try again.');
            return;
        }

        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        setSelfieImage({
            file,
            previewUrl,
            name: file.name,
            size: file.size,
            type: file.type
        });

        setKycError(null);
    };

    const removeSelfie = () => {
        if (selfieImage && selfieImage.previewUrl) {
            URL.revokeObjectURL(selfieImage.previewUrl);
        }
        setSelfieImage(null);
        stopCamera();
    };

    // KYC Submission Functions
    const submitKycVerification = async () => {
        try {
            setIsSubmittingKyc(true);
            setKycError(null);

            // Enhanced validation before submission
            if (!selectedNationality) {
                throw new Error('Please select your nationality before submitting.');
            }

            // Nationality-specific validation
            if (selectedNationality === 'malaysian') {
                if (uploadedDocuments.length < 2) {
                    throw new Error('Please upload both front and back sides of your IC before submitting.');
                }
                if (!malaysianInfo.full_name || !malaysianInfo.ic_number) {
                    throw new Error('Please fill in all required information (Full Name and IC Number) before submitting.');
                }
            } else if (selectedNationality === 'foreigner') {
                if (uploadedDocuments.length < 1) {
                    throw new Error('Please upload your passport image before submitting.');
                }
                if (!foreignerInfo.full_name || !foreignerInfo.passport_number || !foreignerInfo.country) {
                    throw new Error('Please fill in all required information (Full Name, Passport Number, Country) before submitting.');
                }
            }

            if (!selfieImage) {
                throw new Error('Please take a selfie before submitting.');
            }

            console.log('Starting E-KYC verification:', { nationality: selectedNationality, documentsCount: uploadedDocuments.length });

            let formData;
            let response;

            // Prepare metadata for session tracking
            const sessionMetadata = {
                verification_method: 'manual', // Default to manual verification
                session_id: `kyc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            };

            // Call appropriate API based on nationality
            if (selectedNationality === 'malaysian') {
                console.log('Preparing Malaysian E-KYC submission...');
                formData = ekycService.prepareMalaysianFormData(
                    uploadedDocuments,
                    selfieImage,
                    malaysianInfo, // Pass Malaysian user info
                    sessionMetadata
                );
                response = await ekycService.verifyMalaysian(formData);
            } else if (selectedNationality === 'foreigner') {
                console.log('Preparing Foreigner E-KYC submission...');

                formData = ekycService.prepareForeignerFormData(
                    uploadedDocuments,
                    selfieImage,
                    foreignerInfo, // Use real user input
                    sessionMetadata
                );
                response = await ekycService.verifyForeigner(formData);
            } else {
                throw new Error('Invalid nationality selected');
            }

            console.log('E-KYC submission successful:', response.data);

            // Handle successful submission
            if (response.data && (response.data.success !== false)) {
                setKycStatus('pending_review');
                setKycStep(5); // Move to success step

                // Clear localStorage progress since submission is complete
                localStorage.removeItem('kyc_progress');

                // Save submission confirmation
                localStorage.setItem('kyc_submission', JSON.stringify({
                    status: 'pending_review',
                    submissionTime: new Date().toISOString(),
                    nationality: selectedNationality,
                    sessionId: sessionMetadata.session_id
                }));
            } else {
                throw new Error(response.data?.message || 'Verification submission failed');
            }

        } catch (error) {
            console.error('KYC submission failed:', error);

            // Enhanced error handling with API-specific messages
            let userMessage = 'An unexpected error occurred during submission. Please try again.';

            if (error.response) {
                // API responded with error status
                const { status, data } = error.response;

                if (status === 403) {
                    userMessage = 'Permission denied. Please ensure you are logged in and try again.';
                } else if (status === 422) {
                    // Validation errors
                    const validationErrors = data.errors || {};
                    const firstError = Object.values(validationErrors)[0];
                    userMessage = Array.isArray(firstError) ? firstError[0] : (data.message || userMessage);
                } else if (status === 413) {
                    userMessage = 'File size too large. Please compress your images and try again.';
                } else if (status >= 500) {
                    userMessage = 'Server error. Please try again in a few minutes.';
                } else {
                    userMessage = data.message || userMessage;
                }
            } else if (error.request) {
                // Network error
                userMessage = 'Network connection failed. Please check your internet and try again.';
            } else if (error.name === 'NetworkError') {
                userMessage = 'Network connection failed. Please check your internet connection and try again.';
            } else if (error.message.includes('413')) {
                userMessage = 'Files are too large. Please compress your images and try again.';
            } else if (error.message.includes('timeout')) {
                userMessage = 'Request timed out. Please try again with a better internet connection.';
            } else {
                userMessage = error.message || userMessage;
            }

            setKycError(userMessage);

            // Log error for debugging (in production, send to error tracking service)
            console.error('KYC Submission Error Details:', {
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString(),
                nationality: selectedNationality,
                documentsCount: uploadedDocuments.length,
                hasSelfie: !!selfieImage,
                foreignerInfo: selectedNationality === 'foreigner' ? foreignerInfo : null,
                userAgent: navigator.userAgent
            });
        } finally {
            setIsSubmittingKyc(false);
        }
    };

    const resetKycProcess = () => {
        // Clean up uploaded files
        uploadedDocuments.forEach(doc => {
            if (doc.previewUrl) {
                URL.revokeObjectURL(doc.previewUrl);
            }
        });

        if (selfieImage && selfieImage.previewUrl) {
            URL.revokeObjectURL(selfieImage.previewUrl);
        }

        // Stop camera if active
        stopCamera();

        // Reset all states
        setKycStep(1);
        setSelectedNationality(null);
        setUploadedDocuments([]);
        setSelfieImage(null);
        setKycError(null);
        setKycStatus('not_started');
        setIsSubmittingKyc(false);
        setForeignerInfo({
            full_name: '',
            passport_number: '',
            country: ''
        });
        setMalaysianInfo({
            full_name: '',
            ic_number: ''
        });
        setCameraError(null);
        setIsCameraActive(false);
        setCaptureMode('camera');
        setCameraSupported(true);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'verified':
            case 'approved': // Handle backend 'approved' status
                return 'text-green-600 dark:text-green-100 bg-green-50 dark:bg-green-700 border-green-200';
            case 'pending_review':
            case 'pending': // Handle backend 'pending' status
                return 'text-yellow-600 dark:text-yellow-100 bg-yellow-50 dark:bg-yellow-700 border-yellow-200';
            case 'rejected':
            case 'failed': // Handle backend 'failed' status
                return 'text-red-600 dark:text-red-100 bg-red-50 dark:bg-red-700 border-red-200';
            case 'in_progress':
                return 'text-blue-600 dark:text-blue-100 bg-blue-50 dark:bg-blue-700 border-blue-200';
            default:
                return 'text-gray-600 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 border-gray-200';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'verified':
            case 'approved': // Handle backend 'approved' status
                return 'Verified';
            case 'pending_review':
            case 'pending': // Handle backend 'pending' status
                return 'Under Review';
            case 'rejected':
            case 'failed': // Handle backend 'failed' status
                return 'Rejected';
            case 'in_progress':
                return 'In Progress';
            default:
                return 'Not Started';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'verified':
            case 'approved': // Handle backend 'approved' status
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                );
            case 'pending_review':
            case 'pending': // Handle backend 'pending' status
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                );
            case 'rejected':
            case 'failed': // Handle backend 'failed' status
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                );
            case 'in_progress':
                return (
                    <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                );
            default:
                return (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                );
        }
    };

    // Performance optimization: Cleanup on unmount
    React.useEffect(() => {
        return () => {
            // Cleanup camera stream
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
            }

            // Cleanup blob URLs
            uploadedDocuments.forEach(doc => {
                if (doc.previewUrl) {
                    URL.revokeObjectURL(doc.previewUrl);
                }
            });

            if (selfieImage && selfieImage.previewUrl) {
                URL.revokeObjectURL(selfieImage.previewUrl);
            }
        };
    }, []);

    // Enhanced file validation
    const validateFile = (file, type = 'document') => {
        const errors = [];

        // File type validation
        const allowedTypes = type === 'selfie'
            ? ['image/jpeg', 'image/jpg', 'image/png']
            : ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];

        if (!allowedTypes.includes(file.type)) {
            errors.push(`Invalid file type. Please upload ${type === 'selfie' ? 'JPG or PNG' : 'JPG, PNG, or PDF'} files only.`);
        }

        // File size validation
        const maxSize = type === 'selfie' ? 5 * 1024 * 1024 : 10 * 1024 * 1024; // 5MB for selfies, 10MB for documents
        if (file.size > maxSize) {
            errors.push(`File size too large. Maximum size is ${type === 'selfie' ? '5MB' : '10MB'}.`);
        }

        // File name validation
        if (file.name.length > 255) {
            errors.push('File name is too long. Please rename the file and try again.');
        }

        // Additional security checks
        if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
            errors.push('Invalid file name. Please rename the file and try again.');
        }

        return errors;
    };

    // Enhanced error recovery
    const handleRetrySubmission = () => {
        setKycError(null);
        setIsSubmittingKyc(false);
        // Could add retry logic here
    };

    // Accessibility: Focus management
    const focusFirstElement = () => {
        const firstFocusable = document.querySelector('[data-kyc-step] button, [data-kyc-step] input, [data-kyc-step] [tabindex="0"]');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    };

    React.useEffect(() => {
        if (activeModal === 'kyc') {
            // Focus management for accessibility
            setTimeout(focusFirstElement, 100);

            // Add global form submission prevention for KYC modal
            const handleFormSubmit = (e) => {
                console.log('Global form submission detected:', e.target);
                e.preventDefault();
                e.stopPropagation();
                return false;
            };

            document.addEventListener('submit', handleFormSubmit, true);

            return () => {
                document.removeEventListener('submit', handleFormSubmit, true);
            };
        }
    }, [activeModal, kycStep]);

    // Document Upload Area Component
    const DocumentUploadArea = ({ label, onFileSelect, uploadedFile, onRemove }) => {
        const [isDragOver, setIsDragOver] = useState(false);
        const fileInputRef = React.useRef(null);

        const handleDragOver = (e) => {
            e.preventDefault();
            setIsDragOver(true);
        };

        const handleDragLeave = (e) => {
            e.preventDefault();
            setIsDragOver(false);
        };

        const handleDrop = (e) => {
            e.preventDefault();
            setIsDragOver(false);
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                onFileSelect(files[0]);
            }
        };

        const handleFileSelect = (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Additional safety to prevent any form submission
            if (e.type === 'submit') {
                return false;
            }

            const files = Array.from(e.target.files || []);
            if (files.length > 0) {
                onFileSelect(files[0]);
            }
            // Clear the input value to allow re-uploading the same file
            e.target.value = '';
        };

        if (uploadedFile) {
            return (
                <motion.div
                    className="relative bg-white border-2 border-green-200 rounded-2xl p-4"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <span className="text-sm font-medium text-green-800">Uploaded Successfully</span>
                        </div>
                        <button
                            onClick={onRemove}
                            className="p-1 text-red-500 hover:text-red-700 transition-colors"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>

                    {/* File Preview */}
                    <div className="aspect-video bg-gray-100 rounded-xl overflow-hidden mb-3">
                        {uploadedFile.type.startsWith('image/') ? (
                            <img
                                src={uploadedFile.previewUrl}
                                alt="Document preview"
                                className="w-full h-full object-cover"
                            />
                        ) : (
                            <div className="w-full h-full flex items-center justify-center">
                                <div className="text-center">
                                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <p className="text-sm text-gray-600">PDF Document</p>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* File Info */}
                    <div className="text-xs text-gray-600">
                        <p className="font-medium truncate">{uploadedFile.name}</p>
                        <p>{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                </motion.div>
            );
        }

        return (
            <motion.div
                className={`border-2 border-dashed rounded-2xl p-8 text-center cursor-pointer transition-all duration-300 ${
                    isDragOver
                        ? 'border-indigo-400 bg-indigo-50 scale-105'
                        : 'border-gray-300 hover:border-indigo-300 hover:bg-indigo-50'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
            >
                <motion.div
                    animate={{
                        y: isDragOver ? -5 : 0,
                        scale: isDragOver ? 1.1 : 1,
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                        isDragOver ? ' bg-indigo-100 dark:bg-indigo-800' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                        <svg className={`w-8 h-8 ${isDragOver ? 'text-indigo-600 dark:text-indigo-100' : 'text-gray-400 dark:text-indigo-100'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                    </div>
                    <p className={`text-lg font-medium mb-2 ${isDragOver ? 'text-indigo-700 dark:text-indigo-100' : 'text-gray-700 dark:text-indigo-100'}`}>
                        {isDragOver ? 'Drop your file here!' : `Upload ${label}`}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-indigo-100 mb-4">
                        Drag & drop or click to select
                    </p>
                    <div className="text-xs text-gray-400 dark:text-indigo-100">
                        <p>JPG, PNG, PDF • Max 10MB</p>
                    </div>
                </motion.div>

                <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,application/pdf"
                    onChange={handleFileSelect}
                    className="hidden"
                />
            </motion.div>
        );
    };

    // Selfie Capture Component
    const SelfieCapture = () => {
        const videoRef = React.useRef(null);
        const [localCameraError, setLocalCameraError] = useState(null);

        React.useEffect(() => {
            checkCameraSupport();
        }, []);

        React.useEffect(() => {
            if (cameraStream && videoRef.current) {
                videoRef.current.srcObject = cameraStream;
            }
        }, [cameraStream]);

        const handleStartCamera = async () => {
            const stream = await startCamera();
            if (stream && videoRef.current) {
                videoRef.current.srcObject = stream;
            }
        };

        const handleCapture = () => {
            if (videoRef.current) {
                capturePhoto(videoRef.current);
            }
        };

        if (selfieImage) {
            return (
                <motion.div
                    className="text-center"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="max-w-md mx-auto">
                        <div className="relative bg-white dark:bg-gray-800 border-2 border-green-200 rounded-2xl p-4 mb-6">
                            <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center space-x-2">
                                    <div className="w-8 h-8 bg-green-100 dark:bg-green-700 rounded-full flex items-center justify-center">
                                        <svg className="w-4 h-4 text-green-600 dark:text-green-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span className="text-sm font-medium text-green-800 dark:text-green-100">Selfie Captured Successfully</span>
                                </div>
                                <button
                                    onClick={removeSelfie}
                                    className="p-1 text-red-500 hover:text-red-700 transition-colors"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>

                            <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-xl overflow-hidden mb-3">
                                <img
                                    src={selfieImage.previewUrl}
                                    alt="Captured selfie"
                                    className="w-full h-full object-cover"
                                />
                            </div>

                            <div className="text-xs text-gray-600 dark:text-indigo-100">
                                <p className="font-medium">{selfieImage.name}</p>
                                <p>{(selfieImage.size / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                        </div>

                        <motion.button
                            onClick={removeSelfie}
                            className="px-6 py-3 border border-gray-300 text-gray-700 dark:text-indigo-100 rounded-xl hover:bg-gray-50 transition-colors"
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            Retake Photo
                        </motion.button>
                    </div>
                </motion.div>
            );
        }

        return (
            <div className="max-w-2xl mx-auto">
                {/* Mode Selection */}
                <div className="flex justify-center mb-6">
                    <div className="bg-gray-100 dark:bg-gray-700 p-1 rounded-xl flex">
                        <button
                            type="button"
                            onClick={() => setCaptureMode('camera')}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                captureMode === 'camera'
                                    ? 'bg-white dark:bg-gray-800 text-indigo-600 shadow-sm'
                                    : 'text-gray-600 dark:text-indigo-100 hover:text-gray-900'
                            }`}
                            disabled={!cameraSupported}
                        >
                            📷 Use Camera
                        </button>
                        <button
                            type="button"
                            onClick={() => setCaptureMode('upload')}
                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                captureMode === 'upload'
                                    ? 'bg-white dark:bg-gray-800 text-indigo-600 shadow-sm'
                                    : 'text-gray-600 dark:text-indigo-100 hover:text-gray-900'
                            }`}
                        >
                            📁 Upload Photo
                        </button>
                    </div>
                </div>

                {captureMode === 'camera' && cameraSupported ? (
                    <div className="space-y-6">
                        {/* Camera View */}
                        <div className="relative bg-black rounded-2xl overflow-hidden aspect-video max-w-md mx-auto">
                            {isCameraActive ? (
                                <>
                                    <video
                                        ref={videoRef}
                                        autoPlay
                                        playsInline
                                        muted
                                        className="w-full h-full object-cover transform scale-x-[-1]"
                                    />

                                    {/* Face Guide Overlay */}
                                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                        <div className="w-48 h-60 border-2 border-white/50 rounded-full relative">
                                            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 text-white text-xs bg-black/50 px-2 py-1 rounded">
                                                Position your face here
                                            </div>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                    <div className="text-center text-white">
                                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <p className="text-sm">Camera not active</p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Camera Controls */}
                        <div className="flex justify-center space-x-4">
                            {!isCameraActive ? (
                                <motion.button
                                    onClick={handleStartCamera}
                                    disabled={isCapturing}
                                    className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    {isCapturing ? 'Starting Camera...' : 'Start Camera'}
                                </motion.button>
                            ) : (
                                <>
                                    <motion.button
                                        onClick={handleCapture}
                                        className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        📸 Capture Photo
                                    </motion.button>
                                    <motion.button
                                        onClick={stopCamera}
                                        className="bg-gray-600 text-white px-6 py-3 rounded-2xl font-semibold hover:bg-gray-700 transition-colors"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        Stop Camera
                                    </motion.button>
                                </>
                            )}
                        </div>

                        {/* Camera Error */}
                        {(cameraError || localCameraError) && (
                            <motion.div
                                className="bg-red-50 border border-red-200 rounded-xl p-4"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <div className="flex items-center space-x-3">
                                    <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p className="text-red-800 font-medium">{cameraError || localCameraError}</p>
                                </div>
                            </motion.div>
                        )}
                    </div>
                ) : (
                    /* File Upload Mode */
                    <SelfieUploadArea />
                )}
            </div>
        );
    };

    // Selfie Upload Area Component
    const SelfieUploadArea = () => {
        const [isDragOver, setIsDragOver] = useState(false);
        const fileInputRef = React.useRef(null);

        const handleDragOver = (e) => {
            e.preventDefault();
            setIsDragOver(true);
        };

        const handleDragLeave = (e) => {
            e.preventDefault();
            setIsDragOver(false);
        };

        const handleDrop = (e) => {
            e.preventDefault();
            setIsDragOver(false);
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                handleSelfieUpload(files[0]);
            }
        };

        const handleFileSelect = (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Additional safety to prevent any form submission
            if (e.type === 'submit') {
                return false;
            }

            const files = Array.from(e.target.files || []);
            if (files.length > 0) {
                handleSelfieUpload(files[0]);
            }
            // Clear the input value to allow re-uploading the same file
            e.target.value = '';
        };

        return (
            <motion.div
                className={`border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-300 max-w-md mx-auto ${
                    isDragOver
                        ? 'border-indigo-400 bg-indigo-50 dark:bg-indigo-700 scale-105'
                        : 'border-gray-300 hover:border-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-700'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
            >
                <motion.div
                    animate={{
                        y: isDragOver ? -5 : 0,
                        scale: isDragOver ? 1.1 : 1,
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                    <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 ${
                        isDragOver ? ' bg-indigo-100 dark:bg-indigo-800' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                        <svg className={`w-10 h-10 ${isDragOver ? 'text-indigo-600 dark:text-indigo-100' : 'text-gray-400 dark:text-indigo-100'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <p className={`text-xl font-medium mb-3 ${isDragOver ? 'text-indigo-700' : 'text-gray-700'}`}>
                        {isDragOver ? 'Drop your selfie here!' : 'Upload Your Selfie'}
                    </p>
                    <p className="text-gray-500 dark:text-indigo-100 mb-6">
                        Drag & drop or click to select a clear photo of yourself
                    </p>
                    <div className="text-sm text-gray-400 dark:text-indigo-100">
                        <p>JPG, PNG • Max 5MB</p>
                    </div>
                </motion.div>

                <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png"
                    onChange={handleFileSelect}
                    className="hidden"
                />
            </motion.div>
        );
    };

    // KYC Step Renderer
    const renderKycStep = () => {
        switch (kycStep) {
            case 1:
                return (
                    <motion.div
                        className="text-center py-8"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900 dark:to-purple-900 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg className="w-12 h-12 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-4">Welcome to Identity Verification</h3>
                        <p className="text-gray-600 dark:text-indigo-100 mb-8 max-w-2xl mx-auto leading-relaxed">
                            To ensure the security of your account and comply with regulations, we need to verify your identity.
                            This process is quick, secure, and helps protect your account from unauthorized access.
                        </p>

                        <div className="grid md:grid-cols-3 gap-6 mb-8">
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-indigo-900 dark:to-indigo-900 p-6 rounded-xl">
                                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Upload Documents</h4>
                                <p className="text-sm text-gray-600 dark:text-indigo-100">Provide a clear photo of your government-issued ID</p>
                            </div>

                            <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-indigo-900 dark:to-indigo-900 p-6 rounded-xl">
                                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <h4 className="font-semibold text-gray-900 dark:text-indigo-100 mb-2">Take a Selfie</h4>
                                <p className="text-sm text-gray-600 dark:text-indigo-100">Capture a live photo to verify your identity</p>
                            </div>

                            <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-indigo-900 dark:to-indigo-900 p-6 rounded-xl">
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <h4 className="font-semibold text-gray-900 dark:text-indigo-100 mb-2">Get Verified</h4>
                                <p className="text-sm text-gray-600 dark:text-indigo-100">Receive confirmation within 24-48 hours</p>
                            </div>
                        </div>

                        <div className="bg-amber-50 dark:bg-amber-900 border border-amber-200 rounded-xl p-4 mb-8">
                            <div className="flex items-start space-x-3">
                                <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <div className="text-left">
                                    <h5 className="font-medium text-amber-800 dark:text-amber-100 mb-1">Important Information</h5>
                                    <p className="text-sm text-amber-700 dark:text-amber-100">
                                        • Ensure your documents are clear and readable<br/>
                                        • Use good lighting for photos<br/>
                                        • Have your government-issued ID ready<br/>
                                        • The process takes about 5 minutes
                                    </p>
                                </div>
                            </div>
                        </div>

                        <motion.button
                            onClick={() => setKycStep(2)}
                            className="bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-700 dark:to-purple-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 text-lg"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            Start Verification Process
                        </motion.button>
                    </motion.div>
                );

            case 2:
                return (
                    <motion.div
                        className="py-8"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="text-center mb-8">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-4">Select Your Nationality</h3>
                            <p className="text-gray-600 dark:text-indigo-100 max-w-2xl mx-auto">
                                Choose your nationality to determine the required documents for verification. Different nationalities have different document requirements.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                            {[
                                {
                                    type: 'malaysian',
                                    title: 'Malaysian Citizen',
                                    description: 'Malaysian Identity Card (MyKad)',
                                    icon: '🇲🇾',
                                    flag: '🇲🇾',
                                    requirements: ['IC Front Side Photo', 'IC Back Side Photo', 'Selfie Photo'],
                                    documents: 'Malaysian IC (MyKad)',
                                    endpoint: 'POST /api/ekyc/malaysian',
                                    color: 'from-red-500 to-yellow-500'
                                },
                                {
                                    type: 'foreigner',
                                    title: 'Foreign Citizen',
                                    description: 'International Passport',
                                    icon: '🌍',
                                    flag: '🌍',
                                    requirements: ['Passport Photo', 'Personal Information', 'Selfie Photo'],
                                    documents: 'International Passport',
                                    endpoint: 'POST /api/ekyc/foreigner',
                                    color: 'from-blue-500 to-purple-500'
                                }
                            ].map((nationality) => (
                                <motion.div
                                    key={nationality.type}
                                    onClick={() => {
                                        setSelectedNationality(nationality.type);
                                        setKycStep(3);
                                    }}
                                    className={`p-8 rounded-3xl border-2 cursor-pointer transition-all duration-300 ${
                                        selectedNationality === nationality.type
                                            ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-700 shadow-xl'
                                            : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-700 hover:shadow-lg'
                                    }`}
                                    whileHover={{ scale: 1.02, y: -4 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    <div className="text-center">
                                        {/* Flag/Icon */}
                                        <div className={`w-24 h-24 rounded-2xl flex items-center justify-center mx-auto mb-6 bg-gradient-to-br ${nationality.color} shadow-lg`}>
                                            <span className="text-4xl">{nationality.icon}</span>
                                        </div>

                                        {/* Title */}
                                        <h4 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-3">{nationality.title}</h4>
                                        <p className="text-lg text-gray-600 dark:text-indigo-100 mb-6">{nationality.description}</p>

                                        {/* Document Requirements */}
                                        <div className="text-left bg-white dark:bg-gray-800 rounded-xl p-6 mb-6 shadow-sm">
                                            <p className="text-sm font-bold text-gray-700 dark:text-indigo-100 mb-3 uppercase tracking-wide">Required Documents:</p>
                                            <p className="text-lg font-semibold text-indigo-600 dark:text-indigo-100 mb-4">{nationality.documents}</p>
                                            <ul className="text-sm text-gray-600 dark:text-indigo-100 space-y-2">
                                                {nationality.requirements.map((req, index) => (
                                                    <li key={index} className="flex items-center">
                                                        <span className="w-2 h-2 bg-indigo-400 rounded-full mr-3"></span>
                                                        {req}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>


                                    </div>
                                </motion.div>
                            ))}
                        </div>

                        <div className="flex justify-between mt-8">
                            <motion.button
                                onClick={() => setKycStep(1)}
                                className="px-6 py-3 border border-gray-300 text-white dark:text-indigo-100 dark:bg-indigo-700 rounded-xl hover:bg-gray-50 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                        </div>
                    </motion.div>
                );

            case 3:
                return (
                    <motion.div
                        className="py-8"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="text-center mb-8">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-4">
                                Upload Your {selectedNationality === 'malaysian' ? 'Malaysian IC (MyKad)' : 'Passport'}
                            </h3>
                            <p className="text-gray-600 dark:text-indigo-100 max-w-2xl mx-auto">
                                {selectedNationality === 'malaysian'
                                    ? 'Please upload clear, high-quality photos of both front and back sides of your Malaysian IC. Make sure all text is readable and the document is not expired.'
                                    : 'Please upload a clear, high-quality photo of your passport bio-data page. Make sure all text is readable and the document is not expired.'
                                }
                            </p>
                        </div>

                        {/* Document Upload Areas - Nationality Based */}
                        {selectedNationality === 'malaysian' ? (
                            /* Malaysian IC - Front & Back + Personal Info */
                            <div className="max-w-4xl mx-auto mb-8 space-y-8">
                                {/* IC Document Upload */}
                                <div className="grid md:grid-cols-2 gap-6">
                                    {/* IC Front Side Upload */}
                                    <div className="space-y-4">
                                        <h4 className="font-semibold text-gray-900 dark:text-indigo-100 text-center">IC Front Side</h4>
                                        <DocumentUploadArea
                                            label="IC Front Side"
                                            onFileSelect={(file) => handleDocumentUpload(file, 'front')}
                                            uploadedFile={uploadedDocuments.find(doc => doc.side === 'front')}
                                            onRemove={() => removeDocument('front')}
                                        />
                                    </div>

                                    {/* IC Back Side Upload */}
                                    <div className="space-y-4">
                                        <h4 className="font-semibold text-gray-900 dark:text-indigo-100 text-center">IC Back Side</h4>
                                        <DocumentUploadArea
                                            label="IC Back Side"
                                            onFileSelect={(file) => handleDocumentUpload(file, 'back')}
                                            uploadedFile={uploadedDocuments.find(doc => doc.side === 'back')}
                                            onRemove={() => removeDocument('back')}
                                        />
                                    </div>
                                </div>

                                {/* Malaysian Personal Information Form */}
                                <div className="bg-white dark:bg-gray-800 border border-gray-200 rounded-2xl p-6">
                                    <h4 className="font-semibold text-gray-900 dark:text-indigo-100 mb-6 text-center">Personal Information</h4>
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2">
                                                Full Name <span className="text-red-500 dark:text-red-100">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                value={malaysianInfo.full_name}
                                                onChange={(e) => setMalaysianInfo(prev => ({ ...prev, full_name: e.target.value }))}
                                                className="w-full px-4 py-3 border border-gray-300 dark:bg-gray-800 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                                placeholder="Enter your full name as shown in IC"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2">
                                                IC Number <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                value={malaysianInfo.ic_number}
                                                onChange={(e) => setMalaysianInfo(prev => ({ ...prev, ic_number: e.target.value }))}
                                                className="w-full px-4 py-3 border border-gray-300 dark:bg-gray-800 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                                placeholder="Enter your IC number (e.g., 123456-78-9012)"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : selectedNationality === 'foreigner' ? (
                            /* Foreigner - Passport + Personal Info */
                            <div className="max-w-4xl mx-auto mb-8 space-y-8">
                                {/* Passport Upload */}
                                <div className="space-y-4">
                                    <h4 className="font-semibold text-gray-900 dark:text-indigo-100 text-center">Passport Bio-Data Page</h4>
                                    <div className="max-w-md mx-auto">
                                        <DocumentUploadArea
                                            label="Passport Photo"
                                            onFileSelect={(file) => handleDocumentUpload(file, 'passport')}
                                            uploadedFile={uploadedDocuments.find(doc => doc.side === 'passport')}
                                            onRemove={() => removeDocument('passport')}
                                        />
                                    </div>
                                </div>

                                {/* Personal Information Form */}
                                <div className="bg-white dark:bg-gray-800 border border-gray-200 rounded-2xl p-6">
                                    <h4 className="font-semibold text-gray-900 dark:text-indigo-100 mb-6 text-center">Personal Information</h4>
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2">
                                                Full Name <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                value={foreignerInfo.full_name}
                                                onChange={(e) => setForeignerInfo(prev => ({ ...prev, full_name: e.target.value }))}
                                                className="w-full px-4 py-3 border border-gray-300 dark:bg-gray-800 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                                placeholder="Enter your full name as shown in passport"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2">
                                                Passport Number <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                value={foreignerInfo.passport_number}
                                                onChange={(e) => setForeignerInfo(prev => ({ ...prev, passport_number: e.target.value }))}
                                                className="w-full px-4 py-3 border border-gray-300 dark:bg-gray-800 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                                placeholder="Enter your passport number"
                                            />
                                        </div>
                                        <div className="md:col-span-2">
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2">
                                                Country of Citizenship <span className="text-red-500">*</span>
                                            </label>
                                            <input
                                                type="text"
                                                value={foreignerInfo.country}
                                                onChange={(e) => setForeignerInfo(prev => ({ ...prev, country: e.target.value }))}
                                                className="w-full px-4 py-3 border border-gray-300 dark:bg-gray-800 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                                placeholder="Enter your country of citizenship"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : null}

                        {/* Upload Guidelines */}
                        <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 rounded-xl p-6 mb-8 max-w-4xl mx-auto">
                            <div className="flex items-start space-x-3">
                                <svg className="w-6 h-6 text-blue-600 dark:text-blue-100 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div className="text-left">
                                    <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Photo Guidelines</h5>
                                    <ul className="text-sm text-blue-800 dark:text-blue-100 space-y-1">
                                        <li>• Ensure the document is well-lit and all text is clearly visible</li>
                                        <li>• Take photos straight-on, avoiding angles or shadows</li>
                                        <li>• Make sure the entire document fits within the frame</li>
                                        <li>• Use a dark background for better contrast</li>
                                        <li>• File size should be under 10MB per image</li>
                                        <li>• Accepted formats: JPG, PNG, PDF</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        {/* Error Display */}
                        {kycError && (
                            <motion.div
                                className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 max-w-4xl mx-auto"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <div className="flex items-center space-x-3">
                                    <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p className="text-red-800 font-medium">{kycError}</p>
                                    <button
                                        onClick={() => setKycError(null)}
                                        className="ml-auto text-red-600 hover:text-red-800"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </motion.div>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between max-w-4xl mx-auto">
                            <motion.button
                                type="button"
                                onClick={() => setKycStep(2)}
                                className="px-6 py-3 border border-gray-300 text-white dark:text-indigo-100 dark:bg-indigo-700 rounded-xl hover:bg-gray-50 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                            <motion.button
                                type="button"
                                onClick={() => {
                                    // Nationality-specific validation
                                    if (selectedNationality === 'malaysian') {
                                        if (uploadedDocuments.length >= 2 &&
                                            malaysianInfo.full_name &&
                                            malaysianInfo.ic_number) {
                                            setKycStep(4);
                                        } else {
                                            setKycError('Please upload both IC sides and fill in all required information before proceeding.');
                                        }
                                    } else if (selectedNationality === 'foreigner') {
                                        if (uploadedDocuments.length >= 1 &&
                                            foreignerInfo.full_name &&
                                            foreignerInfo.passport_number &&
                                            foreignerInfo.country) {
                                            setKycStep(4);
                                        } else {
                                            setKycError('Please upload your passport and fill in all required information before proceeding.');
                                        }
                                    }
                                }}
                                disabled={
                                    selectedNationality === 'malaysian'
                                        ? uploadedDocuments.length < 2 || !malaysianInfo.full_name || !malaysianInfo.ic_number
                                        : selectedNationality === 'foreigner'
                                        ? uploadedDocuments.length < 1 || !foreignerInfo.full_name || !foreignerInfo.passport_number || !foreignerInfo.country
                                        : true
                                }
                                className={`px-6 py-3 rounded-xl transition-colors ${
                                    (selectedNationality === 'malaysian' && uploadedDocuments.length >= 2 &&
                                     malaysianInfo.full_name && malaysianInfo.ic_number) ||
                                    (selectedNationality === 'foreigner' && uploadedDocuments.length >= 1 &&
                                     foreignerInfo.full_name && foreignerInfo.passport_number && foreignerInfo.country)
                                        ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                                        : 'bg-gray-300 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                                }`}
                                whileHover={
                                    (selectedNationality === 'malaysian' && uploadedDocuments.length >= 2 &&
                                     malaysianInfo.full_name && malaysianInfo.ic_number) ||
                                    (selectedNationality === 'foreigner' && uploadedDocuments.length >= 1 &&
                                     foreignerInfo.full_name && foreignerInfo.passport_number && foreignerInfo.country)
                                        ? { scale: 1.02 } : {}
                                }
                                whileTap={
                                    (selectedNationality === 'malaysian' && uploadedDocuments.length >= 2 &&
                                     malaysianInfo.full_name && malaysianInfo.ic_number) ||
                                    (selectedNationality === 'foreigner' && uploadedDocuments.length >= 1 &&
                                     foreignerInfo.full_name && foreignerInfo.passport_number && foreignerInfo.country)
                                        ? { scale: 0.98 } : {}
                                }
                            >
                                Continue to Selfie
                            </motion.button>
                        </div>
                    </motion.div>
                );

            case 4:
                return (
                    <motion.div
                        className="py-8"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <div className="text-center mb-8">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-4">Take a Selfie</h3>
                            <p className="text-gray-600 dark:text-indigo-100 max-w-2xl mx-auto mb-6">
                                Now we need to verify that you're the same person as in your ID document.
                                Please take a clear selfie or upload a recent photo of yourself.
                            </p>
                        </div>

                        {/* Selfie Capture Interface */}
                        <SelfieCapture />

                        {/* Guidelines */}
                        <div className="bg-green-50 dark:bg-green-900 border border-green-200 rounded-xl p-6 mt-8 max-w-2xl mx-auto">
                            <div className="flex items-start space-x-3">
                                <svg className="w-6 h-6 text-green-600 dark:text-green-100 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div className="text-left">
                                    <h5 className="font-medium text-green-900 dark:text-green-100 mb-2">Selfie Guidelines</h5>
                                    <ul className="text-sm text-green-800 dark:text-green-100 space-y-1">
                                        <li>• Look directly at the camera with a neutral expression</li>
                                        <li>• Ensure your face is well-lit and clearly visible</li>
                                        <li>• Remove glasses, hats, or anything covering your face</li>
                                        <li>• Make sure the photo matches your ID document</li>
                                        <li>• Use a plain background if possible</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        {/* Error Display */}
                        {kycError && (
                            <motion.div
                                className="bg-red-50 border border-red-200 rounded-xl p-4 mt-6 max-w-2xl mx-auto"
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                            >
                                <div className="flex items-center space-x-3">
                                    <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p className="text-red-800 font-medium">{kycError}</p>
                                    <button
                                        onClick={() => setKycError(null)}
                                        className="ml-auto text-red-600 hover:text-red-800"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </motion.div>
                        )}

                        {/* Navigation */}
                        <div className="flex justify-between mt-8 max-w-2xl mx-auto">
                            <motion.button
                                onClick={() => setKycStep(3)}
                                className="px-6 py-3 border border-gray-300 text-white dark:text-indigo-100 dark:bg-indigo-700 rounded-xl hover:bg-gray-50 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                            <motion.button
                                onClick={() => {
                                    if (selfieImage) {
                                        setKycStep(5);
                                    } else {
                                        setKycError('Please take a selfie or upload a photo before proceeding.');
                                    }
                                }}
                                disabled={!selfieImage}
                                className={`px-6 py-3 rounded-xl transition-colors ${
                                    selfieImage
                                        ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                                        : 'bg-gray-300 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                                }`}
                                whileHover={selfieImage ? { scale: 1.02 } : {}}
                                whileTap={selfieImage ? { scale: 0.98 } : {}}
                            >
                                Continue to Review
                            </motion.button>
                        </div>
                    </motion.div>
                );

            case 5:
                return (
                    <motion.div
                        className="py-8"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        {kycStatus === 'verified' ? (
                            /* Verified State - User is already verified */
                            <div className="text-center max-w-2xl mx-auto">
                                <motion.div
                                    className="w-24 h-24 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-full flex items-center justify-center mx-auto mb-6"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ type: "spring", stiffness: 200, damping: 15 }}
                                >
                                    <motion.svg
                                        className="w-12 h-12 text-green-600 dark:text-green-100"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        initial={{ pathLength: 0 }}
                                        animate={{ pathLength: 1 }}
                                        transition={{ duration: 0.8, delay: 0.3 }}
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </motion.svg>
                                </motion.div>

                                <h3 className="text-2xl font-bold text-gray-900 dark:text-indigo-100 mb-4">🎉 You are now verified!</h3>
                                <p className="text-gray-600 dark:text-indigo-100 mb-8 leading-relaxed">
                                    Congratulations! Your identity has been successfully verified. You now have full access to all
                                    Mission X features and can enjoy enhanced security and trust benefits.
                                </p>

                                {/* Verified Status Card */}
                                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900 dark:to-emerald-900 border border-green-200 rounded-2xl p-6 mb-8">
                                    <div className="flex items-center justify-center space-x-3 mb-4">
                                        <div className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${getStatusColor(kycStatus)}`}>
                                            {getStatusIcon(kycStatus)}
                                            <span className="font-medium">{getStatusText(kycStatus)}</span>
                                        </div>
                                    </div>
                                    <p className="text-sm text-green-700 dark:text-green-100">
                                        Your account is fully verified and secure. You can now access all premium features
                                        and enjoy the highest level of trust and security on our platform.
                                    </p>
                                </div>

                                {/* Verification Benefits */}
                                <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 rounded-xl p-6 mb-8">
                                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">✨ Verification Benefits</h4>
                                    <div className="space-y-2 text-sm text-blue-800 dark:text-blue-100">
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Enhanced security and account protection</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Access to premium features and higher limits</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Priority customer support</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Verified badge on your profile</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-center space-x-4">
                                    <motion.button
                                        onClick={() => setActiveModal(null)}
                                        className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        Continue to Dashboard
                                    </motion.button>
                                </div>
                            </div>
                        ) : kycStatus === 'pending_review' ? (
                            /* Pending Review State */
                            <div className="text-center max-w-2xl mx-auto">
                                <motion.div
                                    className="w-24 h-24 bg-gradient-to-br from-amber-100 to-orange-100 rounded-full flex items-center justify-center mx-auto mb-6"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ type: "spring", stiffness: 200, damping: 15 }}
                                >
                                    <motion.svg
                                        className="w-12 h-12 text-amber-600"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        initial={{ pathLength: 0 }}
                                        animate={{ pathLength: 1 }}
                                        transition={{ duration: 0.8, delay: 0.3 }}
                                    >
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </motion.svg>
                                </motion.div>

                                <h3 className="text-2xl font-bold text-gray-900 mb-4">Verification Submitted Successfully!</h3>
                                <p className="text-gray-600 mb-8 leading-relaxed">
                                    Thank you for submitting your identity verification documents. Our team will review your submission
                                    and notify you of the results within 24-48 hours.
                                </p>

                                {/* Status Card */}
                                <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 mb-8">
                                    <div className="flex items-center justify-center space-x-3 mb-4">
                                        <div className={`flex items-center space-x-2 px-4 py-2 rounded-full border ${getStatusColor(kycStatus)}`}>
                                            {getStatusIcon(kycStatus)}
                                            <span className="font-medium">{getStatusText(kycStatus)}</span>
                                        </div>
                                    </div>
                                    <p className="text-sm text-green-700">
                                        Your verification is currently being reviewed by our security team.
                                        You'll receive an email notification once the review is complete.
                                    </p>
                                </div>

                                {/* What's Next */}
                                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
                                    <h4 className="font-semibold text-blue-900 mb-3">What happens next?</h4>
                                    <div className="space-y-2 text-sm text-blue-800">
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Our team reviews your documents for authenticity</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>We verify your selfie matches your ID document</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>You receive email notification with results</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span>Your account gains full verification status</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-center space-x-4">
                                    <motion.button
                                        onClick={() => setActiveModal(null)}
                                        className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        Close
                                    </motion.button>
                                </div>
                            </div>
                        ) : (
                            /* Review State */
                            <div className="max-w-4xl mx-auto">
                                <div className="text-center mb-8">
                                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Review Your Information</h3>
                                    <p className="text-gray-600 max-w-2xl mx-auto">
                                        Please review all your submitted information before final submission.
                                        Make sure all documents are clear and your selfie matches your ID.
                                    </p>
                                </div>

                                {/* Review Cards */}
                                <div className="grid md:grid-cols-2 gap-6 mb-8">
                                    {/* Document Review */}
                                    <div className="bg-white border border-gray-200 rounded-2xl p-6">
                                        <div className="flex items-center space-x-3 mb-4">
                                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-gray-900">Identity Document</h4>
                                                <p className="text-sm text-gray-600 capitalize">
                                                    {selectedNationality === 'malaysian' ? 'Malaysian IC (MyKad)' : 'International Passport'}
                                                </p>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-2 gap-3">
                                            {uploadedDocuments.map((doc) => (
                                                <div key={doc.id} className="relative">
                                                    <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                                                        {doc.type.startsWith('image/') ? (
                                                            <img
                                                                src={doc.previewUrl}
                                                                alt={`${doc.side} side`}
                                                                className="w-full h-full object-cover"
                                                            />
                                                        ) : (
                                                            <div className="w-full h-full flex items-center justify-center">
                                                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                                </svg>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <p className="text-xs text-gray-600 mt-1 capitalize text-center">{doc.side} Side</p>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Selfie Review */}
                                    <div className="bg-white border border-gray-200 rounded-2xl p-6">
                                        <div className="flex items-center space-x-3 mb-4">
                                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold text-gray-900">Selfie Photo</h4>
                                                <p className="text-sm text-gray-600">Identity verification photo</p>
                                            </div>
                                        </div>

                                        {selfieImage && (
                                            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden max-w-48 mx-auto">
                                                <img
                                                    src={selfieImage.previewUrl}
                                                    alt="Selfie"
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Final Confirmation */}
                                <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 mb-8">
                                    <div className="flex items-start space-x-3">
                                        <svg className="w-6 h-6 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <div>
                                            <h5 className="font-medium text-amber-800 mb-2">Important Notice</h5>
                                            <p className="text-sm text-amber-700 mb-3">
                                                By submitting this verification, you confirm that:
                                            </p>
                                            <ul className="text-sm text-amber-700 space-y-1">
                                                <li>• All documents provided are authentic and belong to you</li>
                                                <li>• The selfie photo is a recent and accurate representation</li>
                                                <li>• You understand that false information may result in account suspension</li>
                                                <li>• You consent to the processing of your personal data for verification purposes</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                {/* Error Display */}
                                {kycError && (
                                    <motion.div
                                        className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                    >
                                        <div className="flex items-center space-x-3">
                                            <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                            <p className="text-red-800 font-medium">{kycError}</p>
                                            <button
                                                onClick={() => setKycError(null)}
                                                className="ml-auto text-red-600 hover:text-red-800"
                                            >
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>
                                    </motion.div>
                                )}

                                {/* Navigation */}
                                <div className="flex justify-between">
                                    <motion.button
                                        onClick={() => setKycStep(4)}
                                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                    >
                                        Back to Selfie
                                    </motion.button>

                                    <div className="flex space-x-3">
                                        <motion.button
                                            onClick={resetKycProcess}
                                            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                        >
                                            Start Over
                                        </motion.button>
                                        <motion.button
                                            onClick={submitKycVerification}
                                            disabled={isSubmittingKyc}
                                            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                            whileHover={!isSubmittingKyc ? { scale: 1.05 } : {}}
                                            whileTap={!isSubmittingKyc ? { scale: 0.95 } : {}}
                                        >
                                            {isSubmittingKyc ? (
                                                <div className="flex items-center space-x-2">
                                                    <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                    </svg>
                                                    <span>Submitting...</span>
                                                </div>
                                            ) : (
                                                'Submit Verification'
                                            )}
                                        </motion.button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </motion.div>
                );

            default:
                return (
                    <div className="text-center py-8">
                        <p className="text-gray-600">Step {kycStep} content coming soon...</p>
                        <div className="flex justify-between mt-8">
                            <motion.button
                                onClick={() => setKycStep(Math.max(1, kycStep - 1))}
                                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Back
                            </motion.button>
                            <motion.button
                                onClick={() => setKycStep(Math.min(5, kycStep + 1))}
                                className="px-6 py-3 bg-indigo-600 text-white rounded-xl hover:bg-indigo-700 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                Next
                            </motion.button>
                        </div>
                    </div>
                );
        }
    };

    const renderModalContent = () => {
        switch (activeModal) {
            case 'password':
                return (
                    <motion.div
                        className="fixed inset-0 bg-black/60 dark:bg-gray-900 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={() => setActiveModal(null)}
                    >
                        <motion.div
                            className="relative bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900 dark:to-blue-90 backdrop-blur-2xl border border-indigo-200 dark:border-indigo-700 rounded-3xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto"
                            initial={{ scale: 0.95, opacity: 0, y: 20 }}
                            animate={{ scale: 1, opacity: 1, y: 0 }}
                            exit={{ scale: 0.95, opacity: 0, y: 20 }}
                            onClick={e => e.stopPropagation()}
                        >
                            {/* Header */}
                            <div className="relative px-6 py-6 bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900 dark:to-blue-90 border-b border-gray-100 dark:border-indigo-700 rounded-t-3xl">
                                <div className="flex items-center space-x-3">
                                    <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-blue-600 dark:from-indigo-900 dark:to-blue-90 flex items-center justify-center shadow-lg flex-shrink-0">
                                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                        </svg>
                                    </div>
                            <div>
                                        <h2 className="text-2xl text-left font-bold text-gray-900 dark:text-indigo-100 mb-1">{t('account.password.title')}</h2>
                                        <p className="text-gray-600 dark:text-indigo-100 text-sm">Update your account password for enhanced security.</p>
                                    </div>
                                </div>
                                {/* Close Button */}
                                <button
                                    onClick={() => setActiveModal(null)}
                                    className="absolute top-4 right-4 p-2 bg-white/40 rounded-full hover:bg-blue-100 transition-colors"
                                    aria-label="Close"
                                >
                                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            {/* Form Card */}
                            <div className="p-8">
                                <form className="space-y-6" onSubmit={handlePasswordChange}>
                                    <FormInput
                                        label="Current Password"
                                        name="currentPassword"
                                    type="password"
                                    value={currentPassword}
                                        onChange={e => setCurrentPassword(e.target.value)}
                                        required
                                        showTogglePassword={true}
                                        autoComplete="current-password"
                                    />
                                    <FormInput
                                        label="New Password"
                                        name="newPassword"
                                    type="password"
                                    value={newPassword}
                                        onChange={e => setNewPassword(e.target.value)}
                                        required
                                        showTogglePassword={true}
                                        autoComplete="new-password"
                                    />
                                    <FormInput
                                        label="Confirm New Password"
                                        name="confirmNewPassword"
                                    type="password"
                                    value={confirmNewPassword}
                                        onChange={e => setConfirmNewPassword(e.target.value)}
                                        required
                                        showTogglePassword={true}
                                        autoComplete="new-password"
                                    />
                                    {/* Error Message */}
                                    <AnimatePresence>
                            {passwordError && (
                                            <motion.div
                                                initial={{ opacity: 0, y: -10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, y: -10 }}
                                                className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center gap-3 mb-2"
                                            >
                                                <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <span className="text-red-800 font-medium">{passwordError}</span>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                    {/* Submit Button */}
                                    <motion.button
                                type="submit"
                                disabled={isUpdatingPassword}
                                        className="w-full py-3 rounded-xl font-semibold text-lg bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        whileHover={!isUpdatingPassword ? { scale: 1.03 } : {}}
                                        whileTap={!isUpdatingPassword ? { scale: 0.97 } : {}}
                                    >
                                        {isUpdatingPassword ? (
                                            <span className="flex items-center justify-center">
                                                <svg className="w-5 h-5 animate-spin mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                                Updating...
                                            </span>
                                        ) : (
                                            'Update Password'
                                        )}
                                    </motion.button>
                        </form>
                            </div>
                        </motion.div>
                    </motion.div>
                );
                case 'language':
                return (
                    <SettingsModal title={t('account.language.title')} onClose={() => setActiveModal(null)}>
                        <div className="space-y-4">
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">{t('account.language.chooseLanguage')}</h4>
                                {supportedLanguages.map((lang) => (
                                    <div
                                        key={lang.code}
                                        onClick={() => handleLanguageChange(lang.code)}
                                        className={`px-4 py-2 rounded-md cursor-pointer ${
                                            currentLanguage === lang.code
                                                ? ' bg-indigo-100 dark:bg-indigo-800 text-indigo-700 font-medium'
                                                : 'hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-indigo-100'
                                        }`}
                                    >
                                        {lang.nativeName} {currentLanguage === lang.code && `(${t('account.language.currentLanguage')})`}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </SettingsModal>
                );
            case 'emergencyContact':
                return (
                    <EmergencyContactModal
                        isOpen={true}
                        onClose={() => setActiveModal(null)}
                    />
                );
                case 'kyc':
                return (
                    <motion.div
                        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onKeyDown={handleKycKeyDown}
                        tabIndex={-1}
                        role="dialog"
                        aria-modal="true"
                        aria-labelledby="kyc-modal-title"
                        aria-describedby="kyc-modal-description"
                    >
                        <motion.div
                            className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl w-full max-w-4xl mx-4 shadow-2xl border border-gray-100 dark:border-gray-700 max-h-[90vh] overflow-hidden focus:outline-none"
                            initial={{ scale: 0.95, opacity: 0, y: 20 }}
                            animate={{ scale: isKycModalClosing ? 0.95 : 1, opacity: isKycModalClosing ? 0 : 1, y: isKycModalClosing ? 20 : 0 }}
                            exit={{ scale: 0.95, opacity: 0, y: 20 }}
                            tabIndex={0}
                            onSubmit={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log('Form submission prevented at modal level');
                                return false;
                            }}
                        >
                            {/* Header */}
                            <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 dark:from-indigo-900 dark:via-purple-900 dark:to-pink-900 p-6 relative overflow-hidden">
                                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                                <div className="relative flex justify-between items-center">
                                    <div className="flex items-center space-x-4">
                                        <div className="w-12 h-12 bg-white/20 dark:bg-gray-800 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h2 id="kyc-modal-title" className="text-2xl font-bold text-white mb-1 text-left">{t('account.kyc.title')}</h2>
                                            <p id="kyc-modal-description" className="text-white/80 text-sm text-left">Secure your account with e-KYC verification</p>
                                        </div>
                                    </div>
                                    <motion.button
                                        onClick={() => {
                                            setActiveModal(null);
                                            setKycStep(1);
                                            setKycError(null);
                                        }}
                                        className="p-3 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                    >
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </motion.button>
                                </div>
                            </div>

                            {/* Enhanced Progress Indicator */}
                            <div className="bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-900 border-b border-gray-200/50 px-6 py-4">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                        <span className="text-sm font-medium text-gray-600 dark:text-indigo-100">Step {kycStep} of 5</span>
                                        <div className="flex items-center space-x-1">
                                            {[1, 2, 3, 4, 5].map((step) => (
                                                <div
                                                    key={step}
                                                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                                        step < kycStep
                                                            ? 'bg-green-500'
                                                            : step === kycStep
                                                            ? 'bg-indigo-500 scale-125'
                                                            : 'bg-gray-300'
                                                    }`}
                                                    aria-label={`Step ${step} ${step < kycStep ? 'completed' : step === kycStep ? 'current' : 'pending'}`}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <span className="text-sm font-semibold text-indigo-600 dark:text-indigo-100">{Math.round(kycProgress)}% Complete</span>
                                        <div className="text-xs text-gray-500 dark:text-indigo-100 mt-1">
                                            {kycStatus === 'verified' ? 'Verified ✓' :
                                             kycStatus === 'pending_review' ? 'Under Review' :
                                             kycStep === 1 ? 'Getting Started' :
                                             kycStep === 2 ? 'Nationality Selection' :
                                             kycStep === 3 ? 'Document Upload' :
                                             kycStep === 4 ? 'Selfie Capture' :
                                             'Final Review'}
                                        </div>
                                    </div>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                                    <motion.div
                                        className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 dark:from-indigo-700 dark:via-purple-700 dark:to-pink-700 h-3 rounded-full relative"
                                        initial={{ width: 0 }}
                                        animate={{ width: `${kycProgress}%` }}
                                        transition={{ duration: 0.8, ease: "easeInOut" }}
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent dark:from-gray-700 dark:to-transparent rounded-full"></div>
                                    </motion.div>
                                </div>

                                {/* Keyboard Navigation Hint */}
                                <div className="flex items-center justify-center mt-3 text-xs text-gray-400 dark:text-indigo-100">
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    Use arrow keys to navigate • ESC to close
                                </div>
                            </div>

                            {/* Content Area */}
                            <div
                                className="p-6 overflow-y-auto max-h-[calc(90vh-12rem)]"
                                data-kyc-step={kycStep}
                                role="main"
                                aria-live="polite"
                                aria-atomic="true"
                            >
                                {renderKycStep()}
                            </div>
                        </motion.div>
                    </motion.div>
                );
                case '3rdPartyAccess':
                return (
                    <SettingsModal title={t('account.thirdParty.title')} onClose={() => setActiveModal(null)}>
                        <div className="space-y-4">
                            <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-xl">
                                <h4 className="font-medium text-indigo-900 mb-2">Third-Party Application Access</h4>
                                <p className="text-sm text-justify text-indigo-700">
                                    Allow third-party applications to access your Mission X data. This enables integration with external services and tools.
                                </p>
                            </div>

                            <div className="mt-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium text-center mb-2 text-gray-900 dark:text-indigo-100">{t('account.thirdParty.title')}</h4>
                                        <p className="text-sm text-left text-gray-500 dark:text-gray-400 dark:text-indigo-50">
                                            When enabled, authorized third-party applications can access your profile and activity data.
                                        </p>
                                    </div>
                                    <div className="relative">
                                        <div className="relative w-12 h-6">
                                            <button
                                                onClick={handle3rdPartyAccessToggle}
                                                disabled={isLoading3rdPartyAccess}
                                                className={`absolute inset-0 w-full h-full transition-colors duration-200 rounded-full ${
                                                    allow3rdPartyAccess ? 'bg-indigo-600' : 'bg-gray-300'
                                                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
                                                aria-checked={allow3rdPartyAccess}
                                                role="switch"
                                            >
                                                <span className="sr-only dark:text-indigo-100 text-left">
                                                    {allow3rdPartyAccess ? 'Disable' : 'Enable'} 3rd party access
                                                </span>
                                            </button>
                                            <span
                                                className={`absolute top-0.5 left-0.5 inline-block w-5 h-5 transition-transform duration-200 transform ${
                                                    allow3rdPartyAccess ? 'translate-x-6' : 'translate-x-0'
                                                } bg-white rounded-full shadow-md`}
                                            ></span>
                                        </div>
                                        {isLoading3rdPartyAccess && (
                                            <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">Loading...</span>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="mt-6 border-t border-gray-100 pt-4">
                                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Connected Applications</h4>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                    {allow3rdPartyAccess
                                        ? "These applications currently have access to your Mission X data."
                                        : "No applications can access your data because 3rd party access is disabled."}
                                </p>

                                {allow3rdPartyAccess && (
                                    <div className="space-y-3">
                                        {connectedApps.length > 0 ? (
                                            connectedApps.map(app => (
                                                <div key={app.id} className="p-3 border border-gray-200 rounded-lg flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                                            {app.icon_url ? (
                                                                <img
                                                                    src={app.icon_url}
                                                                    alt={app.name}
                                                                    className="w-6 h-6 object-contain"
                                                                    onError={(e) => {
                                                                        e.target.onerror = null;
                                                                        e.target.style.display = 'none';
                                                                        e.target.parentNode.innerHTML = `
                                                                            <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                                            </svg>
                                                                        `;
                                                                    }}
                                                                />
                                                            ) : (
                                                                <svg className="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                                </svg>
                                                            )}
                                                        </div>
                                                        <div>
                                                            <h5 className="font-medium text-gray-900 dark:text-indigo-100">{app.name}</h5>
                                                            {app.description && (
                                                                <p className="text-xs text-gray-500 dark:text-gray-400 dark:text-indigo-50">{app.description}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="px-3 py-1 bg-gray-100 dark:bg-indigo-900 rounded-md text-xs text-gray-500 dark:text-gray-400 dark:text-indigo-50">
                                                        Connected
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-4">
                                                <p className="text-gray-500 dark:text-gray-400">No applications connected yet.</p>
                                                <p className="text-sm text-gray-400 mt-1">
                                                    Third-party applications will appear here once you connect them.
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </SettingsModal>
                );
            case 'aboutUs':
                return (
                    <SettingsModal title={t('general.aboutUs.title')} onClose={() => setActiveModal(null)}>
                        <div className="max-h-2xl max-w-2xl overflow-y-auto pr-2">
                            {isLoadingAboutUs ? (
                                <div className="flex justify-center items-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                </div>
                            ) : aboutUsError ? (
                                <div className="bg-red-50 text-red-600 p-4 rounded-lg">
                                    <p>{aboutUsError}</p>
                                </div>
                            ) : aboutUsContent ? (
                                <div className="prose prose-indigo max-w-none text-left">
                                    {aboutUsContent.includes('<') && aboutUsContent.includes('>') ? (
                                        // If content appears to be HTML, render it as HTML
                                        <div dangerouslySetInnerHTML={{ __html: aboutUsContent }} />
                                    ) : (
                                        // Otherwise render as plain text with line breaks preserved
                                        <div style={{ whiteSpace: 'pre-wrap' }}>{aboutUsContent}</div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">Loading about us information...</p>
                            )}
                        </div>
                    </SettingsModal>
                );
            case 'termsConditions':
                return (
                    <SettingsModal title={t('general.termsConditions.title')} onClose={() => setActiveModal(null)}>
                        <div className="max-h-[60vh] overflow-y-auto pr-2">
                            {isLoadingTerms ? (
                                <div className="flex justify-center items-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                </div>
                            ) : termsError ? (
                                <div className="bg-red-50 text-red-600 p-4 rounded-lg">
                                    <p>{termsError}</p>
                                </div>
                            ) : termsContent ? (
                                <div className="prose prose-indigo max-w-none text-left">
                                    {termsContent.includes('<') && termsContent.includes('>') ? (
                                        // If content appears to be HTML, render it as HTML
                                        <div dangerouslySetInnerHTML={{ __html: termsContent }} />
                                    ) : (
                                        // Otherwise render as plain text with line breaks preserved
                                        <div style={{ whiteSpace: 'pre-wrap' }}>{termsContent}</div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">Loading terms and conditions...</p>
                            )}
                        </div>
                    </SettingsModal>
                );
            case 'privacyPolicy':
                return (
                    <SettingsModal title={t('general.privacyPolicy.title')} onClose={() => setActiveModal(null)}>
                        <div className="max-h-[60vh] overflow-y-auto pr-2">
                            {isLoadingPrivacy ? (
                                <div className="flex justify-center items-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                </div>
                            ) : privacyError ? (
                                <div className="bg-red-50 text-red-600 p-4 rounded-lg">
                                    <p>{privacyError}</p>
                                </div>
                            ) : privacyContent ? (
                                <div className="prose prose-indigo max-w-none text-left">
                                    {privacyContent.includes('<') && privacyContent.includes('>') ? (
                                        // If content appears to be HTML, render it as HTML
                                        <div dangerouslySetInnerHTML={{ __html: privacyContent }} />
                                    ) : (
                                        // Otherwise render as plain text with line breaks preserved
                                        <div style={{ whiteSpace: 'pre-wrap' }}>{privacyContent}</div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">Loading privacy policy...</p>
                            )}
                        </div>
                    </SettingsModal>
                );
            case 'contactUs':
                return (
                    <SettingsModal title={t('general.contactUs.title')} onClose={() => setActiveModal(null)}>
                        <div className="max-h-[60vh] overflow-y-auto pr-2">
                            {isLoadingContactUs ? (
                                <div className="flex justify-center items-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                </div>
                            ) : contactUsError ? (
                                <div className="bg-red-50 text-red-600 p-4 rounded-lg">
                                    <p>{contactUsError}</p>
                                </div>
                            ) : contactUsContent ? (
                                <div className="prose prose-indigo max-w-none text-left">
                                    {contactUsContent.includes('<') && contactUsContent.includes('>') ? (
                                        // If content appears to be HTML, render it as HTML
                                        <div dangerouslySetInnerHTML={{ __html: contactUsContent }} />
                                    ) : (
                                        // Otherwise render as plain text with line breaks preserved
                                        <div style={{ whiteSpace: 'pre-wrap' }}>{contactUsContent}</div>
                                    )}
                                </div>
                            ) : (
                                <p className="text-gray-500 dark:text-gray-400 italic">Loading contact information...</p>
                            )}
                        </div>
                    </SettingsModal>
                );
            case 'emailVerification':
                return <EmailVerificationModal onClose={() => setActiveModal(null)} />;
            case 'feedback':
                return (
                    <motion.div
                        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={() => setActiveModal(null)}
                        aria-modal="true"
                        role="dialog"
                        aria-labelledby="feedback-modal-title"
                        aria-describedby="feedback-modal-desc"
                    >
                        <motion.div
                            className="relative bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-indigo-900/90 backdrop-blur-2xl border border-indigo-200 rounded-3xl shadow-2xl w-full max-w-xl max-h-[90vh] overflow-y-auto"
                            initial={{ scale: 0.95, opacity: 0, y: 20 }}
                            animate={{ scale: 1, opacity: 1, y: 0 }}
                            exit={{ scale: 0.95, opacity: 0, y: 20 }}
                            onClick={e => e.stopPropagation()}
                            data-feedback-modal
                        >
                            {/* Header */}
                            <div className="relative px-6 py-6 bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900 dark:to-indigo-900 border-b border-gray-100 rounded-t-3xl">
                                <div className="flex items-center space-x-3">
                                    <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center shadow-lg flex-shrink-0">
                                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m-2 8a8 8 0 100-16 8 8 0 000 16zm0 0v-4m0 0h4" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 id="feedback-modal-title" className="text-2xl text-left font-bold text-gray-900 dark:text-indigo-100 mb-1">{t('general.feedback.title')}</h2>
                                        <p id="feedback-modal-desc" className="text-gray-600 text-left dark:text-indigo-100 text-sm">We value your feedback! Let us know your thoughts or issues.</p>
                                    </div>
                                </div>
                                {/* Close Button */}
                                <button
                                    onClick={() => setActiveModal(null)}
                                    className="absolute top-4 right-4 p-2 bg-white/40 rounded-full hover:bg-blue-100 transition-colors"
                                    aria-label="Close"
                                    data-feedback-modal
                                >
                                    <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            {/* Form Card */}
                            <div className="p-8">
                                {isLoadingFeedbackTopics ? (
                                    <div className="flex justify-center items-center py-12">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                        <span className="ml-3 text-indigo-600 font-medium">Loading topics...</span>
                                    </div>
                                ) : (
                                    <form
                                        className="space-y-6"
                                        onSubmit={async e => {
                                            e.preventDefault();
                                            setFeedbackError(null);
                                            if (!feedbackTopic) {
                                                setFeedbackError('Please select a topic.');
                                                return;
                                            }
                                            if (!feedbackDescription.trim()) {
                                                setFeedbackError('Please enter a description.');
                                                return;
                                            }
                                            setIsSubmittingFeedback(true);
                                            try {
                                                const formData = new FormData();
                                                formData.append('topic_id', feedbackTopic);
                                                formData.append('description', feedbackDescription);
                                                feedbackAttachments.forEach(file => {
                                                    formData.append('attachments[]', file);
                                                });
                                                const response = await api.post('/feedback', formData, {
                                                    headers: {
                                                        'Content-Type': 'multipart/form-data',
                                                        'Accept': 'application/json',
                                                    },
                                                });
                                                if (response.data && (response.data.success || response.data.message)) {
                                                    toast.success(response.data.message || 'Feedback submitted!');
                                                    setActiveModal(null);
                                                    setFeedbackTopic('');
                                                    setFeedbackDescription('');
                                                    setFeedbackAttachments([]);
                                                    setFeedbackError(null);
                                                    setFeedbackFileError(null);
                                                } else {
                                                    throw new Error(response.data?.message || 'Unexpected response from server');
                                                }
                                            } catch (err) {
                                                let errorMsg = 'Failed to submit feedback.';
                                                if (err.response) {
                                                    if (err.response.status === 422) {
                                                        // Validation error
                                                        if (err.response.data?.errors) {
                                                            // Show first error message
                                                            const firstError = Object.values(err.response.data.errors)[0];
                                                            errorMsg = Array.isArray(firstError) ? firstError[0] : firstError;
                                                        } else if (err.response.data?.message) {
                                                            errorMsg = err.response.data.message;
                                                        } else {
                                                            errorMsg = 'Validation error. Please check your input.';
                                                        }
                                                    } else if (err.response.status === 429) {
                                                        errorMsg = 'You are submitting feedback too quickly. Please wait and try again.';
                                                    } else if (err.response.data?.message) {
                                                        errorMsg = err.response.data.message;
                                                    }
                                                } else if (err.message) {
                                                    errorMsg = err.message;
                                                }
                                                setFeedbackError(errorMsg);
                                                toast.error(errorMsg);
                                            } finally {
                                                setIsSubmittingFeedback(false);
                                            }
                                        }}
                                        data-feedback-modal
                                    >
                                        {/* Choose Topic */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2" htmlFor="feedback-topic-select">Choose Topic <span className="text-red-500 dark:text-red-200">*</span></label>
                                            <div className="relative">
                                                <select
                                                    id="feedback-topic-select"
                                                    ref={feedbackFirstInputRef}
                                                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white dark:bg-gray-800 appearance-none"
                                                    value={feedbackTopic}
                                                    onChange={e => setFeedbackTopic(e.target.value)}
                                                    required
                                                    aria-label="Choose feedback topic"
                                                    data-feedback-modal
                                                >
                                                    <option value="" disabled>Select a topic</option>
                                                    {feedbackTopics.map(topic => (
                                                        <option key={topic.id || topic.value || topic} value={topic.id || topic.value || topic}>
                                                            {topic.label || topic.name || topic}
                                                        </option>
                                                    ))}
                                                </select>
                                                <svg className="w-5 h-5 text-gray-400 dark:text-indigo-100 absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                        {/* Description */}
                                        <FormInput
                                            label="Description"
                                            name="feedbackDescription"
                                            type="textarea"
                                            value={feedbackDescription}
                                            onChange={e => setFeedbackDescription(e.target.value)}
                                            required
                                            rows={4}
                                            placeholder="Describe your feedback or issue in detail..."
                                            autoFocus={false}
                                            data-feedback-modal={true}
                                        />
                                        {/* Attachment */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-indigo-100 mb-2" htmlFor="feedback-attachment-input">Attachments (optional)</label>
                                            <div className="flex flex-col gap-2">
                                                <input
                                                    id="feedback-attachment-input"
                                                    type="file"
                                                    multiple
                                                    accept="image/jpeg,image/png,image/jpg,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,video/mp4,video/quicktime,video/x-msvideo"
                                                    onChange={e => {
                                                        const files = Array.from(e.target.files || []);
                                                        const allowedTypes = [
                                                            'image/jpeg', 'image/png', 'image/jpg', 'image/gif',
                                                            'application/pdf',
                                                            'application/msword',
                                                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                                            'video/mp4', 'video/quicktime', 'video/x-msvideo'
                                                        ];
                                                        const maxSize = 10 * 1024 * 1024; // 10MB
                                                        let validFiles = [];
                                                        let errorMessages = [];
                                                        files.forEach(file => {
                                                            if (!allowedTypes.includes(file.type)) {
                                                                errorMessages.push(`${file.name}: Invalid file type.`);
                                                                return;
                                                            }
                                                            if (file.size > maxSize) {
                                                                errorMessages.push(`${file.name}: File size exceeds 10MB.`);
                                                                return;
                                                            }
                                                            validFiles.push(file);
                                                        });
                                                        if (errorMessages.length > 0) {
                                                            setFeedbackFileError(errorMessages.join(' '));
                                                        } else {
                                                            setFeedbackFileError(null);
                                                        }
                                                        setFeedbackAttachments(prev => {
                                                            // Only allow up to 5 files total
                                                            const newFiles = validFiles.slice(0, 5 - prev.length);
                                                            return [...prev, ...newFiles].slice(0, 5);
                                                        });
                                                        e.target.value = '';
                                                    }}
                                                    className="block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100 dark:hover:file:bg-indigo-800"
                                                    aria-label="Upload attachments"
                                                    data-feedback-modal
                                                />
                                                <div className="flex flex-wrap gap-2 mt-2 items-center">
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">{feedbackAttachments.length}/5 files attached</span>
                                                    {feedbackAttachments.map((file, idx) => {
                                                        // Helper: get file extension
                                                        const ext = file.name.split('.').pop()?.toLowerCase();
                                                        // Helper: icon for document/video
                                                        let icon = null;
                                                        if (file.type.startsWith('application/pdf') || ext === 'pdf') {
                                                            icon = (
                                                                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <rect x="4" y="4" width="16" height="16" rx="2" fill="#fff"/>
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 8h8v8H8z" />
                                                                    <text x="12" y="16" textAnchor="middle" fontSize="7" fill="#f87171">PDF</text>
                                                                </svg>
                                                            );
                                                        } else if (
                                                            file.type === 'application/msword' || ext === 'doc' ||
                                                            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || ext === 'docx'
                                                        ) {
                                                            icon = (
                                                                <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <rect x="4" y="4" width="16" height="16" rx="2" fill="#fff"/>
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 8h8v8H8z" />
                                                                    <text x="12" y="16" textAnchor="middle" fontSize="7" fill="#3b82f6">DOC</text>
                                                                </svg>
                                                            );
                                                        } else if (
                                                            file.type.startsWith('video/') || ['mp4','mov','avi'].includes(ext)
                                                        ) {
                                                            icon = (
                                                                <svg className="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <rect x="4" y="4" width="16" height="16" rx="2" fill="#fff"/>
                                                                    <polygon points="10,8 16,12 10,16" fill="#a78bfa" />
                                                                </svg>
                                                            );
                                                        }
                                                        return (
                                                            <div key={idx} className="relative flex items-center border border-gray-200 rounded-lg p-1 bg-gray-50 min-w-[120px] max-w-[180px]">
                                                                {file.type.startsWith('image/') ? (
                                                                    <img
                                                                        src={URL.createObjectURL(file)}
                                                                        alt={file.name}
                                                                        className="w-10 h-10 object-cover rounded mr-2"
                                                                    />
                                                                ) : (
                                                                    <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded mr-2">
                                                                        {icon || <span className="text-xs text-gray-600">{ext?.toUpperCase()}</span>}
                                                                    </div>
                                                                )}
                                                                <div className="flex flex-col min-w-0">
                                                                    <span className="text-xs truncate max-w-[80px] font-medium">{file.name}</span>
                                                                    <span className="text-[10px] text-gray-400">{file.type.split('/')[0].toUpperCase()}</span>
                                                                </div>
                                                                <button
                                                                    type="button"
                                                                    onClick={() => setFeedbackAttachments(prev => prev.filter((_, i) => i !== idx))}
                                                                    className="ml-2 p-1 bg-white/80 rounded hover:bg-red-100"
                                                                    aria-label="Remove attachment"
                                                                    data-feedback-modal
                                                                >
                                                                    <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                        </div>
                                        {/* Error Message */}
                                        <AnimatePresence>
                                            {feedbackError && (
                                                <motion.div
                                                    initial={{ opacity: 0, y: -10 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    exit={{ opacity: 0, y: -10 }}
                                                    className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center gap-3 mb-2"
                                                    data-feedback-modal
                                                >
                                                    <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <span className="text-red-800 font-medium">{feedbackError}</span>
                                                </motion.div>
                                            )}
                                        </AnimatePresence>
                                        {/* Show file error message below the input if present */}
                                        {feedbackFileError && (
                                            <div className="text-xs text-red-600 mt-1">{feedbackFileError}</div>
                                        )}
                                        {/* Submit Button */}
                                        <motion.button
                                            type="submit"
                                            disabled={
                                                isSubmittingFeedback ||
                                                !feedbackTopic ||
                                                !feedbackDescription.trim() ||
                                                feedbackAttachments.length > 5 ||
                                                !!feedbackFileError
                                            }
                                            className="w-full py-3 rounded-xl font-semibold text-lg bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                            whileHover={!isSubmittingFeedback ? { scale: 1.03 } : {}}
                                            whileTap={!isSubmittingFeedback ? { scale: 0.97 } : {}}
                                            aria-label="Submit feedback"
                                            data-feedback-modal
                                        >
                                            {isSubmittingFeedback ? (
                                                <span className="flex items-center justify-center">
                                                    <svg className="w-5 h-5 animate-spin mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                    </svg>
                                                    Submitting...
                                                </span>
                                            ) : (
                                                'Submit Feedback'
                                            )}
                                        </motion.button>
                                    </form>
                                )}
                            </div>
                        </motion.div>
                    </motion.div>
                );
            default:
                return null;
        }
    };

    // Fetch feedback topics when Feedback modal opens
    useEffect(() => {
        if (activeModal === 'feedback') {
            setIsLoadingFeedbackTopics(true);
            setFeedbackTopics([]);
            setFeedbackTopic('');
            api.get('/feedback/topics')
                .then(res => {
                    if (Array.isArray(res.data)) {
                        setFeedbackTopics(res.data);
                    } else if (Array.isArray(res.data?.topics)) {
                        setFeedbackTopics(res.data.topics);
                    } else {
                        setFeedbackTopics([]);
                    }
                })
                .catch(err => {
                    setFeedbackTopics([]);
                })
                .finally(() => setIsLoadingFeedbackTopics(false));
        }
    }, [activeModal]);

    return (
        <motion.div
            className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center overflow-y-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
        >
            <motion.div
                className="bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-950 rounded-2xl shadow-2xl w-full max-w-2xl m-4 border border-gray-100 dark:border-gray-700"
                initial={{ scale: 0.95, opacity: 0, y: 20 }}
                animate={{ scale: 1, opacity: 1, y: 0 }}
                exit={{ scale: 0.95, opacity: 0, y: 20 }}
            >
                <div className="p-6 text-gray-800 dark:text-gray-200">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-6 border-b border-gray-100 pb-4 dark:border-gray-700">
                        <div className="flex items-center space-x-3">
                            <div className=" bg-indigo-100 dark:bg-indigo-800 p-2 rounded-lg">
                                <svg className="w-6 h-6 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{t('title')}</h1>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-red-100 dark:hover:bg-red-900 bg-white dark:bg-gray-900 rounded-full transition-all duration-200 hover:rotate-90"
                        >
                            <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div className="max-h-[70vh] overflow-y-auto pr-2 space-y-6">
                        {/* Account Settings */}
                        <div>
                            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span className="text-indigo-600 dark:text-indigo-100">{t('account.title')}</span>
                            </h2>
                            <div className="space-y-2">
                                <button
                                    onClick={() => handleModalOpen('password')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.password.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>

                                <button
                                    onClick={() => handleModalOpen('language')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.language.title')}</h3>
                                        </div>
                                        <div className="flex items-center space-x-3">
                                            <span className="text-sm text-gray-500 dark:text-gray-400">
                                                {supportedLanguages.find(lang => lang.code === currentLanguage)?.nativeName || currentLanguage}
                                            </span>
                                            <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => handleModalOpen('emergencyContact')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2 8.5V21h19V8.5M12 12v6m0 0l-3-3m3 3l3-3M5.5 8.5V5a2.5 2.5 0 015 0v3.5M13.5 8.5V5a2.5 2.5 0 015 0v3.5" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.emergencyContact.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>

                                <button
                                    onClick={() => handleModalOpen('3rdPartyAccess')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.thirdParty.title')}</h3>
                                        </div>
                                        <div className="flex items-center space-x-3">
                                            <span className={`text-sm ${allow3rdPartyAccess ? 'text-green-500' : 'text-gray-500 dark:text-gray-400'} font-medium`}>
                                                {allow3rdPartyAccess ? t('account.thirdParty.enabled') : t('account.thirdParty.disabled')}
                                            </span>
                                            <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => {
                                        handleModalOpen('kyc');
                                        // Reload KYC status when opening modal
                                        loadKycStatus();
                                    }}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.kyc.title')}</h3>
                                        </div>
                                        <div className="flex items-center space-x-3">
                                            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs border ${getStatusColor(kycStatus)}`}>
                                                {getStatusIcon(kycStatus)}
                                                <span className="font-medium">{getStatusText(kycStatus)}</span>
                                            </div>
                                            <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => handleModalOpen('emailVerification')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('account.emailVerification.title')}</h3>
                                                {user?.email && (
                                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{user.email}</p>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-3">
                                            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs border ${
                                                isVerified
                                                    ? 'bg-green-50 text-green-700 border-green-200'
                                                    : 'bg-gray-50 text-gray-600 border-gray-200'
                                            }`}>
                                                {isVerified ? (
                                                    <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                ) : (
                                                    <svg className="w-3 h-3 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                )}
                                                <span className="font-medium">
                                                    {isVerified ? t('account.emailVerification.verified') : t('account.emailVerification.notVerified')}
                                                </span>
                                            </div>
                                            <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </button>

                                {/* Add similar enhanced styling for other account settings buttons */}
                            </div>
                        </div>

                        {/* General Settings */}
                        <div>
                            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span className="text-gray-900 dark:text-indigo-100">{t('general.title')}</span>
                            </h2>
                            <div className="space-y-2">
                                {/* Add similar enhanced styling for general settings buttons */}
                                <button
                                    onClick={() => handleModalOpen('aboutUs')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.aboutUs.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                                <button
                                    onClick={() => handleModalOpen('termsConditions')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.termsConditions.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                                <button
                                    onClick={() => handleModalOpen('privacyPolicy')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 7l-2.286 6.857L12 21l-2.286-6.857L3 7l2.286-6.857L12 3z" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.privacyPolicy.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                                <button
                                    onClick={() => handleModalOpen('contactUs')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8m0 0l-5.65 3.76a2 2 0 01-1.79 0L3 8m0 0V6a2 2 0 012-2h14a2 2 0 012 2v2m0 0v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-2" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.contactUs.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                                <button
                                    onClick={() => handleModalOpen('feedback')}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-700 transition-colors">
                                                <svg className="w-5 h-5 text-indigo-600 dark:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 8h10M7 12h4m-2 8a8 8 0 100-16 8 8 0 000 16zm0 0v-4m0 0h4" />
                                                </svg>
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.feedback.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                                <button
                                    onClick={() => setShowLogoutModal(true)}
                                    className="w-full text-left px-4 py-3 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-red-300 dark:hover:border-red-400 hover:bg-red-50 dark:hover:bg-gray-700 transition-all duration-200 group"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-3">
                                            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg group-hover:bg-red-200 transition-colors">
                                                <FiLogOut className="w-5 h-5 text-red-600 dark:text-red-100" />
                                            </div>
                                            <h3 className="font-medium text-gray-900 dark:text-gray-100">{t('general.logout.title')}</h3>
                                        </div>
                                        <svg className="w-5 h-5 text-gray-400 group-hover:text-red-600 dark:text-red-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </div>
                                </button>
                            </div>
                        </div>

                        {/* Version */}
                        <div className="text-center text-gray-400 text-sm">
                            Version 1.0.0
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* Render active modal */}
            <AnimatePresence>
                {activeModal && renderModalContent()}
            </AnimatePresence>

            <LogoutConfirmation
                isOpen={showLogoutModal}
                onClose={() => setShowLogoutModal(false)}
                onLogout={handleLogout}
            />
        </motion.div>
    );
};

export default Settings;
