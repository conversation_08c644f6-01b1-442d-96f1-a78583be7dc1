import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const CreditPackageCard = ({ 
  package: creditPackage, 
  isSelected, 
  onSelect,
  isLoading = false,
  disabled = false
}) => {
  // Helper function to format price
  const formatPrice = (price) => {
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    return typeof numericPrice === 'number' && !isNaN(numericPrice) 
      ? numericPrice.toFixed(2) 
      : '0.00';
  };

  return (
    <motion.button
      type="button"
      tabIndex={disabled ? -1 : 0}
      aria-pressed={isSelected}
      disabled={disabled}
      whileHover={!disabled ? { scale: 1.03, boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)' } : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      onClick={() => !disabled && onSelect(creditPackage)}
      className={`
        group relative w-full text-left cursor-pointer rounded-2xl border-2 px-6 py-5 transition-all duration-300
        bg-gradient-to-br from-white/90 via-indigo-50 to-blue-100/80 dark:from-gray-900/90 dark:via-gray-900 dark:to-blue-900/60 backdrop-blur-xl shadow-xl
        focus:outline-none focus-visible:ring-4 focus-visible:ring-indigo-300
        ${isSelected 
          ? 'border-indigo-600 bg-gradient-to-br from-indigo-100 via-indigo-50 to-blue-200 dark:from-indigo-900 dark:via-indigo-900 dark:to-blue-900 ring-2 ring-indigo-400/30'
          : 'border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-500'}
        ${disabled ? 'opacity-60 grayscale cursor-not-allowed' : ''}
        min-h-[140px]
      `}
      style={{ minHeight: 140 }}
    >
      {/* Loading Spinner Overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-10 flex items-center justify-center bg-white/70 dark:bg-gray-900/80 backdrop-blur rounded-2xl"
          >
            <svg className="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selection Checkmark */}
      <AnimatePresence>
        {isSelected && (
          <motion.div
            initial={{ scale: 0, opacity: 0, rotate: -90 }}
            animate={{ scale: 1, opacity: 1, rotate: 0 }}
            exit={{ scale: 0, opacity: 0, rotate: 90 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
            className="absolute top-2 right-2 w-7 h-7 bg-indigo-500 rounded-full flex items-center justify-center shadow-lg border-2 border-white dark:border-gray-900 z-20"
          >
            <svg className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected badge */}
      {isSelected && (
        <span className="absolute top-2 left-2 z-10 bg-indigo-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-md">Selected</span>
      )}

      <div className="flex flex-col h-full justify-between gap-2">
        <div>
          <span className="text-2xl font-extrabold text-indigo-700 dark:text-indigo-300">
            {creditPackage.credits.toLocaleString()} <span className="text-base font-semibold text-indigo-400 dark:text-indigo-200">Credits</span>
          </span>
          {creditPackage.description && (
            <span className="block text-xs text-indigo-500 dark:text-indigo-300 font-medium leading-tight truncate max-w-full mt-1">
              {creditPackage.description}
            </span>
          )}
        </div>
        <div className="flex items-center justify-between mt-3">
          <span className="text-lg font-bold text-gray-900 dark:text-gray-100 bg-indigo-100/60 dark:bg-indigo-900/60 px-3 py-1 rounded-lg shadow-sm">
            {creditPackage.currency_code} {formatPrice(creditPackage.price)}
          </span>
          <div className="flex items-center gap-2">
            <img src="/Billplz_Blue.svg" alt="Billplz" className="h-6 w-auto" />
          </div>
        </div>
      </div>
    </motion.button>
  );
};

export default CreditPackageCard; 