import React from 'react';
import { motion } from 'framer-motion';

const GiftTransactionHistory = ({ transactions }) => {
  // Format date with time
  const formatDateTime = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleString(undefined, options);
  };

  // Get icon for transaction type
  const getTransactionIcon = (type) => {
    switch (type) {
      case 'purchase':
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      case 'redeem':
        return (
          <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'gift_sent':
        return (
          <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
          </svg>
        );
      case 'gift_received':
        return (
          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
          </svg>
        );
      case 'sell':
        return (
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
          </svg>
        );
    }
  };

  // Format transaction type for display
  const formatTransactionType = (type) => {
    switch (type) {
      case 'purchase':
        return 'Purchased';
      case 'redeem':
        return 'Redeemed with Points';
      case 'gift_sent':
        return 'Sent as Gift';
      case 'gift_received':
        return 'Received as Gift';
      case 'sell':
        return 'Sold';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Get background color based on transaction type
  const getTransactionBackgroundColor = (type) => {
    switch (type) {
      case 'purchase':
        return 'bg-green-50';
      case 'redeem':
        return 'bg-purple-50';
      case 'gift_sent':
        return 'bg-indigo-50';
      case 'gift_received':
        return 'bg-blue-50';
      case 'sell':
        return 'bg-red-50';
      default:
        return 'bg-gray-50';
    }
  };

  // If no transactions are available
  if (!transactions || transactions.length === 0) {
    return (
      <div className="text-center py-10">
        <svg 
          className="mx-auto h-12 w-12 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth="1" 
            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" 
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No transaction history</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have any gift-related transactions yet.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Transaction History</h2>
        <p className="text-sm text-gray-500">
          Your gift purchase, redemption, and transfer history.
        </p>
      </div>

      <div className="space-y-4">
        {transactions.map((transaction) => (
          <motion.div
            key={transaction.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg ${getTransactionBackgroundColor(transaction.transaction_type)} hover:shadow-md transition-shadow`}
          >
            <div className="flex items-center space-x-3">
              <div className={`flex-shrink-0 rounded-full p-2 ${
                transaction.transaction_type === 'purchase' 
                  ? 'bg-green-100' 
                  : transaction.transaction_type === 'redeem'
                    ? 'bg-purple-100'
                    : transaction.transaction_type === 'gift_sent'
                      ? 'bg-indigo-100'
                      : transaction.transaction_type === 'gift_received'
                        ? 'bg-blue-100'
                        : transaction.transaction_type === 'sell'
                          ? 'bg-red-100'
                          : 'bg-gray-100'
              }`}>
                {getTransactionIcon(transaction.transaction_type)}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-start">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {formatTransactionType(transaction.transaction_type)} {transaction.gift_item.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatDateTime(transaction.created_at)}
                  </p>
                </div>

                <div className="mt-1 flex items-center">
                  <div className="flex items-center">
                    {transaction.gift_item.icon_path && (
                      <img 
                        src={transaction.gift_item.icon_path.startsWith('http') 
                          ? transaction.gift_item.icon_path 
                          : `${process.env.REACT_APP_API_URL}/${transaction.gift_item.icon_path}`} 
                        alt={transaction.gift_item.name} 
                        className="w-5 h-5 mr-2" 
                      />
                    )}
                    <p className="text-sm text-gray-500 mr-2">
                      Quantity: {transaction.quantity}
                    </p>
                  </div>

                  <div className="flex-1"></div>

                  {transaction.payment_method && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      transaction.payment_method === 'credits' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {transaction.payment_method === 'credits' ? 'Credits' : 'Points'}
                    </span>
                  )}
                </div>

                {/* Detailed information */}
                <div className="mt-2 text-xs text-gray-500">
                  {transaction.transaction_type === 'purchase' && transaction.credits_spent && (
                    <p>Cost: {transaction.credits_spent} credits</p>
                  )}
                  {transaction.transaction_type === 'redeem' && transaction.points_spent && (
                    <p>Cost: {transaction.points_spent} points</p>
                  )}
                  {transaction.transaction_type === 'sell' && transaction.metadata?.credits_balance_after && (
                    <p>Balance After: {transaction.metadata.credits_balance_after} credits</p>
                  )}
                  {transaction.transaction_type === 'gift_sent' && transaction.metadata?.recipient_name && (
                    <p>To: {transaction.metadata.recipient_name}</p>
                  )}
                  {transaction.transaction_type === 'gift_received' && transaction.metadata?.sender_name && (
                    <p>From: {transaction.metadata.sender_name}</p>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default GiftTransactionHistory; 