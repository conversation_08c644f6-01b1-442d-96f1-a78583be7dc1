import React from 'react';
import PaymentRetryButton from './PaymentRetryButton';

/**
 * PaymentStatusCard component
 * 
 * Displays the status of a payment and provides retry options for failed payments
 * 
 * @param {Object} props
 * @param {Object} props.payment - The payment object
 * @param {Function} props.onRetrySuccess - Function to call when payment retry is successful
 * @param {Function} props.onViewDetails - Function to call when the user clicks "View Details"
 */
const PaymentStatusCard = ({ payment, onRetrySuccess, onViewDetails }) => {
  if (!payment) return null;
  
  // Determine payment status and styling
  const getStatusConfig = (status) => {
    switch (status) {
      case 'completed':
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          icon: (
            <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          ),
          title: 'Payment Successful',
          description: 'Your payment has been processed successfully.'
        };
      case 'pending':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          icon: (
            <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          title: 'Payment Pending',
          description: 'Your payment is being processed. This may take a few moments.'
        };
      case 'failed':
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          icon: (
            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          title: 'Payment Failed',
          description: 'We encountered an issue processing your payment. Please try again.'
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          icon: (
            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          title: 'Payment Status',
          description: 'Current status of your payment.'
        };
    }
  };
  
  const statusConfig = getStatusConfig(payment.status);
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  return (
    <div className={`${statusConfig.bgColor} border ${statusConfig.borderColor} rounded-lg p-4 mb-4`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          {statusConfig.icon}
        </div>
        
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-medium ${statusConfig.textColor}`}>
            {statusConfig.title}
          </h3>
          
          <div className="mt-2 text-sm">
            <p className={statusConfig.textColor}>{statusConfig.description}</p>
            
            {payment.transaction_id && (
              <p className="mt-1 text-xs text-gray-600">
                Transaction ID: {payment.transaction_id}
              </p>
            )}
            
            {payment.created_at && (
              <p className="mt-1 text-xs text-gray-600">
                Date: {formatDate(payment.created_at)}
              </p>
            )}
            
            {payment.amount && (
              <p className="mt-1 text-xs text-gray-600">
                Amount: {payment.amount} {payment.currency || 'Credits'}
              </p>
            )}
            
            {(payment.status === 'failed' || payment.status === 'error') && payment.failed_attempts && (
              <p className="mt-1 text-xs text-gray-600">
                Failed attempts: {payment.failed_attempts}
              </p>
            )}
          </div>
          
          <div className="mt-4 flex space-x-3">
            {(payment.status === 'failed' || payment.status === 'error') && (
              <PaymentRetryButton
                transactionId={payment.transaction_id}
                buttonText="Retry Payment"
                buttonClassName="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                onSuccess={onRetrySuccess}
              />
            )}
            
            {onViewDetails && (
              <button
                type="button"
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                onClick={() => onViewDetails(payment)}
              >
                View Details
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentStatusCard;
