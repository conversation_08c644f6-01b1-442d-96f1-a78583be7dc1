import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const TransactionFilter = ({ 
  filters, 
  setFilters, 
  onApplyFilters,
  isLoading = false
}) => {
  // Local state for validation
  const [dateError, setDateError] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Track if changes have been made that need to be applied
  useEffect(() => {
    setHasChanges(true);
  }, [filters]);

  // Handle input changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    
    // Clear date error when either date field changes
    if (name === 'startDate' || name === 'endDate') {
      setDateError('');
    }
    
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Handle filter reset
  const handleReset = () => {
    const emptyFilters = {
      type: '',
      startDate: '',
      endDate: ''
    };
    
    setFilters(emptyFilters);
    onApplyFilters(emptyFilters);
    setHasChanges(false);
    setDateError('');
  };

  // Validate date range before applying filters
  const handleApply = () => {
    // Check if end date is before start date
    if (filters.startDate && filters.endDate) {
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);
      
      if (endDate < startDate) {
        setDateError('End date cannot be before start date');
        return;
      }
    }
    
    onApplyFilters(filters);
    setHasChanges(false);
  };

  // Calculate max date (today) for date inputs
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100">
      <div className="border-b border-gray-200 p-4">
        <h3 className="text-lg font-medium text-gray-900">Filter Transactions</h3>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Transaction Type Filter */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Transaction Type
            </label>
            <select
              id="type"
              name="type"
              value={filters.type}
              onChange={handleFilterChange}
              disabled={isLoading}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">All Transactions</option>
              <option value="credit-in">Credits In</option>
              <option value="credit-out">Credits Out</option>
            </select>
          </div>
          
          {/* Date Range Filters */}
          <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Start Date Filter */}
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                max={filters.endDate || today}
                value={filters.startDate}
                onChange={handleFilterChange}
                disabled={isLoading}
                className="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            {/* End Date Filter */}
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                min={filters.startDate}
                max={today}
                value={filters.endDate}
                onChange={handleFilterChange}
                disabled={isLoading}
                className="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
        
        {/* Date validation error message */}
        {dateError && (
          <div className="mt-2 text-sm text-red-600">
            <span className="font-medium">Error:</span> {dateError}
          </div>
        )}
        
        {/* Quick date selectors */}
        <div className="mt-4 flex flex-wrap gap-2">
          <button
            type="button"
            onClick={() => {
              // Set date range to last 7 days
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - 7);
              
              const newFilters = {
                ...filters,
                startDate: start.toISOString().split('T')[0],
                endDate: end.toISOString().split('T')[0]
              };
              
              setFilters(newFilters);
            }}
            disabled={isLoading}
            className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            Last 7 days
          </button>
          
          <button
            type="button"
            onClick={() => {
              // Set date range to last 30 days
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - 30);
              
              const newFilters = {
                ...filters,
                startDate: start.toISOString().split('T')[0],
                endDate: end.toISOString().split('T')[0]
              };
              
              setFilters(newFilters);
            }}
            disabled={isLoading}
            className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            Last 30 days
          </button>
          
          <button
            type="button"
            onClick={() => {
              // Set date range to current month
              const now = new Date();
              const start = new Date(now.getFullYear(), now.getMonth(), 1);
              
              const newFilters = {
                ...filters,
                startDate: start.toISOString().split('T')[0],
                endDate: now.toISOString().split('T')[0]
              };
              
              setFilters(newFilters);
            }}
            disabled={isLoading}
            className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            This month
          </button>
        </div>
        
        {/* Filter Actions */}
        <div className="mt-5 flex justify-end space-x-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleReset}
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
          >
            Reset
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleApply}
            disabled={isLoading || !hasChanges}
            className={`inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${
              !hasChanges ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'
            } focus:outline-none`}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Applying...
              </>
            ) : (
              <>
                <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                Apply Filters
              </>
            )}
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default TransactionFilter; 