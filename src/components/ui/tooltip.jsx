import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { cn } from "../../lib/utils";

const Tooltip = ({ children, content, className }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef(null);

  useEffect(() => {
    if (isVisible && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.top - 30, // Position above the element
        left: rect.left + (rect.width / 2) // Center horizontally
      });
    }
  }, [isVisible]);

  return (
    <div
      ref={triggerRef}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
      className="inline-block"
    >
      {children}
      {isVisible && createPortal(
        <div
          className={cn(
            "fixed z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-lg transform -translate-x-1/2",
            "animate-in fade-in-0 zoom-in-95 duration-100",
            className
          )}
          style={{
            top: `${position.top}px`,
            left: `${position.left}px`
          }}
        >
          {content}
        </div>,
        document.body
      )}
    </div>
  );
};

export { Tooltip }; 