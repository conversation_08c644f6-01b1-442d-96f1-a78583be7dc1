import React, { createContext, useContext, useState } from "react";
import { cn } from "../../lib/utils";

const TabsContext = createContext({
  selectedTab: "",
  setSelectedTab: () => {},
});

export function Tabs({ children, defaultValue, className, ...props }) {
  const [selectedTab, setSelectedTab] = useState(defaultValue);

  return (
    <TabsContext.Provider value={{ selectedTab, setSelectedTab }}>
      <div className={cn("w-full", className)} {...props}>
        {children}
      </div>
    </TabsContext.Provider>
  );
}

export function TabsList({ children, className, ...props }) {
  return (
    <div
      className={cn(
        "flex flex-wrap items-center gap-2 bg-gray-100/30 p-1 rounded-md",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function TabsTrigger({ children, value, className, ...props }) {
  const { selectedTab, setSelectedTab } = useContext(TabsContext);
  const isActive = selectedTab === value;

  return (
    <button
      className={cn(
        "px-3 py-1.5 text-sm font-medium rounded-md transition-all",
        isActive
          ? "bg-white text-indigo-700 shadow-sm"
          : "text-gray-600 hover:text-gray-900 hover:bg-white/50",
        className
      )}
      onClick={() => setSelectedTab(value)}
      data-state={isActive ? "active" : "inactive"}
      {...props}
    >
      {children}
    </button>
  );
}

export function TabsContent({ children, value, className, ...props }) {
  const { selectedTab } = useContext(TabsContext);
  const isActive = selectedTab === value;

  if (!isActive) return null;

  return (
    <div
      className={cn("mt-2", className)}
      data-state={isActive ? "active" : "inactive"}
      {...props}
    >
      {children}
    </div>
  );
} 