/**
 * Animation Wrapper Component
 * Provides consistent animations across the authentication system
 */

import React from 'react';
import { motion } from 'framer-motion';
import { getMotionSettings, getAnimationDuration } from '../../utils/responsiveUtils';

/**
 * AnimationWrapper Component
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.variant - Animation variant
 * @param {number} props.delay - Animation delay
 * @param {Object} props.customAnimation - Custom animation overrides
 * @param {string} props.className - Additional CSS classes
 */
const AnimationWrapper = ({
  children,
  variant = 'fadeInUp',
  delay = 0,
  customAnimation = {},
  className = '',
  ...motionProps
}) => {
  const defaultSettings = getMotionSettings();
  const duration = getAnimationDuration() / 1000; // Convert to seconds

  // Animation variants
  const variants = {
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { duration, delay }
    },
    fadeInUp: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration, delay, ease: "easeOut" }
    },
    fadeInDown: {
      initial: { opacity: 0, y: -20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration, delay, ease: "easeOut" }
    },
    fadeInLeft: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      transition: { duration, delay, ease: "easeOut" }
    },
    fadeInRight: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      transition: { duration, delay, ease: "easeOut" }
    },
    scaleIn: {
      initial: { opacity: 0, scale: 0.8 },
      animate: { opacity: 1, scale: 1 },
      transition: { duration, delay, ease: "easeOut" }
    },
    slideInUp: {
      initial: { y: 50, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      transition: { duration, delay, ease: "easeOut" }
    },
    slideInDown: {
      initial: { y: -50, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      transition: { duration, delay, ease: "easeOut" }
    },
    bounceIn: {
      initial: { opacity: 0, scale: 0.3 },
      animate: { opacity: 1, scale: 1 },
      transition: { 
        duration: duration * 1.5, 
        delay,
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    staggerChild: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration, ease: "easeOut" }
    }
  };

  // Get animation settings
  const animationSettings = customAnimation.initial ? customAnimation : variants[variant] || variants.fadeInUp;

  return (
    <motion.div
      className={className}
      {...animationSettings}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

/**
 * Stagger Container Component
 * Animates children with staggered delays
 */
export const StaggerContainer = ({
  children,
  staggerDelay = 0.1,
  className = '',
  ...props
}) => {
  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: staggerDelay
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={containerVariants}
      initial="initial"
      animate="animate"
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <AnimationWrapper
          key={index}
          variant="staggerChild"
          delay={0}
        >
          {child}
        </AnimationWrapper>
      ))}
    </motion.div>
  );
};

/**
 * Page Transition Component
 * Handles page-level transitions
 */
export const PageTransition = ({
  children,
  className = '',
  ...props
}) => {
  const pageVariants = {
    initial: { 
      opacity: 0,
      scale: 0.98,
      y: 20
    },
    animate: { 
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0,
      scale: 0.98,
      y: -20,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Floating Animation Component
 * Creates floating/breathing animation
 */
export const FloatingAnimation = ({
  children,
  intensity = 10,
  duration = 3,
  className = '',
  ...props
}) => {
  const floatingVariants = {
    animate: {
      y: [-intensity, intensity, -intensity],
      transition: {
        duration,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={floatingVariants}
      animate="animate"
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Pulse Animation Component
 * Creates pulsing animation
 */
export const PulseAnimation = ({
  children,
  scale = 1.05,
  duration = 2,
  className = '',
  ...props
}) => {
  const pulseVariants = {
    animate: {
      scale: [1, scale, 1],
      transition: {
        duration,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={pulseVariants}
      animate="animate"
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Hover Animation Component
 * Adds hover animations
 */
export const HoverAnimation = ({
  children,
  hoverScale = 1.05,
  tapScale = 0.95,
  className = '',
  ...props
}) => {
  return (
    <motion.div
      className={className}
      whileHover={{ scale: hoverScale }}
      whileTap={{ scale: tapScale }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Loading Animation Component
 * Creates loading spinner animation
 */
export const LoadingAnimation = ({
  size = 'default',
  color = 'indigo',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    default: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    indigo: 'text-indigo-600',
    white: 'text-white',
    gray: 'text-gray-600'
  };

  return (
    <motion.div
      className={`${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <svg
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </motion.div>
  );
};

export default AnimationWrapper;
