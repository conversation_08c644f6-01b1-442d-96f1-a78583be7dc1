import React from "react";
import { cn } from "../../lib/utils";

export const Progress = ({ 
  value = 0, 
  max = 100,
  className,
  ...props 
}) => {
  return (
    <div
      className={cn("w-full bg-gray-200 rounded-full overflow-hidden", className)}
      {...props}
    >
      <div
        className="bg-indigo-600 h-full transition-all duration-300"
        style={{ width: `${Math.min(Math.max(0, value), max)}%` }}
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
      />
    </div>
  );
}; 