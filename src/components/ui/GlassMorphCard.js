/**
 * Glass Morphism Card Component
 * Reusable glass-morphism container with consistent styling from Profile.js
 */

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

/**
 * GlassMorphCard Component
 * @param {Object} props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.animation - Framer Motion animation props
 * @param {boolean} props.hover - Enable hover animations
 * @param {string} props.size - Card size variant
 * @param {boolean} props.withDecorations - Include animated background decorations
 * @param {Object} props.decorationColors - Custom decoration colors
 * @param {Object} props.motionProps - Additional Framer Motion props
 */
const GlassMorphCard = ({
  children,
  className = '',
  animation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  },
  hover = true,
  size = 'default',
  withDecorations = true,
  decorationColors = {
    primary: 'from-indigo-400/20 to-purple-400/20',
    secondary: 'from-blue-400/20 to-indigo-400/20'
  },
  ...motionProps
}) => {
  // Size variants
  const sizeClasses = {
    sm: 'p-4 rounded-2xl',
    default: 'p-6 rounded-3xl',
    lg: 'p-8 rounded-3xl',
    xl: 'p-10 rounded-3xl'
  };

  // Hover animation variants
  const hoverAnimation = hover ? {
    whileHover: { y: -5, scale: 1.01 },
    transition: { type: "spring", stiffness: 300, damping: 20 }
  } : {};

  return (
    <motion.div
      className={cn(
        // Base glass-morphism styles
        'relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/80 backdrop-blur-2xl border border-white/30 dark:border-gray-700 shadow-2xl dark:shadow-indigo-900/30 overflow-hidden',
        // Size variant
        sizeClasses[size],
        // Custom classes
        className
      )}
      {...animation}
      {...hoverAnimation}
      {...motionProps}
    >
      {/* Animated background decorations */}
      {withDecorations && (
        <>
          <div className={cn(
            'absolute -top-10 -right-10 w-32 h-32 rounded-full blur-2xl animate-pulse',
            `bg-gradient-to-br ${decorationColors.primary}`
          )} />
          <div className={cn(
            'absolute -bottom-10 -left-10 w-24 h-24 rounded-full blur-xl animate-pulse delay-1000',
            `bg-gradient-to-br ${decorationColors.secondary}`
          )} />
        </>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

/**
 * GlassMorphCard variants for specific use cases
 */

// Authentication card variant
export const AuthCard = ({ children, ...props }) => (
  <GlassMorphCard
    size="lg"
    decorationColors={{
      primary: 'from-indigo-500/20 to-purple-500/20',
      secondary: 'from-blue-500/20 to-cyan-500/20'
    }}
    animation={{
      initial: { opacity: 0, scale: 0.95, y: 20 },
      animate: { opacity: 1, scale: 1, y: 0 },
      transition: { duration: 0.5, ease: "easeOut" }
    }}
    {...props}
  >
    {children}
  </GlassMorphCard>
);

// Form step card variant
export const StepCard = ({ children, isActive = false, ...props }) => (
  <GlassMorphCard
    size="default"
    decorationColors={{
      primary: isActive ? 'from-green-400/20 to-emerald-400/20' : 'from-gray-400/10 to-gray-500/10',
      secondary: isActive ? 'from-blue-400/20 to-green-400/20' : 'from-gray-300/10 to-gray-400/10'
    }}
    className={cn(
      'transition-all duration-300',
      isActive ? 'ring-2 ring-indigo-500/30' : 'opacity-75'
    )}
    hover={isActive}
    {...props}
  >
    {children}
  </GlassMorphCard>
);

// Success/Error message card variant
export const MessageCard = ({ children, type = 'info', ...props }) => {
  const typeColors = {
    success: {
      primary: 'from-green-400/20 to-emerald-400/20',
      secondary: 'from-emerald-400/20 to-green-400/20'
    },
    error: {
      primary: 'from-red-400/20 to-rose-400/20',
      secondary: 'from-rose-400/20 to-red-400/20'
    },
    warning: {
      primary: 'from-yellow-400/20 to-orange-400/20',
      secondary: 'from-orange-400/20 to-yellow-400/20'
    },
    info: {
      primary: 'from-blue-400/20 to-indigo-400/20',
      secondary: 'from-indigo-400/20 to-blue-400/20'
    }
  };

  return (
    <GlassMorphCard
      size="sm"
      decorationColors={typeColors[type]}
      animation={{
        initial: { opacity: 0, x: -20 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: 20 },
        transition: { duration: 0.3 }
      }}
      {...props}
    >
      {children}
    </GlassMorphCard>
  );
};

// Loading card variant
export const LoadingCard = ({ children, ...props }) => (
  <GlassMorphCard
    decorationColors={{
      primary: 'from-indigo-400/10 to-purple-400/10',
      secondary: 'from-blue-400/10 to-indigo-400/10'
    }}
    animation={{
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { duration: 0.3 }
    }}
    hover={false}
    {...props}
  >
    <div className="animate-pulse">
      {children}
    </div>
  </GlassMorphCard>
);

export default GlassMorphCard;
