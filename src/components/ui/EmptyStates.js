/**
 * Empty State Components
 * 
 * These components are used to display empty states when data is not available
 * or when API calls fail.
 */

import React from 'react';

/**
 * Empty state for popular games section
 */
export const EmptyGamesState = ({ onExplore }) => {
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-indigo-100/50 text-center">
      <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">No Games Available</h3>
      <p className="text-gray-600 mb-4">We couldn't load the popular games right now. Please try again later.</p>
      {onExplore && (
        <button 
          onClick={onExplore}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          Explore Games
        </button>
      )}
    </div>
  );
};

/**
 * Empty state for talents section (recommended, new, online)
 */
export const EmptyTalentsState = ({ type = "talents", onExplore }) => {
  const titles = {
    recommended: "No Recommended Talents",
    new: "No New Talents",
    online: "No Online Talents",
    talents: "No Talents Available"
  };
  
  const messages = {
    recommended: "We couldn't load recommended talents right now.",
    new: "We couldn't load new talents right now.",
    online: "We couldn't load online talents right now.",
    talents: "We couldn't load talents right now."
  };
  
  const icons = {
    recommended: (
      <svg className="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    new: (
      <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
      </svg>
    ),
    online: (
      <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
      </svg>
    ),
    talents: (
      <svg className="w-8 h-8 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
    )
  };
  
  const colors = {
    recommended: "bg-orange-100",
    new: "bg-green-100",
    online: "bg-blue-100",
    talents: "bg-indigo-100"
  };
  
  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-indigo-100/50 text-center">
      <div className={`w-16 h-16 ${colors[type]} rounded-full flex items-center justify-center mx-auto mb-4`}>
        {icons[type]}
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">{titles[type]}</h3>
      <p className="text-gray-600 mb-4">{messages[type]} Please try again later.</p>
      {onExplore && (
        <button 
          onClick={onExplore}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          Explore Talents
        </button>
      )}
    </div>
  );
};

/**
 * Empty state for carousel/hero banner
 */
export const EmptyCarouselState = ({ onAction }) => {
  return (
    <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl overflow-hidden mb-6 relative h-[300px] md:h-[400px]">
      <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
      <div className="p-8 flex flex-col items-center justify-center h-full text-center">
        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">Welcome to Mission X</h2>
        <p className="text-indigo-100 mb-6 max-w-md">Connect with gamers, complete missions, and earn rewards in your favorite games.</p>
        {onAction && (
          <button 
            onClick={onAction}
            className="px-6 py-3 bg-white text-indigo-700 rounded-lg font-medium hover:bg-indigo-50 transition-all shadow-lg"
          >
            Get Started
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Empty state for missions section
 */
export const EmptyMissionsState = ({ onExplore }) => {
  return (
    <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-white shadow-lg relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]"></div>
      <div className="relative z-10 text-center py-8">
        <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        </div>
        <h3 className="text-xl font-bold mb-2">Missions Coming Soon</h3>
        <p className="text-white/80 mb-6 max-w-md mx-auto">We're preparing exciting missions for you. Check back soon or create your own mission!</p>
        {onExplore && (
          <button 
            onClick={onExplore}
            className="px-6 py-3 bg-white text-red-600 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all"
          >
            Create Mission
          </button>
        )}
      </div>
    </div>
  );
};
