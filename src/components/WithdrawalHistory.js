import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import walletAPI from '../services/walletService';
import { formatDate, formatCredits, formatCurrency } from '../utils/formatters';
import WithdrawalDetails from './WithdrawalDetails';

const WithdrawalHistory = ({ initialWithdrawals = [], refreshTrigger = 0 }) => {
  const [withdrawals, setWithdrawals] = useState(initialWithdrawals);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [cancellingId, setCancellingId] = useState(null);

  const fetchWithdrawals = useCallback(async () => {
    // If we already have initialWithdrawals, don't load more
    if (initialWithdrawals && initialWithdrawals.length > 0) {
      setWithdrawals(initialWithdrawals);
      return;
    }

    setLoading(true);
    try {
      const response = await walletAPI.getWithdrawals(10, 0); // limit=10, offset=0
      // Handle different possible response structures
      const withdrawalsData = response.data?.transactions ||
                             response.data?.withdrawals ||
                             response.data ||
                             [];
      setWithdrawals(withdrawalsData);
    } catch (err) {
      console.error('Error fetching withdrawals:', err);
      setError('Failed to load withdrawal history');
    } finally {
      setLoading(false);
    }
  }, [initialWithdrawals]);

  // Fetch withdrawals from API
  useEffect(() => {
    fetchWithdrawals();
  }, [fetchWithdrawals, refreshTrigger]);

  // Show withdrawal details
  const handleWithdrawalClick = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setShowDetailsModal(true);
  };

  // Close details modal
  const handleCloseDetails = () => {
    setShowDetailsModal(false);
    // Wait for animation to complete before clearing data
    setTimeout(() => setSelectedWithdrawal(null), 300);
  };

  // Cancel withdrawal request (disabled - not supported by backend)
  const handleCancelWithdrawal = async (withdrawalId) => {
    alert('Withdrawal cancellation is not currently supported. Please contact support if you need assistance.');
    return;

    // Note: Backend doesn't have cancel withdrawal endpoint
    // This functionality would need to be implemented in the backend first
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm animate-pulse">
        <div className="h-7 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-2xl p-6 shadow-sm">
        <div className="text-red-600 mb-2">Failed to load pending withdrawals</div>
        <button
          onClick={fetchWithdrawals}
          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
        >
          Try again
        </button>
      </div>
    );
  }

  if (!withdrawals || withdrawals.length === 0) {
    return null;
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-2xl p-6 shadow-sm"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-4">Pending Withdrawals</h2>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Date
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Fiat Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Bank
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {withdrawals.map((withdrawal) => {
                const metadata = withdrawal.metadata || {};
                const fiatCurrency = metadata.fiat_currency || 'MYR';
                const fiatAmount = metadata.fiat_amount || 0;
                const bankName = withdrawal.bankAccount?.bank_name || metadata.bank_name || 'Bank Transfer';

                return (
                  <tr
                    key={withdrawal.id}
                    className="hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleWithdrawalClick(withdrawal)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(withdrawal.created_at, 'medium')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                      {formatCredits(Math.abs(withdrawal.credits))}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {formatCurrency(fiatAmount, fiatCurrency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {bankName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {withdrawal.status === 'pending' ? 'Pending' : withdrawal.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-3">
                        <button
                          className="text-indigo-600 hover:text-indigo-900"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleWithdrawalClick(withdrawal);
                          }}
                        >
                          View details
                        </button>
                        <button
                          className={`text-red-600 hover:text-red-900 ${cancellingId === withdrawal.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCancelWithdrawal(withdrawal.id);
                          }}
                          disabled={cancellingId === withdrawal.id}
                        >
                          {cancellingId === withdrawal.id ? 'Cancelling...' : 'Cancel'}
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Withdrawal Details Modal */}
      <AnimatePresence>
        {showDetailsModal && selectedWithdrawal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-60 flex items-center justify-center"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="relative bg-white rounded-xl max-w-lg w-full mx-4 p-6 shadow-xl"
            >
              <button
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-500"
                onClick={handleCloseDetails}
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <WithdrawalDetails
                transaction={selectedWithdrawal}
                onCancel={() => handleCancelWithdrawal(selectedWithdrawal.id)}
                cancellingId={cancellingId}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default WithdrawalHistory;