import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useOrderPayment } from '../contexts/OrderPaymentContext';

/**
 * OrderPaymentHistory component
 * 
 * This component displays the payment history for an order.
 * 
 * @param {Object} props
 * @param {number} props.orderId - The ID of the order
 * @param {string} props.orderType - The type of order ('talent_order', 'mission_order')
 */
const OrderPaymentHistory = ({ orderId, orderType = 'talent_order' }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expanded, setExpanded] = useState(false);
  
  const { getOrderPaymentHistory, orderPaymentState, formatCurrency } = useOrderPayment();
  const { paymentHistory } = orderPaymentState;
  
  // Fetch payment history on mount and when orderId changes
  useEffect(() => {
    if (orderId) {
      fetchPaymentHistory();
    }
  }, [orderId]);
  
  // Fetch payment history
  const fetchPaymentHistory = async () => {
    if (!orderId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await getOrderPaymentHistory(orderId);
    } catch (err) {
      console.error('Error fetching payment history:', err);
      setError('Failed to load payment history');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      success: {
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        label: 'Success'
      },
      completed: {
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        label: 'Completed'
      },
      pending: {
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        label: 'Pending'
      },
      failed: {
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        label: 'Failed'
      },
      refunded: {
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        label: 'Refunded'
      }
    };
    
    const config = statusConfig[status] || {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      label: status
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
        {config.label}
      </span>
    );
  };
  
  // If no orderId, don't render anything
  if (!orderId) return null;
  
  // If loading and no payment history yet
  if (isLoading && paymentHistory.length === 0) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-center py-4">
          <div className="w-6 h-6 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
          <span className="ml-2 text-gray-600">Loading payment history...</span>
        </div>
      </div>
    );
  }
  
  // If error
  if (error) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                type="button"
                onClick={fetchPaymentHistory}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // If no payment history
  if (paymentHistory.length === 0) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-800">No Payment History</h3>
            <div className="mt-2 text-sm text-gray-600">
              <p>There is no payment history for this order yet.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4"
    >
      <div 
        className="p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
        onClick={toggleExpanded}
      >
        <div className="flex items-center">
          <svg 
            className={`h-5 w-5 text-gray-500 mr-2 transform transition-transform ${expanded ? 'rotate-90' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
          </svg>
          <h3 className="text-sm font-medium text-gray-800">Payment History</h3>
        </div>
        <div className="text-sm text-gray-500">
          {paymentHistory.length} {paymentHistory.length === 1 ? 'transaction' : 'transactions'}
        </div>
      </div>
      
      {expanded && (
        <div className="border-t border-gray-200">
          <ul className="divide-y divide-gray-200">
            {paymentHistory.map((payment, index) => (
              <li key={payment.id || index} className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900">{payment.description || 'Payment'}</span>
                      <span className="ml-2">{getStatusBadge(payment.status)}</span>
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      {formatDate(payment.created_at)}
                    </div>
                    {payment.transaction_id && (
                      <div className="mt-1 text-xs text-gray-500">
                        Transaction ID: {payment.transaction_id}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className={`font-bold ${payment.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(payment.amount)}
                    </div>
                    {payment.balance_after !== undefined && (
                      <div className="text-xs text-gray-500 mt-1">
                        Balance: {formatCurrency(payment.balance_after)}
                      </div>
                    )}
                  </div>
                </div>
                
                {payment.notes && (
                  <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                    {payment.notes}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </motion.div>
  );
};

export default OrderPaymentHistory;
