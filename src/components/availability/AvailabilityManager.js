import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import availabilityAPI from '../../services/availabilityService';

/**
 * AvailabilityManager component
 * 
 * This component allows users to manage their availability settings.
 * 
 * @param {Object} props
 * @param {Object} props.user - The user whose availability is being managed
 * @param {Function} props.onSave - Function to call when availability is saved
 * @param {Function} props.onCancel - Function to call when the user cancels
 */
const AvailabilityManager = ({ user, onSave, onCancel }) => {
  const [availability, setAvailability] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('weekly');
  
  // Weekly availability state
  const [weeklyAvailability, setWeeklyAvailability] = useState([]);
  const [isRecurring, setIsRecurring] = useState(true);
  
  // Special dates state
  const [specialDates, setSpecialDates] = useState([]);
  
  // General settings
  const [remarks, setRemarks] = useState('');
  const [isAvailable, setIsAvailable] = useState(true);
  
  // Fetch availability data when component mounts
  useEffect(() => {
    if (user && user.id) {
      fetchAvailability();
    }
  }, [user]);
  
  // Fetch availability data
  const fetchAvailability = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await availabilityAPI.getAllAvailability(user.id);

      setAvailability(data);
      setWeeklyAvailability(data.availability_data || []);
      setSpecialDates(data.special_dates || []);
      setIsRecurring(data.is_recurring ?? true);
      setRemarks(data.remarks || '');
      setIsAvailable(data.is_available ?? true);

      setLoading(false);
    } catch (err) {
      setError('Failed to load availability data');
      setLoading(false);
    }
  };
  
  // Save availability data
  const saveAvailability = async () => {
    setSaving(true);
    setError(null);
    
    try {
      if (activeTab === 'weekly') {
        // Only save weekly schedule using the regular endpoint
      const regularData = {
        availability_data: weeklyAvailability,
        is_recurring: isRecurring,
        // Availability schedules should always be saved as available
        is_available: true,
        remarks
      };
        await availabilityAPI.setRegularAvailability(regularData);
      } else if (activeTab === 'special') {
        // Validate remarks
        for (const sd of specialDates) {
          if (!sd.remark || !sd.remark.trim()) {
            setError('Remark is required for all special dates.');
            setSaving(false);
            return;
          }
        }
        // Only save special dates using the special endpoint
      const specialData = {
        special_dates: specialDates,
        // Special dates are saved while user is generally available
        is_available: true
      };
      await availabilityAPI.setSpecialAvailability(specialData);
      } else {
        // Save all data for other tabs
        const allData = {
          availability_data: weeklyAvailability,
          is_recurring: isRecurring,
          // Base availability should default to available
          is_available: true,
          remarks,
          special_dates: specialDates
        };
        await availabilityAPI.saveAllAvailability(allData);
      }
      await fetchAvailability();
      setSaving(false);
      if (onSave) {
        onSave(
          activeTab === 'weekly'
            ? { availability_data: weeklyAvailability, is_recurring: isRecurring, is_available: true, remarks }
            : activeTab === 'special'
              ? { special_dates: specialDates }
              : { availability_data: weeklyAvailability, is_recurring: isRecurring, is_available: true, remarks, special_dates: specialDates }
        );
      }
    } catch (err) {
      setError('Failed to save availability data');
      setSaving(false);
    }
  };
  
  // Add a new weekly time period
  const addWeeklyTimePeriod = (day) => {
    const dayIndex = weeklyAvailability.findIndex(d => d.day === day);
    
    if (dayIndex >= 0) {
      // Day already exists, add a new period
      const updatedWeekly = [...weeklyAvailability];
      updatedWeekly[dayIndex].periods.push({
        start_time: '09:00',
        end_time: '17:00'
      });
      setWeeklyAvailability(updatedWeekly);
    } else {
      // Day doesn't exist, add it with a new period
      setWeeklyAvailability([
        ...weeklyAvailability,
        {
          day,
          periods: [
            {
              start_time: '09:00',
              end_time: '17:00'
            }
          ]
        }
      ]);
    }
  };
  
  // Update a weekly time period
  const updateWeeklyTimePeriod = (day, periodIndex, field, value) => {
    const dayIndex = weeklyAvailability.findIndex(d => d.day === day);
    
    if (dayIndex >= 0) {
      const updatedWeekly = [...weeklyAvailability];
      updatedWeekly[dayIndex].periods[periodIndex][field] = value;
      setWeeklyAvailability(updatedWeekly);
    }
  };
  
  // Remove a weekly time period
  const removeWeeklyTimePeriod = (day, periodIndex) => {
    const dayIndex = weeklyAvailability.findIndex(d => d.day === day);
    
    if (dayIndex >= 0) {
      const updatedWeekly = [...weeklyAvailability];
      updatedWeekly[dayIndex].periods.splice(periodIndex, 1);
      
      // If no periods left, remove the day
      if (updatedWeekly[dayIndex].periods.length === 0) {
        updatedWeekly.splice(dayIndex, 1);
      }
      
      setWeeklyAvailability(updatedWeekly);
    }
  };
  
  // Add a new special date
  const addSpecialDate = () => {
    // Get tomorrow's date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const formattedDate = tomorrow.toISOString().split('T')[0];
    
    setSpecialDates([
      ...specialDates,
      {
        date: formattedDate,
        periods: [
          {
            start_time: '09:00',
            end_time: '17:00'
          }
        ]
      }
    ]);
  };
  
  // Update a special date
  const updateSpecialDate = (index, field, value) => {
    const updatedSpecialDates = [...specialDates];
    
    if (field === 'date') {
      updatedSpecialDates[index].date = value;
    } else if (field === 'remark') {
      updatedSpecialDates[index].remark = value;
    } else {
      // This is a period field
      const [periodIndex, periodField] = field.split('.');
      updatedSpecialDates[index].periods[periodIndex][periodField] = value;
    }
    
    setSpecialDates(updatedSpecialDates);
  };
  
  // Add a time period to a special date
  const addSpecialDateTimePeriod = (index) => {
    const updatedSpecialDates = [...specialDates];
    updatedSpecialDates[index].periods.push({
      start_time: '09:00',
      end_time: '17:00'
    });
    setSpecialDates(updatedSpecialDates);
  };
  
  // Remove a time period from a special date
  const removeSpecialDateTimePeriod = (dateIndex, periodIndex) => {
    const updatedSpecialDates = [...specialDates];
    updatedSpecialDates[dateIndex].periods.splice(periodIndex, 1);
    
    // If no periods left, remove the date
    if (updatedSpecialDates[dateIndex].periods.length === 0) {
      updatedSpecialDates.splice(dateIndex, 1);
    }
    
    setSpecialDates(updatedSpecialDates);
  };
  
  // Remove a special date
  const removeSpecialDate = (index) => {
    const updatedSpecialDates = [...specialDates];
    updatedSpecialDates.splice(index, 1);
    setSpecialDates(updatedSpecialDates);
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };
  
  // Ensure each day in weeklyAvailability has is_available property
  useEffect(() => {
    if (weeklyAvailability && weeklyAvailability.length > 0) {
      setWeeklyAvailability(prev => prev.map(day => ({
        ...day,
        is_available: typeof day.is_available === 'boolean' ? day.is_available : true
      })));
    }
    // eslint-disable-next-line
  }, []);
  
  // Add a handler for toggling is_available for a day
  const toggleDayAvailable = (day) => {
    setWeeklyAvailability(prev => prev.map(d =>
      d.day === day ? { ...d, is_available: !d.is_available } : d
    ));
  };
  
  // If loading
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8 bg-white dark:bg-gray-900">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-200">Loading availability settings...</span>
      </div>
    );
  }
  
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-900/80 dark:to-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/50 dark:border-gray-700 shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="relative p-6 border-b border-white/30 dark:border-gray-700 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 dark:from-indigo-900/30 dark:to-purple-900/30">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 rounded-xl shadow-lg">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-bold text-left bg-gradient-to-r from-indigo-700 to-purple-700 dark:from-indigo-300 dark:to-purple-300 bg-clip-text text-transparent">
              Manage Availability
            </h2>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
          Set your regular weekly schedule and special dates when you're available.
        </p>
          </div>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <motion.div 
          variants={itemVariants}
          className="p-6"
        >
          <div className="bg-red-50/80 dark:bg-red-900/80 backdrop-blur-sm rounded-xl p-4 border border-red-100 dark:border-red-700">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
      
      {/* Tabs */}
      <div className="px-6 py-4 border-b border-white/30 dark:border-gray-700">
        <div className="flex space-x-1 bg-white/50 dark:bg-gray-900/60 backdrop-blur-sm rounded-xl p-1 border border-white/30 dark:border-gray-700">
          {[
            { key: 'weekly', label: 'Weekly Schedule', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
            { key: 'special', label: 'Special Dates', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
            { key: 'settings', label: 'Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' }
          ].map((tab) => (
            <motion.button
              key={tab.key}
            type="button"
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeTab === tab.key
                  ? 'bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 text-white shadow-lg'
                  : 'text-gray-700 dark:text-gray-200 bg-transparent hover:bg-transparent'
              }`}
              whileHover={{ scale: activeTab === tab.key ? 1 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={tab.icon} />
              </svg>
              <span>{tab.label}</span>
            </motion.button>
          ))}
        </div>
      </div>
      
      {/* Tab content */}
      <div className="p-6 bg-white/50 dark:bg-gray-900/60">
        {/* Weekly Schedule Tab */}
        {activeTab === 'weekly' && (
          <motion.div variants={itemVariants}>
            {/* Enable/Disable Toggle */}
            <div className="mb-6 p-6 bg-gradient-to-r from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-lg">
                    <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
          <div>
                    <h3 className="text-lg text-left font-semibold text-gray-900 dark:text-indigo-100">Weekly Recurring Schedule</h3>
                    <p className="text-sm text-gray-600 dark:text-indigo-100">Set your regular availability for each day of the week</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isRecurring}
                  onChange={(e) => setIsRecurring(e.target.checked)}
                    className="sr-only peer"
                />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                  <span className="ml-3 text-sm font-medium text-gray-700">
                    {isRecurring ? 'Enabled' : 'Disabled'}
                  </span>
              </label>
              </div>
            </div>
            
            {isRecurring && (
              <div className="space-y-4">
                {[
                  { day: 'Monday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-blue-100 to-indigo-100', textColor: 'text-blue-600' },
                  { day: 'Tuesday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-purple-100 to-pink-100', textColor: 'text-purple-600' },
                  { day: 'Wednesday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-green-100 to-emerald-100', textColor: 'text-green-600' },
                  { day: 'Thursday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-orange-100 to-red-100', textColor: 'text-orange-600' },
                  { day: 'Friday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-indigo-100 to-purple-100', textColor: 'text-indigo-600' },
                  { day: 'Saturday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-pink-100 to-rose-100', textColor: 'text-pink-600' },
                  { day: 'Sunday', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', color: 'from-yellow-100 to-amber-100', textColor: 'text-yellow-600' }
                ].map((dayConfig, dayIndex) => {
                  const dayData = weeklyAvailability.find(d => d.day === dayConfig.day);
                  const periods = dayData ? dayData.periods : [];
                  const isDayAvailable = dayData ? dayData.is_available !== false : true;
                  
                  return (
                    <motion.div 
                      key={dayConfig.day} 
                      variants={itemVariants}
                      className="p-6 bg-gradient-to-r from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:border-indigo-200 transition-all duration-300 shadow-sm hover:shadow-md relative"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`p-3 bg-gradient-to-br ${dayConfig.color} rounded-xl shadow-sm`}>
                            <svg className={`w-5 h-5 ${dayConfig.textColor}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={dayConfig.icon} />
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-lg text-left font-semibold text-gray-900 dark:text-indigo-100">{dayConfig.day}</h3>
                            <p className="text-sm text-gray-500 dark:text-white">
                              {periods.length > 0 
                                ? `${periods.length} time period${periods.length > 1 ? 's' : ''} set`
                                : 'No availability set'
                              }
                            </p>
                          </div>
                        </div>
                        {/* Per-day is_available toggle */}
                        <label className="relative inline-flex items-center cursor-pointer ml-4">
                          <input
                            type="checkbox"
                            checked={isDayAvailable}
                            onChange={() => toggleDayAvailable(dayConfig.day)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                          <span className="ml-3 text-sm font-medium text-gray-700 dark:text-indigo-100">
                            {isDayAvailable ? 'Available' : 'Unavailable'}
                          </span>
                        </label>
                      </div>
                      
                      {periods.length > 0 ? (
                        <div className="space-y-3">
                          {periods.map((period, periodIndex) => (
                            <div key={periodIndex} className="p-4 bg-white/60 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700 shadow-sm">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-700 dark:text-indigo-100">Time Period {periodIndex + 1}</span>
                                <motion.button
                                  type="button"
                                  onClick={() => removeWeeklyTimePeriod(dayConfig.day, periodIndex)}
                                  className="p-1.5 text-red-400 bg-gray-100 shadow-sm hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                  whileHover={{ scale: 1.1 }}
                                  whileTap={{ scale: 0.9 }}
                                >
                                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </motion.button>
                              </div>
                              <div className="flex items-center space-x-3">
                                <div className="flex-1">
                                  <label className="block text-xs font-medium text-gray-600 dark:text-indigo-100 mb-1">Start Time</label>
                              <select
                                value={period.start_time}
                                onChange={(e) => updateWeeklyTimePeriod(dayConfig.day, periodIndex, 'start_time', e.target.value)}
                                className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm text-gray-900 dark:text-gray-100"
                              >
                                {Array.from({ length: 24 }).map((_, hour) => {
                                  const time = `${hour.toString().padStart(2, '0')}:00`;
                                  const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                                    hour: 'numeric',
                                    minute: '2-digit',
                                    hour12: true
                                  });
                                  return (
                                    <option key={hour} value={time}>
                                      {displayTime}
                                  </option>
                                  );
                                })}
                              </select>
                                </div>
                                
                                <div className="flex items-center justify-center w-8 h-8">
                                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                  </svg>
                                </div>
                                
                                <div className="flex-1">
                                  <label className="block text-xs font-medium text-gray-600 dark:text-indigo-100 mb-1">End Time</label>
                              <select
                                value={period.end_time}
                                onChange={(e) => updateWeeklyTimePeriod(dayConfig.day, periodIndex, 'end_time', e.target.value)}
                                className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm text-gray-900 dark:text-gray-100"
                              >
                                {Array.from({ length: 24 }).map((_, hour) => {
                                  const time = `${hour.toString().padStart(2, '0')}:00`;
                                  const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                                    hour: 'numeric',
                                    minute: '2-digit',
                                    hour12: true
                                  });
                                  return (
                                    <option key={hour} value={time}>
                                      {displayTime}
                                  </option>
                                  );
                                })}
                              </select>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6 bg-gray-50/50 rounded-lg border-2 border-dashed border-gray-200">
                          <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-sm text-gray-500 mb-2">No time periods set for {dayConfig.day}</p>
                          <p className="text-xs text-gray-400">Click below to add availability</p>
                        </div>
                      )}
                      
                      <motion.button
                        type="button"
                        onClick={() => addWeeklyTimePeriod(dayConfig.day)}
                        className="mt-4 w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Time Period
                      </motion.button>
                    </motion.div>
                  );
                })}
              </div>
            )}
            
            {/* Save Button for Weekly Tab */}
            <motion.div 
              variants={itemVariants}
              className="mt-8 pt-6 border-t border-white/30"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                </div>
                <motion.button
                  type="button"
                  onClick={saveAvailability}
                  disabled={saving}
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium rounded-lg hover:from-green-600 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg flex items-center space-x-2"
                  whileHover={{ scale: saving ? 1 : 1.02 }}
                  whileTap={{ scale: saving ? 1 : 0.98 }}
                >
                  {saving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Save Weekly Schedule</span>
                    </>
                  )}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
        
        {/* Special Dates Tab */}
        {activeTab === 'special' && (
          <motion.div variants={itemVariants}>
            {/* Header Section */}
            <div className="mb-6 p-6 bg-gradient-to-r from-purple-50/50 to-indigo-50/50 backdrop-blur-sm rounded-xl border border-purple-100/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
          <div>
                    <h3 className="text-lg text-left font-semibold text-gray-900">Special Dates</h3>
                    <p className="text-sm text-gray-600">Override your regular schedule for specific dates</p>
                  </div>
                </div>
                <motion.button
                type="button"
                onClick={addSpecialDate}
                  className="px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 shadow-lg flex items-center space-x-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
              >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                  <span>Add Special Date</span>
                </motion.button>
              </div>
            </div>
            
            {specialDates.length > 0 ? (
              <div className="space-y-4">
                {specialDates.map((specialDate, dateIndex) => {
                  const date = new Date(specialDate.date);
                  const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
                  const formattedDate = date.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  });
                  
                  return (
                    <motion.div 
                      key={dateIndex} 
                      variants={itemVariants}
                      className="p-6 bg-gradient-to-r from-white/60 to-white/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:border-purple-200 transition-all duration-300 shadow-sm hover:shadow-md"
                    >
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-3 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-xl shadow-sm">
                            <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{formattedDate}</h3>
                            <p className="text-sm text-left text-gray-500">{dayName}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <span className="px-3 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                            Special
                          </span>
                          <motion.button
                        type="button"
                        onClick={() => removeSpecialDate(dateIndex)}
                            className="p-2 bg-red-50 shadow-sm text-red-500 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                      >
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                          </motion.button>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Remark <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={specialDate.remark || ''}
                          onChange={(e) => updateSpecialDate(dateIndex, 'remark', e.target.value)}
                          required
                          className="w-full px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm text-gray-900 dark:text-gray-100"
                          placeholder="e.g., Holiday, Personal day, Extended hours"
                        />
                    </div>
                    
                    <div className="space-y-3">
                      {specialDate.periods.map((period, periodIndex) => (
                          <div key={periodIndex} className="p-4 bg-white/60 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700 shadow-sm">
                            <div className="flex items-center justify-between mb-3">
                              <span className="text-sm font-medium text-gray-700">Time Period {periodIndex + 1}</span>
                              <motion.button
                                type="button"
                                onClick={() => removeSpecialDateTimePeriod(dateIndex, periodIndex)}
                                className="p-1.5 text-gray-400 bg-red-50 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </motion.button>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div className="flex-1">
                                <label className="block text-xs font-medium text-gray-600 mb-1">Start Time</label>
                          <select
                            value={period.start_time}
                            onChange={(e) => updateSpecialDate(dateIndex, `${periodIndex}.start_time`, e.target.value)}
                                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm text-gray-900 dark:text-gray-100"
                                >
                                  {Array.from({ length: 24 }).map((_, hour) => {
                                    const time = `${hour.toString().padStart(2, '0')}:00`;
                                    const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                                      hour: 'numeric',
                                      minute: '2-digit',
                                      hour12: true
                                    });
                                    return (
                                      <option key={hour} value={time}>
                                        {displayTime}
                              </option>
                                    );
                                  })}
                          </select>
                              </div>
                              
                              <div className="flex items-center justify-center w-8 h-8">
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                              </div>
                              
                              <div className="flex-1">
                                <label className="block text-xs font-medium text-gray-600 mb-1">End Time</label>
                          <select
                            value={period.end_time}
                            onChange={(e) => updateSpecialDate(dateIndex, `${periodIndex}.end_time`, e.target.value)}
                                  className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm text-gray-900 dark:text-gray-100"
                                >
                                  {Array.from({ length: 24 }).map((_, hour) => {
                                    const time = `${hour.toString().padStart(2, '0')}:00`;
                                    const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
                                      hour: 'numeric',
                                      minute: '2-digit',
                                      hour12: true
                                    });
                                    return (
                                      <option key={hour} value={time}>
                                        {displayTime}
                              </option>
                                    );
                                  })}
                          </select>
                              </div>
                            </div>
                        </div>
                      ))}
                    </div>
                    
                      <motion.button
                      type="button"
                      onClick={() => addSpecialDateTimePeriod(dateIndex)}
                        className="mt-4 w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-medium rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 shadow-lg"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Time Period
                      </motion.button>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="p-8 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl border border-purple-100 max-w-md mx-auto">
                  <div className="p-4 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                    <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Special Dates Set</h3>
                  <p className="text-gray-600 mb-4">Add special dates to override your regular schedule for holidays, personal days, or extended hours.</p>
                  <motion.button
                    type="button"
                    onClick={addSpecialDate}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-medium rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Your First Special Date
                  </motion.button>
                </div>
              </div>
            )}
            
            {/* Save Button for Special Dates Tab */}
            <motion.div 
              variants={itemVariants}
              className="mt-8 pt-6 border-t border-white/30"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3"></div>
                <motion.button
                  type="button"
                  onClick={saveAvailability}
                  disabled={saving}
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium rounded-lg hover:from-purple-600 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg flex items-center space-x-2"
                  whileHover={{ scale: saving ? 1 : 1.02 }}
                  whileTap={{ scale: saving ? 1 : 0.98 }}
                >
                  {saving ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Save Special Dates</span>
                    </>
                  )}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
        
        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <motion.div variants={itemVariants} className="space-y-6">
            {/* General Availability Toggle */}
            <div className="p-6 bg-gradient-to-r from-white/60 to-white/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl shadow-sm">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
            <div>
                    <h3 className="text-lg text-left font-semibold text-gray-900">General Availability</h3>
                    <p className="text-sm text-gray-600">Control your overall availability status</p>
                  </div>
                </div>
              </div>
              <div className="p-4 bg-white/50 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700">
                <div className="flex items-start space-x-3">
                  <div className={`w-3 h-3 rounded-full mt-1 ${isAvailable ? 'bg-green-300' : 'bg-red-300'}`}></div>
                  <div>
                    <p className="text-sm text-left font-medium text-gray-500">
                      To make yourself unavailable for all bookings, please use the <span className="font-semibold text-indigo-600">'Set Override'</span> button at the top of this page. The settings here control your regular schedule and remarks only.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Summary Card */}
            <div className="p-6 bg-gradient-to-r from-indigo-50/50 to-purple-50/50 backdrop-blur-sm rounded-xl border border-indigo-100/30">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-3 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl shadow-sm">
                  <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg text-left font-semibold text-gray-900">Availability Summary</h3>
                  <p className="text-sm text-gray-600">Overview of your current settings</p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-white/60 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className={`w-3 h-3 rounded-full ${isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Status</span>
                  </div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {isAvailable ? 'Available' : 'Unavailable'}
                  </p>
                </div>
                <div className="p-4 bg-white/60 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700">
                  <div className="flex items-center space-x-2 mb-2">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-700">Weekly Days</span>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {weeklyAvailability.length} days
                  </p>
                </div>
                <div className="p-4 bg-white/60 dark:bg-gray-900/60 rounded-lg border border-white/30 dark:border-gray-700">
                  <div className="flex items-center space-x-2 mb-2">
                    <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-700">Special Dates</span>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">
                    {specialDates.length} dates
                  </p>
              </div>
            </div>
          </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default AvailabilityManager;
