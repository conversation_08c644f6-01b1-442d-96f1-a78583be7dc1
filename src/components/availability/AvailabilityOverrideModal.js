import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import availabilityAPI from '../../services/availabilityService';

/**
 * AvailabilityOverrideModal component
 * 
 * This modal allows users to set availability overrides.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onOverrideSet - Function to call when an override is set
 */
const AvailabilityOverrideModal = ({ isOpen, onClose, onOverrideSet }) => {
  const [overrideMode, setOverrideMode] = useState('available'); // 'available' or 'unavailable'
  const [overrideType, setOverrideType] = useState('available');
  const [remarks, setRemarks] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentOverride, setCurrentOverride] = useState(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  
  // Fetch current override when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchCurrentOverride();
    }
  }, [isOpen]);
  
  // Reset form when override mode changes
  useEffect(() => {
    if (overrideMode === 'available') {
      setOverrideType('available');
    } else {
      setOverrideType('today');
    }
  }, [overrideMode]);
  
  // Fetch current override
  const fetchCurrentOverride = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const res = await availabilityAPI.getAvailabilityOverride();
      const data = res?.data || res;

      setCurrentOverride(data);

      if (data && data.is_active) {
        // Determine the mode based on override type
        if (data.override_type === 'available') {
          setOverrideMode('available');
          setOverrideType('available');
        } else {
          setOverrideMode('unavailable');
        setOverrideType(data.override_type);
        }
        setRemarks(data.remarks || '');
        setStartDate(data.start_date ? data.start_date.slice(0, 16) : '');
        setEndDate(data.end_date ? data.end_date.slice(0, 16) : '');
      } else {
        // No active override, default to available mode
        setOverrideMode('available');
        setOverrideType('available');
        setRemarks('');
        setStartDate('');
        setEndDate('');
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching availability override:', err);
      setError('Failed to load availability override');
      setLoading(false);
    }
  };
  
  // Set availability override
  const setAvailabilityOverride = async () => {
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!overrideType || !remarks.trim()) {
        setError('Override type and remarks are required.');
        setLoading(false);
        return;
      }
      
      let response;
      
      if (overrideMode === 'available') {
        // For available mode, we remove any existing override
        // This allows the regular availability schedule to take over
        await availabilityAPI.removeAvailabilityOverride();
        response = { override_type: 'available', is_active: false };
      } else {
        // For unavailable mode, set the appropriate override
      const overrideData = {
        override_type: overrideType,
          remarks: `Unavailable: ${remarks}`,
          is_active: true
      };

      const res = await availabilityAPI.setAvailabilityOverride(overrideData);
        response = res?.data || res;
      }

      setCurrentOverride(response);
      setLoading(false);
      if (onOverrideSet) {
        onOverrideSet(response);
      }
      onClose();
    } catch (err) {
      console.error('Error setting availability override:', err);
      setError('Failed to set availability override');
      setLoading(false);
    }
  };
  
  // Remove availability override
  const removeAvailabilityOverride = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await availabilityAPI.removeAvailabilityOverride();

      setCurrentOverride(null);
      setLoading(false);
      
      if (onOverrideSet) {
        onOverrideSet(null);
      }
      
      onClose();
    } catch (err) {
      console.error('Error removing availability override:', err);
      setError('Failed to remove availability override');
      setLoading(false);
    }
  };
  
  // Get end date based on override type
  const getEndDate = (type) => {
    const now = new Date();
    
    switch (type) {
      case 'today':
        const endOfDay = new Date(now);
        endOfDay.setHours(23, 59, 59, 999);
        return endOfDay.toISOString();
      
      case 'three_days':
        const threeDaysLater = new Date(now);
        threeDaysLater.setDate(threeDaysLater.getDate() + 3);
        return threeDaysLater.toISOString();
      
      case 'one_week':
        const oneWeekLater = new Date(now);
        oneWeekLater.setDate(oneWeekLater.getDate() + 7);
        return oneWeekLater.toISOString();
      
      case 'one_month':
        const oneMonthLater = new Date(now);
        oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
        return oneMonthLater.toISOString();
      
      case 'fifteen_minutes':
        const fifteenMinutesLater = new Date(now);
        fifteenMinutesLater.setMinutes(fifteenMinutesLater.getMinutes() + 15);
        return fifteenMinutesLater.toISOString();
      
      case 'thirty_minutes':
        const thirtyMinutesLater = new Date(now);
        thirtyMinutesLater.setMinutes(thirtyMinutesLater.getMinutes() + 30);
        return thirtyMinutesLater.toISOString();
      
      case 'one_hour':
        const oneHourLater = new Date(now);
        oneHourLater.setHours(oneHourLater.getHours() + 1);
        return oneHourLater.toISOString();
      
      case 'two_hours':
        const twoHoursLater = new Date(now);
        twoHoursLater.setHours(twoHoursLater.getHours() + 2);
        return twoHoursLater.toISOString();
      
      case 'six_hours':
        const sixHoursLater = new Date(now);
        sixHoursLater.setHours(sixHoursLater.getHours() + 6);
        return sixHoursLater.toISOString();
      
      case 'twelve_hours':
        const twelveHoursLater = new Date(now);
        twelveHoursLater.setHours(twelveHoursLater.getHours() + 12);
        return twelveHoursLater.toISOString();
      
      case 'always':
      case 'available':
      default:
        return null;
    }
  };
  
  // Get human-readable override type
  const getOverrideTypeLabel = (type) => {
    const typeLabels = {
      available: 'Always Available',
      today: overrideMode === 'available' ? 'Available Today' : 'Unavailable Today',
      three_days: overrideMode === 'available' ? 'Available for 3 Days' : 'Unavailable for 3 Days',
      one_week: overrideMode === 'available' ? 'Available for 1 Week' : 'Unavailable for 1 Week',
      one_month: overrideMode === 'available' ? 'Available for 1 Month' : 'Unavailable for 1 Month',
      always: overrideMode === 'available' ? 'Always Available' : 'Always Unavailable',
      fifteen_minutes: overrideMode === 'available' ? 'Available for 15 Minutes' : 'Unavailable for 15 Minutes',
      thirty_minutes: overrideMode === 'available' ? 'Available for 30 Minutes' : 'Unavailable for 30 Minutes',
      one_hour: overrideMode === 'available' ? 'Available for 1 Hour' : 'Unavailable for 1 Hour',
      two_hours: overrideMode === 'available' ? 'Available for 2 Hours' : 'Unavailable for 2 Hours',
      six_hours: overrideMode === 'available' ? 'Available for 6 Hours' : 'Unavailable for 6 Hours',
      twelve_hours: overrideMode === 'available' ? 'Available for 12 Hours' : 'Unavailable for 12 Hours'
    };
    
    return typeLabels[type] || type;
  };
  
  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={onClose}
        >
          <motion.div
            className="relative rounded-3xl shadow-2xl border border-white/30 dark:border-gray-700 bg-white/80 dark:bg-gray-900/90 backdrop-blur-2xl w-full max-w-full sm:max-w-lg max-h-screen sm:max-h-[90vh] overflow-hidden"
            style={{
              background: 'linear-gradient(120deg, rgba(237,233,254,0.97) 0%, rgba(221,214,254,0.95) 100%)',
              boxShadow: '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)',
              border: '1.5px solid rgba(255,255,255,0.25)',
              backdropFilter: 'blur(16px)',
              WebkitBackdropFilter: 'blur(16px)'
            }}
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Animated background decorations */}
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-full blur-xl animate-pulse delay-1000" />

            {/* Header */}
            <div className="relative z-10 px-6 py-6 border-b border-white/20 dark:border-gray-700 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900 dark:to-purple-900">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-500 to-purple-700 dark:from-indigo-300 dark:via-purple-300 dark:to-purple-400 bg-clip-text text-transparent">
                      Set Availability Override
                    </h2>
                    <p className="text-gray-500 dark:text-gray-300 text-base mt-1">
                      Temporarily change your availability for a specific period or event.
                    </p>
                  </div>
                </div>
              <button 
                onClick={onClose}
                  className="absolute top-4 right-4 p-3 bg-white/70 dark:bg-gray-900/60 hover:bg-indigo-100 dark:hover:bg-gray-800 text-indigo-500 dark:text-indigo-300 rounded-full shadow transition-colors duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400"
                  aria-label="Close override modal"
                  tabIndex={0}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              </div>
            </div>
            
            {/* Content */}
            <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)] bg-white/80 dark:bg-gray-900/80">
              {/* Error message */}
              {error && (
                <div className="mb-4 bg-gradient-to-br from-red-100 to-purple-100 dark:from-red-900 dark:to-purple-900 border border-red-200 dark:border-red-700 rounded-xl flex items-center space-x-3 p-4">
                  <svg className="w-6 h-6 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                  <div>
                    <p className="text-base font-semibold text-red-700 dark:text-red-300">{typeof error === 'string' ? error : error.message}</p>
                  </div>
                </div>
              )}
              {/* Current override */}
              {currentOverride && currentOverride.is_active && (
                <div className="mb-4 bg-gradient-to-r from-yellow-100/80 to-amber-50/80 dark:from-yellow-900/80 dark:to-amber-900/80 backdrop-blur-sm rounded-xl p-4 border border-yellow-200 dark:border-yellow-700 flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Current Override</h3>
                      <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <p>
                          Unavailable: {getOverrideTypeLabel(currentOverride.override_type)}
                          {currentOverride.end_date && (
                            <span> (until {new Date(currentOverride.end_date).toLocaleString()})</span>
                          )}
                        </p>
                        {currentOverride.remarks && (
                          <p className="mt-1">{currentOverride.remarks}</p>
                        )}
                    </div>
                  </div>
                </div>
              )}
              {currentOverride && !currentOverride.is_active && (
                <div className="mb-4 bg-gradient-to-r from-emerald-100/80 to-green-50/80 dark:from-emerald-900/80 dark:to-green-900/80 backdrop-blur-sm rounded-xl p-4 border border-emerald-200 dark:border-emerald-700 flex items-start">
                    <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div className="ml-3">
                    <h3 className="text-sm font-medium text-emerald-800 dark:text-emerald-200">Current Status</h3>
                    <div className="mt-2 text-sm text-emerald-700 dark:text-emerald-300">
                        <p>Using regular availability schedule (no override active)</p>
                    </div>
                  </div>
                </div>
              )}
              {/* Override Mode Selection */}
              <div className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-purple-50/60 dark:from-gray-900/90 dark:to-purple-900/60 p-6 border-l-4 border-indigo-300 dark:border-indigo-700 mb-6">
                <div className="flex items-center mb-4">
                  <span className="w-1.5 h-6 bg-gradient-to-b from-indigo-400 to-purple-400 rounded-full mr-3"></span>
                  <label className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Override Mode
                </label>
                </div>
                <div className="flex space-x-4 mb-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="available"
                      checked={overrideMode === 'available'}
                      onChange={(e) => setOverrideMode(e.target.value)}
                      className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-emerald-700 dark:text-emerald-300 font-medium">Make Available</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="unavailable"
                      checked={overrideMode === 'unavailable'}
                      onChange={(e) => setOverrideMode(e.target.value)}
                      className="h-4 w-4 text-rose-600 focus:ring-rose-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-rose-700 dark:text-rose-300 font-medium">Make Unavailable</span>
                  </label>
                </div>
                {overrideMode === 'available' && (
                  <p className="mt-2 text-xs text-emerald-600 dark:text-emerald-300">
                    This will remove any current override and use your regular availability schedule.
                  </p>
                )}
                {overrideMode === 'unavailable' && (
                  <p className="mt-2 text-xs text-rose-600 dark:text-rose-300">
                    This will override your regular schedule and make you unavailable for the selected duration.
                  </p>
                )}
              </div>
              {/* Override type */}
              <div className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-purple-50/60 dark:from-gray-900/90 dark:to-purple-900/60 p-6 border-l-4 border-indigo-300 dark:border-indigo-700 mb-6">
                <div className="flex items-center mb-4">
                  <span className="w-1.5 h-6 bg-gradient-to-b from-indigo-400 to-purple-400 rounded-full mr-3"></span>
                  <label htmlFor="override-type" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Duration
                </label>
                </div>
                <select
                  id="override-type"
                  value={overrideType}
                  onChange={(e) => setOverrideType(e.target.value)}
                  className={`block w-full pl-3 pr-10 py-2 text-base border border-indigo-200 dark:border-indigo-700 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm ${overrideMode === 'available' ? 'opacity-60 cursor-not-allowed' : ''}`}
                  disabled={overrideMode === 'available'}
                >
                  {overrideMode === 'available' ? (
                    <>
                      <option value="available">Use Regular Schedule</option>
                    </>
                  ) : (
                    <>
                      <option value="today">Unavailable Today</option>
                      <option value="three_days">Unavailable for 3 Days</option>
                      <option value="one_week">Unavailable for 1 Week</option>
                      <option value="one_month">Unavailable for 1 Month</option>
                      <option value="always">Always Unavailable</option>
                      <option value="fifteen_minutes">Unavailable for 15 Minutes</option>
                      <option value="thirty_minutes">Unavailable for 30 Minutes</option>
                      <option value="one_hour">Unavailable for 1 Hour</option>
                      <option value="two_hours">Unavailable for 2 Hours</option>
                      <option value="six_hours">Unavailable for 6 Hours</option>
                      <option value="twelve_hours">Unavailable for 12 Hours</option>
                    </>
                  )}
                </select>
                {overrideMode === 'available' && (
                  <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    To select a duration, choose <span className="font-semibold text-rose-600">'Make Unavailable'</span> above.
                  </p>
                )}
              </div>
              {/* Remarks */}
              <div className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-purple-50/60 dark:from-gray-900/90 dark:to-purple-900/60 p-6 border-l-4 border-indigo-300 dark:border-indigo-700 mb-6">
                <div className="flex items-center mb-4">
                  <span className="w-1.5 h-6 bg-gradient-to-b from-indigo-400 to-purple-400 rounded-full mr-3"></span>
                  <label htmlFor="remarks" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Remarks <span className="text-red-500">*</span>
                </label>
                </div>
                <textarea
                  id="remarks"
                  value={remarks}
                  onChange={e => setRemarks(e.target.value)}
                  rows="3"
                  required
                  className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-indigo-200 dark:border-indigo-700 rounded-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
                  placeholder="Add any notes about this override"
                ></textarea>
              </div>
            </div>
            {/* Footer */}
            <div className="sticky bottom-0 left-0 right-0 z-20 bg-gradient-to-r from-white/90 to-purple-50/80 dark:from-gray-900/90 dark:to-purple-900/80 border-t border-indigo-100 dark:border-gray-700 px-0 sm:px-6 py-4 flex justify-end gap-4">
              {currentOverride && currentOverride.is_active ? (
                <button
                  type="button"
                  onClick={removeAvailabilityOverride}
                  disabled={loading}
                  className="px-6 py-3 bg-gradient-to-r from-rose-500 to-rose-400 dark:from-rose-700 dark:to-rose-600 text-white font-bold rounded-2xl shadow hover:from-rose-600 hover:to-rose-500 dark:hover:from-rose-800 dark:hover:to-rose-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-rose-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  {loading ? 'Removing...' : 'Remove Override'}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 text-gray-700 dark:text-gray-200 font-bold rounded-2xl shadow hover:from-gray-300 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 transition-all duration-300"
                >
                  Cancel
                </button>
              )}
              <button
                type="button"
                onClick={setAvailabilityOverride}
                disabled={loading}
                className={`px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 dark:from-indigo-700 dark:to-purple-700 text-white font-bold rounded-2xl shadow hover:from-indigo-600 hover:to-purple-600 dark:hover:from-indigo-800 dark:hover:to-purple-800 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
              >
                {loading ? 'Setting...' : overrideMode === 'available' ? 'Remove Override' : 'Set Unavailable'}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AvailabilityOverrideModal;
