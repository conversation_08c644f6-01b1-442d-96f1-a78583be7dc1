import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import availabilityAPI from '../../services/availabilityService';

/**
 * AvailabilityCalendar component
 * 
 * This component displays a calendar view of a user's availability.
 * 
 * @param {Object} props
 * @param {Object} props.talent - The talent whose availability is being displayed
 * @param {Function} props.onSelectTimeSlot - Function to call when a time slot is selected
 * @param {boolean} props.readOnly - Whether the calendar is read-only (default: true)
 */
const AvailabilityCalendar = ({ talent, onSelectTimeSlot, readOnly = true }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState([]);
  const [availability, setAvailability] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  const [hoveredDate, setHoveredDate] = useState(null);
  
  // Fetch availability data when component mounts or talent changes
  useEffect(() => {
    if (talent && talent.id) {
      fetchAvailability();
    }
  }, [talent]);
  
  // Generate calendar days when month changes
  useEffect(() => {
    if (currentMonth) {
      generateCalendarDays(currentMonth);
    }
  }, [currentMonth]);
  
  // Generate time slots when selected date changes
  useEffect(() => {
    if (selectedDate && availability) {
      generateTimeSlots(selectedDate);
    } else {
      setTimeSlots([]);
    }
  }, [selectedDate, availability]);
  
  // Fetch availability data
  const fetchAvailability = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const res = await availabilityAPI.getUserAvailability({ user_id: talent.id });
      const data = res?.data || res;
      setAvailability(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching availability:', err);
      setError('Failed to load availability data');
      setLoading(false);
    }
  };
  
  // Generate calendar days for the current month
  const generateCalendarDays = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayOfWeek = firstDay.getDay();
    
    // Calculate days from previous month to show
    const daysFromPrevMonth = firstDayOfWeek;
    
    // Calculate total days to show (previous month + current month + next month)
    const totalDays = 42; // 6 rows of 7 days
    
    // Generate array of calendar days
    const days = [];
    
    // Add days from previous month
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();
    
    for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
      days.push({
        date: new Date(year, month - 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    
    // Add days from current month
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      date.setHours(0, 0, 0, 0);
      
      // Check if date is today
      const isToday = date.getTime() === today.getTime();
      
      // Check if date is selectable (not in the past)
      const isSelectable = date.getTime() >= today.getTime();
      
      // Check if date has availability
      const hasAvailability = checkDateAvailability(date);
      
      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelectable,
        hasAvailability
      });
    }
    
    // Add days from next month
    const remainingDays = totalDays - days.length;
    
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    
    setCalendarDays(days);
  };
  
  // Check if a date has availability
  const checkDateAvailability = (date) => {
    if (!availability) return false;
    
    // Format date as YYYY-MM-DD
    const formattedDate = date.toISOString().split('T')[0];
    
    // Check if date is in special dates
    const specialDate = availability.special_dates.find(d => d.date === formattedDate);
    if (specialDate && specialDate.periods.length > 0) {
      return true;
    }
    
    // Check if day of week has availability
    const dayOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
    const dayAvailability = availability.availability_data.find(d => d.day === dayOfWeek);
    
    return dayAvailability && dayAvailability.periods.length > 0;
  };
  
  // Generate time slots for the selected date
  const generateTimeSlots = (date) => {
    if (!availability) return [];
    
    const slots = [];
    const dayOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
    const formattedDate = date.toISOString().split('T')[0];
    
    // Check if date is in special dates
    const specialDate = availability.special_dates.find(d => d.date === formattedDate);
    if (specialDate) {
      // Use special date periods
      specialDate.periods.forEach(period => {
        const [startHour, startMinute] = period.start_time.split(':').map(Number);
        const [endHour, endMinute] = period.end_time.split(':').map(Number);
        
        // Generate slots in 30-minute increments
        for (let hour = startHour; hour <= endHour; hour++) {
          for (let minute = 0; minute < 60; minute += 30) {
            // Skip slots before start time or after end time
            if ((hour === startHour && minute < startMinute) || 
                (hour === endHour && minute > endMinute)) {
              continue;
            }
            
            slots.push({
              time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
              hour,
              minute,
              isAvailable: true
            });
          }
        }
      });
    } else {
      // Use weekly availability
      const dayAvailability = availability.availability_data.find(d => d.day === dayOfWeek);
      if (dayAvailability) {
        dayAvailability.periods.forEach(period => {
          const [startHour, startMinute] = period.start_time.split(':').map(Number);
          const [endHour, endMinute] = period.end_time.split(':').map(Number);
          
          // Generate slots in 30-minute increments
          for (let hour = startHour; hour <= endHour; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
              // Skip slots before start time or after end time
              if ((hour === startHour && minute < startMinute) || 
                  (hour === endHour && minute > endMinute)) {
                continue;
              }
              
              slots.push({
                time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                hour,
                minute,
                isAvailable: true
              });
            }
          }
        });
      }
    }
    
    setTimeSlots(slots);
  };
  
  // Handle date selection
  const handleDateSelect = (day) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
    }
  };
  
  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot) => {
    if (timeSlot.isAvailable && onSelectTimeSlot && selectedDate) {
      // Create a date object with the selected date and time
      const dateTime = new Date(selectedDate);
      dateTime.setHours(timeSlot.hour, timeSlot.minute, 0, 0);
      
      onSelectTimeSlot(dateTime);
    }
  };
  
  // Handle previous month button click
  const handlePrevMonth = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    setCurrentMonth(prevMonth);
  };
  
  // Handle next month button click
  const handleNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };
  
  // Format month for display
  const formatMonth = (date) => {
    if (!date) return '';
    
    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString(undefined, options);
  };
  
  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };
  
  // If loading
  if (loading) {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-12 bg-gradient-to-br from-white to-indigo-50/30 dark:from-gray-900 to-indigo-900/10 rounded-3xl shadow-xl border border-white/20 dark:border-indigo-900/20"
      >
        <div className="relative">
          <div className="w-16 h-16 border-4 border-indigo-200 dark:border-indigo-800 rounded-full animate-spin"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-indigo-600 dark:border-t-indigo-400 rounded-full animate-spin"></div>
        </div>
        <motion.p 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mt-6 text-lg font-medium text-indigo-600 dark:text-indigo-400"
        >
          Loading availability...
        </motion.p>
        <motion.p 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-2 text-sm text-gray-500 dark:text-gray-400"
        >
          Please wait while we fetch the latest schedule
        </motion.p>
      </motion.div>
    );
  }
  
  // If error
  if (error) {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="p-8 bg-gradient-to-br from-red-50 to-pink-50/50 dark:from-red-900/20 dark:to-pink-900/10 rounded-3xl shadow-xl border border-red-200/50 dark:border-red-800/30"
      >
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
              <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold text-red-800 dark:text-red-200 mb-2">
              Oops! Something went wrong
            </h3>
            <p className="text-red-700 dark:text-red-300 mb-4 leading-relaxed">
              {error}
            </p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={fetchAvailability}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-medium rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Try Again</span>
            </motion.button>
          </div>
        </div>
      </motion.div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="bg-gradient-to-br from-white via-indigo-50/20 to-purple-50/30 dark:from-gray-900 dark:via-indigo-900/10 dark:to-purple-900/10 rounded-3xl shadow-2xl border border-white/40 dark:border-indigo-900/30 overflow-hidden backdrop-blur-sm"
    >
      {/* Calendar header */}
      <div className="px-8 py-6 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 dark:from-indigo-800 dark:via-purple-800 dark:to-indigo-900">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-white mb-1">
              Availability Calendar
            </h2>
            <p className="text-indigo-100 dark:text-indigo-200 text-sm">
              Select a date to view available time slots
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              type="button"
              onClick={handlePrevMonth}
              className="p-3 rounded-2xl bg-white/20 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 group"
            >
              <svg className="w-5 h-5 text-white group-hover:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M15 19l-7-7 7-7" />
              </svg>
            </motion.button>
            
            <div className="px-6 py-3 bg-white/20 backdrop-blur-sm rounded-2xl">
              <span className="text-lg font-bold text-white">{formatMonth(currentMonth)}</span>
            </div>
            
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              type="button"
              onClick={handleNextMonth}
              className="p-3 rounded-2xl bg-white/20 hover:bg-white/30 backdrop-blur-sm transition-all duration-200 group"
            >
              <svg className="w-5 h-5 text-white group-hover:text-indigo-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          </div>
        </div>
      </div>
      
      {/* Calendar content */}
      <div className="p-8">
        {/* Day names */}
        <div className="grid grid-cols-7 gap-2 mb-6">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
            <motion.div 
              key={day} 
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="text-center py-3 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-700/50"
            >
              <span className="text-sm font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                {day}
              </span>
            </motion.div>
          ))}
        </div>
        
        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-2 mb-8">
          {calendarDays.map((day, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.01 }}
              whileHover={day.isSelectable ? { scale: 1.05, y: -2 } : {}}
              whileTap={day.isSelectable ? { scale: 0.95 } : {}}
              type="button"
              onClick={() => handleDateSelect(day)}
              onMouseEnter={() => day.isSelectable && setHoveredDate(day.date)}
              onMouseLeave={() => setHoveredDate(null)}
              disabled={!day.isSelectable}
              className={`
                h-14 rounded-2xl flex items-center justify-center text-sm font-semibold relative overflow-hidden transition-all duration-300 transform-gpu
                ${!day.isCurrentMonth 
                  ? 'text-gray-300 dark:text-gray-600 bg-gray-50/50 dark:bg-gray-800/20' 
                  : day.isToday
                    ? 'bg-gradient-to-br from-amber-400 to-orange-500 text-white shadow-lg shadow-amber-500/30 ring-2 ring-amber-300 dark:ring-amber-600'
                    : selectedDate && day.date.getTime() === selectedDate.getTime()
                      ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-xl shadow-indigo-500/40 ring-2 ring-indigo-300 dark:ring-indigo-600'
                      : day.isSelectable
                        ? hoveredDate && day.date.getTime() === hoveredDate.getTime()
                          ? 'bg-gradient-to-br from-indigo-100 to-purple-100/60 dark:from-indigo-800/50 dark:to-purple-800/30 text-indigo-700 dark:text-indigo-200 shadow-lg'
                          : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50/40 dark:hover:from-indigo-900/30 dark:hover:to-purple-900/20 shadow-sm hover:shadow-md border border-gray-200/50 dark:border-gray-700/50'
                        : 'bg-gray-100/50 dark:bg-gray-800/20 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                }
              `}
            >
              <span className="relative z-10">{day.day}</span>
              
              {/* Availability indicator */}
              {day.hasAvailability && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-emerald-400 dark:bg-emerald-300 shadow-sm"
                >
                  <div className="absolute inset-0 w-2 h-2 rounded-full bg-emerald-400 dark:bg-emerald-300 animate-ping opacity-40"></div>
                </motion.div>
              )}
              
              {/* Selection highlight effect */}
              {selectedDate && day.date.getTime() === selectedDate.getTime() && (
                <motion.div
                  layoutId="selectedDate"
                  className="absolute inset-0 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          ))}
        </div>
        
        {/* Time slots */}
        <AnimatePresence mode="wait">
          {selectedDate && (
            <motion.div
              initial={{ opacity: 0, y: 20, height: 0 }}
              animate={{ opacity: 1, y: 0, height: "auto" }}
              exit={{ opacity: 0, y: -20, height: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="overflow-hidden"
            >
              <div className="bg-gradient-to-r from-indigo-50 via-purple-50/50 to-indigo-50 dark:from-indigo-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-3xl p-8 border border-indigo-200/30 dark:border-indigo-800/30">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center space-x-3 mb-6"
                >
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100">
                      Available Times
                    </h3>
                    <p className="text-indigo-600 dark:text-indigo-300 text-sm font-medium">
                      {formatDate(selectedDate)}
                    </p>
                  </div>
                </motion.div>
                
                {timeSlots.length > 0 ? (
                  <motion.div 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3 max-h-80 overflow-y-auto custom-scrollbar"
                  >
                    {timeSlots.map((timeSlot, index) => (
                      <motion.button
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.02 }}
                        whileHover={timeSlot.isAvailable && !readOnly ? { scale: 1.05, y: -2 } : {}}
                        whileTap={timeSlot.isAvailable && !readOnly ? { scale: 0.95 } : {}}
                        type="button"
                        onClick={() => handleTimeSlotSelect(timeSlot)}
                        disabled={!timeSlot.isAvailable || readOnly}
                        className={`
                          py-3 px-4 rounded-2xl text-sm font-semibold transition-all duration-300 transform-gpu relative overflow-hidden
                          ${timeSlot.isAvailable 
                            ? 'bg-gradient-to-br from-emerald-400 to-teal-500 text-white shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-500/30 border border-emerald-300/50' 
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed border border-gray-200/50 dark:border-gray-700/50'
                          }
                          ${readOnly && timeSlot.isAvailable ? 'cursor-default hover:shadow-lg' : ''}
                        `}
                      >
                        <span className="relative z-10">{timeSlot.time}</span>
                        {timeSlot.isAvailable && (
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-700 ease-out" />
                        )}
                      </motion.button>
                    ))}
                  </motion.div>
                ) : (
                  <motion.div 
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 }}
                    className="text-center py-12"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                      <svg className="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-600 dark:text-gray-300 mb-2">
                      No Available Times
                    </h4>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      There are no available time slots for this date. Please select another date.
                    </p>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Custom scrollbar styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #6366f1, #8b5cf6);
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #4f46e5, #7c3aed);
        }
      `}</style>
    </motion.div>
  );
};

export default AvailabilityCalendar;