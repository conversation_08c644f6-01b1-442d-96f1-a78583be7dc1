import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import availabilityAPI from '../../services/availabilityService';

/**
 * AvailabilityCalendar component
 * 
 * This component displays a calendar view of a user's availability.
 * 
 * @param {Object} props
 * @param {Object} props.talent - The talent whose availability is being displayed
 * @param {Function} props.onSelectTimeSlot - Function to call when a time slot is selected
 * @param {boolean} props.readOnly - Whether the calendar is read-only (default: true)
 */
const AvailabilityCalendar = ({ talent, onSelectTimeSlot, readOnly = true }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState([]);
  const [availability, setAvailability] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  
  // Fetch availability data when component mounts or talent changes
  useEffect(() => {
    if (talent && talent.id) {
      fetchAvailability();
    }
  }, [talent]);
  
  // Generate calendar days when month changes
  useEffect(() => {
    if (currentMonth) {
      generateCalendarDays(currentMonth);
    }
  }, [currentMonth]);
  
  // Generate time slots when selected date changes
  useEffect(() => {
    if (selectedDate && availability) {
      generateTimeSlots(selectedDate);
    } else {
      setTimeSlots([]);
    }
  }, [selectedDate, availability]);
  
  // Fetch availability data
  const fetchAvailability = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const res = await availabilityAPI.getUserAvailability({ user_id: talent.id });
      const data = res?.data || res;
      setAvailability(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching availability:', err);
      setError('Failed to load availability data');
      setLoading(false);
    }
  };
  
  // Generate calendar days for the current month
  const generateCalendarDays = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayOfWeek = firstDay.getDay();
    
    // Calculate days from previous month to show
    const daysFromPrevMonth = firstDayOfWeek;
    
    // Calculate total days to show (previous month + current month + next month)
    const totalDays = 42; // 6 rows of 7 days
    
    // Generate array of calendar days
    const days = [];
    
    // Add days from previous month
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();
    
    for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
      days.push({
        date: new Date(year, month - 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    
    // Add days from current month
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      date.setHours(0, 0, 0, 0);
      
      // Check if date is today
      const isToday = date.getTime() === today.getTime();
      
      // Check if date is selectable (not in the past)
      const isSelectable = date.getTime() >= today.getTime();
      
      // Check if date has availability
      const hasAvailability = checkDateAvailability(date);
      
      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelectable,
        hasAvailability
      });
    }
    
    // Add days from next month
    const remainingDays = totalDays - days.length;
    
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false,
        hasAvailability: false
      });
    }
    
    setCalendarDays(days);
  };
  
  // Check if a date has availability
  const checkDateAvailability = (date) => {
    if (!availability) return false;
    
    // Format date as YYYY-MM-DD
    const formattedDate = date.toISOString().split('T')[0];
    
    // Check if date is in special dates
    const specialDate = availability.special_dates.find(d => d.date === formattedDate);
    if (specialDate && specialDate.periods.length > 0) {
      return true;
    }
    
    // Check if day of week has availability
    const dayOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
    const dayAvailability = availability.availability_data.find(d => d.day === dayOfWeek);
    
    return dayAvailability && dayAvailability.periods.length > 0;
  };
  
  // Generate time slots for the selected date
  const generateTimeSlots = (date) => {
    if (!availability) return [];
    
    const slots = [];
    const dayOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
    const formattedDate = date.toISOString().split('T')[0];
    
    // Check if date is in special dates
    const specialDate = availability.special_dates.find(d => d.date === formattedDate);
    if (specialDate) {
      // Use special date periods
      specialDate.periods.forEach(period => {
        const [startHour, startMinute] = period.start_time.split(':').map(Number);
        const [endHour, endMinute] = period.end_time.split(':').map(Number);
        
        // Generate slots in 30-minute increments
        for (let hour = startHour; hour <= endHour; hour++) {
          for (let minute = 0; minute < 60; minute += 30) {
            // Skip slots before start time or after end time
            if ((hour === startHour && minute < startMinute) || 
                (hour === endHour && minute > endMinute)) {
              continue;
            }
            
            slots.push({
              time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
              hour,
              minute,
              isAvailable: true
            });
          }
        }
      });
    } else {
      // Use weekly availability
      const dayAvailability = availability.availability_data.find(d => d.day === dayOfWeek);
      if (dayAvailability) {
        dayAvailability.periods.forEach(period => {
          const [startHour, startMinute] = period.start_time.split(':').map(Number);
          const [endHour, endMinute] = period.end_time.split(':').map(Number);
          
          // Generate slots in 30-minute increments
          for (let hour = startHour; hour <= endHour; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
              // Skip slots before start time or after end time
              if ((hour === startHour && minute < startMinute) || 
                  (hour === endHour && minute > endMinute)) {
                continue;
              }
              
              slots.push({
                time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                hour,
                minute,
                isAvailable: true
              });
            }
          }
        });
      }
    }
    
    setTimeSlots(slots);
  };
  
  // Handle date selection
  const handleDateSelect = (day) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
    }
  };
  
  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot) => {
    if (timeSlot.isAvailable && onSelectTimeSlot && selectedDate) {
      // Create a date object with the selected date and time
      const dateTime = new Date(selectedDate);
      dateTime.setHours(timeSlot.hour, timeSlot.minute, 0, 0);
      
      onSelectTimeSlot(dateTime);
    }
  };
  
  // Handle previous month button click
  const handlePrevMonth = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    setCurrentMonth(prevMonth);
  };
  
  // Handle next month button click
  const handleNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };
  
  // Format month for display
  const formatMonth = (date) => {
    if (!date) return '';
    
    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString(undefined, options);
  };
  
  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };
  
  // If loading
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8 bg-white dark:bg-gray-900">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-200">Loading availability...</span>
      </div>
    );
  }
  
  // If error
  if (error) {
    return (
      <div className="p-6 bg-white dark:bg-gray-900">
        <div className="bg-red-50 dark:bg-red-900 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={fetchAvailability}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-900 rounded-lg shadow-sm overflow-hidden"
    >
      {/* Calendar header */}
      <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Availability Calendar</h2>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={handlePrevMonth}
              className="p-1 rounded-full bg-indigo-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{formatMonth(currentMonth)}</span>
            <button
              type="button"
              onClick={handleNextMonth}
              className="p-1 rounded-full bg-indigo-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      {/* Calendar grid */}
      <div className="p-4 bg-white dark:bg-gray-900">
        {/* Day names */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-1">
              {day}
            </div>
          ))}
        </div>
        
        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1 mb-4">
          {calendarDays.map((day, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleDateSelect(day)}
              disabled={!day.isSelectable}
              className={`
                h-10 rounded-md flex items-center justify-center text-sm relative
                ${day.isCurrentMonth ? 'font-medium' : 'text-gray-400 dark:text-gray-600'}
                ${day.isToday ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200' : 'bg-indigo-700 text-white dark:bg-indigo-800 dark:text-white'}
                ${selectedDate && day.date.getTime() === selectedDate.getTime() ? 'bg-indigo-600 text-white dark:bg-indigo-500 dark:text-white' : ''}
                ${day.isSelectable && !(selectedDate && day.date.getTime() === selectedDate.getTime()) ? 'hover:bg-gray-500 hover:text-white dark:hover:bg-gray-700 dark:hover:text-white' : ''}
                ${!day.isSelectable ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              {day.day}
              {day.hasAvailability && (
                <span className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-green-500 dark:bg-green-400"></span>
              )}
            </button>
          ))}
        </div>
        
        {/* Time slots */}
        {selectedDate && (
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              Available times for {formatDate(selectedDate)}
            </h3>
            
            {timeSlots.length > 0 ? (
              <div className="grid grid-cols-4 sm:grid-cols-6 gap-2 max-h-48 overflow-y-auto">
                {timeSlots.map((timeSlot, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleTimeSlotSelect(timeSlot)}
                    disabled={!timeSlot.isAvailable || readOnly}
                    className={`
                      py-2 px-3 rounded-md text-sm font-medium
                      ${timeSlot.isAvailable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-700' : 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-600 cursor-not-allowed'}
                      ${readOnly ? 'cursor-default' : 'cursor-pointer'}
                    `}
                  >
                    {timeSlot.time}
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                No available time slots for this date
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default AvailabilityCalendar;
