import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import OrderStatusBadge from './OrderStatusBadge';
import orderPaymentService from '../../services/orderPaymentService';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * OrderCard component
 * 
 * This component displays an order card with details and actions.
 * 
 * @param {Object} props
 * @param {Object} props.order - The order data
 * @param {Function} props.onAccept - Function to call when order is accepted
 * @param {Function} props.onReject - Function to call when order is rejected
 * @param {Function} props.onComplete - Function to call when order is completed
 */
const OrderCard = ({ order, onAccept, onReject, onComplete }) => {
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Handle accept button click
  const handleAccept = async () => {
    setLoading(true);
    await onAccept();
    setLoading(false);
  };
  
  // Handle reject button click
  const handleReject = async () => {
    setLoading(true);
    await onReject();
    setLoading(false);
  };
  
  // Handle complete button click
  const handleComplete = async () => {
    setLoading(true);
    await onComplete();
    setLoading(false);
  };
  
  // Get action buttons based on order status
  const getActionButtons = () => {
    switch (order.status.toLowerCase()) {
      case 'pending':
        return (
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleAccept}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : 'Accept'}
            </button>
            <button
              type="button"
              onClick={handleReject}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : 'Reject'}
            </button>
          </div>
        );
      
      case 'accepted':
      case 'in_progress':
        return (
          <button
            type="button"
            onClick={handleComplete}
            disabled={loading}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Processing...' : 'Complete Order'}
          </button>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="border border-gray-200 rounded-lg overflow-hidden"
    >
      {/* Order header */}
      <div className="bg-gray-50 px-4 py-3 sm:px-6 flex flex-wrap justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <img
              src={getCdnUrl(order.talent?.profile_image || '/images/default-avatar.jpg')}
              alt={order.talent?.name || 'Talent'}
              className="h-10 w-10 rounded-full object-cover"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = 'https://via.placeholder.com/40?text=User';
              }}
            />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">
              {order.talent?.name || 'Unknown Talent'}
            </h3>
            <p className="text-xs text-gray-500">
              Order #{order.id} • {formatDate(order.created_at)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 mt-2 sm:mt-0">
          <OrderStatusBadge status={order.status} />
          <button
            type="button"
            onClick={() => setExpanded(!expanded)}
            className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg className={`h-5 w-5 transform transition-transform ${expanded ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Order details */}
      {expanded && (
        <div className="px-4 py-3 sm:px-6 border-t border-gray-200">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Service</dt>
              <dd className="mt-1 text-sm text-gray-900">{order.service_name || 'N/A'}</dd>
            </div>
            
            <div>
              <dt className="text-sm font-medium text-gray-500">Price</dt>
              <dd className="mt-1 text-sm text-gray-900">{orderPaymentService.formatCurrency(order.price || 0)}</dd>
            </div>
            
            <div>
              <dt className="text-sm font-medium text-gray-500">Quantity</dt>
              <dd className="mt-1 text-sm text-gray-900">{order.quantity || 1}</dd>
            </div>
            
            <div>
              <dt className="text-sm font-medium text-gray-500">Total</dt>
              <dd className="mt-1 text-sm font-medium text-indigo-600">{orderPaymentService.formatCurrency((order.price || 0) * (order.quantity || 1))}</dd>
            </div>
            
            {order.scheduled_at && (
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">Scheduled Time</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(order.scheduled_at)}</dd>
              </div>
            )}
            
            {order.description && (
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">Description</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.description}</dd>
              </div>
            )}
            
            {order.notes && (
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">Notes</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.notes}</dd>
              </div>
            )}
          </dl>
          
          {/* Order actions */}
          <div className="mt-4 flex justify-between items-center">
            <Link
              to={`/orders/${order.id}`}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              View Details
            </Link>
            
            {getActionButtons()}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default OrderCard;
