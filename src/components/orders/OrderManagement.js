import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import orderAPI from '../../services/orderService';
import orderPaymentService from '../../services/orderPaymentService';
import walletAPI from '../../services/walletService';
import OrderStatusBadge from './OrderStatusBadge';
import OrderFilters from './OrderFilters';
import OrderCard from './OrderCard';
import InsufficientBalanceModal from './InsufficientBalanceModal';

/**
 * OrderManagement component
 * 
 * This component displays a list of orders and provides functionality to filter and manage them.
 */
const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    date: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0
  });
  const [walletBalance, setWalletBalance] = useState(0);
  const [showInsufficientBalanceModal, setShowInsufficientBalanceModal] = useState(false);
  const [insufficientBalanceData, setInsufficientBalanceData] = useState(null);
  
  // Fetch orders when component mounts or filters change
  useEffect(() => {
    fetchOrders();
    fetchWalletBalance();
  }, [filters, pagination.currentPage]);
  
  // Fetch orders
  const fetchOrders = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Prepare query parameters
      const params = {
        page: pagination.currentPage,
        ...filters
      };
      
      // Fetch orders from API
      const response = await orderAPI.getOrders(params);
      
      // Update state with fetched data
      setOrders(response.data || []);
      
      // Update pagination if available
      if (response.meta) {
        setPagination({
          currentPage: response.meta.current_page,
          totalPages: response.meta.last_page,
          totalItems: response.meta.total
        });
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again later.');
      setLoading(false);
    }
  };
  
  // Fetch wallet balance
  const fetchWalletBalance = async () => {
    try {
      const balance = await orderPaymentService.getWalletBalance();
      setWalletBalance(balance);
    } catch (err) {
      console.error('Error fetching wallet balance:', err);
      // Don't set error state to avoid blocking the UI
    }
  };
  
  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // Reset to first page when filters change
    setPagination(prev => ({
      ...prev,
      currentPage: 1
    }));
  };
  
  // Handle page change
  const handlePageChange = (page) => {
    setPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };
  
  // Handle order response (accept/reject)
  const handleOrderResponse = async (orderId, action) => {
    try {
      await orderAPI.respondToOrder(orderId, { action });
      // Refresh orders after response
      fetchOrders();
    } catch (err) {
      console.error(`Error ${action}ing order:`, err);
      // Show error message
      setError(`Failed to ${action} order. Please try again later.`);
    }
  };
  
  // Handle order completion
  const handleOrderComplete = async (orderId) => {
    try {
      await orderAPI.completeOrder(orderId);
      // Refresh orders after completion
      fetchOrders();
    } catch (err) {
      console.error('Error completing order:', err);
      // Show error message
      setError('Failed to complete order. Please try again later.');
    }
  };
  
  // Handle insufficient balance
  const handleInsufficientBalance = (balanceData) => {
    setInsufficientBalanceData(balanceData);
    setShowInsufficientBalanceModal(true);
  };
  
  // Handle top up credits
  const handleTopUpCredits = () => {
    // Close modal
    setShowInsufficientBalanceModal(false);
    // Navigate to wallet page
    window.location.href = '/wallet';
  };
  
  // If loading
  if (loading && orders.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600">Loading orders...</span>
      </div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm overflow-hidden"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Order Management</h2>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Wallet Balance: <span className="font-medium text-indigo-600">{orderPaymentService.formatCurrency(walletBalance)}</span>
            </div>
            <Link
              to="/wallet"
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Top Up Credits
            </Link>
          </div>
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="px-6 py-4">
          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={fetchOrders}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Filters */}
      <div className="px-6 py-4 border-b border-gray-200">
        <OrderFilters
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </div>
      
      {/* Orders list */}
      <div className="px-6 py-4">
        {orders.length > 0 ? (
          <div className="space-y-4">
            {orders.map((order) => (
              <OrderCard
                key={order.id}
                order={order}
                onAccept={() => handleOrderResponse(order.id, 'accept')}
                onReject={() => handleOrderResponse(order.id, 'reject')}
                onComplete={() => handleOrderComplete(order.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.status || filters.search || filters.date
                ? 'Try changing your filters to see more results.'
                : 'You have no orders yet.'}
            </p>
          </div>
        )}
      </div>
      
      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <button
              type="button"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="text-sm text-gray-600">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>
            <button
              type="button"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage === pagination.totalPages}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
      
      {/* Insufficient Balance Modal */}
      {showInsufficientBalanceModal && (
        <InsufficientBalanceModal
          balanceData={insufficientBalanceData}
          onClose={() => setShowInsufficientBalanceModal(false)}
          onTopUp={handleTopUpCredits}
        />
      )}
    </motion.div>
  );
};

export default OrderManagement;
