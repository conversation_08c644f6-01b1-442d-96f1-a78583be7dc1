import React from 'react';

/**
 * OrderStatusBadge component
 * 
 * This component displays a badge with the order status.
 * 
 * @param {Object} props
 * @param {string} props.status - The order status
 * @param {string} props.className - Additional CSS classes
 */
const OrderStatusBadge = ({ status, className = '' }) => {
  // Define badge styles based on status
  const getBadgeStyles = () => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-indigo-100 text-indigo-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      case 'refunded':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format status for display
  const formatStatus = (status) => {
    return status
      .toLowerCase()
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBadgeStyles()} ${className}`}>
      {formatStatus(status)}
    </span>
  );
};

export default OrderStatusBadge;
