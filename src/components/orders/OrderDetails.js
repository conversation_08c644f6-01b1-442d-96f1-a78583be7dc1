import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import orderAPI from '../../services/orderService';
import orderPaymentService from '../../services/orderPaymentService';
import OrderStatusBadge from './OrderStatusBadge';
import { getCdnUrl } from '../../utils/cdnUtils';
import OrderReviewForm from './OrderReviewForm';

/**
 * OrderDetails component
 * 
 * This component displays detailed information about an order.
 */
const OrderDetails = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  
  // Fetch order details when component mounts
  useEffect(() => {
    fetchOrderDetails();
  }, [orderId]);
  
  // Fetch order details
  const fetchOrderDetails = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await orderAPI.getOrderDetails(orderId);
      setOrder(response.data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching order details:', err);
      setError('Failed to load order details. Please try again later.');
      setLoading(false);
    }
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Handle order response (accept/reject)
  const handleOrderResponse = async (action) => {
    setActionLoading(true);
    
    try {
      await orderAPI.respondToOrder(orderId, { action });
      // Refresh order details
      await fetchOrderDetails();
    } catch (err) {
      console.error(`Error ${action}ing order:`, err);
      setError(`Failed to ${action} order. Please try again later.`);
    } finally {
      setActionLoading(false);
    }
  };
  
  // Handle order completion
  const handleOrderComplete = async () => {
    setActionLoading(true);
    
    try {
      await orderAPI.completeOrder(orderId);
      // Refresh order details
      await fetchOrderDetails();
    } catch (err) {
      console.error('Error completing order:', err);
      setError('Failed to complete order. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };
  
  // Handle review submission
  const handleReviewSubmit = async (reviewData) => {
    setActionLoading(true);
    
    try {
      await orderAPI.createReview(orderId, reviewData);
      // Refresh order details
      await fetchOrderDetails();
      // Hide review form
      setShowReviewForm(false);
    } catch (err) {
      console.error('Error submitting review:', err);
      setError('Failed to submit review. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };
  
  // Get action buttons based on order status
  const getActionButtons = () => {
    if (!order) return null;
    
    switch (order.status.toLowerCase()) {
      case 'pending':
        return (
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={() => handleOrderResponse('accept')}
              disabled={actionLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {actionLoading ? 'Processing...' : 'Accept Order'}
            </button>
            <button
              type="button"
              onClick={() => handleOrderResponse('reject')}
              disabled={actionLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {actionLoading ? 'Processing...' : 'Reject Order'}
            </button>
          </div>
        );
      
      case 'accepted':
      case 'in_progress':
        return (
          <button
            type="button"
            onClick={handleOrderComplete}
            disabled={actionLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {actionLoading ? 'Processing...' : 'Complete Order'}
          </button>
        );
      
      case 'completed':
        // Show review button if no review exists
        if (!order.review) {
          return (
            <button
              type="button"
              onClick={() => setShowReviewForm(true)}
              disabled={actionLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {actionLoading ? 'Processing...' : 'Leave Review'}
            </button>
          );
        }
        return null;
      
      default:
        return null;
    }
  };
  
  // If loading
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600">Loading order details...</span>
      </div>
    );
  }
  
  // If error
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={fetchOrderDetails}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Try Again
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/orders')}
                  className="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Back to Orders
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // If no order found
  if (!order) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Order not found</h3>
          <p className="mt-1 text-sm text-gray-500">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <div className="mt-6">
            <Link
              to="/orders"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Back to Orders
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm overflow-hidden"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex flex-wrap justify-between items-center">
          <div className="flex items-center space-x-3">
            <Link
              to="/orders"
              className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <h2 className="text-xl font-bold text-gray-900">Order #{order.id}</h2>
            <OrderStatusBadge status={order.status} />
          </div>
          <div className="mt-2 sm:mt-0">
            {getActionButtons()}
          </div>
        </div>
      </div>
      
      {/* Order details */}
      <div className="px-6 py-4">
        <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Order Information</h3>
            <dl className="mt-3 space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Service</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.service_name || 'N/A'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Price</dt>
                <dd className="mt-1 text-sm text-gray-900">{orderPaymentService.formatCurrency(order.price || 0)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Quantity</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.quantity || 1}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Total</dt>
                <dd className="mt-1 text-sm font-medium text-indigo-600">{orderPaymentService.formatCurrency((order.price || 0) * (order.quantity || 1))}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Created At</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(order.created_at)}</dd>
              </div>
              {order.scheduled_at && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Scheduled Time</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(order.scheduled_at)}</dd>
                </div>
              )}
            </dl>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900">Talent Information</h3>
            <div className="mt-3 flex items-center space-x-3">
              <img
                src={getCdnUrl(order.talent?.profile_image || '/images/default-avatar.jpg')}
                alt={order.talent?.name || 'Talent'}
                className="h-12 w-12 rounded-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://via.placeholder.com/60?text=User';
                }}
              />
              <div>
                <h4 className="text-sm font-medium text-gray-900">{order.talent?.name || 'Unknown Talent'}</h4>
                <p className="text-sm text-gray-500">{order.talent?.title || 'Talent'}</p>
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900">Order Details</h3>
              <div className="mt-3 space-y-3">
                {order.description && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Description</dt>
                    <dd className="mt-1 text-sm text-gray-900">{order.description}</dd>
                  </div>
                )}
                {order.notes && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Notes</dt>
                    <dd className="mt-1 text-sm text-gray-900">{order.notes}</dd>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Review section */}
        {order.review && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Review</h3>
            <div className="mt-3 bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg
                      key={star}
                      className={`h-5 w-5 ${star <= order.review.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <span className="ml-2 text-sm text-gray-600">
                  {formatDate(order.review.created_at)}
                </span>
              </div>
              <p className="text-sm text-gray-700">{order.review.comment}</p>
            </div>
          </div>
        )}
        
        {/* Review form */}
        {showReviewForm && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <OrderReviewForm
              onSubmit={handleReviewSubmit}
              onCancel={() => setShowReviewForm(false)}
              loading={actionLoading}
            />
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default OrderDetails;
