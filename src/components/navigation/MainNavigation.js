import React, { useEffect, useState, useCallback, useMemo, Suspense } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import useTranslation from "../../hooks/useTranslation";
import { motion, AnimatePresence } from "framer-motion";
import { useNotifications } from "../../context/NotificationContext";
import { useAuth } from "../../contexts/AuthContext";
import { FaCoins } from 'react-icons/fa';
import { FaGlobe } from 'react-icons/fa';
// Import lazy components to allow prefetching
import * as LazyComponents from '../../lazyComponents';

const Settings = React.lazy(() => import("../Settings"));
const NotificationDropdown = React.lazy(() => import("../notifications/NotificationDropdown"));

// Use absolute path so logo loads on any route
const LOGO_PATH = "/missionx-logo.png";

// NotificationBell component for improved UI/UX
const NotificationBell = ({ count, onClick, isOpen }) => {
  // Animation for bell shake on click
  const [isShaking, setIsShaking] = useState(false);
  // Animation for bell ring on new notification
  const [isRinging, setIsRinging] = useState(false);

  // Track previous count to detect new notifications
  const [prevCount, setPrevCount] = useState(count);
  const [badgeBounce, setBadgeBounce] = useState(false);

  // Bell shake on click
  useEffect(() => {
    if (isShaking) {
      const timeout = setTimeout(() => setIsShaking(false), 400);
      return () => clearTimeout(timeout);
    }
  }, [isShaking]);

  // Badge bounce on count change
  useEffect(() => {
    if (count !== prevCount) {
      setBadgeBounce(true);
      setPrevCount(count);
      const timeout = setTimeout(() => setBadgeBounce(false), 400);
      return () => clearTimeout(timeout);
    }
  }, [count, prevCount]);

  // Bell ring on new notification (count increases)
  useEffect(() => {
    if (count > prevCount) {
      setIsRinging(true);
      const timeout = setTimeout(() => setIsRinging(false), 700);
      return () => clearTimeout(timeout);
    }
  }, [count, prevCount]);

  return (
    <motion.button
      onClick={e => {
        setIsShaking(true);
        onClick(e);
      }}
      className={`relative w-12 h-12 md:w-10 md:h-10 rounded-full flex items-center justify-center bg-indigo-50 hover:bg-indigo-100 dark:bg-gradient-to-br dark:from-gray-800 dark:to-gray-900 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition group select-none ${isShaking ? 'animate-shake' : ''} ${isRinging ? 'animate-bell-ring' : ''}`}
      aria-label={`Notifications${count > 0 ? `: ${count} unread` : ''}`}
      tabIndex={0}
      type="button"
      style={{ WebkitTapHighlightColor: 'transparent' }}
    >
      <motion.svg
        className="w-7 h-7 md:w-5 md:h-5 text-indigo-600 dark:text-indigo-400 transition-transform duration-200 group-hover:scale-110"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        initial={false}
        animate={isShaking ? { rotate: [0, -15, 15, -10, 10, -5, 5, 0] } : { rotate: 0 }}
        transition={{ duration: 0.4 }}
        aria-hidden="true"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
        />
      </motion.svg>
      <AnimatePresence>
        {count > 0 && (
          <motion.span
            key="badge"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className={`absolute -top-1 -right-1 min-w-[20px] h-5 px-1 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center shadow ${badgeBounce ? 'animate-bounce' : ''} dark:bg-pink-600 dark:text-white dark:shadow-pink-900/30`}
            aria-live="polite"
          >
            {count > 99 ? '99+' : count}
          </motion.span>
        )}
      </AnimatePresence>
    </motion.button>
  );
};

const MainNavigation = ({ activeItem }) => {
  // Combine UI states into a single object
  const [uiState, setUiState] = useState({
    showSettings: false,
    showNotifications: false,
    isScrolled: false
  });

  // Only subscribe to necessary context values
  const { isAuthenticated, user, loading: userLoading } = useAuth();
  const { notifications, getChatUnreadCount } = useNotifications();
  const { i18n } = useTranslation();
  const languageList = ['en', 'ms', 'cn'];
  const languageNames = { en: 'English', ms: 'Malay', cn: '中文' };
  const currentLang = i18n.language && languageList.includes(i18n.language) ? i18n.language : 'en';
  const nextLang = languageList[(languageList.indexOf(currentLang) + 1) % languageList.length];
  const handleLanguageChange = () => {
    i18n.changeLanguage(nextLang);
  };
  const chatUnreadCount = getChatUnreadCount ? getChatUnreadCount() : (notifications || []).filter(n => (n.data?.type === 'chat_message' || n.category === 'chat') && !n.read).length;
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation(["common"]);

  // Memoize navigation items to prevent unnecessary re-renders
  const navigationItems = useMemo(() => [
    { path: "/", label: t("common:navigation.home") },
    { path: "/talent", label: t("common:navigation.talent") },
    { path: "/explore", label: t("common:navigation.explore") },
    { path: "/chat", label: t("common:navigation.chat") },
    { path: "/profile", label: t("common:navigation.profile") }
  ], [t]);

  // Memoize active state check
  const isActive = useCallback((path) => {
    return location.pathname === path;
  }, [location.pathname]);

  // Memoize navigation click handler
  const handleNavClick = useCallback((path, e) => {
    e?.preventDefault();
    if (!isAuthenticated && path !== '/') {
      navigate('/login');
    } else {
      navigate(path);
    }
  }, [isAuthenticated, navigate]);

  // Memoize UI state handlers
  const toggleSettings = useCallback(() => {
    setUiState(prev => ({ ...prev, showSettings: !prev.showSettings }));
  }, []);

  const toggleNotifications = useCallback(() => {
    setUiState(prev => ({ ...prev, showNotifications: !prev.showNotifications }));
  }, []);

  // Prefetch page bundles when hovering navigation items
  const prefetchPage = useCallback((path) => {
    if (path === '/') {
      LazyComponents.LazyHome?.preload?.();
    } else if (path === '/talent') {
      LazyComponents.LazyTalent?.preload?.();
    } else if (path === '/chat') {
      LazyComponents.LazyChat?.preload?.();
    } else if (path === '/explore') {
      LazyComponents.LazyExplore?.preload?.();
    } else if (path === '/profile') {
      LazyComponents.LazyProfile?.preload?.();
    }
  }, []);

  // Optimize scroll handler with useCallback and throttling
  const handleScroll = useCallback(() => {
    const isScrolled = window.scrollY > 20;
    setUiState(prev => {
      if (prev.isScrolled !== isScrolled) {
        return { ...prev, isScrolled };
      }
      return prev;
    });
  }, []);

  // Effect for scroll handling with throttling
  useEffect(() => {
    let timeoutId;
    const throttledScroll = () => {
      if (timeoutId) return;
      timeoutId = setTimeout(() => {
        handleScroll();
        timeoutId = null;
      }, 100); // Throttle to 100ms
    };

    window.addEventListener("scroll", throttledScroll, { passive: true });
    return () => {
      window.removeEventListener("scroll", throttledScroll);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [handleScroll]);

  // Prefetch heavy pages to improve perceived navigation speed
  useEffect(() => {
    // Prefetch frequently visited pages in the background
    LazyComponents.LazyHome?.preload?.();
    LazyComponents.LazyTalent?.preload?.();
    LazyComponents.LazyChat?.preload?.();
    LazyComponents.LazyExplore?.preload?.();
    LazyComponents.LazyProfile?.preload?.();
  }, []);

  // Memoize wallet display component
  const WalletDisplay = useMemo(() => (
    isAuthenticated ? (
      <Link
        to="/wallet"
        onClick={(e) => handleNavClick('/wallet', e)}
        className="flex items-center bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-800 dark:to-blue-900 px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-blue-100 dark:hover:from-indigo-700 dark:hover:to-blue-800 group border border-indigo-100/50 dark:border-indigo-700/40 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 relative"
      >
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 dark:from-indigo-700 dark:to-blue-800 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-sm dark:shadow-indigo-900/20">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <div className="flex flex-col">
          <span className="text-xs text-indigo-500 dark:text-indigo-200 text-right leading-none font-medium">
            {t("common:wallet.creditsBalance")}
          </span>
          {userLoading ? (
            <span className="inline-block w-16 h-6 bg-gray-200 dark:bg-gray-800 rounded animate-pulse" />
          ) : (
            <span className="font-semibold text-indigo-800 dark:text-white text-lg">
              {(user && typeof user.credits_balance === 'number') ? user.credits_balance.toLocaleString() : 0}
            </span>
          )}
        </div>
      </Link>
    ) : (
      <>
        <Link
          to="/topup"
          onClick={(e) => {
            e.preventDefault();
            navigate('/topup');
          }}
          className="flex items-center bg-gradient-to-r from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-900 px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-gradient-to-r hover:from-yellow-200 hover:to-yellow-300 dark:hover:from-yellow-800 dark:hover:to-yellow-900 group border border-yellow-200/50 dark:border-yellow-700/40 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 relative mr-2"
        >
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-500 dark:from-yellow-500 dark:to-yellow-700 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-sm dark:shadow-yellow-900/20">
            <FaCoins className="w-4 h-4 text-white dark:text-yellow-300" />
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-semibold text-yellow-700 dark:text-yellow-200 leading-none">
              Top Up
            </span>
          </div>
        </Link>
        <Link
          to="/login"
          onClick={(e) => {
            e.preventDefault();
            navigate('/login');
          }}
          className="flex items-center bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-800 dark:to-blue-900 px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-blue-100 dark:hover:from-indigo-700 dark:hover:to-blue-800 group border border-indigo-100/50 dark:border-indigo-700/40 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 relative"
        >
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 dark:from-indigo-700 dark:to-blue-800 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-sm dark:shadow-indigo-900/20">
            <svg className="w-4 h-4 text-white dark:text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div className="flex flex-col">
            <span className="text-sm text-indigo-700 dark:text-indigo-50 leading-none font-semibold">
              {t("common:navigation.loginSignup")}
            </span>
          </div>
        </Link>
      </>
    )
  ), [isAuthenticated, user, userLoading, t, handleNavClick, navigate]);

  return (
    <>
      <nav
        className={`relative z-50 bg-white/70 dark:bg-[#18192a]/80 backdrop-blur-xl border-b border-indigo-100/40 dark:border-indigo-900/60 shadow-[0_8px_32px_0_rgba(24,25,38,0.18)] sticky top-0 w-full transition-all duration-300 ${
          uiState.isScrolled ? "shadow-xl" : "shadow-lg"
        }l`}
        id="main-nav"
      >
        {/* Pink-indigo gradient accent at the bottom */}
        {/* <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-pink-400 via-purple-400 to-indigo-400 opacity-40 pointer-events-none z-10" /> */}
        <div className="max-w-screen-2xl mx-auto px-8 w-full relative z-20 flex justify-between items-center py-3">
          {/* Logo */}
          <div className="flex items-center">
            <Link
              to="/"
              className="text-xl font-brand text-gray-800 dark:text-gray-100 transform transition-all hover:scale-105 duration-300 relative group"
            >
              <img
                src={LOGO_PATH}
                alt="Mission X"
                className="h-9 w-auto shadow-xs"
                loading="eager"
                fetchpriority="high"
              />
            </Link>
            <div className="text-xl ml-3 font-bold text-gray-800 dark:text-indigo-50 transform transition-all hover:scale-105 duration-300 relative group hidden md:block tracking-tight drop-shadow-md">
              Mission X
            </div>
          </div>

          {/* Navigation Links */}
          <nav aria-label="Main Navigation">
            <ul className="hidden md:flex space-x-6 list-none">
              {navigationItems.map((item) => (
                <li key={item.path}>
                <Link
                    to={item.path}
                    onClick={(e) => handleNavClick(item.path, e)}
                    onMouseEnter={() => prefetchPage(item.path)}
                    className={`
                      font-sans font-semibold text-base transition-all duration-200 relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 px-3 py-2
                      ${isActive(item.path)
                        ? "text-indigo-700 dark:text-indigo-50 bg-white/80 dark:bg-gradient-to-r dark:from-indigo-900 dark:to-violet-900 border border-indigo-100/40 dark:border-indigo-800/60 rounded-xl"
                        : "text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 bg-white/60 dark:bg-gray-900/60 hover:bg-white/80 dark:hover:bg-gray-800/80 border border-transparent hover:border-indigo-200 dark:hover:border-indigo-700 rounded-xl"
                      }`}
                    aria-current={isActive(item.path) ? "page" : undefined}
                >
                  <span className="relative z-10">
                      {item.label}
                  </span>
                  {isActive(item.path) && (
                    <span className="absolute bottom-1 left-1/2 -translate-x-1/2 w-2/3 h-1 bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-full opacity-80" />
                  )}
                </Link>
              </li>
              ))}
            </ul>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-3">
            {/* Become a Talent Button (desktop only, unauthenticated only) */}
            {!isAuthenticated && (
              <button
                onClick={() => navigate('/become-talent')}
                className="hidden md:inline-flex items-center bg-white/80 dark:bg-gradient-to-r dark:from-indigo-900 dark:to-violet-900 px-5 py-2.5 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white/90 dark:hover:bg-indigo-900/90 group border border-indigo-100/40 dark:border-indigo-800/60 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 relative mr-2 font-sans font-semibold text-base"
              >
                <div className="w-9 h-9 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 dark:from-indigo-700 dark:to-blue-800 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-md dark:shadow-indigo-900/20">
                  <svg className="w-5 h-5 text-white dark:text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm text-indigo-700 dark:text-indigo-50 leading-none font-semibold drop-shadow font-sans">
                    Become a Talent
                  </span>
                </div>
              </button>
            )}
            {/* Wallet Display */}
            {WalletDisplay}

            {/* Notifications */}
            <div className="relative">
              {isAuthenticated && (
                <>
                  <NotificationBell
                    count={chatUnreadCount}
                    onClick={toggleNotifications}
                    isOpen={uiState.showNotifications}
                  />
                  <Suspense fallback={null}>
                    <NotificationDropdown
                      isOpen={uiState.showNotifications}
                      onClose={() => setUiState(prev => ({ ...prev, showNotifications: false }))}
                      onToggle={toggleNotifications}
                    />
                  </Suspense>
                </>
              )}
            </div>

            {/* Settings or Language Picker */}
            {isAuthenticated ? (
            <motion.button
              onClick={toggleSettings}
              className="relative w-10 h-10 rounded-full bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center hover:from-indigo-100 hover:to-purple-100 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-sm hover:shadow-md group font-sans"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg
                className="w-5 h-5 text-indigo-600 dark:text-indigo-400 relative z-10 transition-transform duration-300 group-hover:rotate-90"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </motion.button>
            ) : (
              <button
                onClick={handleLanguageChange}
                className="relative flex items-center justify-center w-10 h-10 rounded-2xl bg-white/70 dark:bg-gray-900/70 border border-indigo-100/40 dark:border-indigo-800/60 px-3 py-2 font-sans font-semibold text-base text-indigo-700 dark:text-indigo-200 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                aria-label={`Change language. Current: ${languageNames[currentLang]}`}
                title={languageNames[currentLang]}
                type="button"
              >
                <FaGlobe className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                <span className="absolute left-1/2 -translate-x-1/2 top-full mt-2 bg-white/90 dark:bg-gray-900/90 border border-indigo-100/40 dark:border-indigo-800/60 text-indigo-700 dark:text-indigo-200 text-xs font-semibold px-3 py-1 rounded-full shadow pointer-events-none select-none z-20">
                  {languageNames[currentLang]}
                </span>
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* Settings Modal */}
      {uiState.showSettings && (
        <Suspense fallback={null}>
          <Settings
            isOpen={uiState.showSettings}
            onClose={() => setUiState(prev => ({ ...prev, showSettings: false }))}
          />
        </Suspense>
      )}
    </>
  );
};

export default React.memo(MainNavigation);