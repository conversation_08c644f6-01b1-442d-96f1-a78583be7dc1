import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useTranslation from "../../hooks/useTranslation";
import { motion, AnimatePresence } from "framer-motion";
import walletAPI from "../../services/walletService";
import { useAuth } from "../../contexts/AuthContext";

// Update logo path to use In-AppAssets folder
const LOGO_PATH = "/In-AppAssets/Copy of MX Logo .png";

const MobileNavigation = ({ activeItem }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [isBalanceLoading, setIsBalanceLoading] = useState(true);
  const navigate = useNavigate();
  const { t } = useTranslation(["common"]);
  const { isAuthenticated } = useAuth();

  // Effect for fetching wallet data
  useEffect(() => {
    if (!isAuthenticated) {
      setIsBalanceLoading(false);
      return;
    }

    const fetchWalletData = async () => {
      try {
        setIsBalanceLoading(true);
        const walletResponse = await walletAPI.getBalance();
        setWalletBalance(walletResponse.data.credits_balance);
      } catch (err) {
        console.error("Error fetching wallet data:", err);
      } finally {
        setIsBalanceLoading(false);
      }
    };

    fetchWalletData();
  }, [isAuthenticated]);

  const isActive = (path) => {
    return activeItem === path;
  };

  const handleNavClick = (path, event) => {
    event?.preventDefault();
    setIsOpen(false);
    
    // Haptic feedback simulation
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }
    
    if (!isAuthenticated && path !== '/home') {
      navigate('/login');
    } else {
      navigate(path);
    }
  };

  const menuItems = [
    {
      path: "/home",
      label: t("common:navigation.home"),
      icon: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6",
      gradient: "from-violet-500 to-purple-600",
    },
    {
      path: "/talent",
      label: t("common:navigation.talent"),
      icon: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
      gradient: "from-emerald-500 to-teal-600",
    },
    {
      path: "/explore",
      label: t("common:navigation.explore"),
      icon: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
      gradient: "from-orange-500 to-red-600",
    },
    {
      path: "/chat",
      label: t("common:navigation.chat"),
      icon: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",
      gradient: "from-blue-500 to-indigo-600",
    },
    {
      path: "/profile",
      label: t("common:navigation.profile"),
      icon: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",
      gradient: "from-pink-500 to-rose-600",
    },
  ];

  const springTransition = {
    type: "spring",
    stiffness: 400,
    damping: 30,
  };

  return (
    <>
      {/* Enhanced Mobile Navigation Bar */}
      <motion.nav 
        className="fixed inset-x-0 bottom-0 z-30 mx-3 mb-3 md:hidden"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ ...springTransition, delay: 0.2 }}
      >
        <div 
          className="flex justify-around items-center h-20 px-2 rounded-3xl shadow-2xl shadow-black/10 bg-transparent"
          style={{
            background: "rgba(255, 255, 255, 0.15)",
            backdropFilter: "blur(40px)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        >
          {menuItems.map((item, index) => (
            <motion.button
              key={item.path}
              onClick={() => handleNavClick(item.path)}
              className="flex flex-col items-center justify-center w-full h-full relative group focus:outline-none rounded-2xl transition-all duration-500 bg-transparent"
              aria-current={isActive(item.path) ? "page" : undefined}
              whileTap={{ scale: 0.9 }}
              whileHover={{ scale: 1.05 }}
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ ...springTransition, delay: 0.1 * index }}
            >
              {/* Active background with morphing effect */}
              <AnimatePresence>
                {isActive(item.path) && (
                  <motion.div
                    layoutId="activeBackground"
                    className={`absolute inset-0 bg-gradient-to-br ${item.gradient} rounded-2xl opacity-20`}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 0.2 }}
                    exit={{ scale: 0.8, opacity: 0 }}
                    transition={springTransition}
                  />
                )}
              </AnimatePresence>

              {/* Icon container with floating effect */}
              <motion.div 
                className="relative mb-1"
                animate={{ y: isActive(item.path) ? -2 : 0 }}
                transition={springTransition}
              >
                <div className={`relative ${isActive(item.path) ? 'transform' : ''}`}>
                  <svg
                    className={`w-7 h-7 transition-all duration-500 ${
                      isActive(item.path) 
                        ? "text-white" 
                        : "text-slate-600 group-hover:text-slate-800"
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    strokeWidth={isActive(item.path) ? "2.5" : "2"}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d={item.icon}
                    />
                  </svg>
                  
                  {/* Glowing dot indicator */}
                  {isActive(item.path) && (
                    <motion.div
                      className={`absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br ${item.gradient} rounded-full`}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={springTransition}
                    >
                      <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} rounded-full animate-ping opacity-40`}></div>
                    </motion.div>
                  )}
                </div>
              </motion.div>

              {/* Label with enhanced typography */}
              <motion.span
                className={`text-xs font-medium transition-all duration-500 ${
                  isActive(item.path) 
                    ? "text-slate-800 font-semibold" 
                    : "text-slate-600 group-hover:text-slate-800"
                }`}
                animate={{ scale: isActive(item.path) ? 1.05 : 1 }}
                transition={springTransition}
              >
                {item.label}
              </motion.span>

              {/* Ripple effect on tap */}
              <motion.div
                className="absolute inset-0 rounded-2xl bg-white/20 bg-transparent"
                initial={{ scale: 0, opacity: 0 }}
                whileTap={{ scale: 1.2, opacity: 0.3 }}
                transition={{ duration: 0.2 }}
                style={{ backgroundColor: "rgba(255, 255, 255, 0.2)" }}
              />
            </motion.button>
          ))}
        </div>
      </motion.nav>

      {/* Enhanced Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 md:hidden"
            style={{ backdropFilter: "blur(20px)" }}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-transparent"
              style={{ background: "linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(88, 28, 135, 0.5) 50%, rgba(30, 58, 138, 0.8) 100%)" }}
              onClick={() => setIsOpen(false)}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />

            {/* Slide-in Panel */}
            <motion.div
              initial={{ x: "100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "100%", opacity: 0 }}
              transition={{ ...springTransition, stiffness: 300 }}
              className="absolute right-0 top-0 bottom-0 w-80 max-w-[85vw]"
              style={{
                background: "rgba(255, 255, 255, 0.95)",
                backdropFilter: "blur(40px)",
                borderLeft: "1px solid rgba(255, 255, 255, 0.2)",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with gradient */}
              <div className="relative p-6 bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <img 
                      src={LOGO_PATH}
                      alt="MissionX Logo" 
                      className="h-8 w-auto mr-3"
                    />
                    <motion.h2 
                      className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent"
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      Menu
                    </motion.h2>
                  </div>
                  <motion.button
                    onClick={() => setIsOpen(false)}
                    className="p-3 rounded-2xl backdrop-blur-sm border border-white/20 hover:bg-white/70 transition-all duration-300 group bg-transparent"
                    style={{ backgroundColor: "rgba(255, 255, 255, 0.5)" }}
                    whileTap={{ scale: 0.9 }}
                    whileHover={{ scale: 1.05 }}
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <svg
                      className="w-6 h-6 text-slate-600 group-hover:text-slate-800 transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </motion.button>
                </div>
              </div>

              {/* Enhanced Wallet Section */}
              <motion.div 
                className="mx-6 mb-8 p-6 rounded-3xl shadow-lg bg-transparent"
                style={{
                  background: "linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%)",
                  border: "1px solid rgba(99, 102, 241, 0.2)",
                }}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {isAuthenticated ? (
                  <div className="flex items-center">
                    <motion.div 
                      className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 flex items-center justify-center mr-4 shadow-lg shadow-indigo-500/25"
                      whileHover={{ scale: 1.05, rotate: 5 }}
                      transition={springTransition}
                    >
                      <svg
                        className="w-8 h-8 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </motion.div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-indigo-600/80 mb-1">
                        {t("common:wallet.creditsBalance")}
                      </p>
                      <div className="flex items-center">
                        {isBalanceLoading ? (
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                            <div className="w-3 h-3 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-3 h-3 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        ) : (
                          <motion.p 
                            className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent"
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={springTransition}
                          >
                            {walletBalance.toLocaleString()}
                          </motion.p>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <motion.button
                    onClick={() => handleNavClick('/login')}
                    className="w-full py-4 px-6 text-white font-semibold rounded-2xl shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30 transition-all duration-300 bg-transparent"
                    style={{ background: "linear-gradient(135deg, #4f46e5 0%, #9333ea 100%)" }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Login / Signup
                  </motion.button>
                )}
              </motion.div>

              {/* Enhanced Navigation Links */}
              <nav className="px-6 space-y-2">
                {menuItems.map((item, index) => (
                  <motion.a
                    key={item.path}
                    href={item.path}
                    onClick={(e) => handleNavClick(item.path, e)}
                    className={`flex items-center px-6 py-4 rounded-2xl transition-all duration-300 group relative overflow-hidden bg-transparent ${
                      isActive(item.path)
                        ? "backdrop-blur-sm border border-white/30 shadow-lg"
                        : "hover:backdrop-blur-sm"
                    }`}
                    style={{
                      backgroundColor: isActive(item.path) 
                        ? "rgba(255, 255, 255, 0.6)" 
                        : "transparent"
                    }}
                    initial={{ x: 50, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 + index * 0.1, ...springTransition }}
                    whileHover={{ 
                      scale: 1.02,
                      backgroundColor: isActive(item.path) 
                        ? "rgba(255, 255, 255, 0.6)" 
                        : "rgba(255, 255, 255, 0.3)"
                    }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* Gradient background for active item */}
                    {isActive(item.path) && (
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-r ${item.gradient} opacity-10 rounded-2xl`}
                        layoutId="menuActiveBackground"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 0.1 }}
                        transition={springTransition}
                      />
                    )}

                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 bg-transparent ${
                      isActive(item.path) 
                        ? "shadow-lg" 
                        : "group-hover:bg-white/70"
                    }`}
                    style={{
                      background: isActive(item.path) 
                        ? `linear-gradient(135deg, ${item.gradient.includes('violet') ? '#8b5cf6, #a855f7' : 
                            item.gradient.includes('emerald') ? '#10b981, #0d9488' :
                            item.gradient.includes('orange') ? '#f97316, #dc2626' :
                            item.gradient.includes('blue') ? '#3b82f6, #4f46e5' :
                            '#ec4899, #f43f5e'})`
                        : "rgba(255, 255, 255, 0.5)"
                    }}>
                      <svg
                        className={`w-6 h-6 transition-all duration-300 ${
                          isActive(item.path) ? "text-white" : "text-slate-600 group-hover:text-slate-800"
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        strokeWidth={isActive(item.path) ? "2.5" : "2"}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d={item.icon}
                        />
                      </svg>
                    </div>
                    
                    <span className={`text-lg font-medium transition-all duration-300 ${
                      isActive(item.path) 
                        ? "text-slate-800 font-semibold" 
                        : "text-slate-600 group-hover:text-slate-800"
                    }`}>
                      {item.label}
                    </span>

                    {/* Active indicator */}
                    {isActive(item.path) && (
                      <motion.div
                        className={`ml-auto w-2 h-2 bg-gradient-to-br ${item.gradient} rounded-full`}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={springTransition}
                      >
                        <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} rounded-full animate-ping opacity-40`}></div>
                      </motion.div>
                    )}
                  </motion.a>
                ))}
              </nav>

              {/* Footer decoration */}
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileNavigation;