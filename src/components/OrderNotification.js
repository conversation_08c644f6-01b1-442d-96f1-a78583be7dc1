import React, { useState, useEffect } from 'react';
import axios from 'axios';

const OrderNotification = ({ order, onResponded }) => {
    const [timeLeft, setTimeLeft] = useState(() => {
        const expiresAt = new Date(order.expires_at).getTime();
        const now = new Date().getTime();
        return Math.max(0, Math.floor((expiresAt - now) / 1000));
    });
    const [status, setStatus] = useState('pending');
    const [isResponding, setIsResponding] = useState(false);

    useEffect(() => {
        if (timeLeft <= 0 || status !== 'pending') return;

        const timer = setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    setStatus('expired');
                    onResponded(order.order_id, 'expired');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [timeLeft, status, order.order_id, onResponded]);

    const handleResponse = async (action) => {
        if (isResponding) return;
        setIsResponding(true);

        try {
            await axios.post(
                `${process.env.REACT_APP_API_URL}/orders/${order.order_id}/respond`,
                { action },
                { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
            );
            const response = await axios.get(
                `${process.env.REACT_APP_API_URL}/orders/${order.order_id}`,
                { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
            );
            const newStatus = response.data.data.status;
            setStatus(newStatus);
            onResponded(order.order_id, newStatus);
        } catch (error) {
            console.error('Error responding to order:', error);
            alert('Failed to respond to order');
        } finally {
            setIsResponding(false);
        }
    };

    return (
        <div style={{ border: '1px solid #ccc', padding: '20px', margin: '10px', textAlign: 'center' }}>
            {status === 'pending' && timeLeft > 0 ? (
                <>
                    <h3>New Order #{order.order_id} - {order.service_name}</h3>
                    <p>Time left to respond: {timeLeft}s</p>
                    <button
                        onClick={() => handleResponse('accept')}
                        disabled={isResponding}
                        style={{
                            marginRight: '10px',
                            padding: '10px',
                            background: isResponding ? 'gray' : 'green',
                            color: 'white',
                            border: 'none',
                            cursor: isResponding ? 'not-allowed' : 'pointer',
                        }}
                    >
                        Accept
                    </button>
                    <button
                        onClick={() => handleResponse('reject')}
                        disabled={isResponding}
                        style={{
                            padding: '10px',
                            background: isResponding ? 'gray' : 'red',
                            color: 'white',
                            border: 'none',
                            cursor: isResponding ? 'not-allowed' : 'pointer',
                        }}
                    >
                        Reject
                    </button>
                </>
            ) : status === 'accepted' ? (
                <h3>Order #{order.order_id} Accepted!</h3>
            ) : status === 'rejected' ? (
                <h3>Order #{order.order_id} Rejected</h3>
            ) : (
                <h3>Order #{order.order_id} Expired</h3>
            )}
        </div>
    );
};

export default OrderNotification;


// import React, { useState, useEffect } from 'react';

// const OrderNotification = ({ order, onResponded }) => {
//     const [timeLeft, setTimeLeft] = useState(() => {
//         const expiresAt = new Date(order.expires_at).getTime();
//         const now = new Date().getTime();
//         return Math.max(0, Math.floor((expiresAt - now) / 1000));
//     });
//     const [status, setStatus] = useState('pending');
//     const [isResponding, setIsResponding] = useState(false);

//     useEffect(() => {
//         if (timeLeft <= 0 || status !== 'pending') return;

//         const timer = setInterval(() => {
//             setTimeLeft((prev) => {
//                 if (prev <= 1) {
//                     setStatus('expired');
//                     onResponded(order.order_id, 'expired');
//                     return 0;
//                 }
//                 return prev - 1;
//             });
//         }, 1000);

//         return () => clearInterval(timer);
//     }, [timeLeft, status, order.order_id, onResponded]);

//     const handleResponse = (action) => {
//         if (isResponding) return;
//         setIsResponding(true);

//         setTimeout(() => {
//             const newStatus = action === 'accept' ? 'accepted' : 'rejected';
//             setStatus(newStatus);
//             onResponded(order.order_id, newStatus);
//             setIsResponding(false);
//         }, 1000); // Simulate API delay
//     };

//     return (
//         <div style={{ border: '1px solid #ccc', padding: '20px', margin: '10px', textAlign: 'center' }}>
//             {status === 'pending' && timeLeft > 0 ? (
//                 <>
//                     <h3>New Order #{order.order_id} - {order.service_name}</h3>
//                     <p>Time left to respond: {timeLeft}s</p>
//                     <button
//                         onClick={() => handleResponse('accept')}
//                         disabled={isResponding}
//                         style={{
//                             marginRight: '10px',
//                             padding: '10px',
//                             background: isResponding ? 'gray' : 'green',
//                             color: 'white',
//                             border: 'none',
//                             cursor: isResponding ? 'not-allowed' : 'pointer',
//                         }}
//                     >
//                         Accept
//                     </button>
//                     <button
//                         onClick={() => handleResponse('reject')}
//                         disabled={isResponding}
//                         style={{
//                             padding: '10px',
//                             background: isResponding ? 'gray' : 'red',
//                             color: 'white',
//                             border: 'none',
//                             cursor: isResponding ? 'not-allowed' : 'pointer',
//                         }}
//                     >
//                         Reject
//                     </button>
//                 </>
//             ) : status === 'accepted' ? (
//                 <h3>Order #{order.order_id} Accepted!</h3>
//             ) : status === 'rejected' ? (
//                 <h3>Order #{order.order_id} Rejected</h3>
//             ) : (
//                 <h3>Order #{order.order_id} Expired</h3>
//             )}
//         </div>
//     );
// };

// export default OrderNotification;
