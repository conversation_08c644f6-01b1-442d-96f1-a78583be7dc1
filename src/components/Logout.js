// Logout.js
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiLogOut, FiX, FiCheck, FiAlertTriangle } from 'react-icons/fi';
import useTranslation from '../hooks/useTranslation';

const LogoutConfirmation = ({ 
  isOpen, 
  onClose, 
  onLogout, 
  onSettingsClose,
  userInfo = null // Optional user info for personalization
}) => {
  const { t } = useTranslation('settings');
  const modalRef = useRef();
  const [logoutState, setLogoutState] = useState('idle'); // idle, loading, success, error
  const [error, setError] = useState(null);
  const [countdown, setCountdown] = useState(null);

  // Focus trap for accessibility
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setLogoutState('idle');
      setError(null);
      setCountdown(null);
    }
  }, [isOpen]);

  // Auto-close countdown after successful logout
  useEffect(() => {
    if (logoutState === 'success' && countdown === null) {
      setCountdown(3);
      const interval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            handleNavigation();
            return null;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [logoutState]);

  const handleNavigation = useCallback(() => {
    // Smooth transition before navigation
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        // Store a flag to show a welcome message on return
        sessionStorage.setItem('justLoggedOut', 'true');
        window.location.href = '/';
      }
    }, 300);
  }, []);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape' && logoutState !== 'loading') onClose();
    if (e.key === 'Enter' && document.activeElement === modalRef.current && logoutState === 'idle') {
      handleLogout();
    }
  };

  const handleLogout = async () => {
    if (logoutState === 'loading') return;
    
    setLogoutState('loading');
    setError(null);
    
    try {
      // Show immediate feedback
      await new Promise(resolve => setTimeout(resolve, 800)); // Minimum loading time for UX
      
      // Call the logout function
      await onLogout();
      
      setLogoutState('success');
      
      // Close modals with slight delay for smooth animation
      setTimeout(() => {
        onClose();
        if (onSettingsClose) onSettingsClose();
      }, 500);
      
    } catch (err) {
      console.error('Logout failed:', err);
      setError(err.message || 'Logout failed. Please try again.');
      setLogoutState('error');
      
      // Auto-retry or reset after error
      setTimeout(() => {
        if (logoutState === 'error') {
          setLogoutState('idle');
          setError(null);
        }
      }, 4000);
    }
  };

  const getStateContent = () => {
    switch (logoutState) {
      case 'loading':
        return {
          icon: <div className="w-8 h-8 border-3 border-blue-200 border-t-blue-500 rounded-full animate-spin" />,
          iconBg: 'from-blue-100 to-indigo-100/50 dark:from-blue-900 dark:to-indigo-900/40',
          title: t('general.logout.processing') || 'Signing you out...',
          description: t('general.logout.processingDesc') || 'Securing your session and clearing data'
        };
      case 'success':
        return {
          icon: <FiCheck className="w-8 h-8 text-green-500 dark:text-green-400" />,
          iconBg: 'from-green-100 to-emerald-100/50 dark:from-green-900 dark:to-emerald-900/40',
          title: t('general.logout.success') || 'Successfully signed out!',
          description: countdown 
            ? `Redirecting to home in ${countdown}...`
            : (t('general.logout.successDesc') || 'You have been safely logged out')
        };
      case 'error':
        return {
          icon: <FiAlertTriangle className="w-8 h-8 text-amber-500 dark:text-amber-400" />,
          iconBg: 'from-amber-100 to-orange-100/50 dark:from-amber-900 dark:to-orange-900/40',
          title: t('general.logout.error') || 'Logout failed',
          description: error || (t('general.logout.errorDesc') || 'Please try again')
        };
      default:
        return {
          icon: <FiLogOut className="w-8 h-8 text-red-500 dark:text-red-400" />,
          iconBg: 'from-red-100 to-pink-100/50 dark:from-red-900 dark:to-pink-900/40',
          title: userInfo?.name 
            ? `${t('general.logout.confirmMessage')} ${userInfo.name}?`
            : (t('general.logout.confirmMessage') || 'Sign out?'),
          description: t('general.logout.sessionWarning') || 'You will need to sign in again to access your account'
        };
    }
  };

  const { icon, iconBg, title, description } = getStateContent();

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-[100]">
          {/* Enhanced Backdrop with better blur */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/40 backdrop-blur-md"
            onClick={logoutState === 'idle' ? onClose : undefined}
          />

          {/* Modal with enhanced animations */}
          <motion.div
            ref={modalRef}
            tabIndex="0"
            onKeyDown={handleKeyDown}
            initial={{ y: -30, opacity: 0, scale: 0.9 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            exit={{ y: 30, opacity: 0, scale: 0.9 }}
            transition={{ type: "spring", damping: 20, stiffness: 280 }}
            className="relative w-full max-w-md p-8 rounded-3xl 
              bg-gradient-to-br from-white/95 to-gray-50/30 dark:from-gray-900/95 dark:to-gray-800/30
              shadow-2xl shadow-black/10 dark:shadow-black/30
              border border-white/40 dark:border-gray-700/40
              backdrop-blur-xl
              space-y-8
              mx-4
              transform-gpu" // GPU acceleration
          >
            {/* Close Button - only show when idle or error */}
            <AnimatePresence>
              {(logoutState === 'idle' || logoutState === 'error') && (
                <motion.button
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={onClose}
                  className="absolute right-5 top-5 p-2.5 rounded-full
                    hover:bg-white/30 dark:hover:bg-gray-800/50 
                    bg-white/20 dark:bg-gray-800/30 
                    transition-all duration-300 ease-out
                    focus:outline-none focus:ring-2 focus:ring-gray-300/50 dark:focus:ring-gray-600/50
                    group"
                  aria-label="Close confirmation dialog"
                >
                  <FiX className="w-5 h-5 text-gray-600 dark:text-gray-300 group-hover:text-gray-800 dark:group-hover:text-gray-100 transition-colors" />
                </motion.button>
              )}
            </AnimatePresence>

            {/* Animated Icon */}
            <motion.div 
              key={logoutState} // Re-animate on state change
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="flex justify-center"
            >
              <div className={`p-4 rounded-full bg-gradient-to-br ${iconBg} shadow-lg`}>
                {icon}
              </div>
            </motion.div>

            {/* Content with smooth transitions */}
            <motion.div 
              key={`${logoutState}-content`}
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="text-center space-y-3"
            >
              <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-100 leading-tight">
                {title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed px-2">
                {description}
              </p>
              
              {/* Show user avatar/info for personalization */}
              {logoutState === 'idle' && userInfo?.avatar && (
                <motion.div 
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="flex justify-center mt-4"
                >
                  <img 
                    src={userInfo.avatar} 
                    alt="User avatar"
                    className="w-12 h-12 rounded-full border-2 border-white/50 dark:border-gray-600/50 shadow-md"
                  />
                </motion.div>
              )}
            </motion.div>

            {/* Action Buttons - only show when idle or error */}
            <AnimatePresence>
              {(logoutState === 'idle' || logoutState === 'error') && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -20, opacity: 0 }}
                  transition={{ delay: 0.3 }}
                  className="grid grid-cols-2 gap-4 pt-2"
                >
                  <motion.button
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={onClose}
                    className="px-5 py-3 rounded-2xl
                      bg-white/70 hover:bg-white/90 dark:bg-gray-800/70 dark:hover:bg-gray-800/90
                      text-gray-700 dark:text-gray-200 font-medium
                      transition-all duration-300 ease-out
                      border border-white/60 dark:border-gray-700/60 hover:border-white/80 dark:hover:border-gray-600/80
                      backdrop-blur-sm shadow-sm hover:shadow-md
                      focus:outline-none focus:ring-2 focus:ring-gray-300/50 dark:focus:ring-gray-600/50"
                  >
                    {t('general.logout.cancelButton') || 'Cancel'}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02, y: -1 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleLogout}
                    className="px-5 py-3 rounded-2xl
                      bg-gradient-to-r from-red-500 to-pink-500 dark:from-red-600 dark:to-pink-600
                      hover:from-red-600 hover:to-pink-600 dark:hover:from-red-700 dark:hover:to-pink-700
                      text-white font-medium
                      shadow-lg shadow-red-500/25 dark:shadow-red-900/25 hover:shadow-xl hover:shadow-red-500/30
                      transition-all duration-300 ease-out
                      focus:outline-none focus:ring-2 focus:ring-red-400/60 dark:focus:ring-pink-900/60
                      relative overflow-hidden group"
                  >
                    <span className="relative z-10">
                      {logoutState === 'error' 
                        ? (t('general.logout.retryButton') || 'Try Again')
                        : (t('general.logout.confirmButton') || 'Sign Out')
                      }
                    </span>
                    {/* Subtle shine effect on hover */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Success state - Quick access buttons */}
            {logoutState === 'success' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="flex justify-center gap-3"
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleNavigation}
                  className="px-4 py-2 rounded-xl bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium transition-colors shadow-md"
                >
                  {t('general.logout.goToHome') || 'Go to Home'}
                </motion.button>
              </motion.div>
            )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default LogoutConfirmation;