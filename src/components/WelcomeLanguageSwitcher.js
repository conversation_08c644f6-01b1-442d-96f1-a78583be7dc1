import React from 'react';
import EnhancedLanguageSwitcher from './common/EnhancedLanguageSwitcher';
import analyticsService from '../services/analyticsService';

/**
 * Language switcher specifically designed for the Welcome Page
 * Uses the EnhancedLanguageSwitcher component with welcome page specific styling
 */
const WelcomeLanguageSwitcher = () => {
  // Handle language change with analytics tracking
  const handleLanguageChange = (langCode) => {
    // Track language change in analytics
    analyticsService.trackEvent('welcome_language_change', {
      newLanguage: langCode,
      page: 'welcome'
    });
  };

  return (
    <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-50">
      <EnhancedLanguageSwitcher
        variant="compact"
        showFlags={true}
        onChange={handleLanguageChange}
        className="rounded-full"
      />
    </div>
  );
};

export default WelcomeLanguageSwitcher;
