/**
 * OTP Request Form Component
 * First step of registration - phone number entry and OTP request
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import Form<PERSON>ield from '../ui/FormField';
import { OTPButton } from '../ui/GradientButton';
import { phoneValidationService } from '../../services/phoneValidationService';
import { PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';
import { useToast } from '../../components/common/ToastProvider';
import { MY, US, GB, SG, ID, TH, VN, PH } from 'country-flag-icons/react/3x2';

// Country codes data
const countryCodes = [
  { code: '+60', flag: MY, name: 'Malaysia' },
  { code: '+1', flag: US, name: 'United States' },
  { code: '+44', flag: GB, name: 'United Kingdom' },
  { code: '+65', flag: SG, name: 'Singapore' },
  { code: '+62', flag: ID, name: 'Indonesia' },
  { code: '+66', flag: TH, name: 'Thailand' },
  { code: '+84', flag: VN, name: 'Vietnam' },
  { code: '+63', flag: PH, name: 'Philippines' }
];

/**
 * OTPRequestForm Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when OTP is sent successfully
 * @param {Function} props.onBack - Callback to go back
 * @param {string} props.initialPhone - Initial phone number value
 * @param {string} props.className - Additional CSS classes
 */
const OTPRequestForm = ({
  onSuccess,
  onBack,
  initialPhone = '',
  className = ''
}) => {
  const { requestOtp, loading, error } = useAuth();
  const { t } = useTranslation('auth');
  const { success: showSuccessToast, error: showErrorToast } = useToast();
  const [authError, setAuthError] = useState(null);

  // Form state
  const [mobileNumber, setMobileNumber] = useState(initialPhone);
  const [selectedCountryCode, setSelectedCountryCode] = useState('+60'); // Default to Malaysia
  const [validation, setValidation] = useState({ valid: true, message: '' });
  const [showCountrySelector, setShowCountrySelector] = useState(false);

  // Rate limiting state (60 seconds as per backend)
  const [countdown, setCountdown] = useState(0);
  const [lastRequestTime, setLastRequestTime] = useState(null);

  // Success state
  const [otpSent, setOtpSent] = useState(false);
  const [sentToNumber, setSentToNumber] = useState('');

  // Clear authError when component mounts or error prop changes
  useEffect(() => {
    setAuthError(error);
  }, [error]);

  // Initialize countdown from localStorage
  useEffect(() => {
    const savedRequestTime = localStorage.getItem('lastOtpRequestTime');
    if (savedRequestTime) {
      const timeDiff = Date.now() - parseInt(savedRequestTime);
      const remainingTime = Math.max(0, 60 - Math.floor(timeDiff / 1000));
      if (remainingTime > 0) {
        setCountdown(remainingTime);
        setLastRequestTime(parseInt(savedRequestTime));
      }
    }
  }, []);

  // Countdown timer
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            localStorage.removeItem('lastOtpRequestTime');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Handle country code selection
  const handleCountrySelect = (code) => {
    setSelectedCountryCode(code);
    setShowCountrySelector(false);
    
    // Revalidate with new country code
    const fullNumber = code + mobileNumber.replace(/\D/g, '');
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    setValidation(phoneValidation);
  };

  // Handle phone number input
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    
    // Remove any non-digit characters
    const digitsOnly = value.replace(/\D/g, '');
    // Remove leading zero if present
    const formattedValue = digitsOnly.replace(/^0+/, '');
    
    setMobileNumber(formattedValue);

    // Real-time validation with country code
    const fullNumber = selectedCountryCode + formattedValue;
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    setValidation(phoneValidation);

    // Reset success state when phone changes
    if (otpSent && formattedValue !== sentToNumber) {
      setOtpSent(false);
      setSentToNumber('');
      setAuthError(null);
    }
  };

  // Handle OTP request
  const handleRequestOtp = async () => {
    // Validate phone number with country code
    const fullNumber = selectedCountryCode + mobileNumber.replace(/\D/g, '');
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    setValidation(phoneValidation);

    if (!phoneValidation.valid) {
      return;
    }
    setAuthError(null);

    try {
      const result = await requestOtp(mobileNumber, selectedCountryCode);

      if (result.success) {
        // Set countdown and save request time
        const requestTime = Date.now();
        setCountdown(60);
        setLastRequestTime(requestTime);
        localStorage.setItem('lastOtpRequestTime', requestTime.toString());

        // Set success state
        setOtpSent(true);
        setSentToNumber(mobileNumber);

        onSuccess?.(fullNumber, mobileNumber);
        if (result.message) {
          showSuccessToast(result.message, 4000);
        }
      } else {
        if (result.validationErrors && result.validationErrors.mobile_number) {
          setAuthError(result.validationErrors.mobile_number[0]);
        } else {
          setAuthError(result.error || 'Failed to send OTP.');
          if (result.error) {
            showErrorToast(result.error, 4000);
          }
        }
      }
    } catch (err) {
      console.error('OTP request error:', err);
      showErrorToast('Failed to send OTP.', 4000);
    }
  };

  // Handle resend OTP
  const handleResendOtp = () => {
    if (countdown === 0) {
      handleRequestOtp();
    }
  };

  // Get display phone number
  const getDisplayPhone = () => {
    return `${selectedCountryCode} ${mobileNumber}`;
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-indigo-100 mb-2">
          {otpSent ? t('otpRequest.sentTitle', 'Verification Code Sent!') : t('otpRequest.enterMobile', 'Enter Your Mobile Number')}
        </h3>
        <p className="text-gray-600 dark:text-indigo-100">
          {otpSent
            ? t('otpRequest.sentMessage', "We've sent a 6-digit code to") + ` ${getDisplayPhone()}`
            : t('otpRequest.description', "We'll send you a verification code to get started")
          }
        </p>
      </div>

      {/* Error Display */}
      {authError && (
        <motion.div
          className="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-red-600 text-sm text-center">{authError}</p>
        </motion.div>
      )}

      {/* Success Display */}
      {otpSent && (
        <motion.div
          className="p-4 bg-green-50 border border-green-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <p className="text-green-700 font-medium">{t('otpRequest.sentNotification', 'Verification code sent!')}</p>
              <p className="text-green-600 text-sm">{t('otpRequest.checkMessages', 'Check your messages for the 6-digit code')}</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Phone Number Input with Country Code */}
      <div className="space-y-4">
        <div className="relative group flex">
          {/* Country Code Selector */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCountrySelector(!showCountrySelector)}
              className="flex items-center space-x-2 px-4 py-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-l-2xl hover:bg-white/90 dark:hover:bg-gray-800 transition-all duration-300"
            >
              {countryCodes.find(c => c.code === selectedCountryCode)?.flag && 
                React.createElement(countryCodes.find(c => c.code === selectedCountryCode).flag, {
                  className: "w-5 h-5"
                })
              }
              <span className="text-gray-700 dark:text-indigo-100">{selectedCountryCode}</span>
            </button>

            {/* Country Code Dropdown */}
            {showCountrySelector && (
              <div className="absolute z-50 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                {countryCodes.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={() => handleCountrySelect(country.code)}
                    className="flex items-center space-x-2 w-full px-4 py-2 hover:bg-gray-50 active:bg-blue-50 transition-colors duration-200"
                  >
                    {React.createElement(country.flag, {
                      className: "w-5 h-5"
                    })}
                    <span className="text-gray-700">{country.code}</span>
                    <span className="text-gray-500 text-sm">{country.name}</span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Phone Number Input */}
          <div className="flex-1 relative">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 group-focus-within:text-blue-600 transition-colors duration-300 z-10">
              <PhoneIcon className="w-5 h-5 dark:text-indigo-100" />
            </div>
            <input
              type="tel"
              value={mobileNumber}
              onChange={handlePhoneChange}
              placeholder={t('register.placeholders.mobileNumber', '123456789')}
              className="w-full pl-12 pr-5 py-4 bg-transparent backdrop-blur-xl border border-gray-200 rounded-r-2xl text-gray-800 dark:text-indigo-100 placeholder-gray-400 dark:placeholder-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90 dark:group-hover:bg-gray-900"
              required
              disabled={loading}
            />
            {/* Field Glow Effect */}
            <div className="absolute inset-0 rounded-r-2xl bg-gradient-to-r from-blue-500/10 to-indigo-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </div>
        </div>

        {/* Validation Message */}
        {!validation.valid && (
          <p className="text-red-500 text-sm mt-1">{validation.message}</p>
        )}

        {/* OTP Request Button */}
        <OTPButton
          countdown={countdown}
          onResend={handleResendOtp}
          loading={loading}
          disabled={!validation.valid || !mobileNumber || countdown > 0}
          className="w-full"
        >
          {otpSent ? t('otp.resend', 'Resend Code') : t('otpRequest.sendCode', 'Send Verification Code')}
        </OTPButton>
      </div>

      {/* Information Box */}
      <motion.div
        className="p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex space-x-3">
          <InformationCircleIcon className="items-start w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="text-blue-700 dark:text-indigo-100 font-medium">{t('otpRequest.infoTitle', 'Important Information')}</h4>
            <ul className="text-blue-600 dark:text-indigo-100 text-left text-sm space-y-1">
              <li>• SMS charges may apply from your mobile provider</li>
              <li>• Code expires in 5 minutes</li>
              <li>• You can request a new code after 60 seconds</li>
              <li>• Make sure your phone can receive SMS messages</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* Rate Limiting Info */}
      {countdown > 0 && (
        <motion.div
          className="p-3 bg-yellow-50 border border-yellow-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
            <p className="text-yellow-700 text-sm">
              {t('otpRequest.wait', 'Please wait {{count}} seconds before requesting another code', { count: countdown })}
            </p>
          </div>
        </motion.div>
      )}

      {/* Back Button */}
      {onBack && (
        <motion.button
          onClick={onBack}
          className="w-full py-3 text-gray-500 dark:text-indigo-100 bg-transparent hover:bg-transparent hover:text-gray-700 transition-colors text-sm"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {t('otpRequest.back', '← Back to login')}
        </motion.button>
      )}

      {/* Next Steps */}
      {otpSent && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-gray-500 text-sm">
            {t('otpRequest.nextStep', 'Enter the verification code in the next step to continue registration')}
          </p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default OTPRequestForm;
