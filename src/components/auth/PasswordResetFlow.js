/**
 * Password Reset Flow Component
 * Multi-step password reset with OTP verification and security features
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import <PERSON><PERSON>ield from '../ui/FormField';
import { SubmitButton } from '../ui/GradientButton';
import { AuthCard } from '../ui/GlassMorphCard';
import OTPVerificationInput from './OTPVerificationInput';
import { phoneValidationService } from '../../services/phoneValidationService';
import { validatePassword } from '../../utils/validationUtils';
import { LockClosedIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';
import { MY, US, GB, SG, ID, TH, VN, PH } from 'country-flag-icons/react/3x2';

const countryCodes = [
  { code: '+60', flag: MY, name: 'Malaysia' },
  { code: '+1', flag: US, name: 'United States' },
  { code: '+44', flag: GB, name: 'United Kingdom' },
  { code: '+65', flag: SG, name: 'Singapore' },
  { code: '+62', flag: ID, name: 'Indonesia' },
  { code: '+66', flag: TH, name: 'Thailand' },
  { code: '+84', flag: VN, name: 'Vietnam' },
  { code: '+63', flag: PH, name: 'Philippines' }
];

const PhoneStep = ({ t, phoneNumber, countryCode, setCountryCode, setPhoneNumber, onSubmit, loading, validation }) => {
  const [showCountrySelector, setShowCountrySelector] = useState(false);
    return (
    <form onSubmit={onSubmit} className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-yellow-400 mb-2">{t('forgotPassword.title', 'Reset Your Password')}</h3>
          <p className="text-gray-600 dark:text-gray-300">
            {t('forgotPassword.instructions', 'Enter your mobile number to receive a verification code')}
          </p>
        </div>
      <div className="space-y-2">
        <label className="block text-sm text-left font-bold text-gray-700 tracking-wide">
          {t('register.mobileNumber', 'Mobile Number')}
        </label>
        <div className="relative group flex">
          {/* Country Code Selector */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCountrySelector(!showCountrySelector)}
              className="flex items-center space-x-2 px-4 py-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-l-2xl hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:border-blue-500/50"
              aria-label="Select country code"
            >
              {countryCodes.find(c => c.code === countryCode)?.flag &&
                React.createElement(countryCodes.find(c => c.code === countryCode).flag, {
                  className: "w-5 h-5"
                })
              }
              <span className="text-gray-700 dark:text-gray-200 font-medium">{countryCode}</span>
              <svg className="w-4 h-4 text-gray-500 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {showCountrySelector && (
              <motion.div
                className="absolute z-50 mt-1 w-48 bg-white dark:bg-gray-900 rounded-xl shadow-2xl dark:shadow-indigo-900/30 border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto backdrop-blur-xl"
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                {countryCodes.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    onClick={() => { setCountryCode(country.code); setShowCountrySelector(false); }}
                    className="flex items-center bg-white dark:bg-gray-900 space-x-3 w-full px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 active:bg-blue-50 dark:active:bg-blue-900 transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl"
                  >
                    {React.createElement(country.flag, {
                      className: "w-5 h-5"
                    })}
                    <span className="text-gray-700 dark:text-gray-200 font-medium">{country.code}</span>
                    <span className="text-gray-500 dark:text-gray-400 text-sm">{country.name}</span>
                  </button>
                ))}
              </motion.div>
            )}
          </div>
          {/* Phone Number Input */}
          <div className="flex-1 relative">
            <input
          type="tel"
          value={phoneNumber}
              onChange={e => {
                // Remove any non-digit characters
                const digitsOnly = e.target.value.replace(/\D/g, '');
                // Remove leading zero if present
                const formattedValue = digitsOnly.replace(/^0+/, '');
                setPhoneNumber(formattedValue);
              }}
              placeholder={t('register.placeholders.mobileNumber', '123456789')}
              className="w-full max-w-md pl-4 pr-5 py-4 bg-transparent backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-r-2xl text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90 dark:group-hover:bg-gray-900/90"
          required
              aria-label="Phone number"
            />
          </div>
        </div>
        {/* Validation message */}
        {validation.mobileNumber && !validation.mobileNumber.valid && (
          <div className="text-red-500 dark:text-red-400 text-xs mt-1">{validation.mobileNumber.message}</div>
        )}
      </div>
      <SubmitButton loading={loading} disabled={!validation.mobileNumber.valid || !phoneNumber}>
          {t('forgotPassword.sendButton', 'Send Reset Code')}
        </SubmitButton>
      </form>
    );
  };

const OTPStep = ({ mobileNumber, normalizedPhone, onSuccess, onBack, onResend }) => (
      <OTPVerificationInput
    mobileNumber={mobileNumber}
    normalizedPhone={normalizedPhone}
    onSuccess={onSuccess}
    onBack={onBack}
    onResend={onResend}
  />
);

const PasswordStep = ({
  t,
  loading,
  validation,
  setValidation,
  normalizedPhone,
  setCurrentStep,
  resetPassword
}) => {
  const [passwords, setPasswords] = useState({ newPassword: '', confirmPassword: '' });

    const handlePasswordChange = (field) => (e) => {
      const value = e.target.value;
    setPasswords((prev) => ({ ...prev, [field]: value }));

      if (field === 'newPassword') {
        const passwordValidation = validatePassword(value);
      setValidation((prev) => ({ ...prev, newPassword: passwordValidation }));

        if (passwords.confirmPassword) {
          const confirmValidation = value === passwords.confirmPassword
            ? { valid: true, message: 'Passwords match' }
            : { valid: false, message: 'Passwords do not match' };
        setValidation((prev) => ({ ...prev, confirmPassword: confirmValidation }));
        }
      } else if (field === 'confirmPassword') {
        const confirmValidation = value === passwords.newPassword
          ? { valid: true, message: 'Passwords match' }
          : { valid: false, message: 'Passwords do not match' };
      setValidation((prev) => ({ ...prev, confirmPassword: confirmValidation }));
      }
    };

    const handleSubmit = async (e) => {
      e.preventDefault();

      const passwordValidation = validatePassword(passwords.newPassword);
      const confirmValidation = passwords.newPassword === passwords.confirmPassword
        ? { valid: true, message: 'Passwords match' }
        : { valid: false, message: 'Passwords do not match' };

    setValidation((prev) => ({
        ...prev,
        newPassword: passwordValidation,
        confirmPassword: confirmValidation
      }));

      if (!passwordValidation.valid || !confirmValidation.valid) return;

      try {
      const result = await resetPassword(normalizedPhone, passwords.newPassword);
        if (result.success) {
          setCurrentStep(4);
        }
      } catch (err) {
        console.error('Password reset error:', err);
      }
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-yellow-400 mb-2">{t('resetPassword.title', 'Create New Password')}</h3>
          <p className="text-gray-600 dark:text-gray-300">
            {t('resetPassword.instructions', 'Choose a strong password for your account')}
          </p>
        </div>

        <div className="space-y-4">
          <FormField
            label={t('resetPassword.newPassword', 'New Password')}
            type="password"
            value={passwords.newPassword}
            onChange={handlePasswordChange('newPassword')}
            placeholder={t('resetPassword.newPassword', 'Enter your new password')}
            required
            icon={<LockClosedIcon />}
            validation={validation.newPassword}
            helperText={t('register.helperText.password', 'Minimum 6 characters with letters and numbers')}
          />

          <FormField
            label={t('resetPassword.confirmPassword', 'Confirm New Password')}
            type="password"
            value={passwords.confirmPassword}
            onChange={handlePasswordChange('confirmPassword')}
            placeholder={t('resetPassword.confirmPassword', 'Confirm your new password')}
            required
            icon={<LockClosedIcon />}
            validation={validation.confirmPassword}
          />
        </div>

        <SubmitButton
          loading={loading}
          disabled={!validation.newPassword.valid || !validation.confirmPassword.valid || !passwords.newPassword || !passwords.confirmPassword}
        >
          {t('resetPassword.resetButton', 'Reset Password')}
        </SubmitButton>
      </form>
    );
  };

const SuccessStep = ({ t, onSuccess }) => (
    <div className="text-center space-y-6">
      <motion.div
        className="mx-auto w-16 h-16 bg-green-500/20 dark:bg-green-900/30 rounded-full flex items-center justify-center"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
      transition={{ type: 'spring', stiffness: 500, damping: 30 }}
      >
        <CheckCircleIcon className="w-8 h-8 text-green-400 dark:text-green-300" />
      </motion.div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 dark:text-yellow-400 mb-2">{t('resetPassword.success', 'Password Reset Successful!')}</h3>
        <p className="text-gray-600 dark:text-gray-300">
          {t('resetPassword.successMessage', 'Your password has been successfully updated. You have been logged out from all devices for security.')}
        </p>
      </div>

      <div className="space-y-3">
      <SubmitButton onClick={onSuccess}>{t('resetPassword.successContinue', 'Continue to Login')}</SubmitButton>
      <p className="text-gray-500 dark:text-gray-300 text-sm">{t('resetPassword.successInfo', 'Please sign in with your new password')}</p>
      </div>
    </div>
  );

/**
 * PasswordResetFlow Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when password reset succeeds
 * @param {Function} props.onCancel - Callback to cancel and go back
 * @param {string} props.className - Additional CSS classes
 */
const PasswordResetFlow = ({
  onSuccess,
  onCancel,
  className = ''
}) => {
  const { requestOtp, verifyOtpForReset, resetPassword, loading, error } = useAuth();
  const { t } = useTranslation('auth');

  // Flow state
  const [currentStep, setCurrentStep] = useState(1); // 1: Phone, 2: OTP, 3: New Password, 4: Success
  const [resetData, setResetData] = useState({
    mobileNumber: '',
    normalizedPhone: '',
    otp: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Validation state
  const [validation, setValidation] = useState({
    mobileNumber: { valid: true, message: '' },
    newPassword: { valid: true, message: '' },
    confirmPassword: { valid: true, message: '' }
  });

  // Phone input state lives at the top level so it doesn't reset on re-render
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('+60');

  // Step 1: Phone Number Entry handlers
  const handlePhoneChange = (e) => {
    const { value, countryCode: cc } = e.target;
    if (cc) setCountryCode(cc);
    setPhoneNumber(value);

    const fullNumber = (cc || countryCode) + value;
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));
  };

  const handlePhoneSubmit = async (e) => {
    e.preventDefault();

    const fullNumber = countryCode + phoneNumber;
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));

    if (!phoneValidation.valid) return;

    try {
      // Use formatted phone with + prefix for subsequent API calls
      const normalizedPhone = phoneValidationService.formatMalaysianPhone(fullNumber);
      const result = await requestOtp(phoneNumber, countryCode);

      if (result.success) {
        setResetData(prev => ({
          ...prev,
          mobileNumber: fullNumber,
          normalizedPhone
        }));
        setCurrentStep(2);
      }
    } catch (err) {
      console.error('OTP request error:', err);
    }
  };

  // Step 2: OTP Verification handlers
  const handleOtpSuccess = async (otp) => {
    try {
      const result = await verifyOtpForReset(resetData.normalizedPhone, otp);

      if (result.success) {
        setResetData(prev => ({ ...prev, otp }));
        setCurrentStep(3);
      }
    } catch (err) {
      console.error('OTP verification error:', err);
    }
  };

  const handleResend = async () => {
    try {
      await requestOtp(phoneNumber, countryCode);
    } catch (err) {
      console.error('Resend OTP error:', err);
    }
  };

  // Get current step component
  const getCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <PhoneStep
            t={t}
            phoneNumber={phoneNumber}
            countryCode={countryCode}
            setCountryCode={setCountryCode}
            setPhoneNumber={setPhoneNumber}
            onSubmit={handlePhoneSubmit}
            loading={loading}
            validation={validation}
          />
        );
      case 2:
        return (
          <OTPStep
            mobileNumber={resetData.mobileNumber}
            normalizedPhone={resetData.normalizedPhone}
            onSuccess={handleOtpSuccess}
            onBack={() => setCurrentStep(1)}
            onResend={handleResend}
          />
        );
      case 3:
        return (
          <PasswordStep
            t={t}
            loading={loading}
            validation={validation}
            setValidation={setValidation}
            normalizedPhone={resetData.normalizedPhone}
            setCurrentStep={setCurrentStep}
            resetPassword={resetPassword}
          />
        );
      case 4:
        return <SuccessStep t={t} onSuccess={onSuccess} />;
      default:
        return (
          <PhoneStep
            t={t}
            phoneNumber={phoneNumber}
            countryCode={countryCode}
            setCountryCode={setCountryCode}
            setPhoneNumber={setPhoneNumber}
            onSubmit={handlePhoneSubmit}
            loading={loading}
            validation={validation}
          />
        );
    }
  };

  return (
    <motion.div
      className={cn('w-full max-w-md mx-auto', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <AuthCard className="bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-lg dark:shadow-indigo-900/30">
        {/* Progress Indicator */}
        {currentStep < 4 && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
              <span>Step {currentStep} of 3</span>
              <span>{Math.round((currentStep / 3) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2">
              <motion.div
                className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 dark:from-yellow-400 dark:to-pink-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(currentStep / 3) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <motion.div
            className="mb-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-red-600 dark:text-red-400 text-sm text-center">{error}</p>
          </motion.div>
        )}

        {/* Current Step Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {getCurrentStep()}
          </motion.div>
        </AnimatePresence>

        {/* Cancel Button */}
        {currentStep < 4 && (
          <div className="mt-6 text-center">
            <button
              onClick={onCancel}
              className="text-gray-500 dark:text-yellow-400 hover:text-gray-700 dark:hover:text-yellow-300 bg-transparent hover:rounded-2xl hover:bg-gray-100 dark:hover:bg-gray-800 text-sm transition-colors"
              disabled={loading}
            >
              {t('forgotPassword.backToLogin', '← Back to login')}
            </button>
          </div>
        )}
      </AuthCard>
    </motion.div>
  );
};

export default PasswordResetFlow;
