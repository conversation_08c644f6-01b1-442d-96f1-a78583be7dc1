/**
 * OTP Verification Component
 *
 * This component handles OTP verification for various authentication flows.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import OtpInput from '../common/OtpInput';
import useTranslation from '../../hooks/useTranslation';
import otpService from '../../services/otpService';

/**
 * OTPVerification component
 *
 * @param {Object} props
 * @param {string} props.mobileNumber - User's mobile number
 * @param {string} props.countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
 * @param {Function} props.onVerified - Callback when OTP is verified
 * @param {Function} props.onCancel - Callback when verification is cancelled
 * @param {string} props.purpose - Purpose of OTP verification ('registration', 'login', 'reset-password')
 */
const OTPVerification = ({
    mobileNumber,
    countryCode,
    onVerified,
    onCancel,
    purpose = 'registration'
}) => {
    // Get auth context
    const { loading: authLoading } = useAuth();

    // State management
    const [otp, setOtp] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [otpSent, setOtpSent] = useState(false);
    const [resendCooldown, setResendCooldown] = useState(0);

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Use the translation hook
    const { t } = useTranslation('auth');

    // Request OTP on component mount
    useEffect(() => {
        handleRequestOTP();
    }, []);

    // Handle countdown timer for resend
    useEffect(() => {
        let timer;
        if (resendCooldown > 0) {
            timer = setTimeout(() => {
                setResendCooldown(prev => prev - 1);
            }, 1000);
        }

        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [resendCooldown]);

    // Request OTP
    const handleRequestOTP = useCallback(async () => {
        if (loading || authLoading) return;

        setLoading(true);
        setError('');

        try {
            let response;

            // Call the appropriate OTP request method based on purpose
            switch (purpose) {
                case 'registration':
                    response = await otpService.requestRegistrationOtp(mobileNumber, countryCode);
                    break;
                case 'login':
                    response = await otpService.requestLoginOtp(mobileNumber, countryCode);
                    break;
                case 'reset-password':
                    response = await otpService.requestPasswordResetOtp(mobileNumber, countryCode);
                    break;
                default:
                    response = await otpService.requestRegistrationOtp(mobileNumber, countryCode);
            }

            if (response.success) {
                setOtpSent(true);

                // Get expiry from response or default to 60 seconds
                const expiry = response.data?.expiry || 60;
                setResendCooldown(expiry);

                // Clear any existing OTP
                setOtp('');
            } else {
                setError(response.error || t('otp.requestFailed', 'Failed to send verification code'));
            }
        } catch (err) {
            console.error('OTP request failed:', err);
            setError(t('otp.requestFailed', 'Failed to send verification code'));
        } finally {
            setLoading(false);
        }
    }, [mobileNumber, countryCode, loading, authLoading, purpose, t]);

    // Verify OTP
    const handleVerifyOTP = useCallback(async () => {
        if (loading || authLoading) return;

        // Validate OTP format
        if (otp.length !== 6 || !/^\d+$/.test(otp)) {
            setError(t('otp.invalidCode', 'Please enter a valid 6-digit code'));
            return;
        }

        setLoading(true);
        setError('');

        try {
            let response;

            // For registration, actually verify the OTP with the backend
            if (purpose === 'registration') {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`[DEV] Registration flow: Verifying OTP with backend`);
                    console.log(`[DEV] Verifying OTP: ${otp} for registration`);
                }

                // Actually verify the OTP with the backend
                response = await verifyOtp(mobileNumber, countryCode, otp, 'registration');
            } else {
                // For other purposes, use the appropriate verification method
                switch (purpose) {
                    case 'login':
                        response = await otpService.verifyLoginOtp(mobileNumber, countryCode, otp);
                        break;
                    case 'reset-password':
                        response = await otpService.verifyPasswordResetOtp(mobileNumber, countryCode, otp);
                        break;
                    default:
                        response = await otpService.verifyLoginOtp(mobileNumber, countryCode, otp);
                }
            }

            if (response.success) {
                // Call the onVerified callback with the response and include the OTP
                if (onVerified) {
                    // Include the OTP in the response
                    const responseWithOtp = {
                        ...response,
                        otp: otp // Add the OTP to the response
                    };
                    onVerified(responseWithOtp);
                }
            } else {
                setError(response.error || t('otp.verificationFailed', 'Invalid verification code'));
            }
        } catch (err) {
            console.error('OTP verification failed:', err);
            setError(t('otp.verificationFailed', 'Failed to verify code'));
        } finally {
            setLoading(false);
        }
    }, [otp, mobileNumber, countryCode, loading, authLoading, purpose, onVerified, t]);

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-800">
                    {t('otp.title', 'Verification Code')}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                    {t('otp.sentTo', 'We sent a code to')} +60 {mobileNumber}
                </p>

                {/* Development Mode Indicator */}
                {isDevelopment && (
                    <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p className="text-xs font-medium text-yellow-800">Development Mode</p>
                        <p className="text-sm text-yellow-600 mt-1">Check the backoffice for the OTP code</p>
                    </div>
                )}

                {/* Registration Flow Message */}
                {purpose === 'registration' && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                        <p className="text-xs text-blue-800">
                            Enter the OTP code to complete your registration
                        </p>
                    </div>
                )}
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                    {error}
                </div>
            )}

            <div className="space-y-4">
                <OtpInput
                    value={otp}
                    onChange={setOtp}
                    numInputs={6}
                    separator={<span className="w-2"></span>}
                    isInputNum
                    shouldAutoFocus
                    inputStyle="w-12 h-12 text-center border border-gray-300 rounded-lg text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                />

                <div className="flex justify-between items-center">
                    <button
                        type="button"
                        onClick={handleRequestOTP}
                        disabled={resendCooldown > 0 || loading}
                        className="text-sm text-blue-600 hover:text-blue-800 transition-colors duration-150 focus:outline-none disabled:text-gray-400 disabled:cursor-not-allowed"
                    >
                        {resendCooldown > 0
                            ? `${t('otp.resendIn', 'Resend in')} ${resendCooldown}s`
                            : t('otp.resend', 'Resend Code')}
                    </button>

                    <button
                        type="button"
                        onClick={onCancel}
                        disabled={loading}
                        className="text-sm text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none disabled:text-gray-400 disabled:cursor-not-allowed"
                    >
                        {t('otp.cancel', 'Cancel')}
                    </button>
                </div>

                <button
                    type="button"
                    onClick={handleVerifyOTP}
                    disabled={otp.length !== 6 || loading}
                    className="w-full flex justify-center items-center py-3 px-4 rounded-lg text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:bg-blue-300 disabled:cursor-not-allowed"
                >
                    {loading ? (
                        <>
                            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                            {t('otp.verifying', 'Verifying...')}
                        </>
                    ) : purpose === 'registration'
                      ? t('otp.continue', 'Continue Registration')
                      : t('otp.verify', 'Verify Code')}
                </button>
            </div>
        </div>
    );
};

export default OTPVerification;
