/**
 * Registration Flow Container Component
 * Manages multi-step registration process with step navigation and data persistence
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StepCard } from '../ui/GlassMorphCard';
import OTPRequestForm from './OTPRequestForm';
import OTPVerificationInput from './OTPVerificationInput';
import RegistrationForm from './RegistrationForm';
import ProfilePictureUpload from './ProfilePictureUpload';
import ReferralInput from './ReferralInput';
import { useAuth } from '../../contexts/AuthContext';
import { firebaseMessaging } from '../../services/firebaseMessaging';
import { cn } from '../../lib/utils';
import { useTranslation } from 'react-i18next';
import RegistrationMethodStep from './RegistrationMethodStep';
import { oauthAPI } from '../../services/api';
import RegistrationEdgeCases from './RegistrationEdgeCases';
import { useLocation } from 'react-router-dom';

/**
 * RegistrationFlow Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when registration completes
 * @param {Function} props.onBack - Callback to go back to login
 * @param {string} props.className - Additional CSS classes
 */
const RegistrationFlow = ({
  onSuccess,
  onBack,
  className = ''
}) => {
  const location = useLocation();
  const { requestOtp } = useAuth();
  const { t } = useTranslation('auth');

  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // Registration data persistence
  const [registrationData, setRegistrationData] = useState({
    // Step 1: Phone & OTP
    mobileNumber: '',
    normalizedPhone: '',
    otp: '',

    // Step 2: Personal Info
    name: '',
    email: '',
    nickname: '',
    gender: '',
    dateOfBirth: '',
    password: '',
    confirmPassword: '',

    // Step 3: Optional Info
    profilePicture: null,
    referralType: null,
    referralValue: '',

    // Device Info
    deviceToken: null,
    deviceType: 'web',
    deviceName: navigator.userAgent
  });

  // OAuth loading states
  const [oauthLoadingStates, setOAuthLoadingStates] = useState({ google: false, apple: false });
  const [error, setError] = useState(null);

  // Edge case states
  const [edgeCase, setEdgeCase] = useState(null);
  const [retryAfter, setRetryAfter] = useState(0);

  // Step configuration
  const steps = [
    {
      id: 1,
      title: 'Choose Registration Method',
      description: 'Select how you want to register',
      component: 'RegistrationMethod'
    },
    {
      id: 2,
      title: 'Phone Verification',
      description: 'Enter your mobile number',
      component: 'OTPRequest'
    },
    {
      id: 3,
      title: 'Verify Code',
      description: 'Enter the 6-digit code',
      component: 'OTPVerification'
    },
    {
      id: 4,
      title: 'Personal Details',
      description: 'Complete your profile',
      component: 'PersonalInfo'
    },
    {
      id: 5,
      title: 'Finish Setup',
      description: 'Optional profile picture',
      component: 'FinalStep'
    }
  ];

  // Load saved registration data
  useEffect(() => {
    const savedData = localStorage.getItem('registrationData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setRegistrationData(prev => ({ ...prev, ...parsedData }));

        // Determine current step based on saved data
        if (parsedData.otp && parsedData.mobileNumber) {
          setCurrentStep(3);
          setCompletedSteps(new Set([1, 2]));
        } else if (parsedData.mobileNumber) {
          setCurrentStep(2);
          setCompletedSteps(new Set([1]));
        }
      } catch (error) {
        console.error('Error loading registration data:', error);
      }
    }
  }, []);

  // Save registration data to localStorage
  const saveRegistrationData = useCallback((data) => {
    const updatedData = { ...registrationData, ...data };
    setRegistrationData(updatedData);

    // Save to localStorage (exclude sensitive data)
    const dataToSave = { ...updatedData };
    delete dataToSave.password;
    delete dataToSave.confirmPassword;
    delete dataToSave.otp;

    localStorage.setItem('registrationData', JSON.stringify(dataToSave));
  }, [registrationData]);

  // Handle step completion
  const handleStepComplete = useCallback((stepData) => {
    saveRegistrationData(stepData);
    setCompletedSteps(prev => new Set([...prev, currentStep]));

    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [saveRegistrationData, currentStep, steps.length]);

  // Handle step navigation
  const goToStep = useCallback((stepNumber) => {
    if (stepNumber <= currentStep || completedSteps.has(stepNumber - 1)) {
      setCurrentStep(stepNumber);
    }
  }, [currentStep, completedSteps]);

  // Handle going back
  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      // Clear registration data and go back to login
      localStorage.removeItem('registrationData');
      onBack?.();
    }
  }, [currentStep, onBack]);

  // Handle OTP request success
  const handleOtpRequestSuccess = useCallback((normalizedPhone, displayPhone) => {
    handleStepComplete({
      normalizedPhone,
      mobileNumber: displayPhone
    });
  }, [handleStepComplete]);

  // Handle OTP verification success
  const handleOtpVerificationSuccess = useCallback((otp) => {
    handleStepComplete({ otp });
  }, [handleStepComplete]);

  // Handle personal info completion
  const handlePersonalInfoComplete = useCallback((personalData) => {
    handleStepComplete(personalData);
  }, [handleStepComplete]);

  // Handle final registration
  const handleRegistrationComplete = useCallback((finalData) => {
    const completeData = { ...registrationData, ...finalData };

    // Clear saved data
    localStorage.removeItem('registrationData');

    // Call success callback
    onSuccess?.(completeData);
  }, [registrationData, onSuccess]);

  // Handle OTP resend
  const handleOtpResend = useCallback(async () => {
    try {
      await requestOtp(registrationData.normalizedPhone);
    } catch (error) {
      console.error('OTP resend error:', error);
    }
  }, [requestOtp, registrationData.normalizedPhone]);

  // Handle step-specific back navigation
  const handleOtpVerificationBack = useCallback(() => {
    setCurrentStep(1);
  }, []);

  const handlePersonalInfoBack = useCallback(() => {
    setCurrentStep(2);
  }, []);

  const handleFinalStepBack = useCallback(() => {
    setCurrentStep(3);
  }, []);

  // Handle registration method selection
  const handleManualRegister = useCallback(() => {
    handleStepComplete({ registrationMethod: 'manual' });
  }, [handleStepComplete]);

  const handleGoogleSignIn = useCallback(async () => {
    try {
      setOAuthLoadingStates(prev => ({ ...prev, google: true }));
      const response = await oauthAPI.redirect('google');
      window.location.href = response.url;
    } catch (error) {
      console.error('Google sign-in error:', error);
      setError(t('register.errors.oauthError'));
    } finally {
      setOAuthLoadingStates(prev => ({ ...prev, google: false }));
    }
  }, [t]);

  const handleAppleSignIn = useCallback(async () => {
    try {
      setOAuthLoadingStates(prev => ({ ...prev, apple: true }));
      const response = await oauthAPI.redirect('apple');
      window.location.href = response.url;
    } catch (error) {
      console.error('Apple sign-in error:', error);
      setError(t('register.errors.oauthError'));
    } finally {
      setOAuthLoadingStates(prev => ({ ...prev, apple: false }));
    }
  }, [t]);

  // Check browser compatibility
  useEffect(() => {
    const checkBrowserCompatibility = () => {
      const isCompatible = 
        'localStorage' in window &&
        'sessionStorage' in window &&
        'indexedDB' in window &&
        'fetch' in window &&
        'FileReader' in window;

      if (!isCompatible) {
        setEdgeCase('browserCompatibility');
      }
    };

    checkBrowserCompatibility();
  }, []);

  // Handle network errors
  const handleNetworkError = useCallback((error) => {
    if (error.name === 'NetworkError' || !navigator.onLine) {
      setEdgeCase('networkError');
    } else if (error.response?.status === 429) {
      setRetryAfter(error.response.headers['retry-after'] || 60);
      setEdgeCase('rateLimit');
    } else if (error.response?.status === 409) {
      setEdgeCase('duplicateAccount');
    } else if (error.response?.status === 401) {
      setEdgeCase('sessionTimeout');
    }
  }, []);

  // Handle retry
  const handleRetry = useCallback(() => {
    setEdgeCase(null);
    // Retry the last operation
    if (currentStep === 2) {
      handleOtpResend();
    }
  }, [currentStep, handleOtpResend]);

  // Handle restart
  const handleRestart = useCallback(() => {
    setEdgeCase(null);
    setCurrentStep(1);
    setCompletedSteps(new Set());
    localStorage.removeItem('registrationData');
  }, []);

  // Handle login redirect
  const handleLoginRedirect = useCallback(() => {
    onBack?.();
  }, [onBack]);

  // Referral code capture logic inside the component
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const referralCode = params.get('referralCode');
    if (referralCode) {
      localStorage.setItem('referralCode', referralCode);
      setRegistrationData(prev => ({
        ...prev,
        referralType: 'code',
        referralValue: referralCode
      }));
    }
  }, [location, setRegistrationData]);

  // Render edge case component
  const renderEdgeCase = () => {
    if (!edgeCase) return null;

    const props = {
      onBack: handleBack,
      onRetry: handleRetry,
      onRestart: handleRestart,
      onLogin: handleLoginRedirect,
      retryAfter
    };

    switch (edgeCase) {
      case 'networkError':
        return <RegistrationEdgeCases.NetworkError {...props} />;
      case 'sessionTimeout':
        return <RegistrationEdgeCases.SessionTimeout {...props} />;
      case 'duplicateAccount':
        return <RegistrationEdgeCases.DuplicateAccount {...props} />;
      case 'rateLimit':
        return <RegistrationEdgeCases.RateLimit {...props} />;
      case 'browserCompatibility':
        return <RegistrationEdgeCases.BrowserCompatibility {...props} />;
      default:
        return null;
    }
  };

  // Final step component with profile picture and referral
  const FinalStepComponent = ({ registrationData, onComplete, onBack }) => {
    const { register, loading, error } = useAuth();
    const [profilePicture, setProfilePicture] = useState(null);
    const [referralType, setReferralType] = useState(null);
    const [referralValue, setReferralValue] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [referralError, setReferralError] = useState(null);
    const { t } = useTranslation('auth');

    // Auto-populate referral code from localStorage if available and not already set
    useEffect(() => {
      const code = localStorage.getItem('referralCode');
      if (code && !referralType && !referralValue) {
        setReferralType('code');
        setReferralValue(code);
      }
    }, [referralType, referralValue]);

    const handleComplete = async () => {
      try {
        setIsSubmitting(true);
        setReferralError(null);

        // Get device info
        const deviceInfo = {
          device_type: 'web',
          device_name: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language,
          screen_resolution: `${window.screen.width}x${window.screen.height}`,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };

        // Validate referral data if provided
        if (referralType && !referralValue) {
          setReferralError(t('register.validation.referralValueRequired'));
          setIsSubmitting(false);
          return;
        }

        // Prepare final registration data with proper field mapping
        const payload = {
          // Map frontend state keys to backend expected keys
          name: registrationData.name,
          email: registrationData.email,
          nickname: registrationData.nickname,
          gender: registrationData.gender,
          date_of_birth: registrationData.dateOfBirth,
          password: registrationData.password,
          mobile_number: registrationData.normalizedPhone,
          otp: registrationData.otp,
          country_code: 'MY',

          // Optional fields from current step
          profile_picture: profilePicture,
          ...(referralType && referralValue ? {
            referral_type: referralType,
            referral_value: referralValue
          } : {}),

          // Device info
          device_type: deviceInfo.device_type,
          device_name: deviceInfo.device_name
        };

        // Debug logging to see what data is being sent
        console.log('🔍 Final registration data being sent:', {
          ...payload,
          password: '[HIDDEN]',
          otp: '[HIDDEN]'
        });

        const result = await register(payload);
        if (result.success) {
          onComplete(result);
        } else {
          console.error('❌ Registration failed:', result.error);
          if (result.validationErrors) {
            console.error('📋 Validation errors:', result.validationErrors);
            // Handle referral-specific errors
            if (result.validationErrors.referral_value) {
              setReferralError(result.validationErrors.referral_value[0]);
            }
          }
        }
      } catch (error) {
        console.error('💥 Final registration error:', error);
        if (error.response?.data) {
          console.error('🔍 Server response data:', error.response.data);
          // Handle referral-specific errors from API
          if (error.response.data.errors?.referral_value) {
            setReferralError(error.response.data.errors.referral_value[0]);
          }
        }
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('register.finalStep.title')}</h3>
          <p className="text-gray-600">{t('register.finalStep.description')}</p>
        </div>

        {/* Error Display */}
        {(error || referralError) && (
          <motion.div
            className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-red-600 text-sm text-center">{referralError || error}</p>
          </motion.div>
        )}

        <ProfilePictureUpload
          value={profilePicture}
          onChange={setProfilePicture}
          onRemove={() => setProfilePicture(null)}
        />

        <ReferralInput
          referralType={referralType}
          referralValue={referralValue}
          onTypeChange={setReferralType}
          onValueChange={setReferralValue}
          disabled={isSubmitting}
        />

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={onBack}
            disabled={isSubmitting}
            className="flex-1 py-2 px-4 text-white/60 hover:text-white border border-white/30 hover:border-white/50 rounded-xl transition-colors text-sm"
          >
            {t('register.navigation.back')}
          </button>
          
          <button
            onClick={handleComplete}
            disabled={isSubmitting}
            className={cn(
              'flex-1 py-2 px-4 text-white border border-indigo-500 rounded-xl transition-colors text-sm',
              'bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed'
            )}
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('register.creatingAccount')}
              </span>
            ) : (
              t('register.navigation.submit')
            )}
          </button>
        </div>
      </div>
    );
  };

  // Get current step component
  const getCurrentStepComponent = () => {
    if (edgeCase) {
      return renderEdgeCase();
    }

    const step = steps[currentStep - 1];

    switch (step.component) {
      case 'RegistrationMethod':
        return (
          <RegistrationMethodStep
            onManualRegister={handleManualRegister}
            onGoogleSignIn={handleGoogleSignIn}
            onAppleSignIn={handleAppleSignIn}
            disabled={oauthLoadingStates.google || oauthLoadingStates.apple}
            loadingStates={oauthLoadingStates}
          />
        );
      case 'OTPRequest':
        return (
          <OTPRequestForm
            onSuccess={handleOtpRequestSuccess}
            onBack={handleBack}
            initialPhone={registrationData.mobileNumber}
          />
        );
      case 'OTPVerification':
        return (
          <OTPVerificationInput
            mobileNumber={registrationData.mobileNumber}
            normalizedPhone={registrationData.normalizedPhone}
            onSuccess={handleOtpVerificationSuccess}
            onBack={handleOtpVerificationBack}
            onResend={handleOtpResend}
          />
        );
      case 'PersonalInfo':
        return (
          <RegistrationForm
            initialData={registrationData}
            onSuccess={handlePersonalInfoComplete}
            onBack={handlePersonalInfoBack}
          />
        );
      case 'FinalStep':
        return (
          <FinalStepComponent
            registrationData={registrationData}
            onComplete={handleRegistrationComplete}
            onBack={handleFinalStepBack}
          />
        );
      default:
        return null;
    }
  };

  return (
    <motion.div
      className={cn('space-y-6', 'bg-white dark:bg-gray-900 rounded-2xl p-4 border border-gray-100 dark:border-gray-800 shadow-lg dark:shadow-indigo-900/30', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Progress Indicator */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-yellow-400">
            Step {currentStep} of {steps.length}
          </h2>
          <span className="text-gray-600 dark:text-gray-300 text-sm">
            {Math.round((currentStep / steps.length) * 100)}% Complete
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 dark:from-yellow-400 dark:to-pink-400"
            initial={{ width: 0 }}
            animate={{ width: `${(currentStep / steps.length) * 100}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Step Indicators */}
        <div className="flex justify-between">
          {steps.map((step) => (
            <button
              key={step.id}
              onClick={() => goToStep(step.id)}
              disabled={step.id > currentStep && !completedSteps.has(step.id - 1)}
              className={cn(
                'flex flex-col items-center bg-transparent space-y-2 p-2 rounded-lg transition-all duration-300',
                'disabled:cursor-not-allowed',
                step.id === currentStep && 'bg-indigo-50 dark:bg-gray-800',
                step.id < currentStep && 'text-green-600 dark:text-green-300',
                step.id === currentStep && 'text-indigo-700 dark:text-yellow-400',
                step.id > currentStep && !completedSteps.has(step.id) && 'text-gray-400 dark:text-gray-500'
              )}
            >
              <div className={cn(
                'w-8 h-8 rounded-full flex bg-transparent items-center justify-center text-sm font-medium transition-all duration-300',
                completedSteps.has(step.id) && 'bg-green-500 dark:bg-green-700 text-white',
                step.id === currentStep && 'bg-indigo-500 dark:bg-yellow-400 text-white dark:text-gray-900',
                step.id > currentStep && !completedSteps.has(step.id) && 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-300'
              )}>
                {completedSteps.has(step.id) ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  step.id
                )}
              </div>
              <div className="text-center">
                <div className="text-xs font-medium dark:text-yellow-400">{step.title}</div>
                <div className="text-xs opacity-70 hidden sm:block dark:text-gray-300">{step.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Step Content */}
      <StepCard isActive={true} className="min-h-[400px] dark:bg-gray-900 dark:border-gray-700">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {getCurrentStepComponent()}
          </motion.div>
        </AnimatePresence>
      </StepCard>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleBack}
          className="px-4 py-2 text-gray-600 dark:text-yellow-400 bg-transparent hover:bg-transparent hover:text-gray-800 dark:hover:text-yellow-300 transition-colors"
        >
          ← {currentStep === 1 ? 'Back to Login' : 'Previous Step'}
        </button>

        <div className="text-gray-500 dark:text-gray-300 text-sm">
          {currentStep === steps.length ? 'Almost done!' : `${steps.length - currentStep} steps remaining`}
        </div>
      </div>
    </motion.div>
  );
};

export default RegistrationFlow;
