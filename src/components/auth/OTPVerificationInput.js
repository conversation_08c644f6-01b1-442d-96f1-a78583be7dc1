/**
 * OTP Verification Input Component
 * 6-digit OTP input with auto-focus, auto-submit, and resend functionality
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { OTPButton } from '../ui/GradientButton';
import { phoneValidationService } from '../../services/phoneValidationService';
import { cn } from '../../lib/utils';

/**
 * OTPVerificationInput Component
 * @param {Object} props
 * @param {string} props.mobileNumber - Phone number for display and resend
 * @param {string} props.normalizedPhone - Normalized phone for backend
 * @param {Function} props.onSuccess - Callback when OTP is verified
 * @param {Function} props.onBack - Callback to go back
 * @param {Function} props.onResend - Callback when resend is requested
 * @param {string} props.className - Additional CSS classes
 */
const OTPVerificationInput = ({
  mobileNumber,
  normalizedPhone,
  onSuccess,
  onBack,
  onResend,
  className = ''
}) => {
  const { loading, error } = useAuth();

  // OTP input state
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // Resend functionality
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(false);

  // Refs for input management
  const inputRefs = useRef([]);
  const isVerifyingRef = useRef(false);

  // Auto-focus first input on mount
  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  // Initialize resend countdown
  useEffect(() => {
    const savedRequestTime = localStorage.getItem('lastOtpRequestTime');
    if (savedRequestTime) {
      const timeDiff = Date.now() - parseInt(savedRequestTime);
      const remainingTime = Math.max(0, 60 - Math.floor(timeDiff / 1000));
      if (remainingTime > 0) {
        setCountdown(remainingTime);
      } else {
        setCanResend(true);
      }
    } else {
      setCanResend(true);
    }
  }, []);

  // Countdown timer
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Handle OTP verification
  const handleVerifyOtp = useCallback(async (otpString) => {
    if (otpString.length !== 6 || isVerifyingRef.current) return;

    isVerifyingRef.current = true;
    setIsVerifying(true);
    try {
      // For now, simulate verification
      // In real implementation, this would call backend verification
      console.log('Verifying OTP:', otpString, 'for phone:', normalizedPhone);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Call success callback
      onSuccess?.(otpString);
    } catch (error) {
      console.error('OTP verification error:', error);
    } finally {
      isVerifyingRef.current = false;
      setIsVerifying(false);
    }
  }, [normalizedPhone, onSuccess]);

  // Check if OTP is complete
  useEffect(() => {
    const otpString = otp.join('');
    const complete = otpString.length === 6 && /^\d{6}$/.test(otpString);
    setIsComplete(complete);

    // Auto-submit when complete
    if (complete) {
      handleVerifyOtp(otpString);
    }
  }, [otp, handleVerifyOtp]);

  // Handle input change
  const handleInputChange = (index, value) => {
    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];

    if (value.length > 1) {
      // Handle paste
      const pastedDigits = value.slice(0, 6).split('');
      for (let i = 0; i < pastedDigits.length && index + i < 6; i++) {
        newOtp[index + i] = pastedDigits[i];
      }
      setOtp(newOtp);

      // Focus last filled input or next empty
      const nextIndex = Math.min(index + pastedDigits.length, 5);
      if (inputRefs.current[nextIndex]) {
        inputRefs.current[nextIndex].focus();
        setActiveIndex(nextIndex);
      }
    } else {
      // Single digit input
      newOtp[index] = value;
      setOtp(newOtp);

      // Auto-focus next input
      if (value && index < 5) {
        if (inputRefs.current[index + 1]) {
          inputRefs.current[index + 1].focus();
          setActiveIndex(index + 1);
        }
      }
    }
  };

  // Handle key down
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace') {
      if (!otp[index] && index > 0) {
        // Move to previous input if current is empty
        if (inputRefs.current[index - 1]) {
          inputRefs.current[index - 1].focus();
          setActiveIndex(index - 1);
        }
      } else {
        // Clear current input
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1].focus();
      setActiveIndex(index - 1);
    } else if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1].focus();
      setActiveIndex(index + 1);
    }
  };

  // Handle input focus
  const handleFocus = (index) => {
    setActiveIndex(index);
  };

  // Handle resend OTP
  const handleResend = async () => {
    if (!canResend || countdown > 0) return;

    try {
      // Reset countdown
      setCountdown(60);
      setCanResend(false);

      // Save request time
      const requestTime = Date.now();
      localStorage.setItem('lastOtpRequestTime', requestTime.toString());

      // Clear current OTP
      setOtp(['', '', '', '', '', '']);
      setActiveIndex(0);
      if (inputRefs.current[0]) {
        inputRefs.current[0].focus();
      }

      // Call resend callback
      onResend?.(normalizedPhone);
    } catch (error) {
      console.error('Resend OTP error:', error);
    }
  };

  // Clear OTP
  const handleClear = () => {
    setOtp(['', '', '', '', '', '']);
    setActiveIndex(0);
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-indigo-100 mb-2">
          Enter Verification Code
        </h3>
        <p className="text-gray-600 dark:text-indigo-100">
          We sent a 6-digit code to{' '}
          <span className="font-medium text-gray-800 dark:text-indigo-100">
            {phoneValidationService.getDisplayFormat(mobileNumber)}
          </span>
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          className="p-4 bg-red-50 border border-red-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-red-600 dark:text-red-200 text-sm text-center">{error}</p>
        </motion.div>
      )}

      {/* OTP Input Grid */}
      <div className="flex justify-center">
        <div className="flex space-x-3">
          {otp.map((digit, index) => (
            <motion.input
              key={index}
              ref={el => inputRefs.current[index] = el}
              type="text"
              inputMode="numeric"
              maxLength={6}
              value={digit}
              onChange={(e) => handleInputChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onFocus={() => handleFocus(index)}
              className={cn(
                'w-12 h-14 text-center text-xl font-bold rounded-xl border-2 transition-all duration-300',
                'bg-white/90 backdrop-blur-sm text-gray-900',
                'focus:outline-none focus:ring-4 focus:ring-opacity-50',
                activeIndex === index && 'border-indigo-500 focus:border-indigo-500 focus:ring-indigo-500',
                digit && 'border-green-500 bg-green-50',
                !digit && activeIndex !== index && 'border-gray-300',
                loading && 'opacity-50 cursor-not-allowed'
              )}
              disabled={loading || isVerifying}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
            />
          ))}
        </div>
      </div>

      {/* Loading State */}
      {(loading || isVerifying) && isComplete && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div className="inline-flex items-center space-x-2 text-gray-600">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
            <span>Verifying code...</span>
          </div>
        </motion.div>
      )}

      {/* Action Buttons */}
      <div className="space-y-4">
        {/* Resend Button */}
        <div className="text-center">
          <OTPButton
            countdown={countdown}
            onResend={handleResend}
            disabled={!canResend || countdown > 0}
            variant="outline"
            size="default"
          >
            Resend Code
          </OTPButton>
        </div>

        {/* Clear Button */}
        <div className="text-center">
          <button
            onClick={handleClear}
            className="text-gray-500 bg-transparent hover:bg-transparent hover:text-gray-700 text-sm transition-colors"
            disabled={loading || isVerifying}
          >
            Clear and try again
          </button>
        </div>
      </div>

      {/* Help Text */}
      <motion.div
        className="p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="text-center space-y-2">
          <h4 className="text-blue-700 font-medium">Having trouble?</h4>
          <ul className="text-blue-600 text-sm space-y-1">
            <li>• Check your SMS messages</li>
            <li>• Code expires in 5 minutes</li>
            <li>• Make sure you have signal</li>
            <li>• Try requesting a new code if needed</li>
          </ul>
        </div>
      </motion.div>

      {/* Back Button */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="text-gray-500 bg-transparent hover:bg-transparent hover:text-gray-700 text-sm transition-colors"
          disabled={loading || isVerifying}
        >
          ← Change phone number
        </button>
      </div>
    </motion.div>
  );
};

export default OTPVerificationInput;
