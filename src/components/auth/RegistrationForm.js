/**
 * Registration Form Component
 * Complete registration form with all required fields matching backend requirements
 */

import React, { useState, useMemo, useCallback, forwardRef } from 'react';
import { motion } from 'framer-motion';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useAuth } from '../../contexts/AuthContext';
import { firebaseMessaging } from '../../services/firebaseMessaging';
import Form<PERSON>ield, { DateField, SelectField } from '../ui/FormField';
import { SubmitButton } from '../ui/GradientButton';
import { validatePassword, validateNickname } from '../../utils/validationUtils';
import { isValidEmail } from '../../lib/utils';
import { UserIcon, EnvelopeIcon, IdentificationIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';

/**
 * RegistrationForm Component
 * @param {Object} props
 * @param {Object} props.initialData - Initial form data
 * @param {Function} props.onSuccess - Callback when form is completed
 * @param {Function} props.onBack - Callback to go back
 * @param {string} props.className - Additional CSS classes
 */
const RegistrationForm = ({
  initialData = {},
  onSuccess,
  onBack,
  className = ''
}) => {
  // No need for register, loading, error from useAuth here, as this form only collects data
  const [isSubmitting, setIsSubmitting] = useState(false); // Local loading state for this form
  const { t } = useTranslation('auth');

  // Form state
  const [formData, setFormData] = useState({
    name: initialData.name || '',
    email: initialData.email || '',
    nickname: initialData.nickname || '',
    gender: initialData.gender || '',
    dateOfBirth: initialData.dateOfBirth || '',
    password: '',
    confirmPassword: '',
    ...initialData
  });

  // Validation state
  // Initially do not mark fields as valid so the success icon doesn't appear
  const [validation, setValidation] = useState({
    name: { valid: null, message: '' },
    email: { valid: null, message: '' },
    nickname: { valid: null, message: '' },
    gender: { valid: null, message: '' },
    dateOfBirth: { valid: null, message: '' },
    password: { valid: null, message: '' },
    confirmPassword: { valid: null, message: '' }
  });

  // Handle input changes
  const handleInputChange = (field) => (e) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));

    // Real-time validation
    validateField(field, value);
  };

  // Validate individual field
  const validateField = useCallback((field, value, skipStateUpdate = false) => {
    let fieldValidation = { valid: true, message: '' };

    switch (field) {
      case 'name':
        if (!value.trim()) {
          fieldValidation = { valid: false, message: 'Name is required' };
        } else if (value.length > 255) {
          fieldValidation = { valid: false, message: 'Name must not exceed 255 characters' };
        }
        break;

      case 'email':
        if (!value.trim()) {
          fieldValidation = { valid: false, message: 'Email is required' };
        } else if (!isValidEmail(value)) {
          fieldValidation = { valid: false, message: 'Please enter a valid email address' };
        }
        break;

      case 'nickname':
        fieldValidation = validateNickname(value);
        break;

      case 'gender':
        if (!value) {
          fieldValidation = { valid: false, message: 'Gender is required' };
        }
        break;

      case 'dateOfBirth':
        if (!value) {
          fieldValidation = { valid: false, message: 'Date of birth is required' };
        } else {
          const birthDate = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          if (age < 13) {
            fieldValidation = { valid: false, message: 'You must be at least 13 years old' };
          } else if (age > 120) {
            fieldValidation = { valid: false, message: 'Please enter a valid date of birth' };
          }
        }
        break;

      case 'password':
        fieldValidation = validatePassword(value);
        break;

      case 'confirmPassword':
        if (!value) {
          fieldValidation = { valid: false, message: 'Please confirm your password' };
        } else if (value !== formData.password) {
          fieldValidation = { valid: false, message: 'Passwords do not match' };
        }
        break;

      default:
        break;
    }

    if (!skipStateUpdate) {
      setValidation(prev => ({ ...prev, [field]: fieldValidation }));
    }
    return fieldValidation.valid;
  }, [formData.password]);

  // Validate entire form (for submission)
  const validateForm = useCallback(() => {
    const fields = ['name', 'email', 'nickname', 'gender', 'dateOfBirth', 'password', 'confirmPassword'];
    let isValid = true;

    fields.forEach(field => {
      const fieldValid = validateField(field, formData[field]);
      if (!fieldValid) isValid = false;
    });

    return isValid;
  }, [formData, validateField]);

  // Check if form is valid (for button state) - memoized to prevent infinite loops
  const isFormValid = useMemo(() => {
    const fields = ['name', 'email', 'nickname', 'gender', 'dateOfBirth', 'password', 'confirmPassword'];

    return fields.every(field => {
      const fieldValid = validateField(field, formData[field], true); // Skip state update
      return fieldValid;
    });
  }, [formData, validateField]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;

    try {
      // Get device info
      const deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      };

      // Initialize Firebase messaging if supported
      // if (firebaseMessaging.isSupported()) {
      //   try {
      //     await firebaseMessaging.initialize();
      //   } catch (error) {
      //     console.warn('Failed to initialize Firebase messaging:', error);
      //     // Continue with registration even if messaging fails
      //   }
      // }

      if (!validateForm()) {
        setIsSubmitting(false);
        return;
      }

      onSuccess?.(formData, deviceInfo);
      setIsSubmitting(false); // Reset after calling onSuccess
    } catch (error) {
      console.error('Error in registration form submission:', error);
      setIsSubmitting(false);
    }
  };

  // Gender options
  const genderOptions = [
    { value: 'Male', label: 'Male' },
    { value: 'Female', label: 'Female' },
    { value: 'Other', label: 'Other' }
  ];

  // Custom input for react-datepicker that uses FormField styling
  const DateOfBirthInput = forwardRef(({ value, onClick, onChange, onBlur, error, validation, label, required }, ref) => (
    <FormField
      label={label}
      type="text"
      value={value}
      onChange={() => {}} // Prevent manual typing
      onClick={onClick}
      onBlur={onBlur}
      required={required}
      validation={validation}
      readOnly
      ref={ref}
      placeholder="YYYY-MM-DD"
      autoComplete="off"
      error={error}
    />
  ));
  DateOfBirthInput.displayName = 'DateOfBirthInput';

  return (
    <motion.div
      className={cn('space-y-4 mt-5 bg-white dark:bg-gray-900 rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-gray-100 dark:border-gray-800 p-6', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center mb-7">
        <h3 className="text-xl font-semibold text-black-700 dark:text-yellow-400">
          {t('register.header', 'Complete Your Profile')}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mt-2">
          {t('register.subtitle', 'Just a few more details to create your gaming account')}
        </p>
      </div>

      {/* Registration Form */}
      <form onSubmit={handleSubmit} className="mt-15 text-left space-y-3 max-w-4xl">
        {/* Name Field */}
        <FormField
          label={t('register.name', 'Full Name')}
          type="text"
          value={formData.name}
          onChange={handleInputChange('name')}
          placeholder={t('register.placeholders.firstName', 'Enter your full name')}
          required
          icon={<UserIcon className="dark:text-indigo-100" />}
          validation={validation.name}
          maxLength={255}
        />

        {/* Email Field */}
        <FormField
          label={t('register.labels.email', 'Email Address')}
          type="email"
          value={formData.email}
          onChange={handleInputChange('email')}
          placeholder={t('register.placeholders.email', 'Enter your email address')}
          required
          icon={<EnvelopeIcon />}
          validation={validation.email}
          helperText={t('register.helperText.email', "We'll use this for account recovery and important updates")}
        />

        {/* Nickname Field */}
        <FormField
          label={t('register.nickname', 'Gaming Nickname')}
          type="text"
          value={formData.nickname}
          onChange={handleInputChange('nickname')}
          placeholder={t('register.placeholders.nickname', 'Choose your gaming nickname')}
          required
          icon={<IdentificationIcon />}
          validation={validation.nickname}
          helperText={t('register.helperText.nickname', 'Max 20 characters, emojis allowed! 🎮')}
          maxLength={20}
        />

        {/* Gender and Date of Birth Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectField
            label={t('register.gender', 'Gender')}
            options={genderOptions}
            value={formData.gender}
            onChange={handleInputChange('gender')}
            placeholder={t('register.gender', 'Select gender')}
            required
            validation={validation.gender}
          />

          {/* Date of Birth with react-datepicker */}
          <DatePicker
            selected={formData.dateOfBirth ? new Date(formData.dateOfBirth) : null}
            onChange={date => {
              // Format as YYYY-MM-DD for consistency
              const formatted = date ? date.toISOString().split('T')[0] : '';
              setFormData(prev => ({ ...prev, dateOfBirth: formatted }));
              // Real-time validation
              validateField('dateOfBirth', formatted);
            }}
            maxDate={new Date()}
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            dateFormat="yyyy-MM-dd"
            customInput={
              <DateOfBirthInput
                label={t('register.dateOfBirth', 'Date of Birth')}
                required
                validation={validation.dateOfBirth}
                error={validation.dateOfBirth && !validation.dateOfBirth.valid ? validation.dateOfBirth.message : ''}
              />
            }
            placeholderText="YYYY-MM-DD"
            autoComplete="off"
          />
        </div>

        {/* Password Fields */}
        <div className="space-y-4">
          <FormField
            label={t('register.password', 'Password')}
            type="password"
            value={formData.password}
            onChange={handleInputChange('password')}
            placeholder={t('register.placeholders.password', 'Create a secure password')}
            required
            icon={<LockClosedIcon className="dark:text-indigo-100" />}
            validation={validation.password}
            helperText={t('register.helperText.password', 'Minimum 6 characters with letters and numbers')}
          />

          <FormField
            label={t('register.confirmPassword', 'Confirm Password')}
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange('confirmPassword')}
            placeholder={t('register.placeholders.confirmPassword', 'Confirm your password')}
            required
            icon={<LockClosedIcon className="dark:text-indigo-100" />}
            validation={validation.confirmPassword}
          />
        </div>

        {/* Submit Button */}
        <SubmitButton
          loading={isSubmitting} // Use local submitting state
          disabled={!isFormValid || isSubmitting}
        >
          {t('register.navigation.submit', 'Create Account')}
        </SubmitButton>
      </form>

      {/* Terms and Privacy */}
      <motion.div
        className="bg-white/5 dark:bg-gray-800/40 rounded-xl border border-white/10 dark:border-gray-700"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <p className="text-white/60 dark:text-gray-400 text-sm text-center">
          {t('register.termsAgreement', 'By creating an account, you agree to our Terms of Service and Privacy Policy')}
        </p>
      </motion.div>

      {/* Back Button */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="text-indigo-700 dark:text-yellow-400 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl dark:shadow-indigo-900/30 hover:text-gray-700 dark:hover:text-yellow-300 hover:bg-transparent hover:bg-gray-200 dark:hover:bg-gray-700 hover:rounded-2xl text-sm transition-colors"
          disabled={isSubmitting} // Use local submitting state
        >
          {t('register.navigation.back', '← Back to verification')}
        </button>
      </div>
    </motion.div>
  );
};

export default RegistrationForm;
