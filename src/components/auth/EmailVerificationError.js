import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

const EmailVerificationError = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation('auth');

  // Get error message from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const errorMessage = queryParams.get('message') || t('email.verificationError', 'Email verification failed');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center"
      >
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t('email.verificationFailed', 'Verification Failed')}
        </h2>
        
        <p className="text-gray-600 mb-6">
          {errorMessage}
        </p>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-sm text-red-800 mb-6">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="font-medium">{t('email.whatToDo', 'What to do next?')}</p>
              <p>{t('email.errorNextSteps', 'Please try requesting a new verification email or contact support if the problem persists.')}</p>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <button
            onClick={() => navigate('/email/verify')}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            {t('email.requestNewVerification', 'Request New Verification')}
          </button>

          <button
            onClick={() => navigate('/home')}
            className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            {t('email.returnToHome', 'Return to Home')}
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default EmailVerificationError; 