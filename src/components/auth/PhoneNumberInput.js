/**
 * Phone Number Input Component
 * Specialized phone number input with country code selection and validation
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import <PERSON><PERSON>ield from '../ui/FormField';
import { phoneValidationService } from '../../services/phoneValidationService';
import { PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import { MY, US, GB, SG, ID, TH, VN, PH } from 'country-flag-icons/react/3x2';

// Country codes data
export const countryCodes = [
  { code: '+60', flag: MY, name: 'Malaysia' },
  { code: '+1', flag: US, name: 'United States' },
  { code: '+44', flag: GB, name: 'United Kingdom' },
  { code: '+65', flag: SG, name: 'Singapore' },
  { code: '+62', flag: ID, name: 'Indonesia' },
  { code: '+66', flag: TH, name: 'Thailand' },
  { code: '+84', flag: VN, name: 'Vietnam' },
  { code: '+63', flag: PH, name: 'Philippines' }
];

/**
 * PhoneNumberInput Component
 * @param {Object} props
 * @param {string} props.value - Current phone number value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.label - Field label
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Required field
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.helperText - Helper text
 * @param {boolean} props.showFormats - Show supported formats
 * @param {string} props.className - Additional CSS classes
 */
const PhoneNumberInput = ({
  value = '',
  onChange,
  label = 'Mobile Number',
  placeholder = '123456789',
  required = false,
  disabled = false,
  helperText = 'Enter your mobile number',
  showFormats = true,
  disableValidation = false,
  className = 'dark:text-indigo-100 dark:hover:bg-gray-700'
}) => {
  // Internal state for formatting
  const [internalValue, setInternalValue] = useState(value);
  const [selectedCountryCode, setSelectedCountryCode] = useState('+60'); // Default to Malaysia
  const [validation, setValidation] = useState({ valid: true, message: '' });
  const [isFocused, setIsFocused] = useState(false);
  const [showCountrySelector, setShowCountrySelector] = useState(false);

  // Update internal value when prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Handle country code selection
  const handleCountrySelect = (code) => {
    setSelectedCountryCode(code);
    setShowCountrySelector(false);
    
    // Revalidate with new country code
    const fullNumber = code + internalValue.replace(/\D/g, '');
    if (!disableValidation) {
      const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
      setValidation(phoneValidation);
    } else {
      setValidation({ valid: true, message: '' });
    }
    
    // Notify parent of change
    if (onChange) {
      const syntheticEvent = {
        target: {
          value: internalValue,
          countryCode: code
        }
      };
      onChange(syntheticEvent);
    }
  };

  // Handle input change with formatting
  const handleChange = (e) => {
    const inputValue = e.target.value;

    // Keep only digits
    const digitsOnly = inputValue.replace(/\D/g, '');

    // Update internal state as string
    setInternalValue(digitsOnly);

    // Combine with country code before validation
    const fullNumber = selectedCountryCode + digitsOnly;
    if (!disableValidation) {
      const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
      setValidation(phoneValidation);
    } else {
      setValidation({ valid: true, message: '' });
    }
    
    // Call parent onChange with digit string and country code
    if (onChange) {
      const syntheticEvent = {
        target: {
          value: digitsOnly,
          countryCode: selectedCountryCode
        }
      };
      onChange(syntheticEvent);
    }
  };

  // Get display value for the input
  const getDisplayValue = () => {
    if (isFocused || internalValue) {
      return internalValue;
    }
    return '';
  };

  // Get validation state with enhanced messaging
  const getValidationState = () => {
    if (disableValidation) {
      return { valid: true, message: '' };
    }

    if (!internalValue) {
      return required
        ? { valid: false, message: 'Phone number is required' }
        : { valid: true, message: '' };
    }

    return validation;
  };

  const currentValidation = getValidationState();

  return (
    <motion.div
      className={cn('space-y-3', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Main Input Field */}
      <div className="relative group flex">
        {/* Country Code Selector */}
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowCountrySelector(!showCountrySelector)}
            className="flex items-center space-x-2 px-4 py-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-l-2xl hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-300"
          >
            {countryCodes.find(c => c.code === selectedCountryCode)?.flag && 
              React.createElement(countryCodes.find(c => c.code === selectedCountryCode).flag, {
                className: "w-5 h-5"
              })
            }
            <span className="text-gray-700 dark:text-gray-200">{selectedCountryCode}</span>
          </button>

          {/* Country Code Dropdown */}
          {showCountrySelector && (
            <div className="absolute z-50 mt-1 w-48 bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto">
              {countryCodes.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountrySelect(country.code)}
                  className="flex items-center space-x-2 w-full px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-800 active:bg-blue-50 dark:active:bg-blue-900 transition-colors duration-200"
                >
                  {React.createElement(country.flag, {
                    className: "w-5 h-5"
                  })}
                  <span className="text-gray-700 dark:text-gray-200">{country.code}</span>
                  <span className="text-gray-500 dark:text-gray-400 text-sm">{country.name}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <div className="flex-1 relative">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400 group-focus-within:text-blue-600 dark:group-focus-within:text-blue-400 transition-colors duration-300 z-10">
            <PhoneIcon className="w-5 h-5" />
          </div>
          <input
            type="tel"
            inputMode="numeric"
            pattern="[0-9]*"
            value={getDisplayValue()}
            onChange={handleChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            className="w-full pl-12 pr-5 py-4 bg-transparent backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-r-2xl text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90 dark:group-hover:bg-gray-900/90"
            required={required}
            disabled={disabled}
          />
          {/* Field Glow Effect */}
          <div className="absolute inset-0 rounded-r-2xl bg-gradient-to-r from-blue-500/10 to-indigo-500/10 dark:from-blue-900/10 dark:to-indigo-900/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </div>
      </div>

      {/* Validation Message */}
      {!disableValidation && !currentValidation.valid && (
        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{currentValidation.message}</p>
      )}

      {/* Helper Text */}
      {helperText && (
        <p className="text-gray-500 dark:text-yellow-400 text-sm mt-1">{helperText}</p>
      )}

      {/* Format Examples */}
      {showFormats && !disableValidation && (
        <motion.div
          className="p-3 bg-blue-500/10 dark:bg-blue-900/20 border border-blue-500/20 dark:border-blue-900/40 rounded-xl"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="w-4 h-4 text-blue-400 dark:text-blue-300 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="text-blue-400 dark:text-blue-300 font-medium text-sm mb-1">Supported Formats:</h5>
              <div className="grid grid-cols-2 gap-1 text-blue-300/80 dark:text-blue-200/80 text-xs font-mono">
                {phoneValidationService.getSupportedFormats().map((format, index) => (
                  <div key={index} className="truncate">
                    {format}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Real-time Format Preview */}
      {internalValue && !disableValidation && validation.valid && (
        <motion.div
          className="p-2 bg-green-500/10 dark:bg-green-900/20 border border-green-500/20 dark:border-green-900/40 rounded-lg"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 dark:bg-green-300 rounded-full" />
            <span className="text-green-400 dark:text-green-300 text-sm">
              Formatted: {selectedCountryCode}{internalValue}
            </span>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default PhoneNumberInput;
