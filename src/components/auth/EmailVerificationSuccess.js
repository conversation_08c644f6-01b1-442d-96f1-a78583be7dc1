import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

const EmailVerificationSuccess = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('auth');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center"
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t('email.verificationSuccess', 'Email Verified Successfully!')}
        </h2>
        
        <p className="text-gray-600 mb-6">
          {t('email.verificationSuccessMessage', 'Your email has been verified. You can now access all features of your account.')}
        </p>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-sm text-green-800 mb-6">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="font-medium">{t('email.verificationComplete', 'Verification Complete')}</p>
              <p>{t('email.verificationCompleteMessage', 'You can now access all email-related features and receive important notifications.')}</p>
            </div>
          </div>
        </div>

        <button
          onClick={() => navigate('/home')}
          className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
        >
          {t('email.continueToHome', 'Continue to Home')}
        </button>
      </motion.div>
    </div>
  );
};

export default EmailVerificationSuccess; 