import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * OAuthButtons Component
 * @param {Object} props
 * @param {Function} props.onGoogleSignIn - Callback for Google sign in
 * @param {Function} props.onAppleSignIn - Callback for Apple sign in
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.loadingStates - Loading states for each provider
 */
const OAuthButtons = ({
  onGoogleSignIn,
  onAppleSignIn,
  disabled = false,
  className = '',
  loadingStates = {
    google: false,
    apple: false
  }
}) => {
  const { t } = useTranslation('auth');

  const renderButtonContent = (provider, isLoading) => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center space-x-3">
          <ArrowPathIcon className="w-5 h-5 animate-spin text-gray-600" />
          <span className="text-gray-700 font-medium">
            {t(`register.${provider}SignIn`)}
          </span>
        </div>
      );
    }

    if (provider === 'google') {
      return (
        <div className="relative z-10 flex items-center space-x-3">
          <svg className="w-6 h-6" viewBox="0 0 24 24">
            <path
              fill="#4285F4"
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            />
            <path
              fill="#34A853"
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            />
            <path
              fill="#FBBC05"
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            />
            <path
              fill="#EA4335"
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            />
          </svg>
          <span className="text-gray-700 font-bold text-sm">Google</span>
        </div>
      );
    }

    if (provider === 'apple') {
      return (
        <div className="relative z-10 flex items-center space-x-3">
          <svg className="w-6 h-6 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
          </svg>
          <span className="text-gray-800 font-bold text-sm">Apple</span>
        </div>
      );
    }
  };

  return (
    <motion.div
      className={cn('space-y-4', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-300">
            {t('register.orContinueWith')}
          </span>
        </div>
      </div>

      {/* OAuth Buttons */}
      <div className="grid grid-cols-2 gap-3">
        {/* Google Sign In */}
        <motion.button
          onClick={onGoogleSignIn}
          disabled={disabled || loadingStates.google}
          className={cn(
            'group relative flex items-center justify-center py-4 px-6',
            'bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-2xl',
            'hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 shadow-xl dark:shadow-indigo-900/30 overflow-hidden',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            loadingStates.google && 'cursor-wait'
          )}
          whileHover={{ scale: loadingStates.google ? 1 : 1.02, y: loadingStates.google ? 0 : -2 }}
          whileTap={{ scale: loadingStates.google ? 1 : 0.98 }}
          aria-label={t('register.googleSignIn')}
          aria-busy={loadingStates.google}
        >
          {/* Button Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-red-400/10 dark:from-blue-400/20 dark:to-red-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {renderButtonContent('google', loadingStates.google)}
        </motion.button>

        {/* Apple Sign In */}
        <motion.button
          onClick={onAppleSignIn}
          disabled={disabled || loadingStates.apple}
          className={cn(
            'group relative flex items-center justify-center py-4 px-6',
            'bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-2xl',
            'hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-200 shadow-xl dark:shadow-indigo-900/30 overflow-hidden',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            loadingStates.apple && 'cursor-wait'
          )}
          whileHover={{ scale: loadingStates.apple ? 1 : 1.02, y: loadingStates.apple ? 0 : -2 }}
          whileTap={{ scale: loadingStates.apple ? 1 : 0.98 }}
          aria-label={t('register.appleSignIn')}
          aria-busy={loadingStates.apple}
        >
          {/* Button Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-gray-400/10 to-gray-600/10 dark:from-gray-400/20 dark:to-gray-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {renderButtonContent('apple', loadingStates.apple)}
        </motion.button>
      </div>
    </motion.div>
  );
};

export default OAuthButtons; 