/**
 * Login Form Component
 * JWT login form with Malaysian phone input and password authentication
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../components/common/ToastProvider';
import { phoneValidationService } from '../../services/phoneValidationService';
import { validatePassword } from '../../utils/validationUtils';
import { PhoneIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';
import { getBackendBaseUrl } from '../../utils/backendUrl';
import { MY, US, GB, SG, ID, TH, VN, PH } from 'country-flag-icons/react/3x2';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

// Country codes data
const countryCodes = [
  { code: '+60', flag: MY, name: 'Malaysia' },
  { code: '+1', flag: US, name: 'United States' },
  { code: '+44', flag: GB, name: 'United Kingdom' },
  { code: '+65', flag: SG, name: 'Singapore' },
  { code: '+62', flag: ID, name: 'Indonesia' },
  { code: '+66', flag: TH, name: 'Thailand' },
  { code: '+84', flag: VN, name: 'Vietnam' },
  { code: '+63', flag: PH, name: 'Philippines' }
];

/**
 * LoginForm Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when login succeeds
 * @param {Function} props.onForgotPassword - Callback for forgot password
 * @param {string} props.className - Additional CSS classes
 */
const LoginForm = ({
  onSuccess,
  onForgotPassword,
  className = ''
}) => {
  const { login, loading } = useAuth();
  const { error: showErrorToast, success: showSuccessToast, info: showInfoToast } = useToast();
  const { t } = useTranslation('auth');

  // Helper function to show error notifications
  const showError = (message) => {
    showErrorToast(message, 5000);
  };

  // Form state
  const [formData, setFormData] = useState({
    mobileNumber: '',
    password: '',
    countryCode: '+60' // Default to Malaysia
  });

  // Validation state
  const [validation, setValidation] = useState({
    mobileNumber: { valid: true, message: '' },
    password: { valid: true, message: '' }
  });

  // UI state
  const [showCountrySelector, setShowCountrySelector] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Load remembered mobile number
  useEffect(() => {
    const lastMobileNumber = localStorage.getItem('lastMobileNumber');
    if (lastMobileNumber) {
      setFormData(prev => ({ ...prev, mobileNumber: lastMobileNumber }));
      setRememberMe(true);
    }
  }, []);

  // Handle country code selection
  const handleCountrySelect = (code) => {
    setFormData(prev => ({ ...prev, countryCode: code }));
    setShowCountrySelector(false);
  };

  // Handle input changes
  const handleInputChange = (field) => (e) => {
    const value = e.target.value;

    // Handle phone number formatting
    if (field === 'mobileNumber') {
      // Remove any non-digit characters
      const digitsOnly = value.replace(/\D/g, '');
      // Remove leading zero if present
      const formattedValue = digitsOnly.replace(/^0+/, '');
      
      setFormData(prev => ({ ...prev, [field]: formattedValue }));

      // Combine with country code before validation
      const fullNumber = formData.countryCode + formattedValue;
      const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
      setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));

      // Real-time password validation
      if (field === 'password') {
        const passwordValidation = validatePassword(value);
        setValidation(prev => ({ ...prev, password: passwordValidation }));
      }
    }
  };

  // Validate form
  const validateForm = () => {
    // Combine country code and phone number before validation
    const fullNumber = formData.countryCode + formData.mobileNumber;
    const phoneValidation = phoneValidationService.validateWithMessage(fullNumber);
    const passwordValidation = validatePassword(formData.password);

    setValidation({
      mobileNumber: phoneValidation,
      password: passwordValidation
    });

    // Show popup notifications for validation errors
    if (!phoneValidation.valid) {
      showError(phoneValidation.message);
      return false;
    }

    if (!passwordValidation.valid) {
      showError(passwordValidation.message);
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Combine country code and phone number
      const fullPhoneNumber = formData.countryCode + formData.mobileNumber;

      // Prepare device info for push notifications
      const deviceInfo = {
        device_type: 'web',
        device_name: navigator.userAgent
      };

      const result = await login(fullPhoneNumber, formData.password, rememberMe, deviceInfo);

      if (result.success) {
        onSuccess?.(result);
      } else {
        // Show error notification for login failure
        showError(result.message || result.error || 'Login failed. Please check your credentials and try again.');
      }
    } catch (err) {
      console.error('Login error:', err);
      showError(err?.message || 'An unexpected error occurred. Please try again.');
    }
  };

  // Handle social login by redirecting to backend OAuth route
  const handleSocialLogin = (provider) => {
    const baseUrl = getBackendBaseUrl();
    window.location.href = `${baseUrl}/auth/oauth/${provider}/redirect`;
  };

  return (
    <motion.div
      className={cn('w-full space-y-4', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >


      {/* Premium Login Form */}
      <form onSubmit={handleSubmit} className="max-w-xl space-y-2 sm:space-y-4">
        {/* Grouped Phone and Password Fields */}
        <div className="space-y-2 bg-gray-50 dark:bg-gray-900 rounded-xl p-2 sm:p-4 border border-gray-100 dark:border-gray-700 shadow-lg dark:shadow-indigo-900/30">
          {/* Enhanced Mobile Number Field with Country Code - 2025 UI */}
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <div className="flex items-center gap-2 mb-1">
              <label className="block text-sm text-left font-bold text-gray-700 dark:text-yellow-400 tracking-wide m-0 p-0">
                {t('login.phone', 'Phone Number')}
              </label>
              {/* Info Icon for Phone Number Format Guide */}
              <div className="group relative inline-block align-middle">
                <button
                  type="button"
                  tabIndex={0}
                  aria-label="Show phone number format guide"
                  className="flex items-center mt-3 justify-center w-5 h-5 rounded-full bg-green-200 dark:bg-green-900 text-green-700 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800 focus:bg-green-200 dark:focus:bg-green-800 border border-green-200 dark:border-green-700 transition-colors duration-200 shadow-sm ml-1"
                  style={{ fontSize: '14px' }}
                >
                  <InformationCircleIcon className="w-4 h-4 text-green-800 dark:text-green-200 mt-0.5 flex-shrink-0" />
                </button>
                {/* Dropdown/Popover */}
                <div
                  className="absolute left-1/2 -translate-x-1/2 z-50 mt-2 w-80 max-w-xs bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-xl text-green-800 dark:text-green-200 text-xs sm:text-sm shadow-xl p-3 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none group-hover:pointer-events-auto group-focus-within:pointer-events-auto transition-opacity duration-200"
                  role="tooltip"
                >
                  <p className="font-medium text-center">{t('login.phoneGuide.title')}</p>
                  <ul className="mt-1 list-disc list-inside space-y-0.5">
                    <li>{t('login.phoneGuide.correct')}</li>
                    <li>{t('login.phoneGuide.incorrect1')}</li>
                    <li>{t('login.phoneGuide.incorrect2')}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="relative group flex">
              {/* Enhanced Country Code Selector */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowCountrySelector(!showCountrySelector)}
                  className="flex items-center space-x-2 px-4 py-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-l-2xl hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:border-blue-500/50"
                  aria-label="Select country code"
                >
                  {countryCodes.find(c => c.code === formData.countryCode)?.flag && 
                    React.createElement(countryCodes.find(c => c.code === formData.countryCode).flag, {
                      className: "w-5 h-5"
                    })
                  }
                  <span className="text-gray-700 dark:text-gray-200 font-medium">{formData.countryCode}</span>
                  <svg className="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Enhanced Country Code Dropdown */}
                {showCountrySelector && (
                  <motion.div 
                    className="absolute z-50 mt-1 w-48 bg-white dark:bg-gray-900 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto backdrop-blur-xl"
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    {countryCodes.map((country) => (
                      <button
                        key={country.code}
                        type="button"
                        onClick={() => handleCountrySelect(country.code)}
                        className="flex items-center bg-white dark:bg-gray-900 space-x-3 w-full px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 active:bg-blue-50 dark:active:bg-blue-900 transition-colors duration-200 first:rounded-t-xl last:rounded-b-xl"
                      >
                        {React.createElement(country.flag, {
                          className: "w-5 h-5"
                        })}
                        <span className="text-gray-700 dark:text-gray-200 font-medium">{country.code}</span>
                        <span className="text-gray-500 dark:text-gray-400 text-sm">{country.name}</span>
                      </button>
                    ))}
                  </motion.div>
                )}
              </div>

              {/* Enhanced Phone Number Input */}
              <div className="flex-1 relative">
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400 group-focus-within:text-blue-600 dark:group-focus-within:text-blue-400 transition-colors duration-300 z-10">
                  <PhoneIcon className="w-5 h-5" />
                </div>
                <input
                  type="tel"
                  value={formData.mobileNumber}
                  onChange={handleInputChange('mobileNumber')}
                  placeholder={t('login.phonePlaceholder', '123456789')}
                  className="w-full max-w-md pl-12 pr-5 py-4 bg-transparent backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-r-2xl text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90 dark:group-hover:bg-gray-900/90"
                  required
                  aria-label="Phone number"
                />
              </div>
            </div>
          </motion.div>

          {/* Enhanced Password Field - 2025 UI */}
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <label className="block text-sm text-left font-bold text-gray-700 dark:text-yellow-400 tracking-wide">
              {t('login.password', 'Password')}
            </label>
            <div className="relative group w-full mx-auto">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-600 dark:text-gray-400 group-focus-within:text-blue-600 dark:group-focus-within:text-blue-400 transition-colors duration-300 z-10">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={handleInputChange('password')}
                placeholder="••••••••••••"
                className="w-full max-w-md pl-12 pr-12 py-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-2xl text-gray-800 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 group-hover:bg-white/90 dark:group-hover:bg-gray-900/90"
                required
                aria-label="Password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 bg-transparent hover:bg-transparent hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 rounded-md p-1"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
          </motion.div>

          {/* Remember Me & Forgot Password Row - Flex Layout */}
          <motion.div
            className="flex items-center justify-between pt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            {/* Left: Toggle + Label */}
            <div className="flex items-center space-x-2">
              <label className="flex items-center cursor-pointer group pt-9">
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="sr-only pt-4"
                    aria-label="Remember me"
                  />
                  {/* Modern Toggle Switch */}
                  <div className={cn(
                    "relative w-11 h-6 rounded-full transition-all duration-300 ease-out",
                    "border-2 shadow-inner",
                    rememberMe
                      ? "bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-600"
                      : "bg-gray-200 dark:bg-gray-800 border-gray-300 dark:border-gray-700 group-hover:border-blue-400 group-hover:bg-gray-100 dark:group-hover:bg-gray-900"
                  )}>
                    {/* Toggle Knob */}
                    <motion.div
                      className={cn(
                        "absolute top-0.5 w-5 h-5 rounded-full shadow-lg transition-all duration-300 ease-out",
                        "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700"
                      )}
                      animate={{
                        y: rememberMe ? -3 : -3,
                        x: rememberMe ? 20 : 0,
                        scale: rememberMe ? 1.1 : 1
                      }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                    >
                      {/* Check Icon */}
                      {rememberMe && (
                        <motion.svg
                          className="absolute inset-0 w-full h-full text-blue-600 dark:text-blue-400 p-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.2, delay: 0.1 }}
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7" />
                        </motion.svg>
                      )}
                    </motion.div>
                    {/* Toggle Glow Effect */}
                    <div className={cn(
                      "absolute inset-0 rounded-full opacity-0 transition-opacity duration-300",
                      rememberMe ? "bg-gradient-to-r from-blue-400/20 to-indigo-400/20" : "bg-blue-400/10"
                    )} />
                  </div>
                </div>
              </label>
              {/* Remember Me Text - Close to Toggle */}
              <span className="text-sm font-medium text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200 select-none">
                {t('login.rememberMe', 'Remember Me')}
              </span>
            </div>
            {/* Right: Forgot Password Button */}
            <button
              type="button"
              onClick={onForgotPassword}
              className="text-sm font-medium bg-transparent text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 rounded-md px-2 py-1"
              aria-label="Forgot password"
            >
              {t('login.forgotPassword', 'Forgot Password?')}
            </button>
          </motion.div>

          {/* Submit Button - Now inside the grouped fields for seamless connection */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <button
              type="submit"
              disabled={loading || !formData.mobileNumber || !formData.password}
              className="relative w-full px-6 bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700 hover:from-blue-500 hover:via-indigo-500 hover:to-blue-600 text-white font-bold text-lg rounded-2xl transition-all duration-300 disabled:cursor-not-allowed shadow-2xl border border-blue-500/30 overflow-hidden group focus-visible:outline-none focus-visible:ring-4 focus-visible:ring-blue-500/50"
              aria-label="Sign in to your account"
            >
              {/* Enhanced Button Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-indigo-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              {/* Subtle Background Animation */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              {/* Button Content */}
              <div className="relative z-10 flex items-center justify-center space-x-2">
                {loading ? (
                  <>
                    <motion.div
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    <span>{t('login.signingIn', 'Signing In...')}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    <span>{t('login.signInButton', 'Sign In')}</span>
                    <motion.div
                      className="w-2 h-2 bg-white/60 rounded-full"
                      animate={{ scale: [1, 1.5, 1], opacity: [0.6, 1, 0.6] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    />
                  </>
                )}
              </div>
            </button>
          </motion.div>
        </div>
      </form>
      {/* Enhanced Divider - 2025 UI */}
      {/* <motion.div
        className="relative mt-4 mb-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200/60" />
        </div>
        <div className="relative flex justify-center">
          <span className="px-6 py-2 bg-white/90 backdrop-blur-xl rounded-full text-gray-600 text-sm font-medium border border-gray-200/60 shadow-sm">
            {t('login.otherSignIn', 'Other Sign-In Options')}
          </span>
        </div>
      </motion.div> */}

      {/* Enhanced Social Login Buttons - 2025 UI */}
      {/* <motion.div
        className="grid grid-cols-2 gap-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.6 }}
      >
        <button
          onClick={() => handleSocialLogin('google')}
          className="group relative flex items-center justify-center space-x-3 py-4 px-6 bg-white/80 backdrop-blur-xl border border-gray-200 rounded-2xl hover:bg-white/90 hover:border-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl overflow-hidden focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50"
          aria-label="Sign in with Google"
        >
          {/* Enhanced Button Glow Effect */}
          {/* <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-red-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" /> */}

          {/* <div className="relative z-10 flex items-center space-x-3">
            <svg className="w-6 h-6" viewBox="0 0 24 24" aria-hidden="true">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            <span className="text-gray-700 font-bold text-sm group-hover:text-gray-800 transition-colors duration-200">Google</span>
          </div>
        </button>

        <button
          onClick={() => handleSocialLogin('apple')}
          className="group relative flex items-center justify-center space-x-3 py-4 px-6 bg-white/80 backdrop-blur-xl border border-gray-200 rounded-2xl hover:bg-white/90 hover:border-gray-300 transition-all duration-300 shadow-lg hover:shadow-xl overflow-hidden focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50"
          aria-label="Sign in with Apple"
        >
          {/* Enhanced Button Glow Effect */}
          {/* <div className="absolute inset-0 bg-gradient-to-r from-gray-400/10 to-gray-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" /> */}

          {/* <div className="relative z-10 flex items-center space-x-3">
            <svg className="w-6 h-6 text-gray-800" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
            </svg>
            <span className="text-gray-800 font-bold text-sm group-hover:text-gray-900 transition-colors duration-200">Apple</span>
          </div>
        </button>
      </motion.div> */}
    </motion.div>
  );
};

export default LoginForm;
