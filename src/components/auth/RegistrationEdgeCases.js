import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { ExclamationTriangleIcon, XCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * RegistrationEdgeCases Component
 * Handles various edge cases during registration
 */
export const RegistrationEdgeCases = {
  /**
   * Handle network connectivity issues
   * @param {Function} onRetry - Retry callback
   * @param {Function} onBack - Back callback
   */
  NetworkError: ({ onRetry, onBack }) => {
    const { t } = useTranslation('auth');
    return (
      <motion.div
        className="p-6 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="flex items-start space-x-3">
          <XCircleIcon className="w-6 h-6 text-red-500 dark:text-red-400 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">
              {t('register.errors.networkError.title')}
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-4">
              {t('register.errors.networkError.description')}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={onRetry}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 transition-colors"
              >
                {t('common.retry')}
              </button>
              <button
                onClick={onBack}
                className="px-4 py-2 bg-white dark:bg-gray-900 text-red-600 dark:text-red-400 border border-red-600 dark:border-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-gray-800 transition-colors"
              >
                {t('common.goBack')}
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  },

  /**
   * Handle session timeout
   * @param {Function} onRestart - Restart registration callback
   */
  SessionTimeout: ({ onRestart }) => {
    const { t } = useTranslation('auth');
    return (
      <motion.div
        className="p-6 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-6 h-6 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
              {t('register.errors.sessionTimeout.title')}
            </h3>
            <p className="text-yellow-600 dark:text-yellow-400 mb-4">
              {t('register.errors.sessionTimeout.description')}
            </p>
            <button
              onClick={onRestart}
              className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-800 transition-colors"
            >
              {t('register.errors.sessionTimeout.restart')}
            </button>
          </div>
        </div>
      </motion.div>
    );
  },

  /**
   * Handle duplicate account detection
   * @param {Function} onLogin - Login callback
   * @param {Function} onBack - Back callback
   */
  DuplicateAccount: ({ onLogin, onBack }) => {
    const { t } = useTranslation('auth');
    return (
      <motion.div
        className="p-6 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="flex items-start space-x-3">
          <CheckCircleIcon className="w-6 h-6 text-blue-500 dark:text-blue-300 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-2">
              {t('register.errors.duplicateAccount.title')}
            </h3>
            <p className="text-blue-600 dark:text-blue-300 mb-4">
              {t('register.errors.duplicateAccount.description')}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={onLogin}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
              >
                {t('register.errors.duplicateAccount.login')}
              </button>
              <button
                onClick={onBack}
                className="px-4 py-2 bg-white dark:bg-gray-900 text-blue-600 dark:text-blue-300 border border-blue-600 dark:border-blue-300 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-800 transition-colors"
              >
                {t('common.goBack')}
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  },

  /**
   * Handle rate limiting
   * @param {number} retryAfter - Seconds until retry is allowed
   * @param {Function} onBack - Back callback
   */
  RateLimit: ({ retryAfter, onBack }) => {
    const { t } = useTranslation('auth');
    return (
      <motion.div
        className="p-6 bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-6 h-6 text-orange-500 dark:text-orange-400 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-300 mb-2">
              {t('register.errors.rateLimit.title')}
            </h3>
            <p className="text-orange-600 dark:text-orange-300 mb-4">
              {t('register.errors.rateLimit.description', { seconds: retryAfter })}
            </p>
            <button
              onClick={onBack}
              className="px-4 py-2 bg-white dark:bg-gray-900 text-orange-600 dark:text-orange-300 border border-orange-600 dark:border-orange-300 rounded-lg hover:bg-orange-50 dark:hover:bg-gray-800 transition-colors"
            >
              {t('common.goBack')}
            </button>
          </div>
        </div>
      </motion.div>
    );
  },

  /**
   * Handle browser compatibility issues
   * @param {Function} onBack - Back callback
   */
  BrowserCompatibility: ({ onBack }) => {
    const { t } = useTranslation('auth');
    return (
      <motion.div
        className="p-6 bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="w-6 h-6 text-purple-500 dark:text-purple-300 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-300 mb-2">
              {t('register.errors.browserCompatibility.title')}
            </h3>
            <p className="text-purple-600 dark:text-purple-300 mb-4">
              {t('register.errors.browserCompatibility.description')}
            </p>
            <button
              onClick={onBack}
              className="px-4 py-2 bg-white dark:bg-gray-900 text-purple-600 dark:text-purple-300 border border-purple-600 dark:border-purple-300 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-800 transition-colors"
            >
              {t('common.goBack')}
            </button>
          </div>
        </div>
      </motion.div>
    );
  }
};

export default RegistrationEdgeCases; 