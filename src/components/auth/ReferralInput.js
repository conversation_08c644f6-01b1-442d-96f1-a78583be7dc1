/**
 * Referral Input Component
 * Handles both referral code and phone number types with validation
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import FormField from '../ui/FormField';
import PhoneNumberInput, { countryCodes } from './PhoneNumberInput';
import { GiftIcon, UserGroupIcon, PhoneIcon, TicketIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import { useTranslation } from 'react-i18next';

/**
 * ReferralInput Component
 * @param {Object} props
 * @param {string} props.referralType - Current referral type ('code' or 'phone')
 * @param {string} props.referralValue - Current referral value
 * @param {Function} props.onTypeChange - Callback when type changes
 * @param {Function} props.onValueChange - Callback when value changes
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 */
const ReferralInput = ({
  referralType = null,
  referralValue = '',
  onTypeChange,
  onValueChange,
  disabled = false,
  className = ''
}) => {
  const { t } = useTranslation(['auth', 'common']);
  const [selectedType, setSelectedType] = useState(referralType);
  // Separate state for phone digits and country code
  const [phoneDigits, setPhoneDigits] = useState('');
  const [countryCode, setCountryCode] = useState('+60'); // Default to Malaysia
  const [inputValue, setInputValue] = useState(referralValue);
  const [isValidating, setIsValidating] = useState(false);

  // Update internal state when props change
  useEffect(() => {
    setSelectedType(referralType);
    setInputValue(referralValue);

    if (referralType === 'phone') {
      const match = countryCodes.find((c) => referralValue.startsWith(c.code));
      if (match) {
        setCountryCode(match.code);
        setPhoneDigits(referralValue.slice(match.code.length));
      } else {
        setPhoneDigits(referralValue.replace(/\D/g, ''));
      }
    }
  }, [referralType, referralValue]);

  // Handle type selection
  const handleTypeChange = (type) => {
    setSelectedType(type);
    setInputValue('');
    setPhoneDigits('');
    setCountryCode('+60');
    onTypeChange?.(type);
    onValueChange?.('');
  };

  // Handle value change (for code)
  const handleValueChange = (value) => {
    setInputValue(value);
    onValueChange?.(value);
  };

  // Handle phone input change
  const handlePhoneInputChange = (e) => {
    const { value, countryCode: cc } = e.target;
    const digits = String(value);
    setPhoneDigits(digits);
    setCountryCode(cc);
    // Only combine and send to parent when both are present
    if (digits && cc) {
      onValueChange?.(`${cc}${digits}`);
    } else {
      onValueChange?.('');
    }
  };

  // Handle regular input change
  const handleInputChange = (e) => {
    const value = e.target.value.toUpperCase();
    handleValueChange(value);
  };

  // Clear referral
  const handleClear = () => {
    setSelectedType(null);
    setInputValue('');
    setPhoneDigits('');
    setCountryCode('+60');
    onTypeChange?.(null);
    onValueChange?.('');
  };

  return (
    <motion.div
      className={cn('space-y-4', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center space-y-2">
        <motion.h4 
          className="text-lg font-medium text-blue-800 dark:text-yellow-400 flex items-center justify-center space-x-2"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <GiftIcon className="w-5 h-5" />
          <span>{t('register.referralCode')}</span>
        </motion.h4>
        <motion.p 
          className="text-blue-800/80 dark:text-gray-300 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {t('register.helperText.referralCode')}
        </motion.p>
      </div>

      {/* Type Selection */}
      {!selectedType && (
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.p 
            className="text-blue-800 dark:text-yellow-400 text-sm text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {t('register.chooseReferralType')}
          </motion.p>
          
          <div className="grid grid-cols-1 gap-3">
            {/* Referral Code Option */}
            <motion.button
              onClick={() => handleTypeChange('code')}
              disabled={disabled}
              className={cn(
                'p-4 rounded-xl border-2 border-dashed transition-all duration-300',
                'bg-white/5 dark:bg-gray-800/40 hover:bg-indigo-500/10 dark:hover:bg-indigo-900/20',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2',
                'hover:border-indigo-400 dark:hover:border-yellow-400 border-black dark:border-gray-700'
              )}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              aria-label={t('register.referralTypeOptions.code')}
            >
              <div className="text-center space-y-2">
                <motion.div 
                  className="w-10 h-10 bg-indigo-500/20 dark:bg-yellow-400/20 rounded-full flex items-center justify-center mx-auto"
                  whileHover={{ scale: 1.1 }}
                >
                  <TicketIcon className="w-5 h-5 text-indigo-400 dark:text-yellow-400" />
                </motion.div>
                <div>
                  <h5 className="text-blue-800 dark:text-yellow-400 font-medium">{t('register.referralTypeOptions.code')}</h5>
                  <p className="text-blue-800/80 dark:text-gray-300 text-xs">{t('register.enterReferralCode')}</p>
                </div>
              </div>
            </motion.button>

            {/* Phone Number Option */}
            <motion.button
              onClick={() => handleTypeChange('phone')}
              disabled={disabled}
              className={cn(
                'p-4 rounded-xl border-2 border-dashed transition-all duration-300',
                'bg-white/5 dark:bg-gray-800/40 hover:bg-purple-500/10 dark:hover:bg-purple-900/20',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
                'hover:border-purple-400 dark:hover:border-yellow-400 border-black dark:border-gray-700'
              )}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              aria-label={t('register.referralTypeOptions.phoneNumber')}
            >
              <div className="text-center space-y-2">
                <motion.div 
                  className="w-10 h-10 bg-purple-500/20 dark:bg-yellow-400/20 rounded-full flex items-center justify-center mx-auto"
                  whileHover={{ scale: 1.1 }}
                >
                  <PhoneIcon className="w-5 h-5 text-purple-400 dark:text-yellow-400" />
                </motion.div>
                <div>
                  <h5 className="text-blue-800 dark:text-yellow-400 font-medium">{t('register.referralTypeOptions.phoneNumber')}</h5>
                  <p className="text-blue-800/80 dark:text-gray-300 text-xs">{t('register.enterReferrerPhone')}</p>
                </div>
              </div>
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Input Field */}
      <AnimatePresence mode="wait">
        {selectedType && (
          <motion.div
            key={selectedType}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {selectedType === 'code' ? (
              <FormField
                label={t('register.labels.referralCode')}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder={t('register.placeholders.referralCode')}
                icon={<TicketIcon />}
                helperText={t('register.helperText.referralCode')}
                disabled={disabled || isValidating}
                className="uppercase"
                maxLength={20}
                isLoading={isValidating}
                aria-label={t('register.labels.referralCode')}
              />
            ) : (
              <PhoneNumberInput
                label={t('register.labels.referrerPhone')}
                value={phoneDigits}
                onChange={handlePhoneInputChange}
                placeholder={t('register.placeholders.mobileNumber')}
                helperText={t('register.helperText.referrerPhone')}
                disabled={disabled || isValidating}
                showFormats={false}
                disableValidation
                isLoading={isValidating}
                aria-label={t('register.labels.referrerPhone')}
                // countryCode={countryCode}
              />
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <motion.button
                onClick={handleClear}
                disabled={disabled || isValidating}
                className={cn(
                  'flex-1 py-2 px-4 text-white hover:text-white',
                  'border border-white/30 hover:border-white/50 rounded-xl',
                  'transition-all duration-200 text-sm',
                  'focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2',
                  'disabled:opacity-50 disabled:cursor-not-allowed'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                aria-label={t('common.clear')}
              >
                Clear
              </motion.button>
              
              <motion.button
                onClick={() => handleTypeChange(selectedType === 'code' ? 'phone' : 'code')}
                disabled={disabled || isValidating}
                className={cn(
                  'flex-1 py-2 px-4 text-white hover:text-indigo-200',
                  'border border-indigo-500/30 hover:border-indigo-500/50 rounded-xl',
                  'transition-all duration-200 text-sm',
                  'focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-offset-2',
                  'disabled:opacity-50 disabled:cursor-not-allowed'
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                aria-label={t('register.switchTo', { 
                  type: selectedType === 'code' 
                    ? t('register.referralTypeOptions.phoneNumber') 
                    : t('register.referralTypeOptions.code') 
                })}
              >
                {t('register.switchTo', { 
                  type: selectedType === 'code' 
                    ? t('register.referralTypeOptions.phoneNumber') 
                    : t('register.referralTypeOptions.code') 
                })}
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Skip Option */}
      {!selectedType && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <p className="text-white/60 dark:text-gray-400 text-sm hover:text-white/80 dark:hover:text-yellow-400 transition-colors duration-200">
            {t('register.skipReferral')}
          </p>
        </motion.div>
      )}

      {/* Benefits Info */}
      <motion.div
        className="p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 dark:from-yellow-400/10 dark:to-pink-400/10 border border-indigo-500/20 dark:border-yellow-400/20 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-start space-x-3">
          <UserGroupIcon className="w-5 h-5 text-indigo-400 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <h5 className="text-indigo-800 dark:text-yellow-400 font-medium mb-2">{t('register.referralBenefits.title')}</h5>
            <ul className="text-indigo-600/90 dark:text-gray-300 text-sm space-y-1.5">
              {t('register.referralBenefits.items', { returnObjects: true }).map((benefit, index) => (
                <motion.li 
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-center space-x-2"
                >
                  <span className="w-1.5 h-1.5 bg-indigo-400 dark:bg-yellow-400 rounded-full" />
                  <span>{benefit}</span>
                </motion.li>
              ))}
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ReferralInput;
