import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { PhoneIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import OAuthButtons from './OAuthButtons';

/**
 * RegistrationMethodStep Component
 * @param {Object} props
 * @param {Function} props.onManualRegister - Callback when manual registration is selected
 * @param {Function} props.onGoogleSignIn - Callback for Google sign in
 * @param {Function} props.onAppleSignIn - Callback for Apple sign in
 * @param {boolean} props.disabled - Disabled state
 * @param {Object} props.loadingStates - Loading states for OAuth providers
 */
const RegistrationMethodStep = ({
  onManualRegister,
  onGoogleSignIn,
  onAppleSignIn,
  disabled = false,
  loadingStates = {
    google: false,
    apple: false
  }
}) => {
  const { t } = useTranslation('auth');

  // Define benefits items statically to avoid translation mapping issues
  const benefitsItems = [
    t('register.leftSide.features.profile'),
    t('register.leftSide.features.connect'),
    t('register.leftSide.features.compete'),
    t('register.leftSide.features.earn')
  ];

  const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001';
  const handleGoogleSignIn = () => {
    window.location.href = `${baseUrl}/auth/oauth/google/redirect`;
  };
  const handleAppleSignIn = () => {
    window.location.href = `${baseUrl}/auth/oauth/apple/redirect`;
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center space-y-2">
        <motion.h3 
          className="text-xl font-semibold text-gray-800 dark:text-gray-100"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {t('register.leftSide.joinTitle')} {t('register.leftSide.missionX')}
        </motion.h3>
        <motion.p 
          className="text-gray-600 dark:text-gray-300 text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {t('register.leftSide.description')}
        </motion.p>
      </div>

      {/* Registration Options */}
      <div className="grid grid-cols-1 gap-4">
        {/* Manual Registration */}
        <motion.button
          onClick={onManualRegister}
          disabled={disabled}
          className={cn(
            'p-4 rounded-xl border-2 border-dashed transition-all duration-300',
            'bg-white/90 dark:bg-gray-900/90 hover:bg-white dark:hover:bg-gray-900',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            'hover:border-blue-400 border-gray-200 dark:border-gray-700',
            'shadow-sm hover:shadow-md'
          )}
          whileHover={{ scale: 1.02, y: -2 }}
          whileTap={{ scale: 0.98 }}
          aria-label={t('register.title')}
        >
          <div className="text-center space-y-2">
            <motion.div 
              className="w-12 h-12 bg-blue-500/10 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto"
              whileHover={{ scale: 1.1 }}
            >
              <PhoneIcon className="w-6 h-6 text-blue-600 dark:text-blue-300" />
            </motion.div>
            <div>
              <h4 className="text-gray-800 dark:text-gray-100 font-medium">{t('register.title')}</h4>
              <p className="text-gray-600 dark:text-gray-300 text-xs">{t('register.leftSide.cta')}</p>
            </div>
          </div>
        </motion.button>

        {/* OAuth Buttons */}
        <div className="relative">
          <OAuthButtons
            onGoogleSignIn={handleGoogleSignIn}
            onAppleSignIn={handleAppleSignIn}
            disabled={true}
            loadingStates={loadingStates}
            className="mt-2"
          />
          {/* Overlay for Coming Soon */}
          <div className="absolute inset-0 flex items-center justify-center bg-white/20 dark:bg-gray-900/40 backdrop-blur-sm rounded-xl z-10 pointer-events-none">
            <span className="text-lg font-semibold text-gray-600 dark:text-gray-300">Coming Soon</span>
          </div>
        </div>
      </div>

      {/* Benefits Info */}
      <motion.div
        className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 border border-blue-100 dark:border-gray-700 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-start space-x-3">
          <UserGroupIcon className="w-5 h-5 text-blue-600 dark:text-blue-300 mt-0.5 flex-shrink-0" />
          <div>
            <h5 className="text-gray-800 dark:text-gray-100 font-medium mb-2">{t('register.leftSide.subtitle')}</h5>
            <ul className="text-gray-600 dark:text-gray-300 text-sm space-y-1.5">
              {benefitsItems.map((benefit, index) => (
                <motion.li 
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-center space-x-2"
                >
                  <span className="w-1.5 h-1.5 bg-blue-500 dark:bg-blue-300 rounded-full" />
                  <span>{benefit}</span>
                </motion.li>
              ))}
            </ul>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default RegistrationMethodStep; 