/**
 * Authentication Hub Component
 * Main container for authentication with modern two-panel layout
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import LoginForm from './LoginForm';
import RegistrationFlow from './RegistrationFlow';
import PasswordResetFlow from './PasswordResetFlow';
import { cn } from '../../lib/utils';
import LanguageDropdown from '../../components/LanguageDropdown';
import { useTranslation } from '../../hooks/useTranslation';
import { useLocation } from 'react-router-dom';
import { useEffect } from 'react';

/**
 * AuthenticationHub Component
 * @param {Object} props
 * @param {string} props.initialMode - Initial authentication mode ('login' or 'register')
 * @param {Function} props.onModeChange - Callback when mode changes
 * @param {Function} props.onSuccess - Callback when authentication succeeds
 * @param {string} props.className - Additional CSS classes
 */
const AuthenticationHub = ({
  initialMode = 'login',
  onModeChange,
  onSuccess,
  className = ''
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('auth');
  const [currentMode, setCurrentMode] = useState(initialMode); // 'login', 'register', 'reset'
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Referral code modal state
  const location = useLocation();
  const [showReferralModal, setShowReferralModal] = useState(false);
  const [referralCode, setReferralCode] = useState('');

  // Check for referral code in URL or localStorage
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    let code = params.get('referralCode');
    if (!code) {
      code = localStorage.getItem('referralCode');
    }
    if (code) {
      setReferralCode(code);
      setShowReferralModal(true);
    } else {
      setShowReferralModal(false);
    }
  }, [location]);

  // Handle mode switching with transition
  const handleModeSwitch = (newMode) => {
    if (newMode === currentMode) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentMode(newMode);
      setIsTransitioning(false);
      onModeChange?.(newMode);
    }, 150);
  };

  // Handle successful authentication
  const handleAuthSuccess = (result) => {
    // Clear any saved registration data
    localStorage.removeItem('registrationData');

    // Call success callback or navigate appropriately
    if (onSuccess) {
      onSuccess(result);
    } else {
      // Check if this is a new registration (first login)
      const isFirstLogin = localStorage.getItem('isFirstLogin') === 'true';

      if (isFirstLogin) {
        // For new registrations, redirect to welcome page
        navigate('/welcome');
      } else {
        // For existing users, redirect to home
        navigate('/home');
      }
    }
  };

  // Add custom-scrollbar class to both left and right panel scrollable containers
  // Inject custom scrollbar CSS if not already present

  if (typeof window !== 'undefined' && !document.getElementById('custom-scrollbar-style')) {
    const style = document.createElement('style');
    style.id = 'custom-scrollbar-style';
    style.innerHTML = `
      .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        background: transparent;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #6366f1 40%, #ec4899 100%);
        border-radius: 8px;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
      }
      .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #6366f1 #ffffff00;
      }
    `;
    document.head.appendChild(style);
  }

  return (
    <div className={cn(
      'min-h-5xl relative overflow-x-auto',
      'bg-gradient-to-br from-gray-50 via-blue-50 to-gray-50 dark:from-gray-950 dark:via-blue-950 dark:to-gray-950',
      className
    )}>
      {/* Referral Code Toast-Style Notification */}
      {showReferralModal && (
        <div className="fixed top-8 left-8 z-[2000] flex justify-end w-auto pointer-events-none">
          <motion.div
            initial={{ opacity: 0, y: -40, scale: 0.95, borderRadius: 8 }}
            animate={{ opacity: 1, y: 0, scale: 1, borderRadius: 24 }}
            exit={{ opacity: 0, y: -20, scale: 0.95, borderRadius: 8 }}
            transition={{ type: 'spring', stiffness: 500, damping: 30, mass: 1, bounce: 0.25, duration: 0.5 }}
            style={{ borderRadius: 24, padding: 0, background: 'none', minWidth: 350, maxWidth: 400, width: '100%' }}
            className="relative pointer-events-auto"
          >
            {/* Gradient border wrapper */}
            <div
              className="p-1 w-full h-full"
              style={{
                borderRadius: 24,
                background: 'linear-gradient(120deg, #6366f1 0%, #ec4899 50%, #fbbf24 100%)',
                boxShadow: '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)'
              }}
            >
              {/* White content with matching border radius */}
              <div
                className="flex items-center bg-white/90 dark:bg-gray-900/90 p-6 w-full h-full relative overflow-hidden shadow-xl backdrop-blur-lg"
                style={{ borderRadius: 24 }}
              >
                {/* Logo */}
                <img src="/missionx-logo.png" alt="Logo" className="w-12 h-12 mr-4 object-contain select-none rounded-2xl border-2 border-indigo-100 bg-white shadow" draggable="false" />
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="font-bold text-lg mb-1 leading-tight bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-text text-transparent drop-shadow-lg">
                    Referral code copied!
                  </div>
                  <div className="text-gray-800 dark:text-gray-100 text-base break-words">
                    Congratulations! The referral code is now included in the referral input section on the last step! 
                  </div>
                </div>
                {/* Close Button */}
                <button
                  className="absolute top-3 right-3 px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-semibold shadow transition-colors text-sm"
                  onClick={() => setShowReferralModal(false)}
                  style={{ pointerEvents: 'auto' }}
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
      <div className="absolute top-4 text-black mr-6 right-4 z-20"><LanguageDropdown /></div>
      {/* Two-Panel Layout Container - Optimized Dimensions */}
      <div className="relative z-10 min-h-screen flex items-center justify-center overflow-hidden p-4 lg:p-8">
        <div className="w-full max-w-[1500px] mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 h-full max-h-screen overflow-auto lg:items-center custom-scrollbar">

            {/* Left Panel - Premium Authentication Form */}
            <motion.div
              className="relative order-2 lg:order-1 overflow-x-hidden custom-scrollbar"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              {/* Ultra-Premium Authentication Card - Optimized Dimensions */}
              <motion.div
                className="relative backdrop-blur-3xl bg-gradient-to-br from-white/[0.85] via-white/[0.75] to-white/[0.65] dark:bg-gradient-to-br dark:from-gray-900 dark:via-gray-950 dark:to-blue-950 border border-gray-200/50 dark:border-gray-800 rounded-[2rem] p-4 lg:p-6 xl:p-8 shadow-[0_32px_64px_rgba(0,0,0,0.1)] dark:shadow-indigo-900/30 overflow-x-hidden min-h-[600px] lg:min-h-[700px]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                whileHover={{
                  y: -2,
                  transition: { duration: 0.2 }
                }}
              >
                  {/* Subtle Background Decorations */}
                  <motion.div
                    className="absolute -top-16 -right-16 w-48 h-48 bg-gradient-to-br from-blue-200/25 to-indigo-200/25 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full blur-3xl"
                    animate={{
                      opacity: [0.2, 0.3, 0.2],
                    }}
                    transition={{
                      duration: 12,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div
                    className="absolute -bottom-16 -left-16 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-blue-200/20 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-full blur-2xl"
                    animate={{
                      opacity: [0.15, 0.25, 0.15],
                    }}
                    transition={{
                      duration: 15,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 2
                    }}
                  />
                  <motion.div
                    className="absolute top-1/2 right-8 w-24 h-24 bg-gradient-to-br from-blue-300/15 to-indigo-300/15 dark:from-blue-900/10 dark:to-indigo-900/10 rounded-2xl blur-xl"
                    animate={{
                      opacity: [0.1, 0.2, 0.1],
                    }}
                    transition={{
                      duration: 18,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />

                  {/* Premium Border Glow Effect */}
                  <div className="absolute inset-0 rounded-[2rem] bg-gradient-to-r from-blue-300/20 via-indigo-300/20 to-blue-300/20 dark:from-yellow-400/10 dark:via-pink-400/10 dark:to-indigo-400/10 blur-sm" />
                  <div className="absolute inset-[1px] rounded-[2rem] bg-gradient-to-br from-white/[0.85] via-white/[0.75] to-white/[0.65] dark:from-gray-900 dark:via-gray-950 dark:to-blue-950" />

                  <div className="relative z-10">
                    {/* Premium Header with Enhanced Typography - Reduced Sizes */}
                    <div className="text-center mb-6 lg:mb-8">
                      <motion.div
                        className="mb-6 lg:mb-8"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.1 }}
                      >
                        {/* MissionX Logo */}
                        <div className="inline-flex items-center justify-center w-16 h-16 mb-6 bg-gradient-to-br from-blue-600 via-indigo-600 to-blue-700 dark:bg-gradient-to-br dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 rounded-2xl shadow-2xl p-2">
                          <img
                            src="/AuthLogo.png"
                            alt="MissionX Logo"
                            className="w-12 h-12 object-contain rounded-xl shadow-lg bg-white/80 dark:bg-gray-900/80"
                          />
                        </div>
                      </motion.div>

                      <motion.h1
                        className="text-2xl lg:text-3xl xl:text-4xl font-black mb-4 leading-tight"
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                      >
                        <span className="bg-gradient-to-r from-gray-800 via-blue-700 to-indigo-700 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 bg-clip-text text-transparent drop-shadow-lg">
                          {currentMode === 'login' ? `${t('login.Welcome Back')} ` : `${t('login.Start Your Journey Today!')} `}
                        </span>
                        <span className="bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 bg-clip-text text-transparent">
                          {currentMode === 'login' && t("login.Elite Gamer!")}
                        </span>
                      </motion.h1>

                      <motion.p
                        className="text-base lg:text-lg font-medium leading-relaxed max-w-md mx-auto text-gray-600 dark:text-gray-300 mb-6"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                      >
                        {currentMode === 'login' ? t("login.Continue your mission to gaming greatness") : t("login.Join the elite community of gaming talents")}
                      </motion.p>

                      {/* Subtitle with Gaming Elements */}
                      <motion.div
                        className="flex items-center justify-center space-x-3 mt-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                      >
                        <div className="w-2 h-2 bg-blue-500 dark:bg-yellow-400 rounded-full" />
                        <span className="text-gray-500 dark:text-yellow-400 text-xs font-medium tracking-wide uppercase">
                          {currentMode === 'login' ? t('login.Secure Gaming Portal') : t('login.Premium Gaming Network')}
                        </span>
                        <div className="w-2 h-2 bg-indigo-500 dark:bg-pink-400 rounded-full" />
                      </motion.div>
                    </div>

                    {/* Premium Mode Tabs */}
                    {currentMode !== 'reset' && (
                      <motion.div
                        className="flex mb-8 p-2 bg-gray-100/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-3xl border border-gray-200/50 dark:border-gray-700 shadow-2xl dark:shadow-indigo-900/30"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.5 }}
                      >
                        {['login', 'register'].map((mode) => (
                          <motion.button
                            key={mode}
                            onClick={() => handleModeSwitch(mode)}
                            className={cn(
                              'flex-1 py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 relative overflow-hidden group',
                              currentMode === mode
                                ? 'text-white dark:text-gray-900 shadow-2xl dark:shadow-yellow-400/20'
                                : 'text-gray-600 dark:text-yellow-400 hover:text-gray-800 dark:hover:text-yellow-300 bg-transparent hover:bg-gray-200/50 dark:hover:bg-gray-800/50'
                            )}
                            disabled={isTransitioning}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            {/* Active Tab Background */}
                            {currentMode === mode && (
                              <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-700 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 rounded-2xl"
                                layoutId="activeTabBackground"
                                transition={{ type: "spring", stiffness: 400, damping: 30 }}
                              />
                            )}

                            {/* Hover Glow Effect */}
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-blue-600/20 dark:from-yellow-400/10 dark:via-pink-400/10 dark:to-indigo-400/10 rounded-2xl opacity-0 group-hover:opacity-100"
                              transition={{ duration: 0.3 }}
                            />

                            {/* Tab Content */}
                            <span className="relative z-10 flex items-center justify-center space-x-2">
                              {mode === 'login' ? (
                                <>
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                  </svg>
                                  <span>{t('login.Sign In')}</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                  </svg>
                                  <span>{t('login.Join Now')}</span>
                                </>
                              )}
                            </span>

                            {/* Active Tab Indicator */}
                            {currentMode === mode && (
                              <motion.div
                                className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white/70 dark:bg-yellow-400/70 rounded-full"
                                layoutId="activeTabIndicator"
                                transition={{ type: "spring", stiffness: 400, damping: 30 }}
                              />
                            )}
                          </motion.button>
                        ))}
                      </motion.div>
                    )}

                    {/* Content Area */}
                    <div className="relative min-h-[500px] lg:min-h-[550px]">
                      <AnimatePresence mode="wait">
                        {!isTransitioning && (
                          <motion.div
                            key={currentMode}
                            initial={{ opacity: 0, x: currentMode === 'login' ? -20 : 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: currentMode === 'login' ? 20 : -20 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="absolute inset-0"
                          >
                            {/* Render Authentication Components */}
                            {currentMode === 'login' && (
                              <LoginForm
                                onSuccess={handleAuthSuccess}
                                onForgotPassword={() => handleModeSwitch('reset')}
                              />
                            )}

                            {currentMode === 'register' && (
                              <RegistrationFlow
                                onSuccess={handleAuthSuccess}
                                onBack={() => handleModeSwitch('login')}
                              />
                            )}

                            {currentMode === 'reset' && (
                              <PasswordResetFlow
                                onSuccess={() => handleModeSwitch('login')}
                                onCancel={() => handleModeSwitch('login')}
                              />
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>

                      {/* Loading Overlay */}
                      {isTransitioning && (
                        <motion.div
                          className="absolute inset-0 flex items-center justify-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                        >
                          <div className="w-8 h-8 border-2 border-indigo-300 border-t-indigo-600 rounded-full animate-spin" />
                        </motion.div>
                      )}
                    </div>

                    {/* Footer Links */}
                    {currentMode !== 'reset' && (
                      <motion.div
                        className="mb-2 text-center space-y-3"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.7 }}
                      >
                        <div className="text-gray-500 mt-2 text-sm">
                          {currentMode === 'login' ? (
                            <span>
                              {t('login.Don/t have an account?')} {' '}
                              <button
                                onClick={() => handleModeSwitch('register')}
                                className="text-indigo-600 bg-transparent hover:bg-transparent hover:text-indigo-700 font-medium transition-colors underline"
                              >
                                {t('login.Register Now!')}
                              </button>
                            </span>
                          ) : (
                            <span>
                              {t('login.Have an Account?')} {' '}
                              <button
                                onClick={() => handleModeSwitch('login')}
                                className="text-indigo-600 hover:text-indigo-700 font-medium transition-colors underline"
                              >
                                {t('login.Login Now!')}
                              </button>
                            </span>
                          )}
                        </div>

                        <div className="text-gray-400 text-xs">
                          {t('login.By continuing, you agree to our Terms of Service and Privacy Policy')}
                        </div>
                      </motion.div>
                    )}
                  </div>
              </motion.div>
            </motion.div>

            {/* Right Panel - Ultra-Premium Promotional Content */}
            <motion.div
              className="relative order-1 lg:order-2 custom-scrollbar"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
            >
              {/* Revolutionary Promotional Card - Optimized Dimensions */}
              <motion.div
                className="relative bg-gradient-to-br from-blue-600 via-indigo-600 to-blue-700 dark:bg-gradient-to-br dark:from-black dark:via-blue-800 dark:to-black rounded-[2rem] p-6 lg:p-8 xl:p-10 shadow-[0_40px_80px_rgba(0,0,0,0.15)] overflow-hidden text-white min-h-[550px] lg:min-h-[600px]"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                whileHover={{
                  y: -2,
                  transition: { duration: 0.2 }
                }}
              >
                {/* Subtle Background System */}
                <motion.div
                  className="absolute -top-20 -right-20 w-64 h-64 bg-gradient-to-br from-white/15 to-indigo-200/15 rounded-full blur-3xl"
                  animate={{
                    opacity: [0.15, 0.25, 0.15],
                  }}
                  transition={{
                    duration: 15,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute -bottom-20 -left-20 w-48 h-48 bg-gradient-to-br from-blue-200/10 to-indigo-200/10 rounded-full blur-2xl"
                  animate={{
                    opacity: [0.1, 0.2, 0.1],
                  }}
                  transition={{
                    duration: 18,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 3
                  }}
                />
                <motion.div
                  className="absolute top-1/3 right-12 w-32 h-32 bg-gradient-to-br from-indigo-200/15 to-blue-200/15 rounded-3xl blur-xl"
                  animate={{
                    opacity: [0.1, 0.2, 0.1],
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Gaming-Inspired Geometric Patterns */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-8 left-8 w-16 h-16 border-2 border-white/30 rounded-lg rotate-45" />
                  <div className="absolute bottom-12 right-16 w-12 h-12 border-2 border-indigo-200/40 rounded-full" />
                  <div className="absolute top-1/2 left-4 w-8 h-8 bg-white/20 rounded-full" />
                </div>

                {/* Premium Mesh Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/[0.03] to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-indigo-200/[0.05] to-transparent" />

                <div className="relative z-10">
                  {/* Premium Brand Section */}
                  <motion.div
                    className="flex flex-col items-center mb-8 lg:mb-10"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    {/* MissionX Logo - Right Panel */}
                    <div className="relative mb-6">
                      <div className="w-20 h-20 bg-gradient-to-br from-white/20 via-indigo-200/20 to-white/10 rounded-3xl flex items-center justify-center backdrop-blur-xl border border-white/30 shadow-2xl p-3">
                        <img
                          src="/AuthLogo.png"
                          alt="MissionX Logo"
                          className="w-15 h-15 object-contain rounded-2xl shadow-lg bg-white/80"
                        />
                      </div>
                      {/* Static Glow Effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 rounded-3xl blur-xl opacity-50" />
                    </div>

                    {/* Brand Name */}
                    <motion.div
                      className="text-center"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <h3 className="text-xl font-black text-white/90 tracking-wider mb-2">MISSION X</h3>
                      <div className="w-12 h-1 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full mx-auto" />
                    </motion.div>
                  </motion.div>

                  {/* Simple Main Heading - Reduced Sizes */}
                  <motion.h2
                    className="text-2xl lg:text-3xl xl:text-4xl font-black text-center mb-6 lg:mb-8 leading-tight"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <span className="block bg-gradient-to-r from-white via-indigo-100 to-white bg-clip-text text-transparent drop-shadow-2xl">
                      {t('hub.rightSide.heading.line1')}
                    </span>
                    <span className="block bg-gradient-to-r from-indigo-100 via-blue-100 to-indigo-200 bg-clip-text text-transparent">
                      {t('hub.rightSide.heading.line2')}
                    </span>
                  </motion.h2>

                  {/* Premium Subtitle */}
                  <motion.p
                    className="text-base lg:text-lg text-center mb-8 lg:mb-10 text-white/90 font-medium leading-relaxed max-w-lg mx-auto"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    {t('hub.rightSide.subtitle.pre')} <span className="text-indigo-200 font-bold">{t('hub.rightSide.subtitle.highlight1')}</span> {t('hub.rightSide.subtitle.mid')}
                    <span className="text-blue-200 font-bold"> {t('hub.rightSide.subtitle.highlight2')}</span> {t('hub.rightSide.subtitle.conj')}
                    <span className="text-indigo-100 font-bold"> {t('hub.rightSide.subtitle.highlight3')}</span>
                  </motion.p>

                  {/* Revolutionary Features List */}
                  <motion.div
                    className="space-y-4 lg:space-y-6 mb-10 lg:mb-12"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.4 }}
                  >
                    {[
                      {
                        icon: '🎯',
                        title: t('hub.rightSide.features.strategies.title'),
                        subtitle: t('hub.rightSide.features.strategies.subtitle')
                      },
                      {
                        icon: '🌟',
                        title: t('hub.rightSide.features.network.title'),
                        subtitle: t('hub.rightSide.features.network.subtitle')
                      },
                      {
                        icon: '🏆',
                        title: t('hub.rightSide.features.achievements.title'),
                        subtitle: t('hub.rightSide.features.achievements.subtitle')
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={index}
                        className="group flex items-center space-x-4 p-4 rounded-2xl bg-white/[0.08] backdrop-blur-xl border border-white/20 hover:bg-white/[0.12] transition-all duration-500"
                        initial={{ opacity: 0, x: -30, rotateY: -10 }}
                        animate={{ opacity: 1, x: 0, rotateY: 0 }}
                        transition={{
                          duration: 0.6,
                          delay: 1.6 + (index * 0.15),
                          type: "spring",
                          stiffness: 200
                        }}
                        whileHover={{
                          scale: 1.02,
                          x: 8,
                          transition: { duration: 0.2 }
                        }}
                      >
                        {/* Premium Icon Container */}
                        <motion.div
                          className="w-14 h-14 bg-gradient-to-br from-cyan-400/20 to-indigo-400/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-xl group-hover:shadow-2xl transition-all duration-300"
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.6 }}
                        >
                          <span className="text-2xl">{feature.icon}</span>
                        </motion.div>

                        {/* Feature Content */}
                        <div className="flex-1">
                          <h4 className="text-white font-bold text-lg mb-1 group-hover:text-cyan-300 transition-colors duration-300">
                            {feature.title}
                          </h4>
                          <p className="text-white/70 text-sm font-medium group-hover:text-white/90 transition-colors duration-300">
                            {feature.subtitle}
                          </p>
                        </div>

                        {/* Arrow Indicator */}
                        <motion.div
                          className="w-6 h-6 text-white/40 group-hover:text-cyan-300 transition-colors duration-300"
                          whileHover={{ x: 4 }}
                        >
                          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </motion.div>
                      </motion.div>
                    ))}
                  </motion.div>

                  {/* Premium Call to Action */}
                  <motion.div
                    className="text-center space-y-4 lg:space-y-6"
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.8, delay: 2.2, type: "spring", stiffness: 200 }}
                  >

                    {/* Main CTA */}
                    <motion.div
                      className="relative"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/30 to-indigo-400/30 rounded-3xl blur-xl animate-pulse" />
                      <div className="relative bg-gradient-to-r from-white/[0.15] to-white/[0.1] backdrop-blur-xl rounded-3xl p-6 border border-white/30 shadow-2xl">
                        <div className="flex items-center justify-center space-x-3 mb-3">
                          <motion.div
                            animate={{ rotate: [0, 360] }}
                            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                          >
                            <svg className="w-6 h-6 text-cyan-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                          </motion.div>
                          <span className="text-white font-bold text-lg">{t('hub.rightSide.cta.title')}</span>
                        </div>
                        <p className="text-white/80 text-sm font-medium">
                          {t('hub.rightSide.cta.joinPrefix')} <span className="text-cyan-300 font-bold">50,000+</span> {t('hub.rightSide.cta.joinSuffix')}
                        </p>

                        {/* Floating Action Indicators */}
                        <div className="flex justify-center space-x-2 mt-4">
                          {[...Array(3)].map((_, i) => (
                            <motion.div
                              key={i}
                              className="w-2 h-2 bg-cyan-400 rounded-full"
                              animate={{
                                scale: [1, 1.5, 1],
                                opacity: [0.5, 1, 0.5],
                              }}
                              transition={{
                                duration: 1.5,
                                repeat: Infinity,
                                delay: i * 0.2,
                                ease: "easeInOut"
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    </motion.div>

                    {/* Trust Indicators */}
                    <motion.div
                      className="flex items-center justify-center space-x-6 text-white/60 text-sm"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.6, delay: 2.8 }}
                    >
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>{t('hub.rightSide.cta.trust.secure')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{t('hub.rightSide.cta.trust.verified')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
                        </svg>
                        <span>{t('hub.rightSide.cta.trust.premium')}</span>
                      </div>
                    </motion.div>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthenticationHub;
