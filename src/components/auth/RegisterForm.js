import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import OAuthButtons from './OAuthButtons';
import { oauthAPI } from '../../services/api';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

const RegisterForm = () => {
  const { t } = useTranslation('auth');
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [oauthLoadingStates, setOAuthLoadingStates] = useState({
    google: false,
    apple: false
  });

  // OAuth handlers
  const handleGoogleSignIn = async () => {
    try {
      setOAuthLoadingStates(prev => ({ ...prev, google: true }));
      setError(null);
      const response = await oauthAPI.redirect('google');
      // The backend will handle the redirect
      window.location.href = response.url;
    } catch (error) {
      console.error('Google sign-in error:', error);
      setError(t('register.errors.oauthError'));
    } finally {
      setOAuthLoadingStates(prev => ({ ...prev, google: false }));
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setOAuthLoadingStates(prev => ({ ...prev, apple: true }));
      setError(null);
      const response = await oauthAPI.redirect('apple');
      // The backend will handle the redirect
      window.location.href = response.url;
    } catch (error) {
      console.error('Apple sign-in error:', error);
      setError(t('register.errors.oauthError'));
    } finally {
      setOAuthLoadingStates(prev => ({ ...prev, apple: false }));
    }
  };

  // Handle OAuth callback
  useEffect(() => {
    const handleOAuthCallback = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const provider = urlParams.get('provider');

      if (code && provider) {
        try {
          setIsLoading(true);
          setError(null);
          const response = await oauthAPI.callback(provider, code);
          
          // Store token and user data
          localStorage.setItem('token', response.token);
          localStorage.setItem('user', JSON.stringify(response.user));
          
          // Show success message
          setSuccess(t('register.success.oauthSignIn'));
          
          // Redirect after a short delay
          setTimeout(() => {
            navigate('/');
          }, 1500);
        } catch (error) {
          console.error('OAuth callback error:', error);
          setError(t('register.errors.oauthCallbackError'));
        } finally {
          setIsLoading(false);
        }
      }
    };

    handleOAuthCallback();
  }, [navigate, t]);

  return (
    <motion.div
      className="w-full max-w-md mx-auto p-6 space-y-6 bg-white dark:bg-gray-900 rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-gray-100 dark:border-gray-800"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* ... existing form fields ... */}

      {/* OAuth Buttons */}
      <OAuthButtons
        onGoogleSignIn={handleGoogleSignIn}
        onAppleSignIn={handleAppleSignIn}
        disabled={isLoading}
        loadingStates={oauthLoadingStates}
        className="mt-6"
      />

      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-red-400 dark:text-red-300 bg-red-400/10 dark:bg-red-900/30 p-3 rounded-lg"
          >
            <XCircleIcon className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success Message */}
      <AnimatePresence>
        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-400 dark:text-green-300 bg-green-400/10 dark:bg-green-900/30 p-3 rounded-lg"
          >
            <CheckCircleIcon className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{success}</span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* ... existing buttons ... */}
    </motion.div>
  );
};

export default RegisterForm; 