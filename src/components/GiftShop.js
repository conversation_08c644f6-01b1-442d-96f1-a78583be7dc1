import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const GiftShop = ({ giftItems, onPurchase, isSubmitting }) => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  // Handle selection of a gift item
  const handleSelectItem = (item) => {
    setSelectedItem(item);
    setPurchaseQuantity(1);
    setShowPurchaseModal(true);
  };

  // Close the purchase modal
  const handleCloseModal = () => {
    setShowPurchaseModal(false);
    setSelectedItem(null);
  };

  // Complete purchase
  const handleCompletePurchase = () => {
    if (selectedItem && purchaseQuantity > 0) {
      onPurchase(selectedItem.id, purchaseQuantity);
      handleCloseModal();
    }
  };

  // If no items are available
  if (!giftItems || giftItems.length === 0) {
    return (
      <div className="text-center py-10">
        <svg 
          className="mx-auto h-12 w-12 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth="1" 
            d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" 
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No gifts available</h3>
        <p className="mt-1 text-sm text-gray-500">
          There are no gift items available in the shop at the moment.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Gift Shop</h2>
        <p className="text-sm text-gray-500">
          Purchase special gifts with your credits. You can keep them or gift them to other users.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {giftItems.map((item) => (
          <motion.div
            key={item.id}
            whileHover={{ y: -5 }}
            className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
            onClick={() => handleSelectItem(item)}
          >
            <div className="p-1 bg-gradient-to-r from-indigo-500 to-purple-500" />
            
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">{item.name}</h3>
                <span className="text-indigo-600 font-semibold">{item.credit_price} credits</span>
              </div>
              
              <div className="flex items-center justify-center mb-4">
                <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {item.icon_path ? (
                    <img 
                      src={item.icon_path.startsWith('http') ? item.icon_path : `${process.env.REACT_APP_API_URL}/${item.icon_path}`} 
                      alt={item.name} 
                      className="w-full h-full object-contain" 
                    />
                  ) : (
                    <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="1" 
                        d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" 
                      />
                    </svg>
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-500 mb-4">{item.description}</p>
              
              <p className="text-xs text-gray-500 mb-4">
                Sell back value: {item.sell_back_price} credits
              </p>
              
              <button
                className="w-full py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              >
                Purchase
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Purchase Modal */}
      <AnimatePresence>
        {showPurchaseModal && selectedItem && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Purchase Gift</h3>
              
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {selectedItem.icon_path ? (
                    <img 
                      src={selectedItem.icon_path.startsWith('http') ? selectedItem.icon_path : `${process.env.REACT_APP_API_URL}/${selectedItem.icon_path}`} 
                      alt={selectedItem.name} 
                      className="w-full h-full object-contain" 
                    />
                  ) : (
                    <svg className="w-8 h-8 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="1" 
                        d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" 
                      />
                    </svg>
                  )}
                </div>
                
                <div>
                  <h4 className="font-medium">{selectedItem.name}</h4>
                  <p className="text-sm text-gray-500">{selectedItem.credit_price} credits each</p>
                </div>
              </div>
              
              <div className="mb-4">
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  value={purchaseQuantity}
                  onChange={(e) => setPurchaseQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div className="mb-6">
                <div className="flex justify-between pb-2 border-b border-gray-200">
                  <span className="text-gray-500">Price per item:</span>
                  <span className="font-medium">{selectedItem.credit_price} credits</span>
                </div>
                <div className="flex justify-between pt-2">
                  <span className="text-gray-700 font-medium">Total:</span>
                  <span className="text-indigo-600 font-semibold">{selectedItem.credit_price * purchaseQuantity} credits</span>
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <button
                  onClick={handleCloseModal}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleCompletePurchase}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Processing...' : 'Confirm Purchase'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GiftShop; 