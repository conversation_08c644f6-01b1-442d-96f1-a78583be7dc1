import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * AvailabilityReminderModal
 * Centered modal to remind users to set their availability.
 *
 * Props:
 * - open: boolean (controls visibility)
 * - onClose: function (called when modal is dismissed)
 * - onGoToAvailability: function (optional, called when CT<PERSON> is clicked)
 */
const AvailabilityReminderModal = ({ open, onClose, onGoToAvailability }) => {
  if (!open) return null;

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          className="fixed inset-0 z-[1000] flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {/* Blurred, darkened background overlay */}
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-md"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal content */}
          <motion.div
            className="relative z-10 w-full max-w-lg mx-auto"
            initial={{ scale: 0.95, opacity: 0, y: 40 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 40 }}
            transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          >
            {/* Gradient border wrapper */}
            <div
              className="p-2 w-full h-full rounded-3xl"
              style={{
                background:
                  'linear-gradient(120deg, #6366f1 0%, #ec4899 50%, #fbbf24 100%)',
                boxShadow:
                  '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)'
              }}
            >
              <div
                className="flex flex-col items-center bg-white/90 dark:bg-gray-900/90 p-8 w-full h-full rounded-2xl shadow-2xl backdrop-blur-lg relative"
                style={{ borderRadius: 22 }}
              >
                {/* Logo */}
                <img
                  src="/missionx-logo.png"
                  alt="MissionX Logo"
                  className="w-16 h-16 mb-4 object-contain select-none rounded-2xl border-2 border-indigo-100 bg-white shadow"
                  draggable="false"
                />
                {/* Title */}
                <div className="font-black text-2xl mb-2 leading-tight bg-gradient-to-r from-gray-600 to-yellow-400 bg-clip-text text-transparent drop-shadow-lg text-center">
                  Set Your Availability
                </div>
                {/* Divider */}
                <div className="border-b-2 border-transparent bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-border w-1/2 mb-3" style={{borderImage: 'linear-gradient(90deg, #6366f1, #ec4899, #fbbf24) 1'}} />
                {/* Description */}
                <div className="text-gray-800 dark:text-gray-100 text-lg text-center mb-6 max-w-md font-medium">
                  <span className="font-bold block mb-2">You’ve set up your services, but haven’t set your availability yet!</span>
                  <span>
                    To start receiving orders, please set your available times in the <span className="font-semibold text-indigo-600 dark:text-indigo-300">Profile</span> page under <span className="font-semibold text-indigo-600 dark:text-indigo-300">"My Availability"</span>.
                  </span>
                </div>
                {/* CTA Button */}
                <button
                  className="w-full max-w-xs px-6 py-3 mb-2 bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-500 text-white font-bold rounded-xl shadow-lg hover:from-indigo-700 hover:to-yellow-600 transition-all duration-300 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                  onClick={onGoToAvailability}
                >
                  Go to My Availability
                </button>
                {/* Dismiss Button */}
                <button
                  className="mt-2 text-sm text-gray-500 hover:text-gray-700 bg-red-500/20 dark:text-gray-300 dark:hover:text-white underline underline-offset-2 focus:outline-none"
                  onClick={onClose}
                >
                  Dismiss
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AvailabilityReminderModal; 