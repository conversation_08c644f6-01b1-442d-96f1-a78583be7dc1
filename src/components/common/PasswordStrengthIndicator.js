import React from 'react';
import { 
  getPasswordStrengthLabel, 
  getPasswordStrengthColor, 
  getPasswordStrengthTextColor 
} from '../../utils/validationUtils';

/**
 * Password Strength Indicator Component
 * Displays a visual indicator of password strength
 * 
 * @param {Object} props - Component props
 * @param {number} props.strength - Password strength score (0-4)
 * @param {boolean} props.showLabel - Whether to show the strength label
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} PasswordStrengthIndicator component
 */
const PasswordStrengthIndicator = ({ 
  strength, 
  showLabel = true,
  className = '' 
}) => {
  // Don't show anything if strength is 0
  if (strength === 0) return null;
  
  // Get strength label and colors
  const strengthLabel = getPasswordStrengthLabel(strength);
  const strengthColor = getPasswordStrengthColor(strength);
  const strengthTextColor = getPasswordStrengthTextColor(strength);
  
  return (
    <div className={`mt-1 ${className}`}>
      {/* Progress bar */}
      <div className="h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div
          className={`h-full ${strengthColor} transition-all duration-300`}
          style={{ width: `${(strength / 4) * 100}%` }}
        />
      </div>
      
      {/* Strength label */}
      {showLabel && strength > 0 && (
        <p className={`text-xs mt-1 ${strengthTextColor}`}>
          {strengthLabel}
        </p>
      )}
    </div>
  );
};

/**
 * Password Requirements Component
 * Displays password requirements with checkmarks for met requirements
 * 
 * @param {Object} props - Component props
 * @param {string} props.password - The password to check
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} PasswordRequirements component
 */
export const PasswordRequirements = ({ password, className = '' }) => {
  // Check requirements
  const hasLength = password && password.length >= 8;
  const hasUppercase = password && /[A-Z]/.test(password);
  const hasNumber = password && /[0-9]/.test(password);
  const hasSpecial = password && /[!@#$%^&*]/.test(password);
  
  return (
    <div className={`mt-2 text-xs ${className}`}>
      <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">Password requirements:</p>
      <ul className="space-y-1 pl-1">
        <li className="flex items-center">
          <span className={`mr-1.5 ${hasLength ? 'text-green-500' : 'text-gray-400'}`}>
            {hasLength ? (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </span>
          <span className={hasLength ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}>
            At least 8 characters
          </span>
        </li>
        <li className="flex items-center">
          <span className={`mr-1.5 ${hasUppercase ? 'text-green-500' : 'text-gray-400'}`}>
            {hasUppercase ? (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </span>
          <span className={hasUppercase ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}>
            At least one uppercase letter
          </span>
        </li>
        <li className="flex items-center">
          <span className={`mr-1.5 ${hasNumber ? 'text-green-500' : 'text-gray-400'}`}>
            {hasNumber ? (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </span>
          <span className={hasNumber ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}>
            At least one number
          </span>
        </li>
        <li className="flex items-center">
          <span className={`mr-1.5 ${hasSpecial ? 'text-green-500' : 'text-gray-400'}`}>
            {hasSpecial ? (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </span>
          <span className={hasSpecial ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}>
            At least one special character (!@#$%^&*)
          </span>
        </li>
      </ul>
    </div>
  );
};

export default PasswordStrengthIndicator;
