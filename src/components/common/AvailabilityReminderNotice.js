import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * AvailabilityReminderNotice
 * Slide-up notice to remind users to set their availability.
 *
 * Props:
 * - open: boolean (controls visibility)
 * - onClose: function (called when notice is dismissed)
 * - onGoToAvailability: function (called when CT<PERSON> is clicked)
 */
const AvailabilityReminderNotice = ({ open, onClose, onGoToAvailability }) => {
  return (
    <AnimatePresence>
      {open && (
        <motion.div
          initial={{ x: -320, y: 0, opacity: 0 }}
          animate={{ x: 0, y: 0, opacity: 1 }}
          exit={{ x: -320, y: 0, opacity: 0 }}
          transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          className="fixed top-6 left-8 z-[1000]"
          style={{ minWidth: 320, maxWidth: 400 }}
        >
          <div
            className="relative flex items-center gap-4 p-4 pr-6 rounded-2xl shadow-2xl bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg border border-white/30"
            style={{
              background:
                'linear-gradient(120deg, #6366f1 0%, #ec4899 50%, #fbbf24 100%)',
              boxShadow:
                '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)'
            }}
          >
            {/* Logo */}
            <img
              src="/AuthLogo.png"
              alt="MissionX Logo"
              className="w-10 h-10 object-contain align-starts select-none rounded-xl border-2 border-indigo-100 bg-white shadow"
              draggable="false"
            />
            {/* Message and CTA */}
            <div className="flex-1 min-w-0">
              <div className="font-extrabold text-base mb-1 leading-tight bg-gradient-to-r from-white to-yellow-100 bg-clip-text text-transparent drop-shadow-lg">
                MISSIONX REMINDER!
              </div>
              <div className="text-yellow-100 text-left dark:text-gray-100 mt-3 text-sm mb-3">
                If you have set up your services, and wanting to be a talent, please make sure to update your availability by clicking this button below to navigate to your profile and select "My Availability"<br />
                <br />
                <span className="mt-4 text-yellow-100 font-italic">You may ignore this reminder if you are not a talent or if you have set your availability.</span>
              </div>
              <button
                className="mt-1 px-4 py-2 bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-500 text-white font-semibold rounded-lg shadow hover:from-indigo-700 hover:to-yellow-600 transition-all duration-200 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                onClick={onGoToAvailability}
              >
                Go to My Availability
              </button>
            </div>
            {/* Dismiss Button */}
            <button
              className="absolute top-2 right-2 text-white bg-red-500/20 hover:bg-red-500/30 hover:text-gray-700 dark:hover:text-white rounded-full p-1 focus:outline-none"
              onClick={onClose}
              aria-label="Dismiss reminder"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AvailabilityReminderNotice; 