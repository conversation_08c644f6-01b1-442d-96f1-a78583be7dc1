import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  API_ERROR_TYPES, 
  WALLET_ERROR_TYPES, 
  getErrorSeverity, 
  getErrorActions 
} from '../../utils/errorTypes';

/**
 * Enhanced Error Display Component
 * 
 * This component provides beautiful, actionable error displays with animations,
 * severity-based styling, and integrated action buttons for error resolution.
 */
const ErrorDisplay = ({ 
  error, 
  onRetry, 
  onDismiss, 
  onAction,
  className = '',
  variant = 'card', // 'card', 'banner', 'toast', 'inline'
  showActions = true,
  autoHide = false,
  autoHideDelay = 5000,
  compact = false
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && error && error.severity !== 'critical') {
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onDismiss) onDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, error, onDismiss]);

  if (!error || !isVisible) return null;

  // Get error styling based on severity and type
  const { colors, icon } = getErrorStyling(error);
  const actions = getErrorActions(error.type);

  // Handle action clicks
  const handleAction = (action) => {
    if (onAction) {
      onAction(action);
    } else {
      // Default action handling
      switch (action.action) {
        case 'retry':
          if (onRetry) onRetry();
          break;
        case 'verify_email':
          window.location.href = '/verify-email';
          break;
        case 'start_ekyc':
          window.location.href = '/ekyc';
          break;
        case 'add_credits':
          window.location.href = '/wallet';
          break;
        case 'contact_support':
          window.location.href = '/support';
          break;
        default:
          console.log('Unhandled action:', action);
      }
    }
  };

  // Render based on variant
  switch (variant) {
    case 'banner':
      return renderBanner();
    case 'toast':
      return renderToast();
    case 'inline':
      return renderInline();
    default:
      return renderCard();
  }

  function renderCard() {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className={`${colors.bg} ${colors.border} border rounded-xl shadow-lg overflow-hidden ${className}`}
        >
          {/* Header */}
          <div className={`${colors.headerBg} px-6 py-4 flex items-center justify-between`}>
            <div className="flex items-center space-x-3">
              <div className={`${colors.iconBg} p-2 rounded-lg`}>
                {icon}
              </div>
              <div>
                <h3 className={`${colors.title} font-semibold text-lg`}>
                  {getErrorTitle(error.type)}
                </h3>
                {error.severity && (
                  <span className={`${colors.subtitle} text-sm capitalize`}>
                    {error.severity} Priority
                  </span>
                )}
              </div>
            </div>
            
            {onDismiss && (
              <button
                onClick={() => {
                  setIsVisible(false);
                  onDismiss();
                }}
                className={`${colors.text} hover:${colors.textHover} transition-colors`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            <p className={`${colors.text} leading-relaxed ${compact ? 'text-sm' : ''}`}>
              {error.message}
            </p>

            {/* Validation Errors */}
            {error.validationErrors && (
              <div className="mt-4">
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className={`${colors.text} text-sm font-medium hover:${colors.textHover} transition-colors flex items-center`}
                >
                  <span>View Details</span>
                  <svg 
                    className={`w-4 h-4 ml-1 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-2 overflow-hidden"
                    >
                      <ul className={`${colors.text} text-sm space-y-1 list-disc list-inside`}>
                        {Object.entries(error.validationErrors).map(([field, errors]) => (
                          <li key={field}>
                            <span className="font-medium">{field}:</span>{' '}
                            {Array.isArray(errors) ? errors[0] : errors}
                          </li>
                        ))}
                      </ul>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* Error ID */}
            {error.id && !compact && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">Error ID:</p>
                <p className="text-sm font-mono text-gray-700">{error.id}</p>
              </div>
            )}
          </div>

          {/* Actions */}
          {showActions && actions.length > 0 && (
            <div className={`${colors.footerBg} px-6 py-4 border-t ${colors.border}`}>
              <div className="flex flex-wrap gap-3">
                {actions.map((action, index) => (
                  <motion.button
                    key={action.action}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => handleAction(action)}
                    className={`
                      ${action.primary ? colors.primaryButton : colors.secondaryButton}
                      px-4 py-2 rounded-lg font-medium transition-all duration-200 
                      transform hover:scale-105 ${compact ? 'text-sm' : ''}
                    `}
                  >
                    {action.label}
                  </motion.button>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    );
  }

  function renderBanner() {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`${colors.bg} ${colors.border} border-b px-6 py-4 flex items-center justify-between ${className}`}
        >
          <div className="flex items-center space-x-3">
            <div className={`${colors.iconBg} p-2 rounded-lg`}>
              {icon}
            </div>
            <div>
              <p className={`${colors.title} font-medium`}>{error.message}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {showActions && actions.length > 0 && (
              <div className="flex space-x-2">
                {actions.slice(0, 2).map((action) => (
                  <button
                    key={action.action}
                    onClick={() => handleAction(action)}
                    className={`
                      ${action.primary ? colors.primaryButton : colors.secondaryButton}
                      px-3 py-1 rounded text-sm font-medium transition-colors
                    `}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
            
            {onDismiss && (
              <button
                onClick={() => {
                  setIsVisible(false);
                  onDismiss();
                }}
                className={`${colors.text} hover:${colors.textHover} transition-colors`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  function renderToast() {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, x: 300, scale: 0.8 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 300, scale: 0.8 }}
          className={`
            fixed top-4 right-4 z-50 max-w-sm w-full
            ${colors.bg} ${colors.border} border rounded-xl shadow-xl
            ${className}
          `}
        >
          <div className="p-4">
            <div className="flex items-start space-x-3">
              <div className={`${colors.iconBg} p-2 rounded-lg flex-shrink-0`}>
                {icon}
              </div>
              <div className="flex-1 min-w-0">
                <p className={`${colors.title} font-medium text-sm`}>
                  {getErrorTitle(error.type)}
                </p>
                <p className={`${colors.text} text-sm mt-1`}>
                  {error.message}
                </p>
                
                {showActions && actions.length > 0 && (
                  <div className="mt-3 flex space-x-2">
                    {actions.slice(0, 1).map((action) => (
                      <button
                        key={action.action}
                        onClick={() => handleAction(action)}
                        className={`
                          ${colors.primaryButton}
                          px-3 py-1 rounded text-xs font-medium transition-colors
                        `}
                      >
                        {action.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              {onDismiss && (
                <button
                  onClick={() => {
                    setIsVisible(false);
                    onDismiss();
                  }}
                  className={`${colors.text} hover:${colors.textHover} transition-colors flex-shrink-0`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  function renderInline() {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className={`${colors.bg} ${colors.border} border rounded-lg p-4 ${className}`}
        >
          <div className="flex items-center space-x-3">
            <div className={`${colors.iconBg} p-1 rounded`}>
              {React.cloneElement(icon, { className: 'w-4 h-4' })}
            </div>
            <div className="flex-1">
              <p className={`${colors.text} text-sm`}>{error.message}</p>
            </div>
            
            {showActions && actions.length > 0 && (
              <div className="flex space-x-2">
                {actions.slice(0, 1).map((action) => (
                  <button
                    key={action.action}
                    onClick={() => handleAction(action)}
                    className={`
                      ${colors.primaryButton}
                      px-2 py-1 rounded text-xs font-medium transition-colors
                    `}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }
};

// Helper function to get error styling based on type and severity
function getErrorStyling(error) {
  const severity = getErrorSeverity(error.type);
  
  const stylingMap = {
    low: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      headerBg: 'bg-blue-100/50',
      footerBg: 'bg-blue-50/50',
      iconBg: 'bg-blue-100',
      title: 'text-blue-900',
      subtitle: 'text-blue-700',
      text: 'text-blue-800',
      textHover: 'text-blue-900',
      primaryButton: 'bg-blue-600 text-white hover:bg-blue-700',
      secondaryButton: 'bg-blue-100 text-blue-700 hover:bg-blue-200',
      icon: <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    },
    medium: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      headerBg: 'bg-yellow-100/50',
      footerBg: 'bg-yellow-50/50',
      iconBg: 'bg-yellow-100',
      title: 'text-yellow-900',
      subtitle: 'text-yellow-700',
      text: 'text-yellow-800',
      textHover: 'text-yellow-900',
      primaryButton: 'bg-yellow-600 text-white hover:bg-yellow-700',
      secondaryButton: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200',
      icon: <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    },
    high: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      headerBg: 'bg-red-100/50',
      footerBg: 'bg-red-50/50',
      iconBg: 'bg-red-100',
      title: 'text-red-900',
      subtitle: 'text-red-700',
      text: 'text-red-800',
      textHover: 'text-red-900',
      primaryButton: 'bg-red-600 text-white hover:bg-red-700',
      secondaryButton: 'bg-red-100 text-red-700 hover:bg-red-200',
      icon: <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    },
    critical: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      headerBg: 'bg-purple-100/50',
      footerBg: 'bg-purple-50/50',
      iconBg: 'bg-purple-100',
      title: 'text-purple-900',
      subtitle: 'text-purple-700',
      text: 'text-purple-800',
      textHover: 'text-purple-900',
      primaryButton: 'bg-purple-600 text-white hover:bg-purple-700',
      secondaryButton: 'bg-purple-100 text-purple-700 hover:bg-purple-200',
      icon: <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
      </svg>
    }
  };

  return stylingMap[severity] || stylingMap.medium;
}

// Helper function to get error title
function getErrorTitle(errorType) {
  const titleMap = {
    [API_ERROR_TYPES.NETWORK_ERROR]: 'Connection Error',
    [API_ERROR_TYPES.AUTHENTICATION_ERROR]: 'Authentication Required',
    [API_ERROR_TYPES.VALIDATION_ERROR]: 'Validation Error',
    [API_ERROR_TYPES.SERVER_ERROR]: 'Server Error',
    [API_ERROR_TYPES.NOT_FOUND_ERROR]: 'Not Found',
    [API_ERROR_TYPES.RATE_LIMIT_ERROR]: 'Rate Limited',
    [WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED]: 'Email Verification Required',
    [WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED]: 'Identity Verification Required',
    [WALLET_ERROR_TYPES.INSUFFICIENT_BALANCE]: 'Insufficient Balance',
    [WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED]: 'Balance Unavailable',
    [WALLET_ERROR_TYPES.TRANSACTION_FAILED]: 'Transaction Failed',
    [WALLET_ERROR_TYPES.PAYMENT_FAILED]: 'Payment Failed',
    [WALLET_ERROR_TYPES.WITHDRAWAL_FAILED]: 'Withdrawal Failed',
    [WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE]: 'Service Unavailable'
  };

  return titleMap[errorType] || 'Error';
}

export default ErrorDisplay;
