import React from 'react';
import { motion } from 'framer-motion';

/**
 * Beautiful Animated Loading Indicator
 * 
 * This component provides various loading animations for different contexts
 * with pearl white base and royal blue/indigo accents.
 */
const LoadingIndicator = ({ 
  variant = 'spinner', // 'spinner', 'dots', 'pulse', 'skeleton', 'wave'
  size = 'medium', // 'small', 'medium', 'large'
  color = 'primary', // 'primary', 'secondary', 'white', 'gray'
  text = '',
  className = ''
}) => {
  
  // Size configurations
  const sizeConfig = {
    small: {
      spinner: 'w-4 h-4',
      dots: 'w-2 h-2',
      container: 'p-2',
      text: 'text-xs'
    },
    medium: {
      spinner: 'w-8 h-8',
      dots: 'w-3 h-3',
      container: 'p-4',
      text: 'text-sm'
    },
    large: {
      spinner: 'w-12 h-12',
      dots: 'w-4 h-4',
      container: 'p-6',
      text: 'text-base'
    }
  };

  // Color configurations
  const colorConfig = {
    primary: {
      spinner: 'border-indigo-600 border-t-transparent',
      dots: 'bg-indigo-600',
      pulse: 'bg-indigo-600',
      text: 'text-indigo-600'
    },
    secondary: {
      spinner: 'border-blue-500 border-t-transparent',
      dots: 'bg-blue-500',
      pulse: 'bg-blue-500',
      text: 'text-blue-500'
    },
    white: {
      spinner: 'border-white border-t-transparent',
      dots: 'bg-white',
      pulse: 'bg-white',
      text: 'text-white'
    },
    gray: {
      spinner: 'border-gray-400 border-t-transparent',
      dots: 'bg-gray-400',
      pulse: 'bg-gray-400',
      text: 'text-gray-400'
    }
  };

  const currentSize = sizeConfig[size];
  const currentColor = colorConfig[color];

  // Spinner variant
  const renderSpinner = () => (
    <div className={`flex flex-col items-center justify-center ${currentSize.container} ${className}`}>
      <div className={`
        ${currentSize.spinner} 
        ${currentColor.spinner} 
        border-2 rounded-full animate-spin
      `} />
      {text && (
        <p className={`mt-3 ${currentSize.text} ${currentColor.text} font-medium`}>
          {text}
        </p>
      )}
    </div>
  );

  // Dots variant
  const renderDots = () => (
    <div className={`flex flex-col items-center justify-center ${currentSize.container} ${className}`}>
      <div className="flex space-x-2">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={`${currentSize.dots} ${currentColor.dots} rounded-full`}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2
            }}
          />
        ))}
      </div>
      {text && (
        <p className={`mt-3 ${currentSize.text} ${currentColor.text} font-medium`}>
          {text}
        </p>
      )}
    </div>
  );

  // Pulse variant
  const renderPulse = () => (
    <div className={`flex flex-col items-center justify-center ${currentSize.container} ${className}`}>
      <motion.div
        className={`${currentSize.spinner} ${currentColor.pulse} rounded-full`}
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.8, 0.4, 0.8]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      {text && (
        <p className={`mt-3 ${currentSize.text} ${currentColor.text} font-medium`}>
          {text}
        </p>
      )}
    </div>
  );

  // Skeleton variant
  const renderSkeleton = () => (
    <div className={`space-y-3 ${currentSize.container} ${className}`}>
      <div className="animate-pulse space-y-3">
        <div className="h-4 bg-gray-200 rounded-lg w-3/4" />
        <div className="h-4 bg-gray-200 rounded-lg w-1/2" />
        <div className="h-4 bg-gray-200 rounded-lg w-5/6" />
      </div>
    </div>
  );

  // Wave variant
  const renderWave = () => (
    <div className={`flex flex-col items-center justify-center ${currentSize.container} ${className}`}>
      <div className="flex space-x-1">
        {[0, 1, 2, 3, 4].map((index) => (
          <motion.div
            key={index}
            className={`w-1 ${currentColor.pulse} rounded-full`}
            style={{ height: size === 'small' ? '16px' : size === 'medium' ? '24px' : '32px' }}
            animate={{
              scaleY: [1, 2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: index * 0.1
            }}
          />
        ))}
      </div>
      {text && (
        <p className={`mt-3 ${currentSize.text} ${currentColor.text} font-medium`}>
          {text}
        </p>
      )}
    </div>
  );

  // Render based on variant
  switch (variant) {
    case 'dots':
      return renderDots();
    case 'pulse':
      return renderPulse();
    case 'skeleton':
      return renderSkeleton();
    case 'wave':
      return renderWave();
    default:
      return renderSpinner();
  }
};

/**
 * Wallet-specific loading indicators
 */
export const WalletLoadingIndicator = ({ text = 'Loading wallet...', ...props }) => (
  <LoadingIndicator 
    variant="dots" 
    size="medium" 
    color="primary" 
    text={text}
    {...props}
  />
);

export const BalanceLoadingIndicator = ({ text = 'Loading balance...', ...props }) => (
  <LoadingIndicator 
    variant="pulse" 
    size="large" 
    color="white" 
    text={text}
    {...props}
  />
);

export const TransactionLoadingIndicator = ({ text = 'Loading transactions...', ...props }) => (
  <LoadingIndicator 
    variant="skeleton" 
    size="medium" 
    color="gray" 
    text={text}
    {...props}
  />
);

export const PaymentLoadingIndicator = ({ text = 'Processing payment...', ...props }) => (
  <LoadingIndicator 
    variant="wave" 
    size="medium" 
    color="primary" 
    text={text}
    {...props}
  />
);

/**
 * Full-screen loading overlay
 */
export const LoadingOverlay = ({ 
  isVisible = true, 
  text = 'Loading...', 
  variant = 'spinner',
  backdrop = true 
}) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`
        fixed inset-0 z-50 flex items-center justify-center
        ${backdrop ? 'bg-black/20 backdrop-blur-sm' : ''}
      `}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/30 p-8"
      >
        <LoadingIndicator 
          variant={variant}
          size="large"
          color="primary"
          text={text}
        />
      </motion.div>
    </motion.div>
  );
};

/**
 * Inline loading state for buttons
 */
export const ButtonLoadingIndicator = ({ 
  isLoading = false, 
  children, 
  loadingText = 'Loading...',
  ...buttonProps 
}) => (
  <button {...buttonProps} disabled={isLoading || buttonProps.disabled}>
    {isLoading ? (
      <div className="flex items-center justify-center space-x-2">
        <LoadingIndicator variant="spinner" size="small" color="white" />
        <span>{loadingText}</span>
      </div>
    ) : (
      children
    )}
  </button>
);

export default LoadingIndicator;
