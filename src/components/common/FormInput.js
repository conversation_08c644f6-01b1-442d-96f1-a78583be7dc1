import React, { useState } from 'react';

/**
 * Helper function to determine the appropriate autocomplete value based on field name and type
 *
 * @param {string} name - The name of the input field
 * @param {string} type - The type of the input field
 * @returns {string} The appropriate autocomplete value
 */
const getDefaultAutocomplete = (name, type) => {
  // Common field mappings
  const fieldMappings = {
    // Name fields
    firstName: 'given-name',
    lastname: 'family-name',
    fullName: 'name',
    name: 'name',

    // Contact fields
    email: 'email',
    mobileNumber: 'tel',
    phone: 'tel',
    phoneNumber: 'tel',

    // Address fields
    address: 'street-address',
    city: 'address-level2',
    state: 'address-level1',
    zip: 'postal-code',
    postalCode: 'postal-code',
    country: 'country',

    // Account fields
    username: 'username',
    nickname: 'nickname',

    // Password fields - handled by type check

    // Other common fields
    dateOfBirth: 'bday',
    birthday: 'bday',
    gender: 'sex',

    // Referral code - no standard autocomplete
    referralCode: 'off'
  };

  // Check if we have a mapping for this field name
  if (fieldMappings[name]) {
    return fieldMappings[name];
  }

  // Handle password fields
  if (type === 'password') {
    if (name.includes('confirm') || name === 'confirmPassword') {
      return 'new-password';
    }
    return 'current-password';
  }

  // Default values based on input type
  const typeMappings = {
    email: 'email',
    tel: 'tel',
    url: 'url',
    date: 'bday'
  };

  if (typeMappings[type]) {
    return typeMappings[type];
  }

  // If no specific mapping found, return a sensible default
  return 'on';
};

/**
 * Reusable form input component with validation and styling
 *
 * @param {Object} props - Component props
 * @param {string} props.label - Input label
 * @param {string} props.name - Input name attribute
 * @param {string} props.value - Input value
 * @param {function} props.onChange - Change handler function
 * @param {string} props.type - Input type (text, email, password, etc.)
 * @param {string} props.placeholder - Input placeholder text
 * @param {string} props.error - Error message to display
 * @param {boolean} props.required - Whether the field is required
 * @param {React.ReactNode} props.icon - Icon to display in the input
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showTogglePassword - Whether to show password toggle (for password fields)
 * @param {function} props.onBlur - Blur handler function
 * @param {function} props.onFocus - Focus handler function
 * @returns {React.ReactElement} FormInput component
 */
const FormInput = ({
  label,
  name,
  value,
  onChange,
  type = 'text',
  placeholder,
  error,
  required = false,
  icon,
  className = '',
  showTogglePassword = false,
  onBlur,
  onFocus,
  autocomplete,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Handle input focus
  const handleFocus = (e) => {
    setFocused(true);
    if (onFocus) onFocus(e);
  };

  // Handle input blur
  const handleBlur = (e) => {
    setFocused(false);
    if (onBlur) onBlur(e);
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  // Determine input type for password fields
  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="space-y-2 group">
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 bg-transparent hover:bg-indigo-50 hover:rounded-2xl text-gray-400 pointer-events-none">
            {icon}
          </div>
        )}
        <input
          id={name}
          name={name}
          type={inputType}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          autoComplete={autocomplete || getDefaultAutocomplete(name, type)}
          className={`w-full ${icon ? 'pl-10' : 'pl-4'} ${showTogglePassword ? 'pr-10' : 'pr-4'} py-3 border ${
            error
              ? 'border-red-300 focus:ring-red-200 dark:border-red-700 dark:focus:ring-red-900'
              : focused
                ? 'border-blue-500 focus:ring-blue-100 dark:border-blue-700 dark:focus:ring-blue-900'
                : value
                  ? 'border-blue-300 focus:ring-blue-100 dark:border-blue-700 dark:focus:ring-blue-900'
                  : 'border-gray-300 focus:ring-blue-100 dark:border-gray-700 dark:focus:ring-blue-900'
          } rounded-lg bg-white dark:bg-black dark:text-white dark:placeholder-gray-400 focus:outline-none focus:ring-4 transition-all duration-200 shadow-sm ${className}`}
          aria-invalid={error ? 'true' : 'false'}
          {...props}
        />

        {/* Password toggle button */}
        {type === 'password' && showTogglePassword && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 -translate-y-1/2 bg-transparent hover:bg-indigo-50 hover:rounded-2xl text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <p className="text-red-500 dark:text-red-400 text-xs mt-1 flex items-center gap-1.5">
          <svg className="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </p>
      )}
    </div>
  );
};

export default FormInput;
