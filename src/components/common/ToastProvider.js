import React, { createContext, useContext, useState, useCallback } from 'react';
import Toast from './Toast';

// Create a context for the toast functionality
const ToastContext = createContext();

// Custom hook to use the toast context
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Toast provider component
export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  // Function to add a new toast
  const addToast = useCallback((toast, type = 'success', duration = 3000) => {
    let toastObj;
    if (typeof toast === 'string') {
      toastObj = { id: Date.now(), title: toast, type, duration };
    } else {
      toastObj = {
        id: Date.now(),
        title: toast.title || '',
        description: toast.description || '',
        type: toast.type || type,
        duration: toast.duration || duration,
        ...toast
      };
    }
    setToasts(prevToasts => [...prevToasts, toastObj]);
    return toastObj.id;
  }, []);

  // Function to remove a toast by ID
  const removeToast = useCallback(id => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);

  // Shorthand functions for different toast types
  const success = useCallback((toast, duration) => addToast(toast, 'success', duration), [addToast]);
  const error = useCallback((toast, duration) => addToast(toast, 'error', duration), [addToast]);
  const warning = useCallback((toast, duration) => addToast(toast, 'warning', duration), [addToast]);
  const info = useCallback((toast, duration) => addToast(toast, 'info', duration), [addToast]);

  return (
    <ToastContext.Provider value={{ addToast, removeToast, success, error, warning, info }}>
      {children}
      <div className="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 flex flex-col items-center space-y-6 max-w-full pointer-events-none mt-6">
        {toasts.map(toast => (
          <div className="flex justify-center" key={toast.id}>
          <Toast
              title={toast.title}
              description={toast.description}
            type={toast.type}
            duration={toast.duration}
            onClose={() => removeToast(toast.id)}
          />
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

export default ToastProvider;
