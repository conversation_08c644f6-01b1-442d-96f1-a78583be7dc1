import React from 'react';
import useTranslation from '../../hooks/useTranslation';

/**
 * A component for rendering translated HTML content
 * 
 * @param {Object} props - Component props
 * @param {string} props.i18nKey - Translation key
 * @param {string} [props.ns] - Optional namespace
 * @param {Object} [props.values] - Values for interpolation
 * @param {React.ReactNode} [props.children] - Fallback content if translation is missing
 * @param {string} [props.className] - Optional CSS class
 * @returns {React.ReactElement} - Rendered component
 */
const TranslatedHTML = ({ i18nKey, ns, values, children, className, ...rest }) => {
  const { t } = useTranslation(ns);
  
  // If the key doesn't exist and children are provided, render children as fallback
  const translatedHTML = t(i18nKey, { ...values, interpolation: { escapeValue: false } });
  const shouldUseChildren = translatedHTML === i18nKey && children;
  
  if (shouldUseChildren) {
    return <div className={className} {...rest}>{children}</div>;
  }
  
  return (
    <div 
      className={className} 
      {...rest} 
      dangerouslySetInnerHTML={{ __html: translatedHTML }} 
    />
  );
};

export default TranslatedHTML;
