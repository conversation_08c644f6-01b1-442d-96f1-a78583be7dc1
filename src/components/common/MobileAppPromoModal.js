import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGooglePlay, FaApple } from 'react-icons/fa';

const MobileAppPromoModal = ({ open, onClose }) => {
  if (!open) return null;
  return (
    <AnimatePresence>
      <motion.div
        key="modal-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.25 }}
        className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/60 backdrop-blur-sm"
        aria-modal="true"
        role="dialog"
      >
        {/* Modal Card */}
        <motion.div
          key="modal-card"
          initial={{ opacity: 0, scale: 0.92, y: 40 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.92, y: 40 }}
          transition={{ type: 'spring', stiffness: 400, damping: 32, mass: 1 }}
          className="relative w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl mx-2 sm:mx-4 md:mx-0 rounded-3xl shadow-2xl"
          style={{ zIndex: 1001 }}
        >
          {/* Gradient border wrapper */}
          <div
            className="p-0.5 sm:p-1"
            style={{
              borderRadius: 28,
              background:
                'linear-gradient(120deg, #6366f1 0%, #ec4899 50%, #fbbf24 100%)',
              boxShadow:
                '0 8px 40px 0 rgba(99,102,241,0.10), 0 1.5px 8px 0 rgba(139,92,246,0.08)'
            }}
          >
            {/* Glassy content */}
            <div
              className="relative flex flex-col md:flex-row items-center bg-white/90 dark:bg-gray-900/90 p-4 sm:p-6 md:p-8 lg:p-10 rounded-[24px] overflow-hidden shadow-xl backdrop-blur-lg gap-4 md:gap-8"
              style={{ borderRadius: 24 }}
            >
              {/* Close Button */}
              <button
                onClick={onClose}
                aria-label="Close"
                className="absolute top-2 right-2 sm:top-4 sm:right-4 w-9 h-9 sm:w-10 sm:h-10 rounded-full bg-white/70 dark:bg-gray-800/70 border border-indigo-100/40 dark:border-indigo-800/60 flex items-center justify-center text-indigo-700 dark:text-indigo-200 hover:bg-white/90 dark:hover:bg-gray-900/90 transition-all focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-20"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              {/* Left: Text and CTA */}
              <div className="flex-1 flex flex-col items-center md:items-start z-10 gap-2 md:gap-4">
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-1 md:mb-2 text-transparent bg-gradient-to-r from-indigo-600 via-pink-500 to-yellow-400 bg-clip-text drop-shadow text-center md:text-left">Mission X on Mobile!</h3>
                <p className="text-gray-700 dark:text-gray-100 mb-2 md:mb-5 w-full text-center md:text-left font-medium text-sm sm:text-base md:text-lg">
                  Get in touch with talents and friends at your fingertips. Download the Mission X app for the best experience!
                </p>
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mb-4 md:mb-6 w-full justify-center md:justify-start">
                  <a href="https://play.google.com/store/apps/details?id=com.missionx.user" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-4 py-2 sm:px-5 sm:py-3 space-x-3 sm:space-x-4 shadow-lg border border-indigo-800 group w-full sm:w-auto justify-center">
                    <FaGooglePlay className="w-6 h-6 sm:w-7 sm:h-7 text-[#ffffff]" style={{ filter: 'drop-shadow(0 1px 0 #fff)' }} />
                    <span className="flex flex-col items-start ml-2">
                      <span className="text-xs text-indigo-100 group-hover:text-white">Get it on</span>
                      <span className="font-bold text-base text-white group-hover:underline">Google Play</span>
                    </span>
                  </a>
                  <a href="#" className="flex items-center bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-600 hover:to-blue-600 transition-colors rounded-2xl px-4 py-2 sm:px-5 sm:py-3 space-x-3 sm:space-x-4 shadow-lg border border-indigo-800 group w-full sm:w-auto justify-center">
                    <FaApple className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                    <span className="flex flex-col items-start ml-2">
                      <span className="text-xs text-indigo-100 group-hover:text-white">Download on</span>
                      <span className="font-bold text-base text-white group-hover:underline">App Store</span>
                    </span>
                  </a>
                </div>
              </div>
              {/* Right: Mockup Images */}
              <div className="flex-1 flex items-end justify-center md:justify-end relative min-h-[160px] sm:min-h-[220px] md:min-h-[320px] w-full md:w-auto mt-4 md:mt-0">
                {/* Angled wallet mockup in the back */}
                <img
                  src="/Dompet.png"
                  alt="MissionX Wallet"
                  className="absolute left-0 bottom-0 w-20 sm:w-28 md:w-36 opacity-60 blur-[1px] z-0 transform -rotate-6"
                  style={{ filter: 'drop-shadow(0 8px 32px rgba(0,0,0,0.18))' }}
                />
                {/* Main homepage mockup in front */}
                <img
                  src="/HomepageApp.png"
                  alt="MissionX App Home"
                  className="relative w-24 sm:w-32 md:w-44 rounded-2xl shadow-2xl z-10"
                  style={{ filter: 'drop-shadow(0 12px 48px rgba(24,25,38,0.25))' }}
                />
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MobileAppPromoModal; 