import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { getLanguageFlag } from '../../utils/i18nUtils';
import useReducedMotion from '../../hooks/useReducedMotion';

/**
 * Enhanced Language Switcher Component
 *
 * A more user-friendly language switcher with:
 * - Language flags
 * - Smooth animations
 * - Keyboard navigation
 * - Accessibility features
 * - RTL support
 *
 * @param {Object} props - Component props
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Variant style ('minimal', 'compact', 'full')
 * @param {boolean} props.showFlags - Whether to show language flags
 * @param {Function} props.onChange - Callback when language changes
 * @param {string} props.dropDirection - Direction for the dropdown ('up', 'down', or 'auto')
 */
const EnhancedLanguageSwitcher = ({
  className = '',
  variant = 'full',
  showFlags = true,
  onChange = () => {},
  dropDirection = 'auto',
}) => {
  // Get language context
  const {
    currentLanguage,
    supportedLanguages,
    changeLanguage,
    isLanguageLoading,
    dir
  } = useLanguage();

  // Get only enabled languages
  const enabledLanguages = supportedLanguages.filter(lang => lang.enabled !== false);

  // State for dropdown
  const [isOpen, setIsOpen] = useState(false);

  // Ref for dropdown
  const dropdownRef = useRef(null);

  // Check if user prefers reduced motion
  const prefersReducedMotion = useReducedMotion();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) {
        // Open dropdown on arrow down/up when focused
        if ((event.key === 'ArrowDown' || event.key === 'ArrowUp') &&
            dropdownRef.current &&
            dropdownRef.current.contains(event.target)) {
          setIsOpen(true);
          event.preventDefault();
        }
        return;
      }

      // Close dropdown on escape
      if (event.key === 'Escape') {
        setIsOpen(false);
        event.preventDefault();
      }

      // Handle arrow navigation
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        const focusableElements = dropdownRef.current?.querySelectorAll('button[role="option"]');
        if (!focusableElements || focusableElements.length === 0) return;

        const currentIndex = Array.from(focusableElements).findIndex(el => el === document.activeElement);
        let nextIndex;

        if (event.key === 'ArrowDown') {
          nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
        } else {
          nextIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
        }

        focusableElements[nextIndex].focus();
        event.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  // Handle language change
  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode);
    setIsOpen(false);
    onChange(langCode);
  };

  // Get current language
  const currentLang = enabledLanguages.find(lang => lang.code === currentLanguage) || enabledLanguages[0];

  // Determine dropdown direction
  const shouldDropUp = dropDirection === 'up' ||
    (dropDirection === 'auto' && document.querySelector('.fixed.bottom-4.left-4.z-50') !== null);

  // Determine button and dropdown classes based on variant
  let buttonClasses = 'flex items-center space-x-1 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300';
  let dropdownClasses = 'absolute bg-white rounded-lg shadow-xl overflow-hidden z-50';

  // Position dropdown above or below based on direction
  if (shouldDropUp) {
    dropdownClasses += ' bottom-full mb-2'; // Position above
  } else {
    dropdownClasses += ' mt-2'; // Default position below
  }

  switch (variant) {
    case 'minimal':
      buttonClasses += ' px-2 py-1 text-xs text-gray-600 hover:text-gray-900';
      dropdownClasses += ' w-32 right-0';
      break;
    case 'compact':
      buttonClasses += ' px-3 py-1.5 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow text-sm text-gray-700 hover:text-gray-900';
      dropdownClasses += ' w-40 right-0';
      break;
    case 'full':
    default:
      buttonClasses += ' px-3 py-2 bg-white/90 backdrop-blur-sm shadow-md hover:shadow-lg text-sm font-medium text-gray-700 hover:text-gray-900';
      dropdownClasses += ' w-48 right-0';
      break;
  }

  // Add RTL support
  if (dir === 'rtl') {
    dropdownClasses = dropdownClasses.replace('right-0', 'left-0');
  }

  // Add custom classes
  buttonClasses += ` ${className}`;

  return (
    <div
      className="relative"
      ref={dropdownRef}
      aria-label="Language selector"
      dir={dir}
    >
      <button
        className={buttonClasses}
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        disabled={isLanguageLoading}
      >
        {showFlags && (
          <span className="text-lg" aria-hidden="true">
            {getLanguageFlag(currentLang.code)}
          </span>
        )}

        {variant !== 'minimal' && (
          <span className={dir === 'rtl' ? 'mr-1' : 'ml-1'}>
            {currentLang.nativeName}
          </span>
        )}

        <svg
          className={`w-4 h-4 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''} ${isLanguageLoading ? 'animate-spin' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          {isLanguageLoading ? (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          ) : (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
          )}
        </svg>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{
              opacity: 0,
              y: shouldDropUp
                ? (prefersReducedMotion ? 0 : 10) // Animate from below when dropdown is above
                : (prefersReducedMotion ? 0 : -10) // Default animation from above
            }}
            animate={{ opacity: 1, y: 0 }}
            exit={{
              opacity: 0,
              y: shouldDropUp
                ? (prefersReducedMotion ? 0 : 10) // Animate to below when dropdown is above
                : (prefersReducedMotion ? 0 : -10) // Default animation to above
            }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.2 }}
            className={dropdownClasses}
            role="listbox"
            aria-labelledby="language-selector-label"
          >
            <div className="py-1">
              {enabledLanguages.map((lang) => (
                <motion.button
                  key={lang.code}
                  whileHover={{ backgroundColor: '#F3F4F6' }}
                  whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={`w-full text-left px-4 py-2 text-sm ${
                    currentLanguage === lang.code
                      ? 'bg-indigo-50 text-indigo-700 font-medium'
                      : 'text-gray-700'
                  }`}
                  role="option"
                  aria-selected={currentLanguage === lang.code}
                  dir={lang.dir}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {showFlags && (
                        <span className="text-lg mr-2" aria-hidden="true">
                          {getLanguageFlag(lang.code)}
                        </span>
                      )}
                      <span>{lang.nativeName}</span>
                    </div>

                    {currentLanguage === lang.code && (
                      <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedLanguageSwitcher;
