import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import useTranslation from '../hooks/useTranslation';
import useReducedMotion from '../hooks/useReducedMotion';
import useScreenOrientation from '../hooks/useScreenOrientation';
import analyticsService from '../services/analyticsService';
import WelcomeLanguageSwitcher from './WelcomeLanguageSwitcher';
import LazyImage from './common/LazyImage';

/**
 * Enhanced Welcome Page Component
 * Displays a personalized welcome message after registration
 * Provides options to set up profile or continue to home
 */
function WelcomePage() {
    // Translation hook
    const { t } = useTranslation('welcome');

    // State
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [animationReady, setAnimationReady] = useState(false);
    const [showConfetti, setShow<PERSON>onfetti] = useState(false);

    // Check if user prefers reduced motion and get screen orientation
    const prefersReducedMotion = useReducedMotion();
    const { isPortrait, isLandscape } = useScreenOrientation();

    // Refs
    const confettiCanvasRef = useRef(null);

    // Navigation
    const navigate = useNavigate();

    // Initialize analytics
    useEffect(() => {
        // Initialize analytics when component mounts
        analyticsService.initialize();

        // Track page view
        analyticsService.trackPageView('welcome_page');

        return () => {
            // Track page exit when component unmounts
            analyticsService.trackEvent('welcome_page_exit');
        };
    }, []);

    // Track screen orientation changes
    useEffect(() => {
        // Track screen orientation
        analyticsService.trackEvent('welcome_screen_orientation', {
            orientation: isPortrait ? 'portrait' : 'landscape'
        });
    }, [isPortrait]);

    // Load user data and check if this is first login
    useEffect(() => {
        const loadUserData = async () => {
            try {
                // Check for authentication token first
                const authToken = localStorage.getItem('token');
                if (!authToken) {
                    console.error('Authentication token not found. Redirecting to login page.');
                    analyticsService.trackEvent('welcome_redirect_to_login', { reason: 'no_auth_token' });
                    navigate('/');
                    return;
                }

                // Get user data from localStorage (from registration)
                const storedUser = localStorage.getItem('user');
                const isFirstLogin = localStorage.getItem('isFirstLogin') === 'true';

                // If not first login, redirect to home
                if (!isFirstLogin) {
                    console.log('Not first login, redirecting to home');
                    analyticsService.trackEvent('welcome_redirect_to_home', { reason: 'not_first_login' });
                    navigate('/home');
                    return;
                }

                // Parse user data
                if (storedUser) {
                    try {
                        const parsedUser = JSON.parse(storedUser);
                        // Map profile image property to profileImage for avatar display
                        parsedUser.profileImage = parsedUser.profileImage || parsedUser.profile_image || parsedUser.profile_picture || null;
                        setUserData(parsedUser);

                        // Track user properties for analytics
                        analyticsService.setUserProperty('has_nickname', !!parsedUser.nickname);
                        analyticsService.setUserProperty('has_profile_image', !!parsedUser.profileImage);
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                        // Use mock data if parsing fails
                        setUserData(getMockUserData());
                        analyticsService.trackEvent('welcome_error', {
                            type: 'parse_user_data',
                            error: error.message
                        });
                    }
                } else {
                    // Use mock data if no stored user
                    console.log('No user data found, using mock data for testing');
                    setUserData(getMockUserData());
                    analyticsService.trackEvent('welcome_mock_data_used');
                }

                // Finish loading
                setLoading(false);

                // If user prefers reduced motion, skip animations
                if (prefersReducedMotion) {
                    // Set animation ready immediately without delay
                    setAnimationReady(true);

                    // Skip confetti animation
                    analyticsService.trackEvent('welcome_animations_skipped', {
                        reason: 'reduced_motion_preference'
                    });
                } else {
                    // Trigger animations after a short delay
                    setTimeout(() => {
                        setAnimationReady(true);
                        setTimeout(() => {
                            setShowConfetti(true);
                            // Track animation complete
                            analyticsService.trackEvent('welcome_animations_complete');
                        }, 500);
                    }, 300);
                }
            } catch (error) {
                console.error('Error in welcome page initialization:', error);
                analyticsService.trackEvent('welcome_error', {
                    type: 'initialization',
                    error: error.message
                });
                navigate('/home');
            }
        };

        loadUserData();
    }, [navigate, prefersReducedMotion]);

    // Confetti animation effect
    useEffect(() => {
        if (showConfetti && confettiCanvasRef.current) {
            const canvas = confettiCanvasRef.current;
            const ctx = canvas.getContext('2d');
            const confettiCount = 200;
            const confetti = [];

            // Set canvas size
            const resizeCanvas = () => {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            };

            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            // Create confetti particles
            for (let i = 0; i < confettiCount; i++) {
                confetti.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height - canvas.height,
                    size: Math.random() * 10 + 5,
                    color: [
                        '#3B82F6', // blue-500
                        '#6366F1', // indigo-500
                        '#8B5CF6', // violet-500
                        '#EC4899', // pink-500
                        '#F59E0B', // amber-500
                        '#10B981', // emerald-500
                    ][Math.floor(Math.random() * 6)],
                    speed: Math.random() * 3 + 2,
                    angle: Math.random() * 6.28,
                    spin: Math.random() * 0.2 - 0.1,
                });
            }

            // Animation loop
            let animationFrame;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                let stillFalling = false;
                confetti.forEach(particle => {
                    particle.y += particle.speed;
                    particle.angle += particle.spin;

                    ctx.save();
                    ctx.translate(particle.x, particle.y);
                    ctx.rotate(particle.angle);
                    ctx.fillStyle = particle.color;
                    ctx.fillRect(-particle.size / 2, -particle.size / 2, particle.size, particle.size);
                    ctx.restore();

                    // Check if any particles are still falling
                    if (particle.y < canvas.height) {
                        stillFalling = true;
                    }
                });

                if (stillFalling) {
                    animationFrame = requestAnimationFrame(animate);
                }
            };

            animate();

            // Clean up
            return () => {
                cancelAnimationFrame(animationFrame);
                window.removeEventListener('resize', resizeCanvas);
            };
        }
    }, [showConfetti]);

    // Handle profile setup navigation with analytics tracking
    const handleSetupProfile = () => {
        // Track button click
        analyticsService.trackEvent('welcome_setup_profile_click', {
            timeSpentOnPage: Date.now() - performance.now()
        });

        // Clear first login flag
        localStorage.setItem('isFirstLogin', 'false');

        // Set flag to indicate coming from welcome page
        localStorage.setItem('fromWelcomePage', 'true');

        // Verify authentication token exists before navigation
        const authToken = localStorage.getItem('token');

        if (!authToken) {
            console.error('Authentication token not found. Redirecting to login page.');
            // If token doesn't exist, redirect to login
            navigate('/');
            return;
        }

        // Store user data in localStorage if it's not already there
        if (userData && !localStorage.getItem('user')) {
            localStorage.setItem('user', JSON.stringify(userData));
        }

        // Navigate to profile setup page
        navigate('/profile/setup');
    };

    // Handle skip profile setup with analytics tracking
    const handleSetupLater = () => {
        // Track button click
        analyticsService.trackEvent('welcome_setup_later_click', {
            timeSpentOnPage: Date.now() - performance.now()
        });

        // Clear first login flag
        localStorage.setItem('isFirstLogin', 'false');

        // Verify authentication token exists before navigation
        const authToken = localStorage.getItem('token');

        if (!authToken) {
            console.error('Authentication token not found. Redirecting to login page.');
            // If token doesn't exist, redirect to login
            navigate('/');
            return;
        }

        // Store user data in localStorage if it's not already there
        if (userData && !localStorage.getItem('user')) {
            localStorage.setItem('user', JSON.stringify(userData));
        }

        // Navigate to home page
        navigate('/home');
    };

    // Get mock user data for testing
    const getMockUserData = () => ({
        id: 'user123',
        nickname: 'GamerPro',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: null
    });

    // Loading state
    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-950">
                <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 dark:border-blue-400"></div>
                    <p className="mt-4 text-blue-600 dark:text-blue-300 font-medium">{t('loading', 'Preparing your welcome...')}</p>
                </div>
            </div>
        );
    }

    // Use actual user data or mock data for testing
    const user = userData || getMockUserData();

    return (
        <motion.div
            className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 relative overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
                duration: prefersReducedMotion ? 0.1 : 0.5,
                ease: prefersReducedMotion ? 'linear' : 'easeInOut'
            }}
            role="main"
            aria-label={t('ariaLabel', 'Welcome page after registration')}
        >
            {/* Language Switcher */}
            <WelcomeLanguageSwitcher />

            {/* Confetti canvas for celebration animation */}
            {showConfetti && (
                <canvas
                    ref={confettiCanvasRef}
                    className="fixed inset-0 z-10 pointer-events-none"
                    aria-hidden="true"
                />
            )}

            {/* Decorative background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(#4f46e5_1px,transparent_1px)] dark:bg-[radial-gradient(#6366f1_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.15]"></div>
                <div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-200 dark:bg-blue-900 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob"></div>
                <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-purple-200 dark:bg-purple-900 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-2000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-200 dark:bg-pink-900 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-4000"></div>
            </div>

            <div className={`max-w-4xl mx-auto my-4 sm:my-8 bg-white/90 dark:bg-gray-900/90 backdrop-filter backdrop-blur-lg rounded-xl sm:rounded-2xl shadow-xl sm:shadow-2xl overflow-hidden transition-all duration-700 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                {/* Enhanced header with gradient and animations */}
                <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 dark:from-blue-800 dark:via-indigo-900 dark:to-purple-900 px-4 py-8 sm:px-10 sm:py-16 relative overflow-hidden">
                    {/* Animated background patterns */}
                    <div className="absolute inset-0">
                        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.2)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.2)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.08)_1px,transparent_1px)] bg-[size:40px_40px] [background-position:center] opacity-[0.2]"></div>
                        <div className="absolute right-0 bottom-0 w-40 h-40 bg-white/10 dark:bg-white/5 rounded-full filter blur-3xl opacity-30 animate-pulse-slow"></div>
                        <div className="absolute left-0 top-0 w-40 h-40 bg-white/10 dark:bg-white/5 rounded-full filter blur-3xl opacity-30 animate-pulse-slow animation-delay-2000"></div>
                    </div>

                    <div className="flex flex-col items-center text-center relative z-10">
                        {/* User avatar with animations */}
                        <div className={`relative w-28 h-28 sm:w-36 sm:h-36 mb-8 transition-all duration-1000 transform ${animationReady ? 'scale-100 rotate-0' : 'scale-50 rotate-12'}`}>
                            {/* Avatar container with glow effect */}
                            <div className="absolute inset-0 rounded-full bg-white dark:bg-gray-800 p-1 shadow-xl">
                                {user.profileImage ? (
                                    <img
                                        src={user.profileImage}
                                        alt={user.nickname}
                                        className="w-full h-full object-cover rounded-full"
                                    />
                                ) : (
                                    <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-200 to-indigo-200 dark:from-blue-900 dark:to-indigo-900 flex items-center justify-center">
                                        <span className="text-4xl sm:text-5xl font-bold text-blue-600 dark:text-blue-300">
                                            {user.nickname.charAt(0).toUpperCase()}
                                        </span>
                                    </div>
                                )}

                                {/* Animated ring */}
                                <div className="absolute -inset-1 rounded-full border-2 border-white/50 dark:border-blue-900/50 opacity-75 animate-spin-slow"></div>
                            </div>

                            {/* Decorative elements */}
                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 dark:bg-yellow-600 rounded-full animate-bounce shadow-lg"></div>
                            <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-pink-400 dark:bg-pink-700 rounded-full animate-pulse shadow-lg"></div>
                            <div className="absolute top-1/2 -right-4 w-4 h-4 bg-green-400 dark:bg-green-700 rounded-full animate-ping"></div>
                            <div className="absolute bottom-1/2 -left-4 w-4 h-4 bg-purple-400 dark:bg-purple-700 rounded-full animate-ping animation-delay-1000"></div>
                        </div>

                        {/* Welcome text with animations */}
                        <div className={`space-y-4 transition-all duration-700 delay-300 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
                            <h1 className="text-3xl sm:text-5xl font-extrabold text-white tracking-tight dark:text-gray-100">
                                {t('welcome.title', 'Welcome to Mission X')},<br />
                                <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-pink-300 dark:from-yellow-400 dark:to-pink-400">
                                    {user.nickname}!
                                </span>
                            </h1>
                            <p className="mt-3 text-lg sm:text-xl text-blue-100 dark:text-blue-200 max-w-xl mx-auto">
                                {t('welcome.subtitle', 'Your gaming adventure begins now! We\'re excited to have you join our community.')}
                            </p>

                            {/* Animated divider */}
                            <div className="w-24 h-1.5 bg-gradient-to-r from-blue-300 to-purple-300 dark:from-blue-700 dark:to-purple-700 mx-auto rounded-full animate-pulse-slow"></div>
                        </div>
                    </div>
                </div>

                {/* Enhanced content section */}
                <div className="px-4 py-6 sm:px-10 sm:py-16 space-y-6 sm:space-y-10 relative dark:bg-gray-900/80">
                    {/* Decorative elements */}
                    <div className="absolute -top-10 -right-10 w-40 h-40 bg-blue-50 dark:bg-blue-900 rounded-full opacity-70"></div>
                    <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-indigo-50 dark:bg-indigo-900 rounded-full opacity-70"></div>

                    {/* Main content */}
                    <section
                        className={`text-center space-y-4 relative z-10 transition-all duration-700 delay-500 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
                        aria-labelledby="welcome-main-heading"
                    >
                        <div className="inline-block mb-2">
                            {user.profileImage ? (
                                <div className="w-20 h-20 mx-auto">
                                    <LazyImage
                                        src={user.profileImage}
                                        alt={`${user.nickname || user.name}'s profile`}
                                        className="w-full h-full object-cover rounded-full border-4 border-white shadow-md"
                                        placeholderSrc="/images/profile-placeholder.jpg"
                                        onLoad={() => analyticsService.trackEvent('welcome_profile_image_loaded')}
                                    />
                                </div>
                            ) : (
                                <div
                                    className="w-20 h-20 bg-gradient-to-br from-indigo-400 to-indigo-600 dark:from-indigo-900 dark:to-indigo-700 rounded-full flex items-center justify-center mx-auto shadow-md"
                                    aria-hidden="true"
                                >
                                    <span className="text-white dark:text-gray-200 text-xl font-bold">
                                        {user.nickname ? user.nickname.charAt(0).toUpperCase() : (user.name ? user.name.charAt(0).toUpperCase() : '?')}
                                    </span>
                                </div>
                            )}
                        </div>
                        <h2
                            id="welcome-main-heading"
                            className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100"
                        >
                            {t('welcome.readyTitle', 'Your Mission X Account is Ready')}
                        </h2>
                        <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            {t('welcome.readyDescription', 'You\'ve successfully joined our community of gamers, talents, and enthusiasts. Mission X is your platform to showcase your gaming skills, connect with other players, and discover new opportunities in the gaming world.')}
                        </p>
                    </section>

                    {/* Enhanced features section - optimized for screen orientation */}
                    <section
                        className={`grid grid-cols-1 ${isLandscape ? 'sm:grid-cols-3' : 'sm:grid-cols-2 md:grid-cols-3'} gap-4 sm:gap-6 mt-6 sm:mt-8 relative z-10 transition-all duration-700 delay-700 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
                        aria-label={t('welcome.features.ariaLabel', 'Key features of Mission X')}
                    >
                        {/* Feature 1: Create Profile */}
                        <div
                            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-xl p-6 text-center border border-blue-100 dark:border-blue-800 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group"
                            tabIndex={0}
                            role="article"
                            aria-labelledby="feature-profile-title"
                        >
                            <div
                                className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-700 dark:to-blue-900 rounded-full flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110"
                                aria-hidden="true"
                            >
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <h3
                                id="feature-profile-title"
                                className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2"
                            >
                                {t('welcome.features.profile.title', 'Create Your Profile')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                {t('welcome.features.profile.description', 'Showcase your gaming achievements and skills to the community')}
                            </p>
                        </div>

                        {/* Feature 2: Connect with Gamers */}
                        <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900 dark:to-indigo-800 rounded-xl p-6 text-center border border-indigo-100 dark:border-indigo-800 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-indigo-400 to-indigo-600 dark:from-indigo-700 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
                                {t('welcome.features.connect.title', 'Connect with Gamers')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                {t('welcome.features.connect.description', 'Find and connect with other players who share your interests')}
                            </p>
                        </div>

                        {/* Feature 3: Showcase Skills */}
                        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 rounded-xl p-6 text-center border border-purple-100 dark:border-purple-800 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1 group">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-700 dark:to-purple-900 rounded-full flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
                                {t('welcome.features.showcase.title', 'Showcase Skills')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                                {t('welcome.features.showcase.description', 'Offer your gaming services and talents to the community')}
                            </p>
                        </div>
                    </section>

                    {/* Enhanced action buttons - optimized for screen orientation */}
                    <div
                        className={`flex flex-col ${isLandscape ? 'sm:flex-row' : 'md:flex-row'} gap-3 sm:gap-4 justify-center mt-8 sm:mt-12 relative z-10 transition-all duration-700 delay-900 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
                        role="group"
                        aria-label={t('welcome.buttons.ariaLabel', 'Profile setup options')}
                    >
                        <button
                            onClick={handleSetupProfile}
                            className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 overflow-hidden"
                            aria-label={t('welcome.buttons.setupNowAria', 'Set up your profile now')}
                            tabIndex={0}
                        >
                            {/* Button glow effect */}
                            <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400/0 via-white/30 dark:via-blue-900/30 to-blue-400/0 -translate-x-full animate-shimmer-slow group-hover:animate-shimmer" aria-hidden="true"></span>

                            <span className="relative flex items-center justify-center">
                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                {t('welcome.buttons.setupNow', 'Set Up Profile Now')}
                            </span>
                        </button>
                        <button
                            onClick={handleSetupLater}
                            className="px-8 py-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 font-medium rounded-xl shadow border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900 transform transition-all duration-300 hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 focus:ring-offset-2"
                            aria-label={t('welcome.buttons.setupLaterAria', 'Skip profile setup and go to home page')}
                            tabIndex={0}
                        >
                            <span className="flex items-center justify-center">
                                <svg className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                {t('welcome.buttons.setupLater', 'Set Up Profile Later')}
                            </span>
                        </button>
                    </div>

                    {/* Additional help text */}
                    <div
                        className={`text-center text-sm text-gray-500 dark:text-gray-300 mt-8 transition-all duration-700 delay-1000 transform ${animationReady ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
                        role="contentinfo"
                        aria-label={t('welcome.helpSection', 'Support information')}
                    >
                        <p>
                            {t('welcome.helpText', 'Need help? Contact our support team at')}
                            <a
                                href="mailto:<EMAIL>"
                                className="text-blue-600 dark:text-blue-400 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-700 focus:ring-offset-2 rounded-sm mx-1"
                                aria-label={t('welcome.contactSupport', 'Contact support via <NAME_EMAIL>')}
                            >
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            {/* CSS animations */}
            <style jsx="true">{`
                @keyframes shimmer {
                    100% {
                        transform: translateX(100%);
                    }
                }

                @keyframes spin-slow {
                    from {
                        transform: rotate(0deg);
                    }
                    to {
                        transform: rotate(360deg);
                    }
                }

                @keyframes blob {
                    0% {
                        transform: translate(0px, 0px) scale(1);
                    }
                    33% {
                        transform: translate(30px, -50px) scale(1.1);
                    }
                    66% {
                        transform: translate(-20px, 20px) scale(0.9);
                    }
                    100% {
                        transform: translate(0px, 0px) scale(1);
                    }
                }

                .animate-shimmer {
                    animation: shimmer 2s infinite;
                }

                .animate-shimmer-slow {
                    animation: shimmer 3s infinite;
                }

                .animate-spin-slow {
                    animation: spin-slow 10s linear infinite;
                }

                .animate-blob {
                    animation: blob 7s infinite;
                }

                .animation-delay-2000 {
                    animation-delay: 2s;
                }

                .animation-delay-4000 {
                    animation-delay: 4s;
                }

                .animation-delay-1000 {
                    animation-delay: 1s;
                }
            `}</style>
        </motion.div>
    );
}

export default WelcomePage;