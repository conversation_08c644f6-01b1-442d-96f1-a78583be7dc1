import React, { useState, useRef, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { DateTime } from 'luxon';
import Avatar from './Avatar';
import MentionsInput from './MentionsInput';
import socialPostService from '../services/socialPostService';

// Component to render text with @mentions
const MentionText = ({ text }) => {
    // Regular expression to match @username pattern
    const mentionRegex = /@(\w+)/g;
    
    if (!text) return null;
    
    // Split text by @mentions and create an array of text and mention elements
    const parts = [];
    let lastIndex = 0;
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
        // Add text before the mention
        if (match.index > lastIndex) {
            parts.push(text.substring(lastIndex, match.index));
        }
        
        // Add the mention as a link
        const username = match[1];
        parts.push(
            <a 
                key={`${username}-${match.index}`}
                href={`/profile/${username}`}
                className="text-indigo-600 hover:underline font-medium"
                onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering parent click handlers
                }}
            >
                @{username}
            </a>
        );
        
        lastIndex = match.index + match[0].length;
    }
    
    // Add any remaining text
    if (lastIndex < text.length) {
        parts.push(text.substring(lastIndex));
    }
    
    // Combine all parts
    return <>{parts.map((part, i) => <React.Fragment key={i}>{part}</React.Fragment>)}</>;
};

// Renders a comment and its nested replies
const Comment = ({ comment, postId, onAddReply, depth = 0 }) => {
    const queryClient = useQueryClient();
    const [isReplying, setIsReplying] = useState(false);
    const [replyContent, setReplyContent] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [isLiking, setIsLiking] = useState(false);
    const [isLiked, setIsLiked] = useState(comment.is_liked || false);
    const [totalLikes, setTotalLikes] = useState(comment.total_likes || 0);
    const replyInputRef = useRef(null);
    
    // Sync local state with comment prop when it changes
    useEffect(() => {
        setIsLiked(comment.is_liked || false);
        setTotalLikes(comment.total_likes || 0);
    }, [comment.is_liked, comment.total_likes]);
    
    // Ensure comment has a valid user object
    const user = comment?.user || { 
        id: 0, 
        username: 'Unknown User', 
        name: 'Unknown User', 
        profile_picture: null 
    };
    
    // Function to handle posting a reply
    const handleReply = async () => {
        if (!replyContent.trim() || submitting) return;
        
        setSubmitting(true);
        try {
            const newReply = await socialPostService.createCommentReply(
                postId, 
                comment.id, 
                replyContent
            );
            
            // Notify parent so it can refresh or update state
            if (typeof onAddReply === 'function') {
                onAddReply(newReply);
            }
            
            // Reset state
            setReplyContent('');
            setIsReplying(false);
        } catch (error) {
            console.error('Error posting reply:', error);
        } finally {
            setSubmitting(false);
        }
    };
    
    // Cancel reply and reset state
    const handleCancelReply = () => {
        setReplyContent('');
        setIsReplying(false);
    };
    
    // Like/unlike comment handler
    const handleToggleLike = async () => {
        if (isLiking) return;
        setIsLiking(true);
        
        const currentIsLiked = isLiked;
        const currentTotalLikes = totalLikes;
        
        // Optimistic update for instant UI feedback
        setIsLiked((prev) => !prev);
        setTotalLikes((prev) => (currentIsLiked ? prev - 1 : prev + 1));
        
        try {
            await socialPostService.toggleCommentLike(postId, comment.id);
        } catch (err) {
            // If the API call fails, roll back the optimistic update
            setIsLiked(currentIsLiked);
            setTotalLikes(currentTotalLikes);
            console.error('Error toggling like:', err);
        } finally {
            // Always refetch comments to sync with the server's state
            queryClient.invalidateQueries({ queryKey: ['comments', 'list', postId] });
            setIsLiking(false);
        }
    };
    
    return (
        <div className={`${depth > 0 ? 'ml-8' : ''}`}>
            <div className="flex mb-3">
                <Avatar user={user} size={depth > 0 ? "sm" : "md"} />
                <div className="ml-3 flex-1">
                    <div className={`rounded-lg py-2 px-3 ${depth > 0 ? 'bg-gray-50' : 'bg-gray-100'}`}>
                        <p className="font-medium text-left text-gray-900 text-sm">{user.username}</p>
                        <p className="text-gray-700 text-left text-sm break-words">
                            <MentionText text={comment.content} />
                        </p>
                    </div>
                    
                    <div className="flex items-center mt-1 text-xs text-gray-500 space-x-4">
                        <span>{DateTime.fromISO(comment.created_at).toRelative()}</span>
                        {depth === 0 && (
                            <button 
                                onClick={() => setIsReplying(true)}
                                className="hover:text-indigo-800 text-indigo-600 bg-indigo-200 hover:bg-transparent rounded-md border border-indigo font-medium"
                            >
                                Reply
                            </button>
                        )}
                        {/* Like button */}
                        <button
                            onClick={handleToggleLike}
                            disabled={isLiking}
                            className={`flex items-center space-x-1 bg-transparent hover:bg-transparent focus:outline-none ${isLiked ? 'text-red-500' : 'text-gray-400'} ${isLiking ? 'opacity-50 cursor-not-allowed' : 'hover:text-red-600'}`}
                            aria-label={isLiked ? 'Unlike comment' : 'Like comment'}
                        >
                            <svg
                                className="w-4 h-4"
                                fill={isLiked ? 'currentColor' : 'none'}
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                />
                            </svg>
                            <span className="text-xs bg-transparent hover:bg-transparent hover:text-red-500 font-medium">{totalLikes}</span>
                        </button>
                    </div>
                    
                    {/* Reply Input */}
                    {isReplying && (
                        <div className="mt-2">
                            <MentionsInput
                                ref={replyInputRef}
                                value={replyContent}
                                onChange={setReplyContent}
                                placeholder="Write a reply..."
                                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                                disabled={submitting}
                            />
                            <div className="flex justify-end mt-2 space-x-2">
                                <button
                                    onClick={handleCancelReply}
                                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                                    disabled={submitting}
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleReply}
                                    disabled={!replyContent.trim() || submitting}
                                    className={`px-3 py-1 text-sm bg-indigo-600 text-white rounded-md ${
                                        !replyContent.trim() || submitting 
                                            ? 'opacity-50 cursor-not-allowed' 
                                            : 'hover:bg-indigo-700'
                                    }`}
                                >
                                    {submitting ? (
                                        <span className="flex items-center">
                                            <span className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></span>
                                            Posting...
                                        </span>
                                    ) : (
                                        'Post'
                                    )}
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            
            {/* Render Replies */}
            {comment.replies && comment.replies.length > 0 && (
                <div className="space-y-3">
                    {comment.replies.map(reply => (
                        <Comment 
                            key={reply.id} 
                            comment={reply} 
                            postId={postId}
                            onAddReply={onAddReply}
                            depth={depth + 1}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default Comment; 