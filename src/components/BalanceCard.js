import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import walletAPI from '../services/walletService';
import { DateTime } from 'luxon';

const BalanceCard = ({ 
  balance, 
  lastTransaction = null, 
  onTopUpClick,
  onWithdrawClick,
  isLoading = false,
  error = null
}) => {
  const [prevBalance, setPrevBalance] = useState(balance);
  const [showBalanceChange, setShowBalanceChange] = useState(false);
  const [balanceIncreased, setBalanceIncreased] = useState(false);
  
  useEffect(() => {
    // If balance changes, show the animation indicator
    if (prevBalance !== null && balance !== prevBalance) {
      setBalanceIncreased(balance > prevBalance);
      setShowBalanceChange(true);
      
      // Hide the indicator after animation completes
      const timer = setTimeout(() => {
        setShowBalanceChange(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
    
    setPrevBalance(balance);
  }, [balance, prevBalance]);

  const formatAmount = (amount) => {
    if (amount === null || amount === undefined) return '0';
    return amount.toLocaleString();
  };

  const getLastTransactionText = () => {
    if (!lastTransaction) return 'No recent transactions';
    
    const transactionDate = DateTime.fromISO(lastTransaction.created_at);
    const now = DateTime.now();
    const timeAgo = transactionDate.toRelative();
    
    if (lastTransaction.transaction_type === 'add') {
      return `Credits added ${timeAgo}`;
    } else {
      return `Credits used ${timeAgo}`;
    }
  };

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-sm overflow-hidden relative"
      >
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 text-red-500">
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Error Loading Balance</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl shadow-sm overflow-hidden relative"
    >
      {/* Background decoration */}
      <div className="absolute -right-6 -top-6 h-32 w-32 bg-indigo-100 rounded-full opacity-70" />
      <div className="absolute right-16 top-16 h-16 w-16 bg-blue-100 rounded-full opacity-50" />
      <div className="absolute -left-6 -bottom-6 h-24 w-24 bg-green-100 rounded-full opacity-40" />
      
      <div className="p-6 sm:p-8 relative z-10">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-6">
          {/* Balance Section */}
          <div className="flex-1">
            <h2 className="text-lg text-gray-600 mb-1">Your Balance</h2>
            {isLoading ? (
              <div className="animate-pulse h-10 w-48 bg-gray-200 rounded mb-2" />
            ) : (
              <p className="text-4xl font-bold text-gray-900">{formatAmount(balance)} <span className="text-lg font-normal text-gray-500">Credits</span></p>
            )}
            
            {isLoading ? (
              <div className="animate-pulse h-4 w-36 bg-gray-200 rounded mt-2" />
            ) : (
              <p className="text-sm text-gray-500">{getLastTransactionText()}</p>
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onTopUpClick}
              disabled={isLoading}
              className={`px-5 py-3 rounded-xl font-medium text-white shadow-sm transition-colors flex items-center justify-center gap-2
                ${isLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Top Up
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onWithdrawClick}
              disabled={isLoading || !balance || balance <= 0}
              className={`px-5 py-3 rounded-xl font-medium shadow-sm transition-colors flex items-center justify-center gap-2
                ${isLoading || !balance || balance <= 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'}`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
              Withdraw
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default BalanceCard; 