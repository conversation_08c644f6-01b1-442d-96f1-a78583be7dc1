import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUserPlus, FaUserCheck, FaStar, FaHeart, FaEye, FaCrown, FaShieldAlt, FaFire, FaDiamond } from 'react-icons/fa';
import { formatTalentForDisplay } from '../models/TalentModel';
import { useToggleFollow } from '../hooks/talent/useTalents';
import { motion } from 'framer-motion';

const TalentCard = ({ talent }) => {
    // Format the talent data for display
    const formattedTalent = formatTalentForDisplay(talent);

    const {
        id,
        displayName,
        gender,
        levelNumber,
        levelName,
        profileImage,
        lowestPrice,
        isFeatured,
        rating,
        services = [],
        verified,
        vip
    } = formattedTalent;

    const navigate = useNavigate();
    const toggleFollowMutation = useToggleFollow();
    const [isFollowing, setIsFollowing] = useState(formattedTalent.isFollowing);
    const [followLoading, setFollowLoading] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [imageLoaded, setImageLoaded] = useState(false);

    const handleCardClick = () => {
        navigate(`/talents/${id}`);
    };

    const handleFollowClick = async (e) => {
        e.stopPropagation();
        setFollowLoading(true);
        const newState = !isFollowing;
        setIsFollowing(newState);
        try {
            const res = await toggleFollowMutation.mutateAsync({ talentId: id, isFollowing: newState });
            if (typeof res?.is_following === 'boolean') {
                setIsFollowing(res.is_following);
            }
        } catch (err) {
            console.error('Error toggling follow:', err);
            setIsFollowing(!newState);
        }
        setFollowLoading(false);
    };

    // Default image URL for fallback
    const defaultImageUrl = '/AuthLogo.png';

    // Service badges logic
    const maxServices = 2;
    const visibleServices = services.slice(0, maxServices);
    const extraServices = services.length > maxServices ? services.length - maxServices : 0;

    // Animation variants
    const cardVariants = {
        initial: { 
            scale: 1, 
            rotateY: 0,
            boxShadow: "0 10px 30px -5px rgba(0, 0, 0, 0.3)"
        },
        hover: { 
            scale: 1.05, 
            rotateY: 2,
            boxShadow: "0 25px 60px -5px rgba(0, 0, 0, 0.4)"
        }
    };

    const imageVariants = {
        initial: { scale: 1.1, filter: "brightness(0.9)" },
        hover: { scale: 1.2, filter: "brightness(1.1)" }
    };

    const overlayVariants = {
        initial: { opacity: 0.7 },
        hover: { opacity: 0.9 }
    };

    return (
        <motion.div
            className="group relative w-full max-w-[95vw] sm:max-w-xs md:max-w-sm lg:max-w-md mx-auto h-[420px] cursor-pointer overflow-hidden"
            variants={cardVariants}
            initial="initial"
            whileHover="hover"
            onClick={handleCardClick}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            style={{ 
                perspective: "1000px",
                transformStyle: "preserve-3d"
            }}
        >
            {/* Main Card Container with Multiple Layers */}
            <div className="relative w-full h-full rounded-3xl overflow-hidden shadow-2xl">
                {/* Animated Background Gradients */}
                <div className="absolute inset-0 z-0">
                    {/* Primary gradient layer */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-pink-500 to-red-500 opacity-90" />
                    
                    {/* Secondary animated gradient layer */}
                    <motion.div 
                        className="absolute inset-0 bg-gradient-to-tr from-cyan-400 via-blue-500 to-purple-600 opacity-70"
                        animate={{
                            background: [
                                "linear-gradient(45deg, #06b6d4, #3b82f6, #8b5cf6)",
                                "linear-gradient(45deg, #f59e0b, #ef4444, #ec4899)",
                                "linear-gradient(45deg, #10b981, #06b6d4, #8b5cf6)",
                                "linear-gradient(45deg, #06b6d4, #3b82f6, #8b5cf6)"
                            ]
                        }}
                        transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    />
                    
                    {/* Animated mesh gradient overlay */}
                    <div className="absolute inset-0 opacity-30">
                        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000 ease-out" />
                    </div>
                </div>

                {/* Floating Particles Effect */}
                <div className="absolute inset-0 z-5 pointer-events-none overflow-hidden">
                    {[...Array(6)].map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute w-2 h-2 bg-white/30 rounded-full"
                            initial={{
                                x: Math.random() * 300,
                                y: Math.random() * 400,
                                opacity: 0
                            }}
                            animate={{
                                x: Math.random() * 300,
                                y: Math.random() * 400,
                                opacity: [0, 1, 0]
                            }}
                            transition={{
                                duration: 3 + Math.random() * 2,
                                repeat: Infinity,
                                delay: Math.random() * 2
                            }}
                        />
                    ))}
                </div>

                {/* Profile Image Container */}
                <div className="absolute inset-0 z-10">
                    <motion.img
                        src={profileImage || defaultImageUrl}
                        alt={displayName || 'Talent Profile'}
                        className="w-full h-full object-cover object-center"
                        variants={imageVariants}
                        onLoad={() => setImageLoaded(true)}
                        onError={e => { 
                            e.target.onerror = null; 
                            e.target.src = defaultImageUrl; 
                            setImageLoaded(true);
                        }}
                        style={{
                            filter: "contrast(1.1) saturate(1.2)"
                        }}
                    />
                    
                    {/* Image loading shimmer effect */}
                    {!imageLoaded && (
                        <div className="absolute inset-0 bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 animate-pulse" />
                    )}
                </div>

                {/* Premium Border Ring */}
                <div className="absolute inset-0 z-15 rounded-3xl border-4 border-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 opacity-80 group-hover:opacity-100 transition-opacity duration-300" 
                     style={{
                         background: 'linear-gradient(45deg, transparent, transparent) padding-box, linear-gradient(45deg, #fbbf24, #ec4899, #8b5cf6) border-box'
                     }} />

                {/* Dynamic Overlay with Mesh Gradient */}
                <motion.div 
                    className="absolute inset-0 z-20 rounded-3xl"
                    variants={overlayVariants}
                    style={{
                        background: `
                            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
                            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
                            linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 70%, rgba(0, 0, 0, 0.95) 100%)
                        `
                    }}
                />

                {/* Top Section - Badges and Status */}
                <div className="absolute top-0 left-0 right-0 z-30 p-4 flex justify-between items-start">
                    {/* Featured Badge */}
                    {isFeatured && (
                        <motion.div
                            className="relative"
                            initial={{ scale: 0, rotate: -180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ type: "spring", delay: 0.2 }}
                        >
                            <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 rounded-full blur opacity-75 animate-pulse" />
                            <span className="relative flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 text-white font-black text-xs tracking-wider rounded-full shadow-xl backdrop-blur-sm border border-white/20">
                                <FaFire className="w-3 h-3 animate-bounce" />
                                <span>HOT</span>
                                <FaDiamond className="w-3 h-3 animate-pulse" />
                            </span>
                        </motion.div>
                    )}

                    {/* Online Status Indicator */}
                    <motion.div 
                        className="flex items-center space-x-2"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <div className="relative">
                            <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse shadow-lg" />
                            <div className="absolute inset-0 w-4 h-4 bg-green-400 rounded-full animate-ping opacity-40" />
                        </div>
                        <span className="text-green-300 text-xs font-bold tracking-wide drop-shadow-lg">ONLINE</span>
                    </motion.div>
                </div>

                {/* Bottom Content Section */}
                <div className="absolute bottom-0 left-0 right-0 z-30 p-5">
                    {/* Profile Info */}
                    <div className="mb-4">
                        <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3 min-w-0 flex-1">
                                <h3 className="text-2xl font-black text-white truncate drop-shadow-lg">
                                    <span className="bg-gradient-to-r from-white via-yellow-100 to-pink-100 bg-clip-text text-transparent">
                                        {displayName || 'Unknown Talent'}
                                    </span>
                                </h3>
                                
                                {/* Status Badges */}
                                <div className="flex items-center space-x-2">
                                    {verified && (
                                        <motion.div
                                            className="relative group/badge"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full blur opacity-60 group-hover/badge:opacity-100 transition-opacity" />
                                            <div className="relative flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-blue-500/80 to-cyan-500/80 text-white rounded-full backdrop-blur-sm border border-white/30 shadow-xl">
                                                <FaShieldAlt className="w-3 h-3" />
                                                <span className="text-xs font-bold">VERIFIED</span>
                                            </div>
                                        </motion.div>
                                    )}
                                    
                                    {vip && (
                                        <motion.div
                                            className="relative group/badge"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur opacity-60 group-hover/badge:opacity-100 transition-opacity animate-pulse" />
                                            <div className="relative flex items-center space-x-1 px-2 py-1 bg-gradient-to-r from-yellow-400/90 to-orange-500/90 text-white rounded-full backdrop-blur-sm border border-white/30 shadow-xl">
                                                <FaCrown className="w-3 h-3 animate-bounce" />
                                                <span className="text-xs font-bold">VIP</span>
                                            </div>
                                        </motion.div>
                                    )}
                                </div>
                            </div>

                            {/* Rating Badge */}
                            <motion.div 
                                className="relative group/rating"
                                whileHover={{ scale: 1.05 }}
                            >
                                <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur opacity-70 group-hover/rating:opacity-100 transition-opacity" />
                                <div className="relative flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-yellow-400/90 to-orange-500/90 rounded-full shadow-xl backdrop-blur-sm border border-white/20">
                                    <FaStar className="w-4 h-4 text-white drop-shadow" />
                                    <span className="text-white font-black text-sm drop-shadow">
                                        {typeof rating === 'number' ? rating.toFixed(1) : '5.0'}
                                    </span>
                                </div>
                            </motion.div>
                        </div>

                        {/* Level and Price Info */}
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-2">
                                <span className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white text-xs font-bold rounded-full border border-white/30 shadow-lg">
                                    Level {levelNumber}
                                </span>
                                {levelName && (
                                    <span className="px-3 py-1 bg-gradient-to-r from-indigo-500/80 to-purple-500/80 backdrop-blur-sm text-white text-xs font-bold rounded-full border border-white/30 shadow-lg">
                                        {levelName}
                                    </span>
                                )}
                            </div>
                            
                            {lowestPrice && (
                                <div className="text-right">
                                    <p className="text-white/80 text-xs font-medium">Starting at</p>
                                    <p className="text-white font-black text-lg drop-shadow-lg">
                                        ${typeof lowestPrice === 'number' ? lowestPrice.toFixed(2) : lowestPrice}
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Services and Actions */}
                    <div className="flex items-center justify-between space-x-3">
                        {/* Service Tags */}
                        <div className="flex flex-wrap gap-2 min-w-0 flex-1">
                            {visibleServices.map((service, i) => (
                                <motion.span
                                    key={i}
                                    className="relative group/service px-3 py-1 rounded-full backdrop-blur-sm border border-white/30 shadow-lg overflow-hidden"
                                    whileHover={{ scale: 1.05 }}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.1 * i }}
                                >
                                    <div className="absolute inset-0 bg-gradient-to-r from-white/30 to-white/10 group-hover/service:from-white/40 group-hover/service:to-white/20 transition-all duration-300" />
                                    <span className="relative text-white font-bold text-xs truncate max-w-[120px] block">
                                        {typeof service === 'string' ? service : service.type || service.name || ''}
                                    </span>
                                </motion.span>
                            ))}
                            
                            {extraServices > 0 && (
                                <motion.span 
                                    className="relative px-3 py-1 rounded-full overflow-hidden shadow-lg"
                                    whileHover={{ scale: 1.05 }}
                                    initial={{ opacity: 0, scale: 0 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 animate-pulse" />
                                    <span className="relative text-white font-bold text-xs border border-white/30 rounded-full px-2 py-0.5 backdrop-blur-sm">
                                        +{extraServices} more
                                    </span>
                                </motion.span>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center space-x-2 flex-shrink-0">
                            {/* View Profile Button */}
                            <motion.button
                                className="relative group/view p-3 rounded-full overflow-hidden shadow-xl"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleCardClick();
                                }}
                            >
                                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 group-hover/view:from-indigo-400 group-hover/view:to-purple-400 transition-all duration-300" />
                                <FaEye className="relative w-4 h-4 text-white" />
                            </motion.button>

                            {/* Follow Button */}
                            <div className="relative">
                                <motion.button
                                    className={`relative group/follow p-3 rounded-full overflow-hidden shadow-xl transition-all duration-300 ${
                                        isFollowing 
                                            ? 'bg-gradient-to-r from-pink-500 to-red-500' 
                                            : 'bg-gradient-to-r from-green-500 to-emerald-500'
                                    } ${followLoading ? 'opacity-60 cursor-not-allowed' : ''}`}
                                    whileHover={{ scale: followLoading ? 1 : 1.1 }}
                                    whileTap={{ scale: followLoading ? 1 : 0.9 }}
                                    onClick={handleFollowClick}
                                    onMouseEnter={() => setShowTooltip(true)}
                                    onMouseLeave={() => setShowTooltip(false)}
                                    disabled={followLoading}
                                >
                                    <div className={`absolute inset-0 transition-all duration-300 ${
                                        isFollowing 
                                            ? 'bg-gradient-to-r from-pink-400 to-red-400 group-hover/follow:from-pink-300 group-hover/follow:to-red-300' 
                                            : 'bg-gradient-to-r from-green-400 to-emerald-400 group-hover/follow:from-green-300 group-hover/follow:to-emerald-300'
                                    }`} />
                                    
                                    {followLoading ? (
                                        <div className="relative">
                                            <svg className="animate-spin w-4 h-4 text-white" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </div>
                                    ) : (
                                        <div className="relative">
                                            {isFollowing ? (
                                                <FaHeart className="w-4 h-4 text-white animate-pulse" />
                                            ) : (
                                                <FaUserPlus className="w-4 h-4 text-white" />
                                            )}
                                        </div>
                                    )}
                                </motion.button>

                                {/* Enhanced Tooltip */}
                                {showTooltip && !followLoading && (
                                    <motion.div
                                        className="absolute z-50 left-1/2 -translate-x-1/2 bottom-full mb-3 px-3 py-2 bg-black/90 backdrop-blur-sm text-white text-xs font-bold rounded-lg shadow-xl border border-white/20"
                                        initial={{ opacity: 0, y: 10, scale: 0.9 }}
                                        animate={{ opacity: 1, y: 0, scale: 1 }}
                                        exit={{ opacity: 0, y: 10, scale: 0.9 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <div className="relative">
                                            {isFollowing ? 'Unfollow' : 'Follow'}
                                            <div className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90" />
                                        </div>
                                    </motion.div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Hover Glow Effect */}
                <motion.div
                    className="absolute inset-0 z-5 rounded-3xl pointer-events-none"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    style={{
                        background: 'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)',
                        boxShadow: 'inset 0 0 60px rgba(255, 255, 255, 0.1)'
                    }}
                />
            </div>
        </motion.div>
    );
};

export default TalentCard;