import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUserPlus, FaUserCheck } from 'react-icons/fa';
import { formatTalentForDisplay } from '../models/TalentModel';
import { useToggleFollow } from '../hooks/talent/useTalents';
import { motion } from 'framer-motion';

const TalentCard = ({ talent }) => {
    // Format the talent data for display
    const formattedTalent = formatTalentForDisplay(talent);

    const {
        id,
        displayName,
        gender,
        levelNumber,
        levelName,
        profileImage,
        lowestPrice,
        isFeatured,
        rating,
        services = [],
        verified,
        vip
    } = formattedTalent;

    const navigate = useNavigate();
    const toggleFollowMutation = useToggleFollow();
    const [isFollowing, setIsFollowing] = useState(formattedTalent.isFollowing);
    const [followLoading, setFollowLoading] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);

    const handleCardClick = () => {
        navigate(`/talents/${id}`);
    };

    const handleFollowClick = async (e) => {
        e.stopPropagation();
        setFollowLoading(true);
        const newState = !isFollowing;
        setIsFollowing(newState);
        try {
            const res = await toggleFollowMutation.mutateAsync({ talentId: id, isFollowing: newState });
            if (typeof res?.is_following === 'boolean') {
                setIsFollowing(res.is_following);
            }
        } catch (err) {
            console.error('Error toggling follow:', err);
            setIsFollowing(!newState);
        }
        setFollowLoading(false);
    };

    // Default image URL for fallback
    const defaultImageUrl = '/AuthLogo.png';

    // Service badges logic
    const maxServices = 1;
    const visibleServices = services.slice(0, maxServices);
    const extraServices = services.length > maxServices ? services.length - maxServices : 0;

    return (
        <motion.div
            className="group relative w-full max-w-[95vw] sm:max-w-xs md:max-w-sm lg:max-w-md mx-auto aspect-[3/4] sm:aspect-[4/5] bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-3xl border-2 border-white/20 p-1 shadow-[0_8px_32px_0_rgba(24,25,38,0.45)] hover:shadow-[0_12px_48px_0_rgba(24,25,38,0.65)] backdrop-blur-2xl transition-all duration-300 cursor-pointer overflow-hidden animate-fade-in flex flex-col"
            whileHover={{ scale: 1.03 }}
            onClick={handleCardClick}
            style={{ minWidth: 0 }}
        >
            {/* RECOMMENDED/Featured Badge */}
            {isFeatured && (
                <div className="absolute top-4 left-4 z-40">
                    <span className="uppercase text-xs font-extrabold tracking-widest px-4 py-1 rounded-full bg-gradient-to-r from-fuchsia-500 to-indigo-600 text-white shadow-lg backdrop-blur-md border-2 border-white/20">
                        RECOMMENDED
                    </span>
                </div>
            )}
            {/* Glowing ring effect */}
            <div className="absolute inset-0 z-0 rounded-3xl pointer-events-none"
                style={{
                    background: 'radial-gradient(circle at 50% 60%, rgba(165,180,252,0.35) 0%, rgba(192,132,252,0.25) 40%, rgba(236,72,153,0.18) 80%, transparent 100%)',
                    filter: 'blur(12px)',
                    opacity: 0.85
                }}
            />
            {/* Profile Image as background */}
            <div className="absolute inset-0 w-full h-full flex items-center justify-center">
                <img
                    src={profileImage || defaultImageUrl}
                    alt={displayName || 'Talent Profile'}
                    className="w-full h-full object-cover object-center scale-105 rounded-3xl shadow-[0_0_0_4px_rgba(255,255,255,0.10)] group-hover:scale-110 transition-transform duration-500"
                    onError={e => { e.target.onerror = null; e.target.src = defaultImageUrl; }}
                />
                {/* Overlay for readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-80 group-hover:opacity-90 transition-opacity rounded-3xl"></div>
            </div>
            {/* Curved/angled bottom overlay */}
            <div className="absolute bottom-0 left-0 w-full h-[45%] z-10 pointer-events-none">
                <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full absolute bottom-0 left-0">
                    <defs>
                        <linearGradient id="talentCardGradient" x1="0" y1="0" x2="1" y2="1">
                            <stop offset="0%" stopColor="#23243a" />
                            <stop offset="40%" stopColor="#3b2170" />
                            <stop offset="70%" stopColor="#23243a" />
                            <stop offset="100%" stopColor="#1a1a22" />
                        </linearGradient>
                    </defs>
                    <path d="M0,30 Q50,0 100,30 L100,100 L0,100 Z" fill="url(#talentCardGradient)" />
                </svg>
            </div>
            {/* Content */}
            <div className="relative z-20 flex flex-col justify-end h-full p-3 sm:p-4">
                {/* Info Row: Name, Icons, Rating */}
                <div className="flex flex-wrap items-center justify-between gap-2 mb-2 sm:mb-3">
                    <div className="flex items-center gap-2 min-w-0">
                        <span className="font-extrabold text-white text-lg truncate drop-shadow flex items-center">
                            <span className="bg-gradient-to-r from-indigo-200 via-white to-purple-300 bg-clip-text text-transparent">
                                {displayName || 'Unknown Talent'}
                            </span>
                            {/* Status badge: Verified or VIP */}
                            {verified && (
                                <span className="ml-2 inline-flex items-center px-2 py-1 bg-gradient-to-r from-blue-500/70 to-indigo-500/70 border border-white/30 rounded-full shadow text-xs font-bold text-white backdrop-blur-md">
                                    <svg className="w-4 h-4 text-blue-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    Verified
                                </span>
                            )}
                            {vip && (
                                <span className="ml-2 inline-flex items-center px-2 py-1 bg-gradient-to-r from-yellow-400/80 to-pink-400/80 border border-white/30 rounded-full shadow text-xs font-bold text-white backdrop-blur-md">
                                    <svg className="w-4 h-4 text-yellow-200 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 2l2.39 4.84L18 7.27l-3.91 3.81L15.18 18 10 14.77 4.82 18l1.09-6.92L2 7.27l5.61-.43L10 2z" />
                                    </svg>
                                    VIP
                                </span>
                            )}
                        </span>
                    </div>
                    <div className="flex items-center gap-1 flex-shrink-0 bg-yellow-400/90 px-2 py-1 rounded-full shadow-lg">
                        <svg className="w-4 h-4 text-white drop-shadow" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292c1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="font-extrabold text-white text-sm drop-shadow">{typeof rating === 'number' ? rating.toFixed(1) : '5.0'}</span>
                    </div>
                </div>
                {/* Service Badges & Follow Button Row */}
                <div className="flex flex-wrap items-center justify-between gap-2 mb-2">
                    <div className="flex flex-wrap gap-2 min-w-0 flex-1">
                        {visibleServices.map((service, i) => (
                            <span
                                key={i}
                                className="px-3 py-1 rounded-full bg-white/90 text-indigo-800 font-bold text-xs shadow backdrop-blur-md truncate max-w-[140px] border-2 border-indigo-200"
                                title={typeof service === 'string' ? service : service.type || service.name || ''}
                            >
                                {typeof service === 'string' ? service : service.type || service.name || ''}
                            </span>
                        ))}
                        {extraServices > 0 && (
                            <span className="px-3 py-1 rounded-full bg-gradient-to-r from-fuchsia-500 to-indigo-600 text-white font-bold text-xs shadow backdrop-blur-md border-2 border-white/40">
                                +{extraServices}
                            </span>
                        )}
                    </div>
                    <div className="relative flex-shrink-0">
                        <motion.button
                            className={`flex items-center justify-center rounded-full p-2 text-xs sm:text-sm font-semibold shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60
                                ${isFollowing ? 'bg-green-500 text-white hover:bg-green-600' : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'}
                                ${followLoading ? 'opacity-60 cursor-not-allowed' : 'hover:scale-105 active:scale-95'}`}
                            onClick={handleFollowClick}
                            aria-label={isFollowing ? 'Unfollow Talent' : 'Follow Talent'}
                            disabled={followLoading}
                            onMouseEnter={() => setShowTooltip(true)}
                            onMouseLeave={() => setShowTooltip(false)}
                            whileHover={{ scale: 1.08 }}
                            whileTap={{ scale: 0.96 }}
                            style={{ minWidth: 32, minHeight: 32 }}
                        >
                            {followLoading ? (
                                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            ) : isFollowing ? (
                                <FaUserCheck className="h-4 w-4" />
                            ) : (
                                <FaUserPlus className="h-4 w-4" />
                            )}
                        </motion.button>
                        {showTooltip && !followLoading && (
                            <div className="absolute z-20 left-1/2 -translate-x-1/2 top-full mt-2 px-3 py-1 bg-black text-white text-xs rounded shadow-lg whitespace-nowrap">
                                {isFollowing ? 'Unfollow' : 'Follow'}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default TalentCard;