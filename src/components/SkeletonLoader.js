import React from 'react';
import { motion } from 'framer-motion';

// Base skeleton component with shimmer effect
const SkeletonBase = ({ className = '', children, ...props }) => {
    return (
        <div 
            className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer rounded ${className}`}
            {...props}
        >
            {children}
        </div>
    );
};

// Post card skeleton for feed
export const PostCardSkeleton = () => {
    return (
        <motion.div 
            className="mb-4 break-inside-avoid rounded-2xl overflow-hidden shadow-lg bg-white/80 backdrop-blur-sm border border-white/20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
        >
            {/* Media skeleton */}
            <div className="relative bg-gray-200 aspect-[4/5]">
                <SkeletonBase className="w-full h-full" />
                
                {/* Shimmer overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer-slow"></div>
            </div>
            
            {/* Content skeleton */}
            <div className="p-4">
                {/* User info skeleton */}
                <div className="flex items-center space-x-3 mb-3">
                    <SkeletonBase className="w-10 h-10 rounded-full" />
                    <div className="flex-1 space-y-2">
                        <SkeletonBase className="h-4 w-3/4" />
                        <SkeletonBase className="h-3 w-1/2" />
                    </div>
                </div>
                
                {/* Description skeleton */}
                <div className="space-y-2 mb-3">
                    <SkeletonBase className="h-3 w-full" />
                    <SkeletonBase className="h-3 w-4/5" />
                </div>
                
                {/* Stats skeleton */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                            <SkeletonBase className="w-4 h-4 rounded" />
                            <SkeletonBase className="h-3 w-6" />
                        </div>
                        <div className="flex items-center space-x-1">
                            <SkeletonBase className="w-4 h-4 rounded" />
                            <SkeletonBase className="h-3 w-6" />
                        </div>
                    </div>
                    <SkeletonBase className="h-6 w-16 rounded-full" />
                </div>
            </div>
        </motion.div>
    );
};

// Feed skeleton with multiple post cards
export const FeedSkeleton = ({ count = 6 }) => {
    return (
        <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
            {Array.from({ length: count }).map((_, index) => (
                <PostCardSkeleton key={index} />
            ))}
        </div>
    );
};

// Comment skeleton
export const CommentSkeleton = () => {
    return (
        <motion.div 
            className="flex space-x-3 p-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
        >
            <SkeletonBase className="w-8 h-8 rounded-full flex-shrink-0" />
            <div className="flex-1 space-y-2">
                <div className="flex items-center space-x-2">
                    <SkeletonBase className="h-3 w-20" />
                    <SkeletonBase className="h-3 w-12" />
                </div>
                <SkeletonBase className="h-4 w-full" />
                <SkeletonBase className="h-4 w-3/4" />
                <div className="flex items-center space-x-4 mt-2">
                    <SkeletonBase className="h-3 w-8" />
                    <SkeletonBase className="h-3 w-12" />
                </div>
            </div>
        </motion.div>
    );
};

// Post detail skeleton
export const PostDetailSkeleton = () => {
    return (
        <motion.div 
            className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl overflow-hidden w-full max-w-6xl flex flex-col md:flex-row max-h-[90vh] border border-white/20"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        >
            {/* Media skeleton */}
            <div className="relative w-full md:w-3/5 bg-gray-200 flex items-center justify-center">
                <SkeletonBase className="w-full h-full min-h-[400px]" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer-slow"></div>
            </div>
            
            {/* Content skeleton */}
            <div className="w-full md:w-2/5 flex flex-col max-h-[90vh] md:max-h-full bg-gradient-to-br from-white to-gray-50/50">
                {/* Header skeleton */}
                <div className="p-6 border-b border-gray-200/50">
                    <div className="flex items-center">
                        <SkeletonBase className="w-12 h-12 rounded-full" />
                        <div className="ml-4 flex-1 space-y-2">
                            <SkeletonBase className="h-5 w-32" />
                            <SkeletonBase className="h-3 w-24" />
                        </div>
                    </div>
                </div>
                
                {/* Content skeleton */}
                <div className="p-6 border-b border-gray-200/50 space-y-3">
                    <SkeletonBase className="h-6 w-full" />
                    <SkeletonBase className="h-4 w-full" />
                    <SkeletonBase className="h-4 w-3/4" />
                    <SkeletonBase className="h-3 w-20 mt-4" />
                </div>
                
                {/* Actions skeleton */}
                <div className="p-6 border-b border-gray-200/50 flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                        <SkeletonBase className="h-10 w-20 rounded-full" />
                        <SkeletonBase className="h-10 w-20 rounded-full" />
                    </div>
                    <SkeletonBase className="h-8 w-8 rounded-full" />
                </div>
                
                {/* Comments skeleton */}
                <div className="flex-1 p-6 space-y-4">
                    <SkeletonBase className="h-5 w-32" />
                    {Array.from({ length: 3 }).map((_, index) => (
                        <CommentSkeleton key={index} />
                    ))}
                </div>
                
                {/* Comment form skeleton */}
                <div className="p-6 border-t border-gray-200/50">
                    <div className="space-y-4">
                        <SkeletonBase className="h-12 w-full rounded-2xl" />
                        <div className="flex justify-between items-center">
                            <SkeletonBase className="h-3 w-20" />
                            <SkeletonBase className="h-10 w-32 rounded-2xl" />
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

// User card skeleton
export const UserCardSkeleton = () => {
    return (
        <motion.div 
            className="bg-white rounded-2xl p-4 shadow-lg border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="flex items-center space-x-3">
                <SkeletonBase className="w-12 h-12 rounded-full" />
                <div className="flex-1 space-y-2">
                    <SkeletonBase className="h-4 w-24" />
                    <SkeletonBase className="h-3 w-32" />
                </div>
                <SkeletonBase className="h-8 w-20 rounded-full" />
            </div>
        </motion.div>
    );
};

// Navigation skeleton
export const NavigationSkeleton = () => {
    return (
        <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-40">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    <SkeletonBase className="h-8 w-32" />
                    <div className="flex items-center space-x-6">
                        <SkeletonBase className="h-10 w-24 rounded-full" />
                        <SkeletonBase className="h-10 w-24 rounded-full" />
                    </div>
                    <SkeletonBase className="w-8 h-8 rounded-full" />
                </div>
            </div>
        </div>
    );
};

// Generic skeleton for any content
export const GenericSkeleton = ({ 
    lines = 3, 
    avatar = false, 
    className = '',
    ...props 
}) => {
    return (
        <motion.div 
            className={`space-y-3 ${className}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            {...props}
        >
            {avatar && (
                <div className="flex items-center space-x-3">
                    <SkeletonBase className="w-10 h-10 rounded-full" />
                    <div className="space-y-2">
                        <SkeletonBase className="h-4 w-24" />
                        <SkeletonBase className="h-3 w-16" />
                    </div>
                </div>
            )}
            {Array.from({ length: lines }).map((_, index) => (
                <SkeletonBase 
                    key={index}
                    className={`h-4 ${
                        index === lines - 1 ? 'w-3/4' : 'w-full'
                    }`}
                />
            ))}
        </motion.div>
    );
};

export default SkeletonBase;
