import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const VoiceNote = ({ onSave, initialAudio }) => {
    const [isRecording, setIsRecording] = useState(false);
    const [recordingTime, setRecordingTime] = useState(0);
    const [audioUrl, setAudioUrl] = useState(initialAudio || null);
    const [isPlaying, setIsPlaying] = useState(false);
    const mediaRecorder = useRef(null);
    const audioChunks = useRef([]);
    const timerRef = useRef(null);
    const audioRef = useRef(new Audio());

    useEffect(() => {
        if (initialAudio) {
            audioRef.current.src = initialAudio;
        }
        return () => {
            audioRef.current.pause();
            URL.revokeObjectURL(audioUrl);
        };
    }, [initialAudio]);

    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder.current = new MediaRecorder(stream);
            audioChunks.current = [];

            mediaRecorder.current.ondataavailable = (event) => {
                audioChunks.current.push(event.data);
            };

            mediaRecorder.current.onstop = () => {
                const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
                const url = URL.createObjectURL(audioBlob);
                setAudioUrl(url);
                audioRef.current.src = url;
                if (onSave) onSave(audioBlob);
            };

            mediaRecorder.current.start();
            setIsRecording(true);
            setRecordingTime(0);
            
            timerRef.current = setInterval(() => {
                setRecordingTime(prev => {
                    if (prev >= 15) {
                        stopRecording();
                        return prev;
                    }
                    return prev + 1;
                });
            }, 1000);
        } catch (error) {
            console.error('Error accessing microphone:', error);
        }
    };

    const stopRecording = () => {
        if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
            mediaRecorder.current.stop();
            mediaRecorder.current.stream.getTracks().forEach(track => track.stop());
            clearInterval(timerRef.current);
            setIsRecording(false);
        }
    };

    const togglePlayback = () => {
        if (isPlaying) {
            audioRef.current.pause();
            setIsPlaying(false);
        } else {
            audioRef.current.play();
            setIsPlaying(true);
        }
    };

    const deleteRecording = () => {
        URL.revokeObjectURL(audioUrl);
        setAudioUrl(null);
        audioRef.current.src = '';
        if (onSave) onSave(null);
    };

    useEffect(() => {
        audioRef.current.onended = () => setIsPlaying(false);
        return () => {
            clearInterval(timerRef.current);
            if (mediaRecorder.current?.stream) {
                mediaRecorder.current.stream.getTracks().forEach(track => track.stop());
            }
        };
    }, []);

    return (
        <div className="bg-white rounded-xl shadow-sm p-6 space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Voice Note</h3>
                <span className="text-sm text-gray-500">
                    {isRecording ? `${recordingTime}s / 15s` : 'Max 15 seconds'}
                </span>
            </div>

            <div className="flex items-center justify-center space-x-4">
                {!audioUrl && !isRecording && (
                    <motion.button
                        onClick={startRecording}
                        className="p-4 bg-red-100 rounded-full text-red-600 hover:bg-red-200 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <circle cx="10" cy="10" r="6" />
                        </svg>
                    </motion.button>
                )}

                {isRecording && (
                    <motion.button
                        onClick={stopRecording}
                        className="p-4 bg-gray-100 rounded-full text-gray-600 hover:bg-gray-200 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <rect x="6" y="6" width="8" height="8" />
                        </svg>
                    </motion.button>
                )}

                {audioUrl && !isRecording && (
                    <>
                        <motion.button
                            onClick={togglePlayback}
                            className="p-4 bg-indigo-100 rounded-full text-indigo-600 hover:bg-indigo-200 transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            {isPlaying ? (
                                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            ) : (
                                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                                </svg>
                            )}
                        </motion.button>

                        <motion.button
                            onClick={deleteRecording}
                            className="p-4 bg-red-100 rounded-full text-red-600 hover:bg-red-200 transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </motion.button>
                    </>
                )}
            </div>

            {isRecording && (
                <motion.div
                    className="w-full bg-gray-200 rounded-full h-2.5"
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: recordingTime / 15 }}
                    transition={{ duration: 0.2 }}
                >
                    <div
                        className="bg-red-600 h-2.5 rounded-full"
                        style={{ width: `${(recordingTime / 15) * 100}%` }}
                    />
                </motion.div>
            )}
        </div>
    );
};

export default VoiceNote; 