import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import giftAPI from '../../services/giftService';
import { StatCard, EmptyState, ErrorState, SearchBar, FilterChip } from './GiftSharedComponents';

const GiftTransactionManager = () => {
  // State management
  const [transactions, setTransactions] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all'); // all, purchase, sell, gift_sent, gift_received, redeem
  const [sortBy, setSortBy] = useState('date'); // date, amount, type
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;

  // Load transaction history and statistics with retry mechanism
  useEffect(() => {
    loadTransactionData();
    
    // Implement retry mechanism for failed requests
    if (error && retryCount < MAX_RETRIES) {
      const retryTimer = setTimeout(() => {
        console.log(`Retrying transaction data load (attempt ${retryCount + 1} of ${MAX_RETRIES})...`);
        setRetryCount(prev => prev + 1);
        loadTransactionData();
      }, 2000 * Math.pow(2, retryCount)); // Exponential backoff: 2s, 4s, 8s
      
      return () => clearTimeout(retryTimer);
    }
  }, [error, retryCount]);

  const loadTransactionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use a more resilient approach with individual try/catch blocks
      let transactionData = [];
      let statisticsData = {};
      
      try {
        const transactionsResponse = await giftAPI.getTransactionHistory(50, 0);
        transactionData = transactionsResponse.data.transactions || [];
      } catch (transactionErr) {
        console.error('Error loading transaction history:', transactionErr);
        // Continue execution to try loading statistics
      }
      
      try {
        const statisticsResponse = await giftAPI.getGiftStatistics();
        statisticsData = statisticsResponse.data || {};
      } catch (statsErr) {
        console.error('Error loading gift statistics:', statsErr);
        // Continue execution with empty statistics
      }
      
      setTransactions(transactionData);
      setStatistics(statisticsData);
      
      // Only show error if both requests failed
      if (transactionData.length === 0 && Object.keys(statisticsData).length === 0) {
        setError('Unable to load transaction data. Please try again later.');
      }
    } catch (err) {
      console.error('Unexpected error loading transaction data:', err);
      setError(err.friendlyMessage || err.message || 'Failed to load transaction history');
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort transactions
  const filteredAndSortedTransactions = useMemo(() => {
    let filtered = transactions;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(transaction =>
        transaction.gift_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.transaction_type?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(transaction => transaction.transaction_type === filterType);
    }

    // Sort transactions
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'amount':
          return (b.credits_spent || b.points_spent || 0) - (a.credits_spent || a.points_spent || 0);
        case 'type':
          return (a.transaction_type || '').localeCompare(b.transaction_type || '');
        case 'date':
        default:
          return new Date(b.created_at || 0) - new Date(a.created_at || 0);
      }
    });

    return sorted;
  }, [transactions, searchTerm, filterType, sortBy]);

  // Get transaction type display info
  const getTransactionTypeInfo = (type) => {
    const types = {
      purchase: { label: 'Purchase', color: 'bg-blue-100 text-blue-800', icon: '🛒' },
      sell: { label: 'Sell Back', color: 'bg-green-100 text-green-800', icon: '💰' },
      gift_sent: { label: 'Gift Sent', color: 'bg-purple-100 text-purple-800', icon: '🎁' },
      gift_received: { label: 'Gift Received', color: 'bg-pink-100 text-pink-800', icon: '💝' },
      redeem: { label: 'Redeemed', color: 'bg-orange-100 text-orange-800', icon: '⭐' }
    };
    return types[type] || { label: type, color: 'bg-gray-100 text-gray-800', icon: '📦' };
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
            📊 Transaction History
          </h2>
          <p className="text-gray-600 dark:text-gray-300">Loading your gift transactions...</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-xl p-4 animate-pulse">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
                <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to Load Transaction History"
        message={
          <>
            <p>{error}</p>
            {retryCount >= MAX_RETRIES && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                We've tried {MAX_RETRIES} times but couldn't load your data. 
                You can try again manually or check back later.
              </p>
            )}
          </>
        }
        onRetry={() => {
          setRetryCount(0); // Reset retry count on manual retry
          loadTransactionData();
        }}
        className="max-w-md mx-auto mt-8"
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
          📊 Transaction History
        </h2>
        <p className="text-gray-600 dark:text-gray-300">View your gift transaction history and analytics</p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <StatCard
          title="Total Transactions"
          value={statistics.total_transactions}
          color="blue"
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
        />
        <StatCard
          title="Credits Spent"
          value={statistics.total_credits_spent}
          color="green"
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        />
        <StatCard
          title="Points Used"
          value={statistics.total_points_spent}
          color="purple"
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
          }
        />
        <StatCard
          title="Gifts Sent"
          value={statistics.gifts_sent}
          color="orange"
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
        />
      </div>

      {/* Filters and Search */}
      {transactions.length > 0 && (
        <div className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-2xl p-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search Bar */}
            <SearchBar
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Search transactions..."
              className="flex-1 max-w-md"
            />

            {/* Filter Chips */}
            <div className="flex flex-wrap gap-2">
              {['all', 'purchase', 'sell', 'gift_sent', 'gift_received', 'redeem'].map((type) => (
                <FilterChip
                  key={type}
                  label={type === 'all' ? 'All' : getTransactionTypeInfo(type).label}
                  active={filterType === type}
                  onClick={() => setFilterType(type)}
                />
              ))}
            </div>

            {/* Sort Options */}
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 dark:text-gray-100"
              >
                <option value="date">Date</option>
                <option value="amount">Amount</option>
                <option value="type">Type</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Transaction List */}
      {filteredAndSortedTransactions.length === 0 ? (
        <EmptyState
          icon={
            <svg className="w-12 h-12 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
          title={searchTerm || filterType !== 'all' ? "No matching transactions found" : "No transactions yet"}
          description={searchTerm || filterType !== 'all' ? "Try adjusting your search or filter criteria" : "Start purchasing gifts to see your transaction history here!"}
          actionLabel={searchTerm || filterType !== 'all' ? "Clear Filters" : "Go to Gift Shop"}
          onAction={searchTerm || filterType !== 'all' ? () => { setSearchTerm(''); setFilterType('all'); } : () => window.location.hash = '#/gifts'}
        />
      ) : (
        <div className="space-y-4">
          <AnimatePresence>
            {filteredAndSortedTransactions.map((transaction, index) => {
              const typeInfo = getTransactionTypeInfo(transaction.transaction_type);
              
              return (
                <motion.div
                  key={transaction.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-xl p-4 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center space-x-4">
                    {/* Transaction Icon */}
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-400 to-purple-500 dark:from-indigo-700 dark:to-purple-800 rounded-full flex items-center justify-center text-white text-xl">
                      {typeInfo.icon}
                    </div>

                    {/* Transaction Details */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-800 dark:text-gray-100">
                          {transaction.gift_name || 'Unknown Gift'}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                          {typeInfo.label}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Quantity: {transaction.quantity || 1}</span>
                        <span>•</span>
                        <span>{formatDate(transaction.created_at)}</span>
                      </div>
                    </div>

                    {/* Transaction Amount */}
                    <div className="text-right">
                      {transaction.credits_spent && (
                        <div className="font-semibold text-green-600 dark:text-green-300">
                          {transaction.credits_spent > 0 ? '-' : '+'}{Math.abs(transaction.credits_spent)} credits
                        </div>
                      )}
                      {transaction.points_spent && (
                        <div className="font-semibold text-purple-600 dark:text-purple-300">
                          -{transaction.points_spent} points
                        </div>
                      )}
                      {transaction.payment_method && (
                        <div className="text-xs text-gray-500 dark:text-gray-300 capitalize">
                          via {transaction.payment_method}
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};

export default GiftTransactionManager;
