import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GiftInventoryManager from './GiftInventoryManager';
import GiftShopManager from './GiftShopManager';
import GiftTransactionManager from './GiftTransactionManager';
import { InlineLoader } from '../ui/LoadingIndicator';

const GiftModalHub = ({ isOpen, onClose, gifts, loading, error }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">Gift Inventory</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <InlineLoader size="large" color="red" />
              <p className="text-gray-600 ml-4">Loading gifts...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="p-6 bg-red-50 rounded-2xl border border-red-100 max-w-md mx-auto">
                <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-600 font-medium mb-3">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : gifts.length === 0 ? (
            <div className="text-center py-12">
              <div className="p-6 bg-gray-50 rounded-2xl border border-gray-100 max-w-md mx-auto">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
                <p className="text-gray-600 font-medium mb-3">No gifts yet</p>
                <p className="text-gray-500">Your gift inventory is empty. Gifts will appear here when you receive them.</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {gifts.map((gift) => (
                <div
                  key={gift.id}
                  className="bg-white rounded-xl border border-gray-200 p-4 hover:border-red-200 transition-all duration-300"
                >
                  <div className="aspect-w-1 aspect-h-1 mb-4">
                    <img
                      src={gift.image_url}
                      alt={gift.name}
                      className="object-cover rounded-lg"
                    />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">{gift.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{gift.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-red-600">
                      {gift.points} points
                    </span>
                    <span className="text-xs text-gray-500">
                      Received {new Date(gift.received_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GiftModalHub;
