import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import giftAPI from '../../services/giftService';
import { EmptyState, ErrorState } from './GiftSharedComponents';
import { getCdnUrl } from '../../utils/cdnUtils';

const GiftShopManager = () => {
  // State management
  const [availableGifts, setAvailableGifts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [selectedGift, setSelectedGift] = useState(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [usePoints, setUsePoints] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load available gifts
  useEffect(() => {
    loadAvailableGifts();
  }, []);

  const loadAvailableGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await giftAPI.getGiftItems();
      setAvailableGifts(response.data.gifts || []);
    } catch (err) {
      console.error('Error loading available gifts:', err);
      setError(err.message || 'Failed to load gift shop');
    } finally {
      setLoading(false);
    }
  };

  // Handle purchasing a gift
  const handlePurchaseGift = async () => {
    if (!selectedGift || purchaseQuantity <= 0) return;

    try {
      setIsSubmitting(true);

      if (usePoints) {
        await giftAPI.redeemGift(selectedGift.id, purchaseQuantity);
      } else {
        await giftAPI.purchaseGift(selectedGift.id, purchaseQuantity);
      }

      // Close modal
      setShowPurchaseModal(false);
      setSelectedGift(null);
      setPurchaseQuantity(1);
      setUsePoints(false);

      // Show success message
      alert(`Successfully ${usePoints ? 'redeemed' : 'purchased'} ${purchaseQuantity} ${selectedGift.name}!`);
    } catch (err) {
      console.error('Error purchasing gift:', err);
      alert(err.message || 'Failed to purchase gift. Please check your balance and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open purchase modal
  const openPurchaseModal = (gift) => {
    setSelectedGift(gift);
    setPurchaseQuantity(1);
    setUsePoints(false);
    setShowPurchaseModal(true);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
            🛍️ Gift Shop
          </h2>
          <p className="text-gray-600 dark:text-gray-300">Loading amazing gifts...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-xl p-4 animate-pulse">
              <div className="w-16 h-16 bg-gray-300 dark:bg-gray-700 rounded-full mx-auto mb-3"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to Load Gift Shop"
        message={error}
        onRetry={loadAvailableGifts}
        className="max-w-md mx-auto mt-8"
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
          🛍️ Gift Shop
        </h2>
        <p className="text-gray-600 dark:text-gray-300">Discover amazing gifts and surprises!</p>
      </div>

      {/* Gift Grid */}
      {availableGifts.length === 0 ? (
        <EmptyState
          icon={
            <svg className="w-12 h-12 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
            </svg>
          }
          title="Shop Coming Soon"
          description="We're preparing an amazing collection of gifts for you. Check back soon!"
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <AnimatePresence>
            {availableGifts.map((gift, index) => (
              <motion.div
                key={gift.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className="group relative"
              >
                {/* Popular Badge */}
                {gift.is_popular && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm rounded-full px-3 py-1 font-bold shadow-lg z-10">
                    🔥 Popular
                  </div>
                )}

                {/* Main Card */}
                <div className="bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-900/80 dark:to-gray-800/60 backdrop-blur-lg border border-white/30 dark:border-gray-700 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1">

                  {/* Gift Image/Icon */}
                  <div className="relative mb-4">
                    <div className="w-24 h-24 bg-gradient-to-br from-indigo-400 via-purple-500 to-pink-500 dark:from-indigo-900 dark:via-purple-900 dark:to-pink-900 rounded-2xl mx-auto flex items-center justify-center relative overflow-hidden shadow-lg">
                      {gift.image_url ? (
                        <img
                          src={getCdnUrl(gift.image_url)}
                          alt={gift.name}
                          className="w-full h-full object-cover rounded-2xl"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <span
                        className="text-white font-bold text-2xl"
                        style={{ display: gift.image_url ? 'none' : 'flex' }}
                      >
                        {gift.name?.[0] || '🎁'}
                      </span>
                    </div>
                  </div>

                  {/* Gift Info */}
                  <div className="text-center mb-4">
                    <h3 className="font-bold text-lg text-gray-800 dark:text-gray-100 mb-2 truncate">{gift.name || 'Gift Item'}</h3>

                    {/* Price Display */}
                    <div className="space-y-2 mb-3">
                      {gift.price && (
                        <div className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 text-green-700 dark:text-green-300 rounded-full text-sm font-semibold">
                          {gift.price} credits
                        </div>
                      )}
                      {gift.points_price && (
                        <div className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-900 dark:to-indigo-900 text-purple-700 dark:text-purple-300 rounded-full text-sm font-semibold">
                          {gift.points_price} points
                        </div>
                      )}
                    </div>

                    {/* Description */}
                    {gift.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">{gift.description}</p>
                    )}
                  </div>

                  {/* Purchase Button */}
                  <motion.button
                    onClick={() => openPurchaseModal(gift)}
                    className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
                      </svg>
                      <span>Purchase</span>
                    </div>
                  </motion.button>

                  {/* Decorative Elements */}
                  <div className="absolute top-4 right-4 opacity-10">
                    <svg className="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Purchase Modal */}
      <AnimatePresence>
        {showPurchaseModal && selectedGift && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowPurchaseModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-md rounded-2xl shadow-2xl max-w-md w-full p-6 border border-white/20 dark:border-gray-700"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-4">Purchase Gift</h3>

              {/* Gift Preview */}
              <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-gray-900 rounded-xl">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-400 to-purple-500 dark:from-indigo-700 dark:to-purple-800 rounded-xl flex items-center justify-center">
                  {selectedGift.image_url ? (
                    <img
                      src={getCdnUrl(selectedGift.image_url)}
                      alt={selectedGift.name}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  ) : (
                    <span className="text-white font-bold text-lg">{selectedGift.name?.[0] || '🎁'}</span>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-800 dark:text-gray-100">{selectedGift.name}</h4>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {usePoints ? `${selectedGift.points_price} points each` : `${selectedGift.price} credits each`}
                  </div>
                </div>
              </div>

              {/* Quantity Selection */}
              <div className="mb-4">
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                  Quantity
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  max={selectedGift.max_quantity || 10}
                  value={purchaseQuantity}
                  onChange={(e) => setPurchaseQuantity(parseInt(e.target.value) || 1)}
                  className="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                />
              </div>

              {/* Payment Method */}
              {selectedGift.points_price && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                    Payment Method
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={!usePoints}
                        onChange={() => setUsePoints(false)}
                        className="mr-2"
                      />
                      <span className="text-gray-700 dark:text-gray-200">Credits ({selectedGift.price} each)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={usePoints}
                        onChange={() => setUsePoints(true)}
                        className="mr-2"
                      />
                      <span className="text-purple-700 dark:text-purple-300">Points ({selectedGift.points_price} each)</span>
                    </label>
                  </div>
                </div>
              )}

              {/* Total Cost */}
              <div className="mb-6 p-3 bg-indigo-50 dark:bg-indigo-900 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700 dark:text-gray-200">Total Cost:</span>
                  <span className="font-bold text-indigo-600 dark:text-indigo-300">
                    {usePoints
                      ? (selectedGift.points_price || 0) * purchaseQuantity
                      : (selectedGift.price || 0) * purchaseQuantity
                    } {usePoints ? 'points' : 'credits'}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowPurchaseModal(false)}
                  disabled={isSubmitting}
                  className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <motion.button
                  onClick={handlePurchaseGift}
                  disabled={isSubmitting}
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 dark:from-green-700 dark:to-emerald-700 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 dark:hover:from-green-800 dark:hover:to-emerald-800 transition-all duration-300 disabled:opacity-50"
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? 'Purchasing...' : 'Purchase'}
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GiftShopManager;
