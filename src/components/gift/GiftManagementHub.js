import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GiftInventoryManager from './GiftInventoryManager';
import GiftShopManager from './GiftShopManager';
import GiftTransactionManager from './GiftTransactionManager';

const GiftManagementHub = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('inventory');

  const tabs = [
    {
      id: 'inventory',
      label: 'My Gifts',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      description: 'Manage your gift inventory',
      component: GiftInventoryManager
    },
    {
      id: 'shop',
      label: 'Gift Shop',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
        </svg>
      ),
      description: 'Browse and purchase gifts',
      component: GiftShopManager
    },
    {
      id: 'history',
      label: 'History',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: 'View transaction history and analytics',
      component: GiftTransactionManager
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || GiftInventoryManager;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 dark:bg-black/80 backdrop-blur-sm">
      <div className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-800/90 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-3xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Animated background decorations */}
        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-full blur-2xl animate-pulse" />
        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-full blur-xl animate-pulse delay-1000" />

        {/* Header with close button */}
        <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 rounded-2xl shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 dark:from-indigo-300 dark:to-purple-300 bg-clip-text text-transparent">
                  Gift Management Center
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-left text-sm">
                  Manage, shop, and track your gifts
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-3 bg-white/20 dark:bg-gray-900/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:bg-white/30 dark:hover:bg-gray-800 transition-all duration-300 shadow-sm hover:shadow-md"
            >
              <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 overflow-y-auto max-h-[calc(90vh-80px)] bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            {/* Tab Navigation */}
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden mb-8 border border-white/30 dark:border-gray-700">
              {/* Background gradient */}
              <div className="relative">
                <div className="absolute inset-0 h-full w-full bg-gradient-to-r from-indigo-600 via-indigo-600 to-blue-600 dark:from-indigo-900 dark:via-indigo-900 dark:to-blue-900" />
                {/* Tab navigation */}
                <div className="relative flex">
                  {tabs.map((tab) => (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex-1 px-6 py-4 bg-transparent text-center transition-all duration-300 relative ${
                        activeTab === tab.id
                          ? 'text-white bg-white/20 dark:bg-gray-900/40 backdrop-blur-sm'
                          : 'text-white/70 bg-transparent hover:bg-transparent hover:text-white hover:bg-white/10 dark:hover:bg-gray-800/40'
                      } cursor-pointer`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        <div className={`transition-transform duration-300 ${
                          activeTab === tab.id ? 'scale-110' : 'scale-100'
                        }`}>
                          {tab.icon}
                        </div>
                        <div>
                          <div className="font-semibold text-sm">{tab.label}</div>
                          <div className="text-xs opacity-75 hidden sm:block">{tab.description}</div>
                        </div>
                      </div>
                      {/* Active tab indicator */}
                      {activeTab === tab.id && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute bottom-0 left-0 right-0 h-1 bg-white dark:bg-indigo-400 rounded-t-full"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden border border-white/30 dark:border-gray-700">
              <div className="p-6">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ActiveComponent />
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/* Help Section */}
            <div className="mt-8 text-center">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 border border-blue-200 dark:border-blue-700 rounded-xl p-6 max-w-2xl mx-auto">
                <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">💡 How it works</h3>
                <div className="text-sm text-blue-700 dark:text-blue-200 space-y-2">
                  <p><strong>My Gifts:</strong> View and manage your gift collection. Sell gifts back for credits or gift them to other users.</p>
                  <p><strong>Gift Shop:</strong> Browse available gifts and purchase them using credits or redeem with points.</p>
                  <p><strong>History:</strong> Track all your gift transactions including purchases, sales, and gifts sent/received.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftManagementHub;
