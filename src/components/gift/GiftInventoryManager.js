import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import giftAPI from '../../services/giftService';
import { GiftCard, StatCard, EmptyState, ErrorState } from './GiftSharedComponents';
import { getCdnUrl } from '../../utils/cdnUtils';

const GiftInventoryManager = () => {
  // State management
  const [userGifts, setUserGifts] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [selectedGift, setSelectedGift] = useState(null);
  const [showSellModal, setShowSellModal] = useState(false);
  const [showGiftModal, setShowGiftModal] = useState(false);
  const [sellQuantity, setSellQuantity] = useState(1);
  const [giftQuantity, setGiftQuantity] = useState(1);
  const [recipientUserId, setRecipientUserId] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load user gifts and statistics
  useEffect(() => {
    loadUserGifts();
  }, []);

  const loadUserGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      const [giftsResponse, statsResponse] = await Promise.all([
        giftAPI.getUserGifts(),
        giftAPI.getInventoryStatistics()
      ]);

      setUserGifts(giftsResponse.data.user_gifts || []);
      setStatistics(statsResponse.data.gift_inventory_statistics || {});
    } catch (err) {
      console.error('Error loading user gifts:', err);
      setError(err.message || 'Failed to load gift inventory');
    } finally {
      setLoading(false);
    }
  };

  // Use all user gifts since search is removed
  const filteredGifts = userGifts;

  // Handle selling a gift
  const handleSellGift = async () => {
    if (!selectedGift || sellQuantity <= 0) return;

    try {
      setIsSubmitting(true);
      await giftAPI.sellGift(selectedGift.id, sellQuantity);

      // Refresh data
      await loadUserGifts();

      // Close modal
      setShowSellModal(false);
      setSelectedGift(null);
      setSellQuantity(1);

      // Show success message (you can implement toast notifications)
      alert(`Successfully sold ${sellQuantity} ${selectedGift.giftItem?.name}!`);
    } catch (err) {
      console.error('Error selling gift:', err);
      alert(err.message || 'Failed to sell gift');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle gifting to another user
  const handleGiftToUser = async () => {
    if (!selectedGift || giftQuantity <= 0 || !recipientUserId) return;

    try {
      setIsSubmitting(true);
      await giftAPI.giftToUser(selectedGift.id, recipientUserId, giftQuantity);

      // Refresh data
      await loadUserGifts();

      // Close modal
      setShowGiftModal(false);
      setSelectedGift(null);
      setGiftQuantity(1);
      setRecipientUserId('');

      // Show success message
      alert(`Successfully gifted ${giftQuantity} ${selectedGift.giftItem?.name}!`);
    } catch (err) {
      console.error('Error gifting item:', err);
      alert(err.message || 'Failed to gift item');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open sell modal
  const openSellModal = (gift) => {
    setSelectedGift(gift);
    setSellQuantity(1);
    setShowSellModal(true);
  };

  // Open gift modal
  const openGiftModal = (gift) => {
    setSelectedGift(gift);
    setGiftQuantity(1);
    setRecipientUserId('');
    setShowGiftModal(true);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
            🎁 My Gift Inventory
          </h2>
          <p className="text-gray-600 dark:text-gray-300">Loading your gifts...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-xl p-4 animate-pulse">
              <div className="w-16 h-16 bg-gray-300 dark:bg-gray-700 rounded-full mx-auto mb-3"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorState
        title="Failed to Load Gift Inventory"
        message={error}
        onRetry={loadUserGifts}
        className="max-w-md mx-auto mt-8"
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl w-full h-full font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
          🎁 My Gift Inventory
        </h2>
        <p className="text-gray-600 dark:text-gray-300">Manage your collected gifts</p>
      </div>

      {/* Enhanced Statistics Cards with Profile.js Aesthetic */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {/* Total Items Card */}
        <motion.div
          className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-6 shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          whileHover={{ y: -3, scale: 1.02 }}
        >
          <div className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full blur-xl animate-pulse" />
          <div className="relative z-10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 rounded-xl shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Items</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 dark:from-blue-300 dark:to-indigo-300 bg-clip-text text-transparent">
                  {statistics.total_items || 0}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Unique Items Card */}
        <motion.div
          className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-6 shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{ y: -3, scale: 1.02 }}
        >
          <div className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-br from-purple-400/20 to-pink-400/20 dark:from-purple-900/20 dark:to-pink-900/20 rounded-full blur-xl animate-pulse" />
          <div className="relative z-10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 dark:from-purple-700 dark:to-pink-800 rounded-xl shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Unique Items</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-purple-700 to-pink-700 dark:from-purple-300 dark:to-pink-300 bg-clip-text text-transparent">
                  {statistics.total_unique_items || 0}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Giftable Items Card */}
        <motion.div
          className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-6 shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          whileHover={{ y: -3, scale: 1.02 }}
        >
          <div className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-br from-green-400/20 to-emerald-400/20 dark:from-green-900/20 dark:to-emerald-900/20 rounded-full blur-xl animate-pulse" />
          <div className="relative z-10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 dark:from-green-700 dark:to-emerald-800 rounded-xl shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Giftable</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-green-700 to-emerald-700 dark:from-green-300 dark:to-emerald-300 bg-clip-text text-transparent">
                  {statistics.giftable_items?.total || 0}
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Received Items Card */}
        <motion.div
          className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-6 shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          whileHover={{ y: -3, scale: 1.02 }}
        >
          <div className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-br from-orange-400/20 to-red-400/20 dark:from-orange-900/20 dark:to-red-900/20 rounded-full blur-xl animate-pulse" />
          <div className="relative z-10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 dark:from-orange-700 dark:to-red-800 rounded-xl shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Received</p>
                <p className="text-2xl font-bold bg-gradient-to-r from-orange-700 to-red-700 dark:from-orange-300 dark:to-red-300 bg-clip-text text-transparent">
                  {statistics.non_giftable_items?.total || 0}
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Gift Grid */}
      {filteredGifts.length === 0 ? (
        <EmptyState
          icon={
            <svg className="w-12 h-12 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          }
          title="No gifts in inventory"
          description="Visit the Gift Shop to purchase your first gifts!"
          actionLabel="Go to Gift Shop"
          onAction={() => {
            // Switch to shop tab if we're in a modal context
            const event = new CustomEvent('switchGiftTab', { detail: { tab: 'shop' } });
            window.dispatchEvent(event);
          }}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {filteredGifts.map((userGift, index) => (
              <motion.div
                key={userGift.id}
                layout
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: -20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-900/90 dark:to-gray-800/70 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-6 shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300"
                whileHover={{ y: -5, scale: 1.02 }}
              >
                {/* Animated background decoration */}
                <div className="absolute -top-8 -right-8 w-24 h-24 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 dark:from-indigo-900/10 dark:to-purple-900/10 rounded-full blur-xl animate-pulse" />
                <div className="absolute -bottom-8 -left-8 w-20 h-20 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 dark:from-blue-900/10 dark:to-indigo-900/10 rounded-full blur-xl animate-pulse delay-1000" />

                <div className="relative z-10">
                  <GiftCard
                    gift={{
                      id: userGift.gift_item_id || userGift.giftItem?.id,
                      name: userGift.name || userGift.giftItem?.name,
                      description: userGift.description || userGift.giftItem?.description,
                      image_url: getCdnUrl(userGift.image_url || userGift.giftItem?.image_url),
                      price: userGift.sell_back_price || userGift.giftItem?.sell_back_price,
                      quantity: userGift.quantity,
                      is_giftable: !userGift.gifted_by_user_id
                    }}
                    actionLabel="Manage"
                    onAction={() => setSelectedGift(userGift)}
                  />

                  {/* Enhanced Action Buttons */}
                  <div className="mt-4 space-y-3">
                    {userGift.can_sell && (
                      <motion.button
                        onClick={() => openSellModal(userGift)}
                        className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg flex items-center justify-center space-x-2"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>Sell Back</span>
                      </motion.button>
                    )}
                    {!userGift.gifted_by_user_id && (
                      <motion.button
                        onClick={() => openGiftModal(userGift)}
                        className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white text-sm font-semibold rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 shadow-lg flex items-center justify-center space-x-2"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <span>Gift to User</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Sell Modal */}
      <AnimatePresence>
        {showSellModal && selectedGift && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowSellModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative bg-gradient-to-br from-white/95 to-white/85 dark:from-gray-900/95 dark:to-gray-800/85 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-2xl p-8 max-w-md w-full shadow-2xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Animated background decoration */}
              <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-green-400/20 to-emerald-400/20 dark:from-green-900/20 dark:to-emerald-900/20 rounded-full blur-2xl animate-pulse" />
              <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full blur-xl animate-pulse delay-1000" />

              <div className="relative z-10">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 dark:from-green-700 dark:to-emerald-800 rounded-2xl shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-green-700 to-emerald-700 dark:from-green-300 dark:to-emerald-300 bg-clip-text text-transparent">
                    Sell Gift
                  </h3>
                </div>

                <div className="text-center mb-6 p-6 bg-gradient-to-r from-green-50/80 to-emerald-50/80 dark:from-green-900/80 dark:to-emerald-900/80 backdrop-blur-sm rounded-2xl border border-green-100/50 dark:border-green-700/50">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 dark:from-green-700 dark:to-emerald-800 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-2xl">
                      {(selectedGift.name || selectedGift.giftItem?.name)?.[0] || '🎁'}
                    </span>
                  </div>
                  <h4 className="font-bold text-gray-800 dark:text-gray-100 text-lg mb-2">{selectedGift.name || selectedGift.giftItem?.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">Available: <span className="font-semibold">{selectedGift.quantity}</span></p>
                  <p className="text-lg text-green-700 dark:text-green-300 font-bold">
                    💰 {selectedGift.sell_back_price || selectedGift.giftItem?.sell_back_price || 0} credits each
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                  Quantity to sell
                </label>
                <input
                  type="number"
                  min="1"
                  max={selectedGift.quantity}
                  value={sellQuantity}
                  onChange={(e) => setSellQuantity(parseInt(e.target.value) || 1)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                />
                <p className="text-sm text-gray-500 dark:text-gray-300 mt-1">
                  Total: {(selectedGift.giftItem?.sell_back_price || 0) * sellQuantity} credits
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSellModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSellGift}
                  disabled={isSubmitting || sellQuantity <= 0 || sellQuantity > selectedGift.quantity}
                  className="flex-1 px-4 py-2 bg-green-500 dark:bg-green-700 text-white rounded-lg hover:bg-green-600 dark:hover:bg-green-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Selling...' : 'Sell'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gift Modal */}
      <AnimatePresence>
        {showGiftModal && selectedGift && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={() => setShowGiftModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-4">Gift to User</h3>
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-400 to-purple-500 dark:from-indigo-700 dark:to-purple-800 rounded-full mx-auto mb-3 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {(selectedGift.name || selectedGift.giftItem?.name)?.[0] || '🎁'}
                  </span>
                </div>
                <h4 className="font-semibold text-gray-800 dark:text-gray-100">{selectedGift.name || selectedGift.giftItem?.name}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">Available: {selectedGift.quantity}</p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                  Recipient User ID
                </label>
                <input
                  type="text"
                  value={recipientUserId}
                  onChange={(e) => setRecipientUserId(e.target.value)}
                  placeholder="Enter user ID"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                  Quantity to gift
                </label>
                <input
                  type="number"
                  min="1"
                  max={selectedGift.quantity}
                  value={giftQuantity}
                  onChange={(e) => setGiftQuantity(parseInt(e.target.value) || 1)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowGiftModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleGiftToUser}
                  disabled={isSubmitting || giftQuantity <= 0 || giftQuantity > selectedGift.quantity || !recipientUserId}
                  className="flex-1 px-4 py-2 bg-purple-500 dark:bg-purple-700 text-white rounded-lg hover:bg-purple-600 dark:hover:bg-purple-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Gifting...' : 'Gift'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GiftInventoryManager;
