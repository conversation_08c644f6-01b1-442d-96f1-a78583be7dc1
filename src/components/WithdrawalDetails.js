import React from 'react';
import { motion } from 'framer-motion';
import { formatDate, formatCurrency } from '../utils/formatters';

const WithdrawalDetails = ({ transaction, onCancel, cancellingId }) => {
  if (!transaction) return null;

  // Extract metadata
  const metadata = transaction.metadata || {};
  const fiatCurrency = metadata.fiat_currency || 'MYR';
  const fiatAmount = metadata.fiat_amount || 0;
  const processingFee = metadata.processing_fee || 0;
  const conversionRate = metadata.conversion_rate || 0;
  
  // Format the dates
  const createdDate = new Date(transaction.created_at);
  const updatedDate = transaction.updated_at ? new Date(transaction.updated_at) : null;
  
  // Helper to get status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  // Helper function to format date
  const formatDateString = (date) => {
    if (!date) return 'N/A';
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm p-5"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Withdrawal Details</h3>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Transaction ID</p>
            <p className="mt-1 text-sm text-gray-900">{transaction.id}</p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-500">Amount</p>
            <p className="mt-1 text-sm text-gray-900 font-semibold">
              {Math.abs(transaction.credits)} Credits
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-500">Processing Fee</p>
            <p className="mt-1 text-sm text-gray-900">
              {processingFee} Credits
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-500">Net Amount</p>
            <p className="mt-1 text-sm text-gray-900">
              {Math.abs(transaction.credits) - processingFee} Credits
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-500">Fiat Amount</p>
            <p className="mt-1 text-sm text-gray-900 font-semibold">
              {fiatAmount} {fiatCurrency}
            </p>
            <p className="mt-0.5 text-xs text-gray-500">
              Rate: 1 Credit = {conversionRate} {fiatCurrency}
            </p>
          </div>
        </div>
        
        <div className="space-y-4">
          {transaction.bankAccount && (
            <div>
              <p className="text-sm font-medium text-gray-500">Bank Account</p>
              <div className="mt-1 text-sm text-gray-900">
                <p className="font-medium">{transaction.bankAccount.bank_name}</p>
                <p>{transaction.bankAccount.account_number.replace(/^(.{4})(.*)(.{4})$/, '$1••••$3')}</p>
                <p>{transaction.bankAccount.account_holder_name}</p>
              </div>
            </div>
          )}
          
          <div>
            <p className="text-sm font-medium text-gray-500">Status</p>
            <p className="mt-1 text-sm text-gray-900">
              {transaction.status === 'pending' && (
                <span className="text-yellow-600">
                  Pending approval (estimated {metadata.estimated_days})
                </span>
              )}
              {transaction.status === 'completed' && (
                <span className="text-green-600">
                  Completed
                </span>
              )}
              {transaction.status === 'failed' && (
                <span className="text-red-600">
                  Failed: {metadata.failure_reason || 'Unknown error'}
                </span>
              )}
              {transaction.status === 'cancelled' && (
                <span className="text-gray-600">
                  Cancelled
                </span>
              )}
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-500">Created Date</p>
            <p className="mt-1 text-sm text-gray-900">
              {formatDateString(createdDate)}
            </p>
          </div>
          
          {updatedDate && (
            <div>
              <p className="text-sm font-medium text-gray-500">Last Updated</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatDateString(updatedDate)}
              </p>
            </div>
          )}
        </div>
      </div>
      
      {transaction.status === 'pending' && (
        <div className="mt-6 text-right">
          <button
            className={`px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors ${
              cancellingId === transaction.id ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={() => {
              if (onCancel && typeof onCancel === 'function') {
                onCancel(transaction.id);
              }
            }}
            disabled={cancellingId === transaction.id}
          >
            {cancellingId === transaction.id ? 'Cancelling...' : 'Cancel Request'}
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default WithdrawalDetails; 