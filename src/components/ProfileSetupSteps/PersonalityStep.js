import React, { useState, useEffect } from 'react';
import api from '../../services/api';

function PersonalityStep({ profileData, updateProfileData, onNext }) {
    // Animation state
    const [animationReady, setAnimationReady] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [availableTraits, setAvailableTraits] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Categories for traits
    const categories = [
        { id: 'all', label: 'All Traits' },
        { id: 'positive', label: 'Positive Traits' },
        { id: 'gaming', label: 'Gaming Style' },
        { id: 'social', label: 'Social Traits' }
    ];

    // Fetch personalities from backend
    useEffect(() => {
        setAnimationReady(true);
        setLoading(true);
        setError(null);
        api.get('/personalities')
            .then(res => {
                // Accept either res.data or res.data.data
                const traits = Array.isArray(res.data) ? res.data : res.data.data;
                setAvailableTraits(Array.isArray(traits) ? traits : []);
                setLoading(false);
            })
            .catch(err => {
                setError('Failed to load personalities');
                setAvailableTraits([]);
                setLoading(false);
            });
    }, []);

    // Toggle trait selection
    const toggleTrait = (traitId) => {
        const currentTraits = [...profileData.personalityTraits];

        if (currentTraits.includes(traitId)) {
            // Remove trait if already selected
            updateProfileData('personalityTraits', currentTraits.filter(id => id !== traitId));
        } else {
            // Add trait if not already selected (limit to 5 traits)
            if (currentTraits.length < 5) {
                updateProfileData('personalityTraits', [...currentTraits, traitId]);
            }
        }
    };

    // Check if a trait is selected
    const isTraitSelected = (traitId) => {
        return profileData.personalityTraits.includes(traitId);
    };

    // Handle next button click
    const handleNext = () => {
        // Personality traits are optional, so we can proceed without validation
        onNext();
    };

    return (
        <div className="space-y-6 dark:bg-gray-950">
            <div className={`text-center mb-8 ${animationReady ? 'animate-fade-in' : ''}`}>
                <div className="inline-block mb-3">
                    <span className="text-3xl animate-float">🌟</span>
                </div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Select Your Personality</h2>
                <p className="text-gray-600 mt-1 dark:text-gray-300">Choose up to 5 traits that best describe your gaming style</p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 space-y-6 relative overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-50 dark:bg-blue-900 rounded-full opacity-70"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-indigo-50 dark:bg-indigo-900 rounded-full opacity-70"></div>

                {/* Loading and error states */}
                {loading && (
                    <div className="text-center py-10 text-blue-500 font-medium dark:text-blue-300">Loading personalities...</div>
                )}
                {error && (
                    <div className="text-center py-10 text-red-500 font-medium dark:text-red-400">{error}</div>
                )}

                {/* Only show the rest if not loading and no error */}
                {!loading && !error && <>
                {/* Selected traits count */}
                <div className={`flex justify-between items-center ${animationReady ? 'animate-fade-in' : ''}`}>
                    <div className="flex items-center">
                        <span className="text-xl mr-2">🏷️</span>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                            Selected traits
                        </span>
                    </div>
                    <div className="flex items-center">
                        <div className="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mr-2">
                            <div
                                className={`h-full rounded-full transition-all duration-300 ${
                                    profileData.personalityTraits.length === 5
                                        ? 'bg-red-500 dark:bg-red-400'
                                        : profileData.personalityTraits.length >= 3
                                            ? 'bg-green-500 dark:bg-green-400'
                                            : 'bg-blue-500 dark:bg-blue-400'
                                }`}
                                style={{ width: `${(profileData.personalityTraits.length / 5) * 100}%` }}
                            ></div>
                        </div>
                        <span className={`text-sm font-medium ${
                            profileData.personalityTraits.length === 5 ? 'text-red-500 dark:text-red-400' : 'text-blue-600 dark:text-blue-400'
                        }`}>
                            {profileData.personalityTraits.length}/5
                        </span>
                    </div>
                </div>

                {/* Category filters */}
                <div className={`${animationReady ? 'animate-fade-in delay-100' : ''}`}>
                    <div className="flex overflow-x-auto pb-2 -mx-1 mb-2">
                        {categories.map(category => (
                            <button
                                key={category.id}
                                type="button"
                                onClick={() => setSelectedCategory(category.id)}
                                className={`px-4 py-2 mx-1 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                                    selectedCategory === category.id
                                        ? 'bg-blue-600 dark:bg-blue-500 text-white shadow-md'
                                        : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
                                }`}
                            >
                                {category.label}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Traits grid */}
                <div className={`grid grid-cols-2 sm:grid-cols-3 gap-3 ${animationReady ? 'animate-fade-in delay-200' : ''}`}>
                    {availableTraits.map((trait, index) => (
                        <button
                            key={trait.id}
                            type="button"
                            onClick={() => toggleTrait(trait.id)}
                            disabled={profileData.personalityTraits.length >= 5 && !isTraitSelected(trait.id)}
                            className={`relative rounded-xl py-3 px-4 text-left transition-all duration-200 transform hover:scale-[1.02] ${
                                isTraitSelected(trait.id)
                                    ? 'bg-blue-500 dark:bg-blue-600 text-white shadow-md'
                                    : 'bg-white dark:bg-gray-800 border-2 border-gray-100 dark:border-gray-700 text-gray-700 dark:text-gray-100 hover:border-blue-200 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900'
                            } ${
                                profileData.personalityTraits.length >= 5 && !isTraitSelected(trait.id)
                                    ? 'opacity-40 cursor-not-allowed'
                                    : 'cursor-pointer'
                            } ${animationReady ? `animate-fade-in delay-${Math.min(index * 50, 500)}` : ''}`}
                        >
                            <div className="flex items-center">
                                {/* No icon from backend, so skip or use a default */}
                                {/* <span className="text-xl mr-2">{trait.icon || ''}</span> */}
                                <span className="font-medium text-sm">{trait.name}</span>
                            </div>

                            {isTraitSelected(trait.id) && (
                                <div className="absolute top-2 right-2 bg-white dark:bg-gray-900 rounded-full p-1 shadow-md">
                                    <svg className="w-3 h-3 text-blue-500 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            )}
                        </button>
                    ))}
                </div>

                {/* Selected traits display */}
                {profileData.personalityTraits.length > 0 && (
                    <div className={`mt-6 ${animationReady ? 'animate-fade-in delay-300' : ''}`}>
                        <div className="flex items-center mb-3">
                            <span className="text-xl mr-2">✨</span>
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200">Your selected traits:</h3>
                        </div>
                        <div className="flex flex-wrap gap-2">
                            {profileData.personalityTraits.map((traitId, index) => {
                                const trait = availableTraits.find(t => t.id === traitId || t.id === Number(traitId));
                                if (!trait) return null;
                                return (
                                    <div
                                        key={traitId}
                                        className={`bg-blue-500 dark:bg-blue-600 text-white text-sm px-3 py-1.5 rounded-full flex items-center shadow-sm transition-all duration-200 transform hover:scale-105 ${animationReady ? `animate-fade-in delay-${100 + (index * 100)}` : ''}`}
                                    >
                                        {/* No icon from backend, so skip or use a default */}
                                        <span className="font-medium">{trait.name}</span>
                                        <button
                                            type="button"
                                            onClick={() => toggleTrait(traitId)}
                                            className="ml-1.5 p-0.5 rounded-full hover:bg-white/20 dark:hover:bg-gray-900/30 transition-colors"
                                            aria-label={`Remove ${trait.name}`}
                                        >
                                            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Tip */}
                <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 rounded-xl p-5 border border-blue-100 dark:border-blue-800 relative ${animationReady ? 'animate-fade-in delay-400' : ''}`}>
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-400 dark:bg-blue-700 rounded-full animate-pulse opacity-70"></div>
                    <div className="flex">
                        <div className="text-2xl mr-3 animate-float">💡</div>
                        <div>
                            <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-1">Why personality traits matter</h4>
                            <p className="text-sm text-blue-700 dark:text-blue-200">
                                Choose traits that accurately represent your gaming personality.
                                This helps other players understand what to expect when playing with you and
                                improves matchmaking for better gaming experiences.
                            </p>
                        </div>
                    </div>
                </div>
                </>}
            </div>

            {/* Next Button */}
            <div className={`pt-6 ${animationReady ? 'animate-fade-in delay-500' : ''}`}>
                <button
                    type="button"
                    onClick={handleNext}
                    className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-700 dark:hover:to-indigo-800 text-white font-medium rounded-xl shadow-lg transition-all duration-200 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                    <span className="mr-2">Continue to Next Step</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </button>
            </div>
        </div>
    );
}

export default PersonalityStep;
