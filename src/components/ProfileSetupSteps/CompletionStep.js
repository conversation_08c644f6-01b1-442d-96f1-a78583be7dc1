import React, { useState, useEffect } from 'react';

function CompletionStep({ profileData, onComplete, onEditProfile }) {
    const [animationReady, setAnimationReady] = useState(false);
    const [showConfetti, setShowConfetti] = useState(false);

    useEffect(() => {
        // Trigger animations after component mounts
        setAnimationReady(true);

        // Show confetti effect after a short delay
        const timer = setTimeout(() => {
            setShowConfetti(true);
        }, 500);

        return () => clearTimeout(timer);
    }, []);
    return (
        <div className="text-center space-y-10 dark:bg-gray-950">
            {/* Confetti effect */}
            {showConfetti && (
                <div className="fixed inset-0 pointer-events-none z-10">
                    {[...Array(20)].map((_, i) => (
                        <div
                            key={i}
                            className={`absolute w-3 h-3 rounded-full animate-confetti-fall`}
                            style={{
                                left: `${Math.random() * 100}%`,
                                top: `-20px`,
                                animationDelay: `${Math.random() * 3}s`,
                                animationDuration: `${3 + Math.random() * 2}s`,
                                backgroundColor: ['#FF5E5B', '#D8D8D8', '#FFFFEA', '#00CECB', '#FFED66'][Math.floor(Math.random() * 5)]
                            }}
                        ></div>
                    ))}
                </div>
            )}

            {/* Success animation */}
            <div className={`relative w-40 h-40 mx-auto ${animationReady ? 'animate-scale-in' : ''}`}>
                <div className="absolute inset-0 bg-green-100 dark:bg-green-900 rounded-full animate-ping opacity-25"></div>
                <div className="relative w-40 h-40 bg-gradient-to-br from-green-400 to-green-600 dark:from-green-700 dark:to-green-900 rounded-full flex items-center justify-center shadow-xl shadow-green-500/30 animate-float">
                    <svg className="w-20 h-20 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                    </svg>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-3 -right-3 w-8 h-8 bg-blue-500 dark:bg-blue-700 rounded-full animate-bounce"></div>
                <div className="absolute -bottom-3 -left-3 w-7 h-7 bg-purple-500 dark:bg-purple-700 rounded-full animate-pulse"></div>
                <div className="absolute top-1/4 -right-4 w-6 h-6 bg-yellow-500 dark:bg-yellow-600 rounded-full animate-bounce delay-150"></div>
                <div className="absolute bottom-1/4 -left-4 w-6 h-6 bg-pink-500 dark:bg-pink-700 rounded-full animate-pulse delay-300"></div>
                <div className="absolute top-1/2 right-0 w-5 h-5 bg-indigo-500 dark:bg-indigo-700 rounded-full animate-bounce delay-300"></div>
                <div className="absolute bottom-1/2 left-0 w-5 h-5 bg-red-500 dark:bg-red-700 rounded-full animate-pulse delay-150"></div>
            </div>

            <div className={`space-y-4 ${animationReady ? 'animate-fade-in delay-200' : ''}`}>
                <h1 className="text-3xl sm:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-teal-500 dark:from-green-400 dark:to-teal-400">
                    Profile Completed!
                </h1>

                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-md mx-auto">
                    You're all set! Your profile is ready—start exploring the world of Mission X now!
                </p>

                <div className="w-24 h-1.5 bg-gradient-to-r from-green-500 to-teal-500 dark:from-green-700 dark:to-teal-700 mx-auto rounded-full"></div>
            </div>

            {/* Profile summary */}
            <div className={`bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 max-w-md mx-auto relative overflow-hidden ${animationReady ? 'animate-fade-in delay-300' : ''}`}>
                {/* Decorative elements */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-green-50 dark:bg-green-900 rounded-full opacity-70"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-blue-50 dark:bg-blue-900 rounded-full opacity-70"></div>

                <div className="relative z-10">
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100">Profile Summary</h2>
                        <div className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium px-2.5 py-1 rounded-full">
                            Complete
                        </div>
                    </div>

                    <div className="space-y-6">
                        {/* Basic info */}
                        <div className="flex items-start">
                            <div className="flex-shrink-0">
                                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center shadow-sm">
                                    <span className="text-xl">👤</span>
                                </div>
                            </div>
                            <div className="ml-4 text-left">
                                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Personal Info</h3>
                                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                    <span className="font-medium">{profileData.nickname}</span>
                                    {profileData.gender && <span> • {profileData.gender.charAt(0).toUpperCase() + profileData.gender.slice(1)}</span>}
                                    {profileData.birthday && <span> • {new Date(profileData.birthday).toLocaleDateString()}</span>}
                                </p>
                            </div>
                        </div>

                        {/* Bio */}
                        {profileData.description && (
                            <div className="flex items-start">
                                <div className="flex-shrink-0">
                                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 flex items-center justify-center shadow-sm">
                                        <span className="text-xl">💬</span>
                                    </div>
                                </div>
                                <div className="ml-4 text-left">
                                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Bio</h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                                        {profileData.description.substring(0, 100)}
                                        {profileData.description.length > 100 ? '...' : ''}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Personality traits */}
                        {profileData.personalityTraits.length > 0 && (
                            <div className="flex items-start">
                                <div className="flex-shrink-0">
                                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 flex items-center justify-center shadow-sm">
                                        <span className="text-xl">🌟</span>
                                    </div>
                                </div>
                                <div className="ml-4 text-left">
                                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Personality</h3>
                                    <div className="flex flex-wrap gap-1.5 mt-2">
                                        {profileData.personalityTraits.map((trait, index) => {
                                            const traitName = typeof trait === 'string' ? trait : trait.name;
                                            return (
                                                <span
                                                    key={typeof trait === 'string' ? `${trait}-${index}` : `${trait.id}-${index}`}
                                                   className={`inline-block px-2.5 py-1 text-xs font-medium rounded-full ${
                                                       [
                                                           'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
                                                           'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
                                                           'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
                                                           'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
                                                           'bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200'
                                                       ][index % 5]
                                                   }`}
                                                >
                                                    {traitName && traitName.charAt(0).toUpperCase() + traitName.slice(1).replace('-', ' ')}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Services */}
                        {profileData.services.length > 0 && (
                            <div className="flex items-start">
                                <div className="flex-shrink-0">
                                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-100 to-yellow-200 dark:from-yellow-900 dark:to-yellow-800 flex items-center justify-center shadow-sm">
                                        <span className="text-xl">🎮</span>
                                    </div>
                                </div>
                                <div className="ml-4 text-left">
                                    <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200">Services</h3>
                                    <div className="mt-2 space-y-2">
                                        {profileData.services.map(service => (
                                           <div key={service.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-100 dark:border-gray-700">
                                               <div className="flex justify-between items-center">
                                                   <span className="text-sm font-medium text-gray-700 dark:text-gray-200">{service.name}</span>
                                                   <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1.5 py-0.5 rounded">
                                                        {service.styles.length} style{service.styles.length !== 1 ? 's' : ''}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Action buttons */}
            <div className={`pt-8 ${animationReady ? 'animate-fade-in delay-400' : ''}`}>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                        type="button"
                        onClick={onComplete}
                        className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 text-white font-medium rounded-xl shadow-lg hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-700 dark:hover:to-indigo-800 transition-all duration-200 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        <span className="text-lg">Start Exploring</span>
                        <svg className="ml-2 w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                    </button>

                    <button
                        type="button"
                        onClick={onEditProfile || onComplete}
                        className="inline-flex items-center justify-center px-8 py-4 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-300 border-2 border-blue-200 dark:border-blue-700 font-medium rounded-xl shadow-sm hover:bg-blue-50 dark:hover:bg-blue-900 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        <span>Edit Profile</span>
                    </button>
                </div>

                <p className="mt-6 text-sm text-gray-500 dark:text-gray-300">
                    You can always update your profile later from your account settings
                </p>
            </div>
        </div>
    );
}

export default CompletionStep;
