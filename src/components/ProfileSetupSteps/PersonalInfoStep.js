import React, { useState, useEffect } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import { useToast } from '../common/ToastProvider';
import profileService from '../../services/profileService';

function PersonalInfoStep({ profileData, updateProfileData, onNext }) {
    const { updateProfile } = useProfile();
    const toast = useToast();

    // Animation sequence for form fields
    const [animationReady, setAnimationReady] = useState(false);

    useEffect(() => {
        // Trigger animations after component mounts
        setAnimationReady(true);
    }, []);
    const [errors, setErrors] = useState({});

    // Handle image upload
    const handleImageUpload = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const validation = profileService.validateMediaFile(file, 'image');
        if (!validation.isValid) {
            setErrors(prev => ({ ...prev, profileImage: validation.errors.join(', ') }));
            return;
        }

        updateProfileData('profileImage', file);
        setErrors(prev => ({ ...prev, profileImage: null }));
    };

    // Handle form input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        updateProfileData(name, value);

        // Clear error when user types
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: null }));
        }
    };

    // Handle gender selection
    const handleGenderSelect = (gender) => {
        updateProfileData('gender', gender);
        setErrors(prev => ({ ...prev, gender: null }));
    };

    // Validate form before proceeding
    const validateForm = () => {
        const newErrors = {};

        if (!profileData.nickname) newErrors.nickname = 'Nickname is required';
        if (!profileData.gender) newErrors.gender = 'Please select your gender';
        if (!profileData.birthday) newErrors.birthday = 'Birthday is required';

        // Height and weight validation (optional fields but must be numbers if provided)
        if (profileData.height && isNaN(profileData.height)) {
            newErrors.height = 'Height must be a number';
        }

        if (profileData.weight && isNaN(profileData.weight)) {
            newErrors.weight = 'Weight must be a number';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle next button click
    const handleNext = async () => {
        if (!validateForm()) return;

        const genderMap = { male: 'Male', female: 'Female' };
        // Example static mapping, ideally fetch from API
        const raceNameToId = { malay: 1, indian: 2, chinese: 3, other: 4 };
        const payload = {
            nickname: profileData.nickname,
            gender: genderMap[profileData.gender] || profileData.gender,
            date_of_birth: profileData.birthday,
            height: profileData.height,
            weight: profileData.weight,
            race_id: raceNameToId[profileData.race] || profileData.race_id || '',
            profile_picture: profileData.profileImage
        };

        const result = await updateProfile(payload);
        if (result.success) {
            toast.success('Profile updated');
            onNext();
        } else {
            toast.error(result.error || 'Failed to update profile');
        }
    };

    return (
        <div className="space-y-6 animate-fade-in dark:bg-gray-950">
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Tell Us About Yourself</h2>
                <p className="text-gray-600 mt-1 dark:text-gray-300">Let's start with your basic information</p>
            </div>

            {/* Profile Picture Upload */}
            <div className="flex justify-center mb-10">
                <div className={`relative group ${animationReady ? 'animate-scale-in' : ''}`}>
                    {/* Decorative elements */}
                    <div className="absolute -top-3 -right-3 w-6 h-6 bg-blue-400 dark:bg-blue-700 rounded-full animate-pulse opacity-70"></div>
                    <div className="absolute -bottom-3 -left-3 w-6 h-6 bg-indigo-400 dark:bg-indigo-700 rounded-full animate-pulse delay-300 opacity-70"></div>

                    {/* Profile image container with gradient border */}
                    <div className="w-40 h-40 rounded-full p-1 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-800 dark:to-indigo-900 shadow-xl">
                        <div className="w-full h-full rounded-full overflow-hidden border-4 border-white dark:border-gray-800">
                            {(profileData.profileImage || profileData.profile_picture) ? (
                                <img
                                    src={
                                        profileData.profileImage instanceof File
                                            ? URL.createObjectURL(profileData.profileImage)
                                            : profileData.profileImage || profileData.profile_picture
                                    }
                                    alt="Profile"
                                    className="w-full h-full object-cover"
                                />
                            ) : (
                                <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center">
                                    <svg className="w-20 h-20 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Upload Button */}
                    <label className="absolute bottom-2 right-2 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 rounded-full p-3 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-200">
                        <input
                            type="file"
                            className="hidden"
                            onChange={handleImageUpload}
                            accept="image/*"
                        />
                        <svg className="w-6 h-6 text-white dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </label>

                    {errors.profileImage && (
                        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-red-50 dark:bg-red-900 text-red-500 dark:text-red-300 px-3 py-1 rounded-full text-xs font-medium border border-red-200 dark:border-red-700">
                            {errors.profileImage}
                        </div>
                    )}
                </div>
            </div>

            {/* Upload instruction */}
            <div className="text-center mb-6">
                <p className="text-sm text-gray-500 dark:text-gray-300">Upload a profile picture to make your profile more personal</p>
            </div>

            <form className="space-y-6">
                {/* Nickname */}
                <div className={`${animationReady ? 'animate-fade-in' : ''}`}>
                    <label htmlFor="nickname" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                        <span className="mr-1">👤</span> Nickname <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="relative">
                        <input
                            type="text"
                            id="nickname"
                            name="nickname"
                            value={profileData.nickname}
                            onChange={handleChange}
                            className={`w-full px-4 py-3 rounded-lg border ${errors.nickname ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200`}
                            placeholder="Enter your gaming nickname"
                        />
                        {profileData.nickname && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 dark:text-green-400">
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        )}
                    </div>
                    {errors.nickname ? (
                        <p className="mt-1 text-xs text-red-500 dark:text-red-300 flex items-center">
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            {errors.nickname}
                        </p>
                    ) : (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">This is how other players will know you</p>
                    )}
                </div>

                {/* Gender Selection */}
                <div className={`${animationReady ? 'animate-fade-in delay-100' : ''}`}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2 flex items-center">
                        <span className="mr-1">⚥</span> Gender <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                        <button
                            type="button"
                            onClick={() => handleGenderSelect('male')}
                            className={`py-3 px-4 rounded-xl flex items-center justify-center ${
                                profileData.gender === 'male'
                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-700 dark:to-blue-800 text-white shadow-md'
                                    : 'bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-100 hover:border-blue-300 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900'
                            } transition-all duration-200 transform hover:scale-[1.02]`}
                        >
                            <span className="text-xl mr-2">👨</span>
                            <span className="font-medium">Male</span>
                        </button>
                        <button
                            type="button"
                            onClick={() => handleGenderSelect('female')}
                            className={`py-3 px-4 rounded-xl flex items-center justify-center ${
                                profileData.gender === 'female'
                                    ? 'bg-gradient-to-r from-pink-500 to-pink-600 dark:from-pink-700 dark:to-pink-800 text-white shadow-md'
                                    : 'bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-100 hover:border-pink-300 dark:hover:border-pink-400 hover:bg-pink-50 dark:hover:bg-pink-900'
                            } transition-all duration-200 transform hover:scale-[1.02]`}
                        >
                            <span className="text-xl mr-2">👩</span>
                            <span className="font-medium">Female</span>
                        </button>
                    </div>
                    {errors.gender && (
                        <p className="mt-1 text-xs text-red-500 dark:text-red-300 flex items-center">
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            {errors.gender}
                        </p>
                    )}
                </div>

                {/* Height and Weight (side by side) */}
                <div className={`grid grid-cols-2 gap-4 ${animationReady ? 'animate-fade-in delay-200' : ''}`}>
                    <div>
                        <label htmlFor="height" className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                            <span className="mr-1">📏</span> Height (cm)
                        </label>
                        <div className="relative">
                            <input
                                type="number"
                                id="height"
                                name="height"
                                value={profileData.height}
                                onChange={handleChange}
                                className={`w-full px-4 py-3 rounded-lg border ${errors.height ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'} focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
                                placeholder="175"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-300 text-sm">
                                cm
                            </div>
                        </div>
                        {errors.height && (
                            <p className="mt-1 text-xs text-red-500 dark:text-red-300 flex items-center">
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.height}
                            </p>
                        )}
                    </div>
                    <div>
                        <label htmlFor="weight" className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                            <span className="mr-1">⚖️</span> Weight (kg)
                        </label>
                        <div className="relative">
                            <input
                                type="number"
                                id="weight"
                                name="weight"
                                value={profileData.weight}
                                onChange={handleChange}
                                className={`w-full px-4 py-3 rounded-lg border ${errors.weight ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'} focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
                                placeholder="70"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-300 text-sm">
                                kg
                            </div>
                        </div>
                        {errors.weight && (
                            <p className="mt-1 text-xs text-red-500 dark:text-red-300 flex items-center">
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.weight}
                            </p>
                        )}
                    </div>
                </div>

                {/* Birthday */}
                <div className={`${animationReady ? 'animate-fade-in delay-300' : ''}`}>
                    <label htmlFor="birthday" className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                        <span className="mr-1">🎂</span> Birthday <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="relative">
                        <input
                            type="date"
                            id="birthday"
                            name="birthday"
                            value={profileData.birthday}
                            onChange={handleChange}
                            className={`w-full px-4 py-3 rounded-lg border ${errors.birthday ? 'border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900' : 'border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'} focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
                        />
                        {/* Removed custom calendar SVG icon to avoid overlap with native icon */}
                    </div>
                    {errors.birthday ? (
                        <p className="mt-1 text-xs text-red-500 dark:text-red-300 flex items-center">
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            {errors.birthday}
                        </p>
                    ) : (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">You must be at least 13 years old</p>
                    )}
                </div>

                {/* Race */}
                <div className={`${animationReady ? 'animate-fade-in delay-400' : ''}`}>
                    <label htmlFor="race" className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                        <span className="mr-1">🌍</span> Race
                    </label>
                    <div className="relative">
                        <select
                            id="race"
                            name="race"
                            value={profileData.race}
                            onChange={handleChange}
                            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none transition-all duration-200"
                        >
                            <option value="">Select your race</option>
                            <option value="malay">Malay</option>
                            <option value="indian">Indian</option>
                            <option value="chinese">Chinese</option>
                            <option value="other">Other</option>
                        </select>
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-300 pointer-events-none">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">Optional information</p>
                </div>

                {/* Email (read-only) */}
                <div className={`${animationReady ? 'animate-fade-in delay-500' : ''}`}>
                    <label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-1 flex items-center">
                        <span className="mr-1">✉️</span> Email
                    </label>
                    <div className="relative">
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value={profileData.email}
                            readOnly
                            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 dark:text-gray-400 cursor-not-allowed"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-300">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">Your email is private and secure</p>
                </div>

                {/* Next Button */}
                <div className={`pt-6 ${animationReady ? 'animate-fade-in delay-600' : ''}`}>
                    <button
                        type="button"
                        onClick={handleNext}
                        className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-700 dark:hover:to-indigo-800 text-white font-medium rounded-xl shadow-lg transition-all duration-200 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                    >
                        <span className="mr-2">Continue to Next Step</span>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    );
}

export default PersonalInfoStep;
