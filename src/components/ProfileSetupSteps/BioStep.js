import React, { useState, useEffect } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import { useToast } from '../common/ToastProvider';

function BioStep({ profileData, updateProfileData, onNext }) {
    const { updateBiography } = useProfile();
    const toast = useToast();
    const [errors, setErrors] = useState({});
    const maxWords = 100;
    const [animationReady, setAnimationReady] = useState(false);
    const [activeExample, setActiveExample] = useState(0);

    // Example descriptions for carousel
    const exampleDescriptions = [
        {
            title: "Competitive Player",
            text: "I'm a competitive MOBA player with 5+ years of experience in League of Legends and DOTA 2. I've reached Diamond rank in LoL and 5k MMR in DOTA. I specialize in the support role and can help you improve your map awareness and positioning. I'm patient, strategic, and focused on helping you reach your gaming goals. Available weekday evenings and weekends."
        },
        {
            title: "Casual Gamer",
            text: "Hey there! I'm a casual gamer who loves to have fun and make new friends. I play a variety of games including Minecraft, Fortnite, and Animal Crossing. I'm not super competitive, but I enjoy working together as a team and having a good time. I'm friendly, easy-going, and always up for trying new games. I usually play on weekends and some weeknights."
        },
        {
            title: "FPS Specialist",
            text: "FPS games are my specialty with over 1000 hours in Valorant and CS:GO. I've competed in several local tournaments and reached Immortal rank in Valorant. My aim is precise, and I excel at tactical gameplay and team coordination. I can help you improve your shooting mechanics, game sense, and strategic thinking. I'm detail-oriented and results-driven."
        }
    ];

    // Animation and carousel effects
    useEffect(() => {
        setAnimationReady(true);

        // Auto-rotate example descriptions
        const interval = setInterval(() => {
            setActiveExample((prev) => (prev + 1) % exampleDescriptions.length);
        }, 8000);

        return () => clearInterval(interval);
    }, [exampleDescriptions.length]);

    // Count words in description
    const countWords = (text) => {
        if (!text) return 0;
        return text.trim().split(/\s+/).filter(word => word !== '').length;
    };

    const wordCount = countWords(profileData.description);

    // Handle description change
    const handleDescriptionChange = (e) => {
        const value = e.target.value;

        // Check if word count exceeds limit
        if (countWords(value) > maxWords) {
            setErrors({ description: `Description cannot exceed ${maxWords} words` });
            return;
        }

        updateProfileData('description', value);
        setErrors({});
    };

    // Handle next button click
    const handleNext = async () => {
        const result = await updateBiography({ biography: profileData.description });
        if (result.success) {
            toast.success('Biography saved');
            onNext();
        } else {
            toast.error(result.error || 'Failed to save biography');
        }
    };

    return (
        <div className="space-y-6">
            <div className={`text-center mb-8 ${animationReady ? 'animate-fade-in' : ''}`}>
                <div className="inline-block mb-3">
                    <span className="text-3xl">✏️</span>
                </div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Make Your Profile Stand Out</h2>
                <p className="text-gray-600 dark:text-gray-300 mt-1">Tell others about yourself and your gaming style</p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 space-y-6 relative overflow-hidden border border-gray-100 dark:border-gray-700">
                {/* Decorative elements */}
                <div className="absolute -top-10 -right-10 w-20 h-20 bg-blue-50 dark:bg-blue-900 rounded-full opacity-70"></div>
                <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-indigo-50 dark:bg-indigo-900 rounded-full opacity-70"></div>

                <div className={`relative z-10 ${animationReady ? 'animate-fade-in' : ''}`}>
                    <label htmlFor="description" className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        <span className="mr-2 text-xl">💬</span>
                        <span>Your Gaming Bio</span>
                    </label>
                    <div className="relative">
                        <textarea
                            id="description"
                            name="description"
                            value={profileData.description}
                            onChange={handleDescriptionChange}
                            rows={8}
                            className={`w-full px-5 py-4 rounded-xl border-2 ${
                                errors.description ? 'border-red-300 bg-red-50 dark:border-red-500 dark:bg-red-950' : 'border-gray-200 dark:border-gray-700 dark:bg-gray-800'
                            } focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 resize-none transition-all duration-200 shadow-sm text-gray-900 dark:text-gray-100`}
                            placeholder="Tell us about yourself, your gaming experience, and what makes you unique as a player..."
                        ></textarea>

                        {/* Word count indicator */}
                        <div className="absolute bottom-3 right-3">
                            <div className="flex items-center bg-white bg-opacity-90 dark:bg-gray-900 dark:bg-opacity-90 rounded-full px-2 py-1 shadow-sm border border-gray-100 dark:border-gray-700">
                                <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mr-2">
                                    <div
                                        className={`h-full rounded-full ${
                                            wordCount > maxWords * 0.9
                                                ? 'bg-red-500'
                                                : wordCount > maxWords * 0.7
                                                    ? 'bg-yellow-500'
                                                    : 'bg-green-500'
                                        }`}
                                        style={{ width: `${Math.min((wordCount / maxWords) * 100, 100)}%` }}
                                    ></div>
                                </div>
                                <p className={`text-xs font-medium ${
                                    wordCount > maxWords * 0.9
                                        ? 'text-red-500'
                                        : wordCount > maxWords * 0.7
                                            ? 'text-yellow-500'
                                            : 'text-gray-500 dark:text-gray-300'
                                }`}>
                                    {wordCount}/{maxWords}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end mt-2">
                        {errors.description && (
                            <p className="text-xs text-red-500 flex items-center">
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                {errors.description}
                            </p>
                        )}
                    </div>
                </div>

                <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 rounded-xl p-5 border border-blue-100 dark:border-blue-900 relative ${animationReady ? 'animate-fade-in delay-200' : ''}`}>
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-400 dark:bg-blue-700 rounded-full animate-pulse opacity-70"></div>
                    <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 flex items-center mb-3">
                        <span className="text-xl mr-2">💡</span>
                        Tips for a Great Gaming Bio
                    </h3>
                    <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-blue-700 dark:text-blue-200">
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Mention your gaming achievements</span>
                        </li>
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Describe your play style and strengths</span>
                        </li>
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Share what games you specialize in</span>
                        </li>
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Mention your availability</span>
                        </li>
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Keep it friendly and professional</span>
                        </li>
                        <li className="flex items-start">
                            <svg className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Highlight your communication style</span>
                        </li>
                    </ul>
                </div>

                {/* Example descriptions carousel */}
                <div className={`bg-white dark:bg-gray-900 rounded-xl p-5 border-2 border-gray-100 dark:border-gray-700 shadow-sm relative overflow-hidden ${animationReady ? 'animate-fade-in delay-300' : ''}`}>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3 flex items-center">
                        <span className="text-xl mr-2">🎮</span>
                        Example Bios
                    </h3>

                    <div className="relative">
                        {/* Carousel navigation */}
                        <div className="flex justify-between mb-2">
                            <h4 className="text-sm font-medium text-blue-600 dark:text-blue-300">
                                {exampleDescriptions[activeExample].title}
                            </h4>
                            <div className="flex space-x-1">
                                {exampleDescriptions.map((_, index) => (
                                    <button
                                        key={index}
                                        type="button"
                                        onClick={() => setActiveExample(index)}
                                        className={`w-2 h-2 rounded-full ${
                                            activeExample === index ? 'bg-blue-600 dark:bg-blue-400' : 'bg-gray-300 dark:bg-gray-700'
                                        }`}
                                        aria-label={`Example ${index + 1}`}
                                    />
                                ))}
                            </div>
                        </div>

                        {/* Carousel content */}
                        <div className="overflow-hidden relative h-24">
                            {exampleDescriptions.map((example, index) => (
                                <div
                                    key={index}
                                    className={`absolute inset-0 transition-all duration-500 ease-in-out ${
                                        activeExample === index
                                            ? 'opacity-100 translate-x-0'
                                            : activeExample < index
                                                ? 'opacity-0 translate-x-full'
                                                : 'opacity-0 -translate-x-full'
                                    }`}
                                >
                                    <p className="text-sm text-gray-600 dark:text-gray-300 italic">
                                        "{example.text}"
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Next Button */}
            <div className={`pt-6 ${animationReady ? 'animate-fade-in delay-400' : ''}`}>
                <button
                    type="button"
                    onClick={handleNext}
                    className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl shadow-lg transition-all duration-200 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 flex items-center justify-center"
                >
                    <span className="mr-2">Continue to Next Step</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </button>
            </div>
        </div>
    );
}

export default BioStep;
