import React, { useState, useEffect } from 'react';
import userServiceApi from '../../services/userServiceApi';
import { useToast } from '../common/ToastProvider';
import api from '../../services/api';

function ServiceSetupStep({ profileData, updateProfileData, onNext }) {
    const toast = useToast();
    // Sub-step state
    const [subStep, setSubStep] = useState('category'); // 'category', 'game', 'other', 'pricing'

    // Service categories from backend
    const [serviceCategories, setServiceCategories] = useState([]);
    const [categoriesLoading, setCategoriesLoading] = useState(true);
    const [categoriesError, setCategoriesError] = useState(null);

    useEffect(() => {
        setCategoriesLoading(true);
        setCategoriesError(null);
        api.get('/service-configuration/categories')
            .then(res => {
                const cats = Array.isArray(res.data) ? res.data : res.data.data;
                setServiceCategories(Array.isArray(cats) ? cats : []);
                setCategoriesLoading(false);
            })
            .catch(err => {
                setCategoriesError('Failed to load service categories');
                setServiceCategories([]);
                setCategoriesLoading(false);
            });
    }, []);

    // Current service being configured
    const [currentService, setCurrentService] = useState({
        type: '', // 'game' or 'other'
        name: '', // game name or skill name
        pricingModel: 'hourly', // 'hourly', 'fixed', etc.
        styles: [] // array of service styles with prices
    });

    // Available games with icons
    const availableGames = [
        { id: 'mobile-legends', name: 'Mobile Legends', icon: '🎮' },
        { id: 'league-of-legends', name: 'League of Legends', icon: '🏆' },
        { id: 'dota-2', name: 'DOTA 2', icon: '🛡️' },
        { id: 'valorant', name: 'Valorant', icon: '🔫' },
        { id: 'fortnite', name: 'Fortnite', icon: '🏝️' },
        { id: 'pubg', name: 'PUBG', icon: '🪂' },
        { id: 'apex-legends', name: 'Apex Legends', icon: '🏃' },
        { id: 'call-of-duty', name: 'Call of Duty', icon: '💣' },
        { id: 'genshin-impact', name: 'Genshin Impact', icon: '⚔️' },
        { id: 'minecraft', name: 'Minecraft', icon: '⛏️' }
    ];

    // Available other skills with icons (now fetched from API)
    const [availableOtherSkills, setAvailableOtherSkills] = useState([]);
    const [skillsLoading, setSkillsLoading] = useState(true);
    const [skillsError, setSkillsError] = useState(null);

    useEffect(() => {
        setSkillsLoading(true);
        setSkillsError(null);
        api.get('/service-configuration/types')
            .then(res => {
                const skills = Array.isArray(res.data) ? res.data : res.data.data;
                setAvailableOtherSkills(Array.isArray(skills) ? skills : []);
                setSkillsLoading(false);
            })
            .catch(err => {
                setSkillsError('Failed to load skills');
                setAvailableOtherSkills([]);
                setSkillsLoading(false);
            });
    }, []);

    // Available service styles for games with icons and descriptions
    const gameServiceStyles = [
        { id: 'casual', name: 'Casual', icon: '🎮', description: 'Relaxed, friendly gaming sessions' },
        { id: 'competitive', name: 'Competitive', icon: '🏆', description: 'Serious gameplay with ranking focus' },
        { id: 'professional', name: 'Professional', icon: '👨‍🏫', description: 'Extra serious gameplay with extra sauce' }
    ];

    // Available pricing models (now fetched from API)
    const [pricingModels, setPricingModels] = useState([]);
    const [pricingLoading, setPricingLoading] = useState(true);
    const [pricingError, setPricingError] = useState(null);

    useEffect(() => {
        setPricingLoading(true);
        setPricingError(null);
        api.get('/service-configuration/pricing-option-types')
            .then(res => {
                const models = Array.isArray(res.data) ? res.data : res.data.data;
                setPricingModels(Array.isArray(models) ? models : []);
                setPricingLoading(false);
            })
            .catch(err => {
                setPricingError('Failed to load pricing options');
                setPricingModels([]);
                setPricingLoading(false);
            });
    }, []);

    // State for the pricing modal
    const [showPricingModal, setShowPricingModal] = useState(false);
    const [currentStyleForPricing, setCurrentStyleForPricing] = useState(null);
    const [priceInput, setPriceInput] = useState('');
    const [errors, setErrors] = useState({});

    // Format credits for display
    const formatCredits = (amount) => {
        if (amount === null || amount === undefined) return '0';
        return amount.toLocaleString();
    };

    // Handle category selection
    const handleCategorySelect = (category) => {
        setCurrentService(prev => ({
            ...prev,
            type: category
        }));

        setSubStep(category === 'game' ? 'game' : 'other');
    };

    // Handle game selection
    const handleGameSelect = (gameId) => {
        const game = availableGames.find(g => g.id === gameId);
        setCurrentService(prev => ({
            ...prev,
            name: game.name
        }));
        setSubStep('pricing');
    };

    // Handle other skill selection
    const handleOtherSkillSelect = (skillId) => {
        const skill = availableOtherSkills.find(s => s.id === skillId);
        setCurrentService(prev => ({
            ...prev,
            name: skill.name
        }));
        setSubStep('pricing');
    };

    // Handle pricing model selection
    const handlePricingModelSelect = (modelId) => {
        setCurrentService(prev => ({
            ...prev,
            pricingModel: modelId
        }));
    };

    // Toggle service style selection
    const toggleServiceStyle = (styleId) => {
        const currentStyles = [...currentService.styles];
        const styleIndex = currentStyles.findIndex(s => s.id === styleId);

        if (styleIndex >= 0) {
            // Remove style if already selected
            setCurrentService(prev => ({
                ...prev,
                styles: prev.styles.filter(s => s.id !== styleId)
            }));
        } else {
            // Add style if not already selected
            const style = gameServiceStyles.find(s => s.id === styleId);
            setCurrentService(prev => ({
                ...prev,
                styles: [...prev.styles, { id: styleId, name: style.name, price: 0 }]
            }));
        }
    };

    // Open pricing modal for a style
    const openPricingModal = (styleId) => {
        const style = currentService.styles.find(s => s.id === styleId);
        setCurrentStyleForPricing(style);
        setPriceInput(style.price.toString());
        setShowPricingModal(true);
    };

    // Save price from modal
    const savePrice = () => {
        const price = parseFloat(priceInput);
        if (isNaN(price) || price < 0) return;

        setCurrentService(prev => ({
            ...prev,
            styles: prev.styles.map(s =>
                s.id === currentStyleForPricing.id
                    ? { ...s, price }
                    : s
            )
        }));

        setShowPricingModal(false);
    };

    // Add current service to profile
    const addService = async () => {
        // Validate service has at least one style with price
        if (currentService.styles.length === 0 ||
            !currentService.styles.some(s => s.price > 0)) {
            return; // Don't add invalid service
        }

        const newService = {
            id: Date.now().toString(), // Generate unique ID
            type: currentService.type,
            name: currentService.name,
            pricingModel: currentService.pricingModel,
            styles: currentService.styles
        };

        const result = await userServiceApi.createService(newService);
        if (result.success) {
            toast.success('Service added');
            updateProfileData('services', [...profileData.services, newService]);
        } else {
            toast.error(result.error || 'Failed to add service');
            return;
        }

        // Reset current service and go back to category selection
        setCurrentService({
            type: '',
            name: '',
            pricingModel: 'hourly',
            styles: []
        });
        setSubStep('category');
    };

    // Remove a service
    const removeService = (serviceId) => {
        updateProfileData('services', profileData.services.filter(s => s.id !== serviceId));
    };

    // Handle next button click
    const handleNext = () => {
        // Services are optional, so we can proceed without validation
        onNext();
    };

    // Render category selection
    const renderCategorySelection = () => (
        <div className="space-y-8">
            <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Choose Service Category</h2>
                <p className="text-gray-600 mt-2 dark:text-gray-300">What type of services do you want to offer?</p>
            </div>

            {categoriesLoading && (
                <div className="text-center py-10 text-blue-500 font-medium dark:text-blue-300">Loading categories...</div>
            )}
            {categoriesError && (
                <div className="text-center py-10 text-red-500 font-medium dark:text-red-400">{categoriesError}</div>
            )}

            {!categoriesLoading && !categoriesError && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    {serviceCategories.map(category => (
                        <button
                            key={category.id}
                            type="button"
                            onClick={() => handleCategorySelect(category.slug || category.id)}
                            className="bg-white dark:bg-gray-900 border-2 border-gray-200 dark:border-gray-700 rounded-2xl p-6 text-left hover:border-blue-400 hover:shadow-xl dark:hover:border-blue-500 transition-all duration-300 group transform hover:-translate-y-1"
                        >
                            <div className="flex items-center">
                                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center group-hover:from-blue-400 group-hover:to-blue-500 dark:group-hover:from-blue-700 dark:group-hover:to-blue-600 transition-colors shadow-md">
                                    <span className="text-3xl group-hover:text-white dark:group-hover:text-blue-200 transition-colors">{category.icon || '🎮'}</span>
                                </div>
                                <div className="ml-5">
                                    <h3 className="text-xl font-semibold text-gray-800 group-hover:text-blue-600 dark:text-gray-100 dark:group-hover:text-blue-400 transition-colors">{category.name}</h3>
                                    <p className="text-sm text-gray-600 mt-1 dark:text-gray-300">{category.description || 'Offer your expertise and services'}</p>
                                </div>
                            </div>
                        </button>
                    ))}
                </div>
            )}

            <div className="bg-blue-50 dark:bg-blue-900 rounded-xl p-4 border border-blue-100 dark:border-blue-800 mt-6">
                <div className="flex items-start">
                    <div className="flex-shrink-0 pt-0.5">
                        <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <p className="text-sm text-blue-700 dark:text-blue-200">
                            <span className="font-medium">Tip:</span> You can add multiple skills after setting up your first one. Choose the category that best represents your primary service.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );

    // Render game selection
    const renderGameSelection = () => (
        <div className="space-y-6">
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Select Game</h2>
                <p className="text-gray-600 mt-1 dark:text-gray-300">Choose the game you want to offer services for</p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                {availableGames.map(game => (
                    <button
                        key={game.id}
                        type="button"
                        onClick={() => handleGameSelect(game.id)}
                        className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-5 text-center hover:border-blue-400 hover:shadow-lg dark:hover:border-blue-500 transition-all duration-200 transform hover:scale-105"
                    >
                        <div className="flex flex-col items-center">
                            <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 flex items-center justify-center mb-3 shadow-sm">
                                <span className="text-3xl">{game.icon}</span>
                            </div>
                            <h3 className="text-sm font-medium text-gray-800 dark:text-gray-100">{game.name}</h3>
                        </div>
                    </button>
                ))}
            </div>

            <button
                type="button"
                onClick={() => setSubStep('category')}
                className="mt-6 flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-transparent hover:bg-transparent font-medium transition-colors"
            >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
                Back to categories
            </button>
        </div>
    );

    // Render other skills selection
    const renderOtherSkillsSelection = () => (
        <div className="space-y-6">
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Select Skill</h2>
                <p className="text-gray-600 mt-1 dark:text-gray-300">Choose the skill you want to offer</p>
            </div>

            {skillsLoading && (
                <div className="text-center py-10 text-blue-500 font-medium dark:text-blue-300">Loading skills...</div>
            )}
            {skillsError && (
                <div className="text-center py-10 text-red-500 font-medium dark:text-red-400">{skillsError}</div>
            )}

            {!skillsLoading && !skillsError && (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                    {availableOtherSkills.map(skill => (
                        <button
                            key={skill.id}
                            type="button"
                            onClick={() => handleOtherSkillSelect(skill.id)}
                            className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-5 text-center hover:border-purple-400 hover:shadow-lg dark:hover:border-purple-500 transition-all duration-200 transform hover:scale-105"
                        >
                            <div className="flex flex-col items-center">
                                <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900 dark:to-pink-900 flex items-center justify-center mb-3 shadow-sm">
                                    <span className="text-3xl">{skill.icon || '🎨'}</span>
                                </div>
                                <h3 className="text-sm font-medium text-gray-800 dark:text-gray-100">{skill.name}</h3>
                            </div>
                        </button>
                    ))}
                </div>
            )}

            <button
                type="button"
                onClick={() => setSubStep('category')}
                className="mt-6 flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
            >
                <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
                Back to categories
            </button>
        </div>
    );

    // Render pricing setup
    const renderPricingSetup = () => (
        <div className="space-y-6">
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Set Up Your Service</h2>
                <p className="text-gray-600 mt-1 dark:text-gray-300">Configure pricing for {currentService.name}</p>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm p-6 space-y-5">
                {/* Service name display */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <div className={`w-10 h-10 rounded-full ${currentService.type === 'game' ? 'bg-blue-100 dark:bg-blue-900' : 'bg-purple-100 dark:bg-purple-900'} flex items-center justify-center`}>
                            <svg className={`w-5 h-5 ${currentService.type === 'game' ? 'text-blue-600 dark:text-blue-300' : 'text-purple-600 dark:text-purple-300'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                {currentService.type === 'game' ? (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                                ) : (
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                )}
                            </svg>
                        </div>
                        <h3 className="ml-3 text-lg font-medium text-gray-800 dark:text-gray-100">{currentService.name}</h3>
                    </div>

                    <button
                        type="button"
                        onClick={() => setSubStep(currentService.type)}
                        className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Pricing model selection */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                        Pricing Model
                    </label>
                    {pricingLoading && (
                        <div className="text-blue-500 font-medium py-2 dark:text-blue-300">Loading pricing options...</div>
                    )}
                    {pricingError && (
                        <div className="text-red-500 font-medium py-2 dark:text-red-400">{pricingError}</div>
                    )}
                    {!pricingLoading && !pricingError && (
                        <select
                            value={currentService.pricingModel}
                            onChange={(e) => handlePricingModelSelect(e.target.value)}
                            className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                        >
                            {pricingModels.map(model => (
                                <option key={model.id} value={model.id}>{model.name}</option>
                            ))}
                        </select>
                    )}
                </div>

                {/* Service styles selection */}
                {currentService.type === 'game' && (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
                            Service Styles
                        </label>
                        <div className="space-y-4">
                            {gameServiceStyles.map(style => {
                                const isSelected = currentService.styles.some(s => s.id === style.id);
                                const selectedStyle = currentService.styles.find(s => s.id === style.id);

                                return (
                                    <div
                                        key={style.id}
                                        className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                                            isSelected
                                                ? 'border-blue-500 bg-blue-50 dark:border-blue-400 dark:bg-blue-900 shadow-md'
                                                : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-400'
                                        }`}
                                    >
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-2xl">{style.icon}</span>
                                                <div>
                                                    <h3 className="font-medium text-gray-900 dark:text-gray-100 text-left">{style.name}</h3>
                                                    <p className="text-sm text-gray-500 dark:text-gray-300">{style.description}</p>
                                                </div>
                                            </div>
                                            <button
                                                onClick={() => toggleServiceStyle(style.id)}
                                                className={`relative w-12 h-6 rounded-full transition-colors duration-300 ${
                                                    isSelected ? 'bg-blue-600 dark:bg-blue-400' : 'bg-gray-200 dark:bg-gray-700'
                                                }`}
                                            >
                                                <span
                                                    className={`absolute w-4 h-4 bg-white dark:bg-gray-900 rounded-full top-1 transition-all duration-200 ${
                                                        isSelected ? 'left-7' : 'left-1'
                                                    }`}
                                                />
                                            </button>
                                        </div>

                                        {isSelected && (
                                            <div className="mt-3 flex items-center space-x-2 pl-10">
                                                <div className="relative flex-1 max-w-[150px]">
                                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span className="text-gray-500 dark:text-gray-300">X</span>
                                                    </div>
                                                    <input
                                                        type="number"
                                                        value={selectedStyle.price}
                                                        readOnly
                                                        className="w-full pl-8 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 cursor-pointer text-gray-900 dark:text-gray-100"
                                                        onClick={() => openPricingModal(style.id)}
                                                    />
                                                </div>
                                                <span className="text-gray-500 dark:text-gray-300">/hour</span>
                                                <button
                                                    type="button"
                                                    onClick={() => openPricingModal(style.id)}
                                                    className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
                                                >
                                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Add service button */}
                <div className="pt-4">
                    <button
                        type="button"
                        onClick={addService}
                        disabled={currentService.styles.length === 0}
                        className={`w-full py-3 px-4 rounded-lg font-medium ${
                            currentService.styles.length === 0
                                ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                                : 'bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-md transition-colors'
                        }`}
                    >
                        Add Service
                    </button>
                </div>
            </div>

            {/* Pricing modal */}
            {showPricingModal && currentStyleForPricing && (
                <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-80 flex items-center justify-center z-50">
                    <div className="bg-white dark:bg-gray-900 rounded-xl p-6 w-96 max-w-full shadow-xl">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                                Set Price for {currentStyleForPricing.name}
                            </h3>
                            <button
                                type="button"
                                onClick={() => setShowPricingModal(false)}
                                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="mb-6">
                            <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                                Price per hour (Credits)
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-300 sm:text-sm">X</span>
                                </div>
                                <input
                                    type="number"
                                    id="price"
                                    value={priceInput}
                                    onChange={(e) => setPriceInput(e.target.value)}
                                    min="0"
                                    step="1"
                                    className="w-full pl-8 pr-12 py-3 rounded-lg border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 text-lg"
                                    placeholder="100"
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 dark:text-gray-300 sm:text-sm">Credits</span>
                                </div>
                            </div>
                            <p className="mt-2 text-sm text-gray-500 dark:text-gray-300">
                                Set your price in MissionX Credits. 1 Credit ≈ $0.10 USD
                            </p>
                        </div>

                        <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-3 border border-blue-100 dark:border-blue-800 mb-6">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-blue-700 dark:text-blue-200">
                                        Higher prices may attract more experienced players, while lower prices can help you get more bookings.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                type="button"
                                onClick={() => setShowPricingModal(false)}
                                className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                onClick={savePrice}
                                className="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors shadow-sm"
                            >
                                Save
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Added services list */}
            {profileData.services.length > 0 && (
                <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm p-6 space-y-5">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Your Services</h3>
                        <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-1 rounded-full">
                            {profileData.services.length} {profileData.services.length === 1 ? 'Service' : 'Services'}
                        </span>
                    </div>

                    <div className="space-y-4">
                        {profileData.services.map(service => {
                            // Find the icon for this service
                            const serviceIcon = service.type === 'game'
                                ? availableGames.find(g => g.name === service.name)?.icon || '🎮'
                                : availableOtherSkills.find(s => s.name === service.name)?.icon || '🎨';

                            // Find the pricing model icon
                            const pricingModelIcon = pricingModels.find(m => m.id === service.pricingModel)?.icon || '⏱️';

                            return (
                                <div
                                    key={service.id}
                                    className="bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200"
                                >
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center">
                                            <div className={`w-10 h-10 rounded-full ${
                                                service.type === 'game'
                                                    ? 'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800'
                                                    : 'bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800'
                                                } flex items-center justify-center shadow-sm`}>
                                                <span className="text-xl">{serviceIcon}</span>
                                            </div>
                                            <div className="ml-3">
                                                <h4 className="text-base font-medium text-gray-800 dark:text-gray-100">{service.name}</h4>
                                                <div className="flex items-center text-xs text-gray-500 dark:text-gray-300">
                                                    <span className="mr-1">{pricingModelIcon}</span>
                                                    <span>{pricingModels.find(m => m.id === service.pricingModel)?.name}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <button
                                            type="button"
                                            onClick={() => removeService(service.id)}
                                            className="p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:text-gray-500 dark:hover:text-red-400 dark:hover:bg-red-900 rounded-full transition-colors"
                                            aria-label="Remove service"
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>

                                    <div className="grid grid-cols-2 gap-3 mt-3">
                                        {service.styles.map(style => {
                                            // Find the icon for this style
                                            const styleIcon = gameServiceStyles.find(s => s.id === style.id)?.icon || '🎮';

                                            return (
                                                <div key={style.id} className="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
                                                    <div className="flex items-center">
                                                        <span className="text-lg mr-2">{styleIcon}</span>
                                                        <span className="text-sm text-gray-700 dark:text-gray-100">{style.name}</span>
                                                    </div>
                                                    <span className="font-medium text-sm text-gray-900 dark:text-gray-100">{style.price} <span className="text-xs text-gray-500 dark:text-gray-300">Credits/hr</span></span>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    <button
                        type="button"
                        onClick={() => setSubStep('category')}
                        className="w-full py-3 px-4 border-2 border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900 font-medium transition-colors flex items-center justify-center"
                    >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add Another Service
                    </button>
                </div>
            )}

            {/* Next Button */}
            <div className="pt-6">
                <button
                    type="button"
                    onClick={handleNext}
                    className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-800 dark:to-indigo-900 hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-700 dark:hover:to-indigo-800 text-white font-medium rounded-xl shadow-lg transition-all duration-200 transform hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                    <span className="mr-2">Continue to Next Step</span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </button>
            </div>
        </div>
    );

    // Render appropriate sub-step
    const renderSubStep = () => {
        switch (subStep) {
            case 'category':
                return renderCategorySelection();
            case 'game':
                return renderGameSelection();
            case 'other':
                return renderOtherSkillsSelection();
            case 'pricing':
                return renderPricingSetup();
            default:
                return renderCategorySelection();
        }
    };

    return (
        <div className="animate-fade-in dark:bg-gray-950">
            {renderSubStep()}
        </div>
    );
}

export default ServiceSetupStep;
