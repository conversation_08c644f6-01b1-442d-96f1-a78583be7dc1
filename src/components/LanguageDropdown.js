import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../hooks/useTranslation';

const LanguageDropdown = ({ className = '' }) => {
  const { currentLanguage, supportedLanguages, changeLanguage } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState('bottom');
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (lang) => {
    changeLanguage(lang.code);
    setIsOpen(false);
  };

  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      if (window.innerHeight - rect.bottom < 200) {
        setMenuPosition('top');
      } else {
        setMenuPosition('bottom');
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setIsOpen(false);
      }
    };
    if (isOpen) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  const dropdownVariants = {
    hidden: { opacity: 0, y: menuPosition === 'bottom' ? -10 : 10, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: 'spring', damping: 20, stiffness: 300 }
    },
    exit: {
      opacity: 0,
      y: menuPosition === 'bottom' ? -10 : 10,
      scale: 0.95,
      transition: { duration: 0.15 }
    }
  };

  const selected = supportedLanguages.find((l) => l.code === currentLanguage) || supportedLanguages[0];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="w-full p-2 rounded-lg text-indigo-600 dark:text-white backdrop-blur-sm bg-white/20 border border-indigo-200 font-medium text-left flex justify-between items-center hover:bg-white/30 transition-colors duration-300 shadow-sm"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <span className="flex items-center">
          <span className="mr-2">{selected.flag}</span>
        <span>{selected.nativeName}</span>
        </span>
        <svg
          className={`w-4 h-4 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            variants={dropdownVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={`absolute w-full rounded-lg backdrop-blur-sm bg-white/20 border border-indigo-200 text-indigo-600 overflow-hidden z-10 shadow-lg ${menuPosition === 'bottom' ? 'mt-2' : '-mt-2'}`}
          >
            <ul role="listbox" aria-activedescendant={selected.code}>
              {supportedLanguages.map((lang) => (
                <li key={lang.code}>
                  <button
                    onClick={() => handleSelect(lang)}
                    className="w-full p-2 text-left hover:bg-white/30 bg-transparent transition-colors duration-200 flex items-center relative"
                    role="option"
                    aria-selected={selected.code === lang.code}
                  >
                    <span className="flex items-center">
                      <span className="mr-2">{lang.flag}</span>
                      <span>{lang.nativeName}</span>
                    </span>
                    {selected.code === lang.code && (
                      <svg className="w-4 h-4 ml-auto text-indigo-600 dark:text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageDropdown;
