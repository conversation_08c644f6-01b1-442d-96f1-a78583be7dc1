import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import walletAPI from '../services/walletService';
import { usePayment } from '../contexts/PaymentContext';

/**
 * PaymentRetryModal component
 *
 * This modal allows users to retry a failed payment
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {string} props.transactionId - The ID of the transaction to retry
 * @param {Function} props.onSuccess - Function to call when the payment is successfully retried
 */
const PaymentRetryModal = ({ isOpen, onClose, transactionId, onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [step, setStep] = useState('initial'); // initial, processing, success, error

  // Get payment context
  const { retryPayment } = usePayment();

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsLoading(false);
      setError(null);
      setStep('initial');
    }
  }, [isOpen]);

  // Handle retry payment
  const handleRetryPayment = async () => {
    if (!transactionId) {
      setError('Invalid transaction ID');
      return;
    }

    setIsLoading(true);
    setError(null);
    setStep('processing');

    try {
      // Use the payment context to retry the payment
      await retryPayment({
        transactionId,
        onSuccess: (data) => {
          setStep('success');
          setTimeout(() => {
            if (onSuccess) onSuccess(data);
            onClose();
          }, 2000);
        },
        onError: (err) => {
          console.error('Error retrying payment:', err);
          setStep('error');

          // Extract error message from response if available
          if (err.response && err.response.data) {
            setError(err.response.data.message || 'Failed to retry payment');
          } else {
            setError(err.message || 'Failed to retry payment. Please try again later.');
          }

          setIsLoading(false);
        }
      });

      // Note: The redirect is handled by the PaymentContext
    } catch (err) {
      console.error('Error retrying payment:', err);
      setStep('error');

      // Extract error message from response if available
      if (err.response && err.response.data) {
        setError(err.response.data.message || 'Failed to retry payment');
      } else {
        setError(err.message || 'Failed to retry payment. Please try again later.');
      }

      setIsLoading(false);
    }
  };

  // Handle close modal
  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };

  // Render different content based on current step
  const renderContent = () => {
    switch (step) {
      case 'processing':
        return renderProcessingStep();
      case 'success':
        return renderSuccessStep();
      case 'error':
        return renderErrorStep();
      default:
        return renderInitialStep();
    }
  };

  // Initial step content
  const renderInitialStep = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Retry Payment</h2>
        <button
          onClick={handleClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="mb-6">
        <p className="text-gray-700 mb-4">
          Your previous payment attempt was not completed. Would you like to try again?
        </p>

        <div className="bg-blue-50 p-4 rounded-lg mb-4">
          <div className="flex items-center text-blue-800">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="font-medium">Transaction ID: {transactionId}</span>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <button
          type="button"
          className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50"
          onClick={handleClose}
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="button"
          className="flex-1 py-3 px-4 rounded-lg text-white font-medium bg-blue-600 hover:bg-blue-700"
          onClick={handleRetryPayment}
          disabled={isLoading}
        >
          Retry Payment
        </button>
      </div>
    </>
  );

  // Processing step content
  const renderProcessingStep = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 border-t-4 border-blue-600 border-solid rounded-full animate-spin mx-auto mb-4"></div>
      <h2 className="text-xl font-bold mb-2">Processing Payment</h2>
      <p className="text-gray-600">Please wait while we process your payment...</p>
    </div>
  );

  // Success step content
  const renderSuccessStep = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h2 className="text-xl font-bold mb-2">Payment Successful</h2>
      <p className="text-gray-600">Your payment has been processed successfully.</p>
    </div>
  );

  // Error step content
  const renderErrorStep = () => (
    <>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Payment Error</h2>
        <button
          onClick={handleClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="mb-6">
        <div className="bg-red-50 p-4 rounded-lg mb-4">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>

        <p className="text-gray-700">
          We encountered an error while processing your payment. Please try again or contact support if the issue persists.
        </p>
      </div>

      <div className="flex gap-3">
        <button
          type="button"
          className="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50"
          onClick={handleClose}
        >
          Cancel
        </button>
        <button
          type="button"
          className="flex-1 py-3 px-4 rounded-lg text-white font-medium bg-blue-600 hover:bg-blue-700"
          onClick={handleRetryPayment}
        >
          Try Again
        </button>
      </div>
    </>
  );

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              {renderContent()}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PaymentRetryModal;
