import React from 'react';
import { motion } from 'framer-motion';
import { formatDate, formatTransactionStatus } from '../utils/formatters';
import PaymentRetryButton from './PaymentRetryButton';

// Animation variants
const item = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 80,
      damping: 12
    }
  }
};

const TransactionItem = ({ transaction, onClick, onRetrySuccess }) => {
  // Extract metadata
  const metadata = transaction.metadata || {};
  const isWithdrawal =
    transaction.transaction_type === 'deduct' &&
    metadata.withdrawal_method;

  // Format date and time
  const formatTimeString = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Determine the right icon based on transaction type and metadata
  const getTransactionIcon = () => {
    // For withdrawal transactions
    if (isWithdrawal) {
      return (
        <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16v2a2 2 0 01-2 2H5a2 2 0 01-2-2v-7a2 2 0 012-2h2m3-4H9a2 2 0 00-2 2v7a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-1m-1 4l-3 3m0 0l-3-3m3 3V3" />
        </svg>
      );
    }

    // For other transactions
    if (transaction.transaction_type === 'add') {
      // Top-up via payment gateway
      if (metadata.payment_gateway) {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      }
      // Refund transaction
      else if (metadata.refund_for) {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
          </svg>
        );
      }
      // Other credit additions
      else {
        return (
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      }
    }
    // For deduction transactions
    else {
      const desc = transaction.description.toLowerCase();

      if (desc.includes('platform fee')) {
        return (
          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        );
      } else if (desc.includes('buy gift') || desc.includes('purchase')) {
        return (
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
          </svg>
        );
      } else {
        return (
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 12H6" />
          </svg>
        );
      }
    }
  };

  // Get transaction action phrase
  const getTransactionAction = () => {
    // Specific action for withdrawal
    if (isWithdrawal) {
      return 'Withdrawn to bank';
    }

    // For other transaction types
    const desc = transaction.description.toLowerCase();

    if (metadata.payment_gateway) return 'Added to wallet';
    if (metadata.refund_for) return 'Refund';
    if (desc.includes('top up')) return 'Added to wallet';
    if (desc.includes('platform fee')) return 'Platform fee';
    if (desc.includes('buy gift')) return 'Purchased gift';
    if (desc.includes('mission income')) return 'Earned from mission';
    if (desc.includes('expense')) return 'Payment sent';

    // Default actions based on transaction type
    return transaction.transaction_type === 'add' ? 'Credit added' : 'Credit deducted';
  };

  // Get status badge for transactions with status
  const getStatusBadge = () => {
    if (!transaction.status) return null;

    const statusColors = {
      pending: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      processing: 'bg-blue-100 text-blue-800'
    };

    const color = statusColors[transaction.status] || 'bg-gray-100 text-gray-800';

    return (
      <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${color}`}>
        {formatTransactionStatus(transaction.status)}
      </span>
    );
  };

  // Check if this is a failed payment transaction
  const isFailedPayment = () => {
    // Check if this is a payment transaction
    const isPaymentTransaction =
      metadata &&
      metadata.payment_gateway &&
      metadata.payment_transaction_id;

    // Check if the payment failed
    const isFailedStatus =
      transaction.status === 'failed' ||
      (metadata && metadata.payment_status === 'failed') ||
      (metadata && metadata.payment_error);

    return isPaymentTransaction && isFailedStatus;
  };

  return (
    <motion.div
      variants={item}
      className="bg-white border border-gray-100 rounded-xl p-4 hover:bg-gray-50 transition-colors cursor-pointer hover:shadow-sm"
      onClick={onClick}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            transaction.transaction_type === 'add'
              ? 'bg-green-100 text-green-600'
              : 'bg-red-100 text-red-600'
          }`}>
            {getTransactionIcon()}
          </div>

          <div>
            <div className="flex items-center space-x-2">
              <p className="font-semibold text-gray-900">{transaction.description}</p>
              {getStatusBadge()}
              {metadata.mission_id && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                  Mission
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3 mt-1">
              <p className="text-sm text-gray-500">
                {getTransactionAction()}
              </p>
              <span className="text-gray-400">&bull;</span>
              <p className="text-sm text-gray-500">
                {formatTimeString(transaction.created_at)}
              </p>

              {/* Show fiat amount for withdrawals */}
              {isWithdrawal && metadata.fiat_amount && metadata.fiat_currency && (
                <>
                  <span className="text-gray-400">&bull;</span>
                  <p className="text-sm text-gray-500">
                    {metadata.fiat_amount} {metadata.fiat_currency}
                  </p>
                </>
              )}

              {/* Show bank name for withdrawals */}
              {isWithdrawal && (transaction.bankAccount?.bank_name || metadata.bank_name) && (
                <>
                  <span className="text-gray-400">&bull;</span>
                  <p className="text-sm text-gray-500">
                    {transaction.bankAccount?.bank_name || metadata.bank_name}
                  </p>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="text-right">
          <p className={`font-semibold ${
            transaction.transaction_type === 'add'
              ? 'text-green-600'
              : 'text-red-600'
          }`}>
            {transaction.transaction_type === 'add' ? '+' : ''}{transaction.credits} credits
          </p>
          <p className="text-sm text-gray-500 mt-1">Balance: {transaction.balance_after} credits</p>
        </div>
      </div>

      {/* Payment retry button for failed payments */}
      {isFailedPayment() && (
        <div className="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
          <div className="text-sm text-red-600">
            <svg className="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            Payment failed
          </div>

          <PaymentRetryButton
            transactionId={metadata.payment_transaction_id}
            buttonText="Retry Payment"
            buttonClassName="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onSuccess={onRetrySuccess}
          />
        </div>
      )}

      {/* Action indicator */}
      <div className="flex items-center justify-end mt-2">
        <svg
          className="w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </motion.div>
  );
};

export default TransactionItem;