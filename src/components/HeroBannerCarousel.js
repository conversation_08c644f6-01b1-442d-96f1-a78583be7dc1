import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useHomepage } from '../contexts/HomepageContext';
import { getCdnUrl } from '../utils/cdnUtils';

const HeroBannerCarousel = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const { carouselSlides, loading, error } = useHomepage();

  // Auto-advance slides
  useEffect(() => {
    if (carouselSlides.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [carouselSlides.length]);

  // Handle slide click
  const handleSlideClick = (slide) => {
    if (slide.isClickable && slide.buttonUrl) {
      const url = slide.buttonUrl;
      if (/^https?:\/\//i.test(url)) {
        window.open(url, '_blank');
      } else {
        navigate(url);
      }
    }
  };

  if (loading) {
    return (
      <div className="w-full h-[400px] bg-gray-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !carouselSlides.length) {
    return (
      <div className="w-full h-[400px] bg-gray-100 flex items-center justify-center">
        <p className="text-gray-500">Failed to load carousel slides</p>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[400px] overflow-hidden">
      {/* Slides */}
      <div className="relative w-full h-full">
        {carouselSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute w-full h-full transition-opacity duration-500 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
            onClick={() => handleSlideClick(slide)}
            style={{ cursor: slide.isClickable ? 'pointer' : 'default' }}
          >
            {/* Background Image */}
            {slide.mediaFiles && slide.mediaFiles.length > 0 && (
              <div className="absolute inset-0">
                <img
                  src={getCdnUrl(slide.mediaFiles[0].url)}
                  alt={slide.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              </div>
            )}

            {/* Content */}
            <div className="relative h-full flex items-center justify-center text-center text-white p-8">
              <div className="max-w-2xl">
                <h2 className="text-4xl font-bold mb-4">{slide.title}</h2>
                <p className="text-xl mb-8">{slide.content}</p>
                {slide.isClickable && slide.buttonText && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(slide.buttonUrl);
                    }}
                    className="bg-primary hover:bg-primary-dark text-white font-bold py-3 px-8 rounded-full transition-colors duration-200"
                  >
                    {slide.buttonText}
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Dots */}
      {carouselSlides.length > 1 && (
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
          {carouselSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentSlide ? 'bg-white' : 'bg-white bg-opacity-50'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default HeroBannerCarousel; 