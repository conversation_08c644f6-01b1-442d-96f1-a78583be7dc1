/**
 * Edit Profile Modal Component
 *
 * A comprehensive modal for editing user profile information with:
 * - Basic profile fields (nickname, gender, height, weight, race, DOB, email)
 * - Biography editing
 * - Profile picture upload with preview
 * - Voice note upload/delete functionality
 * - Third-party access settings
 * - Real-time validation and error handling
 * - Glassmorphism styling consistent with other modals
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import profileService from '../../services/profileService';
import userServiceApi from '../../services/userServiceApi';
import { profileAPI } from '../../services/api';
import { profileKeys } from '../../queryKeys/profileKeys';
import { InlineLoader } from '../ui/LoadingIndicator';
import useTranslation from '../../hooks/useTranslation';
import { useToast } from '../common/ToastProvider';

// Remove BasicInfoForm import since we're moving fields directly into sections
import MediaSettings, {
    validateMediaFiles,
    processValidFile,
} from "./editProfile/MediaSettings";
import ServiceManagement, {
    validateServiceForm,
    resetServiceForm,
    serviceKeys,
} from "./editProfile/ServiceManagement";
// Add new section components: ProfileDetailsSection, PersonalitiesSection
import FloatingLabelInput from "./editProfile/FloatingLabelInput";
import { toNumberIfPossible } from "../../utils/formatters";

// Helper to check if any error values are present
const hasActiveErrors = (errObj) =>
    Object.values(errObj || {}).some((val) => Boolean(val));

const EditProfileModal = ({
    isOpen = false,
    onClose,
    onProfileUpdated,
    profileQueryKey = profileKeys.detail()
}) => {
    const { t } = useTranslation(['profile', 'common']);
    const { success: showSuccessToast, error: showErrorToast } = useToast();
    // Update the sections array to include: "Profile Details", "Personalities", "Services"
    const sections = useMemo(() => [
        { id: 'profile', name: 'Profile Details', icon: '👤' },
        { id: 'personalities', name: 'Personalities', icon: '🎭' },
        { id: 'services', name: 'Services', icon: '💼' }
    ], []);
    // Add missing state variables
    const [profilePicturePreview, setProfilePicturePreview] = useState(null);
    const [profileUploadProgress, setProfileUploadProgress] = useState(0);
    const [currentVoiceNote, setCurrentVoiceNote] = useState(null);
    const [voiceNoteFile, setVoiceNoteFile] = useState(null);
    const [isProfileLoading, setIsProfileLoading] = useState(false);

    // Add active section state
    const [activeSection, setActiveSection] = useState('profile');

    const queryClient = useQueryClient();

    // Add touched state for form fields
    const [touched, setTouched] = useState({});

    // Existing state management
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [errors, setErrors] = useState({});
    const [successMessage, setSuccessMessage] = useState('');

    // Form data state
    const [formData, setFormData] = useState({
        nickname: '',
        gender: '',
        height: '',
        weight: '',
        race_id: '',
        date_of_birth: '',
        email: '',
        biography: '',
        profile_picture: null,
        voice_note: null,
        allow_3rd_party_access: true,
        personality_ids: []
    });

    // Cover photo states
    const [coverMediaFile, setCoverMediaFile] = useState(null);
    const [coverMediaPreview, setCoverMediaPreview] = useState(null);
    const [currentCoverMedia, setCurrentCoverMedia] = useState(null);
    const [coverMediaType, setCoverMediaType] = useState(null); // 'image' or 'video'
    const [isDragging, setIsDragging] = useState(false);
    const [draggedItem, setDraggedItem] = useState(null);
    const [mediaOrder, setMediaOrder] = useState([]);

    // Store initial data to track changes and prevent clearing unchanged fields
    const [initialData, setInitialData] = useState({});

    // Service configuration and user services
    const [showServiceForm, setShowServiceForm] = useState(false);
    const [serviceForm, setServiceForm] = useState({
        service_category_id: '', // numeric string or ID returned by API
        service_category_slug: '', // slug of selected service category
        service_type_id: '', // may be numeric or string depending on backend
        service_type_title: '',
        service_type_description: '',
        pricing_option_id: '', // selected pricing option
        price: '', // string: will be converted to number when sending to API
        service_style: [] // array: will be populated with selected styles and their prices
    });
    const [serviceStylePrices, setServiceStylePrices] = useState({}); // object: { styleId: price }
    const [serviceMessage, setServiceMessage] = useState('');

    // Add state for confirmation dialog
    const [showDeleteCoverConfirm, setShowDeleteCoverConfirm] = useState(false);
    const [pendingDeleteOrder, setPendingDeleteOrder] = useState(null);

    // Add state for personality save
    const [personalitySaving, setPersonalitySaving] = useState(false);
    const [personalitySuccess, setPersonalitySuccess] = useState('');
    const [personalityError, setPersonalityError] = useState('');
    const [lastSavedPersonalities, setLastSavedPersonalities] = useState([]);

    // Add state for races
    const [races, setRaces] = useState([]);
    const [racesLoading, setRacesLoading] = useState(false);
    const [racesError, setRacesError] = useState('');

    // Add state for personalities
    const [availablePersonalities, setAvailablePersonalities] = useState([]);
    const [personalitiesLoading, setPersonalitiesLoading] = useState(false);
    const [personalitiesError, setPersonalitiesError] = useState('');

    // Animation variants
    const isMobile = window.innerWidth < 768;
    const modalVariants = isMobile
        ? {
            hidden: { opacity: 0, y: '100%' },
            visible: { opacity: 1, y: 0 },
            exit: { opacity: 0, y: '100%' }
        }
        : {
            hidden: { opacity: 0, scale: 0.95, y: 20 },
            visible: { opacity: 1, scale: 1, y: 0 },
            exit: { opacity: 0, scale: 0.95, y: 20 }
        };

    // Clean up the reset form state when modal closes
    useEffect(() => {
        if (isOpen) {
            // Reset all state when modal opens
            setErrors({});
            setSuccessMessage('');
            setActiveSection('profile'); // Changed from 'basic' to 'profile'
        } else {
            // Reset form data when modal closes
            setFormData({
                nickname: '',
                gender: '',
                height: '',
                weight: '',
                race_id: '',
                date_of_birth: '',
                email: '',
                biography: '',
                profile_picture: null,
                voice_note: null,
                allow_3rd_party_access: false,
                personality_ids: []
            });
            setInitialData({});
            setProfilePicturePreview(null);
            setProfileUploadProgress(0);
            setCurrentVoiceNote(null);
            setCurrentCoverMedia(null);
            setCoverMediaFile(null);
            setCoverMediaPreview(null);
            setCoverMediaType(null);
            setVoiceNoteFile(null);
            // Also reset service form states when modal closes
            setServiceForm({
                service_category_id: '',
                service_category_slug: '',
                service_type_id: '',
                pricing_option_id: '',
                service_style: [],
                price: '',
                service_type_title: '',
                service_type_description: ''
            });
            setServiceStylePrices({});
            setShowServiceForm(false);
            // Reset personality states
            setPersonalitySaving(false);
            setPersonalitySuccess('');
            setPersonalityError('');
            setLastSavedPersonalities([]);
            // Reset races states
            setRaces([]);
            setRacesLoading(false);
            setRacesError('');
            // Reset personalities states
            setAvailablePersonalities([]);
            setPersonalitiesLoading(false);
            setPersonalitiesError('');
        }
    }, [isOpen]);

    // Add functionality to pre-populate form data from backend
    useEffect(() => {
        if (isOpen) {
            // Fetch and populate profile data when modal opens
            const fetchProfileData = async () => {
                try {
                    setLoading(true);
                    const result = await profileService.getCompleteProfile();

                    if (result.success && result.data) {
                        const userData = result.data;

                        // Pre-populate form data with backend response
                        setFormData(prev => ({
                            uid: userData.uid || '',
                            nickname: userData.nickname || '',
                            email: userData.email || '',
                            height: userData.height || '',
                            weight: userData.weight || '',
                            date_of_birth: userData.date_of_birth
                                ? new Date(userData.date_of_birth).toISOString().split('T')[0]
                                : '',
                            gender: userData.gender || '',
                            race_id: userData.race_id
                                ? userData.race_id.toString()
                                : (userData.race && userData.race.id
                                    ? userData.race.id.toString()
                                    : ''),
                            biography: userData.biography || '',
                            profile_picture: prev.profile_picture || null, // Don't overwrite if already set
                            voice_note: prev.voice_note || null, // Don't overwrite if already set
                            allow_3rd_party_access: userData.allow_3rd_party_access ?? true,
                            personality_ids: (userData.personalities || []).map(p => p.id)
                        }));

                        // Set initial data for comparison
                        setInitialData({
                            uid: userData.uid || '',
                            nickname: userData.nickname || '',
                            email: userData.email || '',
                            height: userData.height || '',
                            weight: userData.weight || '',
                            date_of_birth: userData.date_of_birth
                                ? new Date(userData.date_of_birth).toISOString().split('T')[0]
                                : '',
                            gender: userData.gender || '',
                            race_id: userData.race_id ? userData.race_id.toString() : '',
                            biography: userData.biography || '',
                            personality_ids: (userData.personalities || []).map(p => p.id)
                        });

                        // Set last saved personalities for comparison
                        setLastSavedPersonalities((userData.personalities || []).map(p => p.id));

                        // Handle profile picture preview if exists
                        if (userData.profile_picture) {
                            const cdnUrl = process.env.REACT_APP_CDN_URL || 'http://localhost:8001';
                            const profilePictureUrl = userData.profile_picture.startsWith('http')
                                ? userData.profile_picture
                                : `${cdnUrl}/${userData.profile_picture}`;
                            setProfilePicturePreview(profilePictureUrl);
                        }

                        // Handle voice note if exists
                        if (userData.voice_note) {
                            const cdnUrl = process.env.REACT_APP_CDN_URL || 'http://localhost:8001';
                            const voiceNoteUrl = userData.voice_note.startsWith('http')
                                ? userData.voice_note
                                : `${cdnUrl}/${userData.voice_note}`;
                            setCurrentVoiceNote(voiceNoteUrl);
                        }

                        // Handle cover media if exists
                        if (userData.profile_media) {
                            const cdnUrl = process.env.REACT_APP_CDN_URL || 'http://localhost:8001';
                            let coverMedia = [];

                            // Handle photos array from profile_media
                            if (userData.profile_media.photos && Array.isArray(userData.profile_media.photos)) {
                                coverMedia = userData.profile_media.photos.map((photo, index) => ({
                                    id: photo.id || index,
                                    url: photo.path.startsWith('http')
                                        ? photo.path
                                        : `${cdnUrl}/${photo.path}`,
                                    type: 'image',
                                    order: photo.order || index + 1
                                }));
                            }

                            // Handle video if present
                            if (userData.profile_media.video) {
                                const videoItem = {
                                    id: 'video',
                                    url: userData.profile_media.video.startsWith('http')
                                        ? userData.profile_media.video
                                        : `${cdnUrl}/${userData.profile_media.video}`,
                                    type: 'video',
                                    order: coverMedia.length + 1
                                };
                                coverMedia.push(videoItem);
                            }

                            setMediaOrder(coverMedia);
                        }
                    } else {
                        console.error('Failed to fetch profile data:', result.error);
                    }
                } catch (error) {
                    console.error('Error fetching profile data:', error);
                } finally {
                    setLoading(false);
                }
            };

            fetchProfileData();
        }
    }, [isOpen]);

    // Add functionality to fetch races when modal opens
    useEffect(() => {
        if (isOpen) {
            const fetchRaces = async () => {
                try {
                    setRacesLoading(true);
                    const result = await profileService.getRaces();

                    if (result.success && result.data) {
                        setRaces(result.data);
                    } else {
                        console.error('Failed to fetch races:', result.error);
                        setRacesError('Failed to load races. Please try again later.');
                    }
                } catch (error) {
                    console.error('Error fetching races:', error);
                    setRacesError('Failed to load races. Please try again later.');
                } finally {
                    setRacesLoading(false);
                }
            };

            fetchRaces();
        }
    }, [isOpen]);

    // Add functionality to fetch personalities when modal opens
    useEffect(() => {
        if (isOpen) {
            const fetchPersonalities = async () => {
                try {
                    setPersonalitiesLoading(true);
                    const result = await profileAPI.getAvailablePersonalities();

                    if (result && Array.isArray(result)) {
                        setAvailablePersonalities(result);
                    } else if (result && result.data && Array.isArray(result.data)) {
                        setAvailablePersonalities(result.data);
                    } else {
                        console.error('Failed to fetch personalities:', result);
                        setPersonalitiesError('Failed to load personalities. Please try again later.');
                    }
                } catch (error) {
                    console.error('Error fetching personalities:', error);
                    setPersonalitiesError('Failed to load personalities. Please try again later.');
                } finally {
                    setPersonalitiesLoading(false);
                }
            };

            fetchPersonalities();
        }
    }, [isOpen]);

    // Ensure race_id matches a valid race after both races and formData.race_id are loaded
    useEffect(() => {
        if (
            races.length > 0 &&
            formData.race_id &&
            !races.some(race => String(race.id) === String(formData.race_id))
        ) {
            // If the current race_id is not in the loaded races, reset it
            setFormData(prev => ({
                ...prev,
                race_id: ''
            }));
        }
    }, [races, formData.race_id]);

    // Handle form field changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear field-specific errors
        if (errors[field]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }
    };

    // Handle profile picture upload
    const handleProfilePictureChange = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        console.log('Profile picture selected:', file);
        console.log('File type:', file.type);
        console.log('File size:', file.size);
        console.log('Is file instanceof File?', file instanceof File);

        // Validate the file
        const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/heic', 'image/heif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!validTypes.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                profile_picture: 'Please select a valid image file (JPEG, PNG, JPG, HEIC, HEIF, WebP)'
            }));
            return;
        }

        if (file.size > maxSize) {
            setErrors(prev => ({
                ...prev,
                profile_picture: 'Image size must be less than 5MB'
            }));
            return;
        }

        console.log('✅ Profile picture validation passed');

        setFormData(prev => {
            const newFormData = {
                ...prev,
                profile_picture: file
            };
            console.log('Updated formData with profile picture:', newFormData);
            return newFormData;
        });

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            setProfilePicturePreview(e.target.result);
            console.log('Profile picture preview created');
        };
        reader.readAsDataURL(file);

        // Clear errors
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.profile_picture;
            return newErrors;
        });
    };

    // Handle voice note upload
    const handleVoiceNoteChange = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        setVoiceNoteFile(file);
        setFormData(prev => ({
            ...prev,
            voice_note: file
        }));
        const previewUrl = URL.createObjectURL(file);
        setCurrentVoiceNote(previewUrl);

        // Clear errors
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.voice_note;
            return newErrors;
        });
    };

    // Handle cover media file selection
    const handleCoverMediaChange = async (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const errors = validateMediaFiles(file, 'cover');
        if (Object.keys(errors).length > 0) {
            setErrors(prev => ({ ...prev, ...errors }));
            return;
        }

        const isVideo = file.type.startsWith('video/');
        processValidFile(
            file,
            isVideo,
            setErrors,
            setCoverMediaFile,
            setCoverMediaType,
            setCoverMediaPreview
        );

        await handleCoverMediaUpload(file, isVideo);
    };

    // Add: handle cover media upload
    const handleCoverMediaUpload = async (fileParam = null, isVideo = false) => {
        const fileToUpload = fileParam || coverMediaFile;
        if (!fileToUpload) return;
        setSaving(true);
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.cover_media;
            return newErrors;
        });
        try {
            const type = isVideo || coverMediaType === 'video' ? 'video' : 'image';
            const response = await profileService.uploadMedia(fileToUpload, type);
            if (response.success) {
                queryClient.invalidateQueries(profileQueryKey);
                setSuccessMessage(response.message || 'Cover media uploaded successfully');
                setCoverMediaFile(null);
                setCoverMediaPreview(null);
                setCoverMediaType(null);
                // Reload the page after successful upload
                window.location.reload();
            } else {
                setErrors(prev => ({ ...prev, cover_media: response.error || 'Failed to upload cover media' }));
            }
        } catch (error) {
            setErrors(prev => ({ ...prev, cover_media: error.message || 'Failed to upload cover media' }));
        } finally {
            setSaving(false);
        }
    };

    // Handle cover media deletion
    const handleDeleteCoverMedia = async (mediaOrder) => {
        try {
            const result = await profileService.deleteMedia(mediaOrder);

            if (result.success) {
                setCurrentCoverMedia(null);
                setSuccessMessage('Cover media deleted successfully');
                setTimeout(() => setSuccessMessage(''), 3000);
                // Reload the page after successful delete
                window.location.reload();
            } else {
                throw new Error(result.error || 'Failed to delete cover media');
            }
        } catch (error) {
            console.error('Error deleting cover media:', error);
            setErrors(prev => ({
                ...prev,
                cover_media: 'Failed to delete cover media. Please try again.'
            }));
        }
    };

    // Handle voice note deletion
    const handleDeleteVoiceNote = async () => {
        try {
            const result = await profileService.deleteVoiceNote();

            if (result.success) {
                // Instantly clear UI state
                setCurrentVoiceNote(null);
                setVoiceNoteFile(null);
                setFormData(prev => ({
                    ...prev,
                    voice_note: null
                }));
                setSuccessMessage('Voice note deleted successfully');
                setTimeout(() => setSuccessMessage(''), 3000);
            } else {
                throw new Error(result.error || 'Failed to delete voice note');
            }
        } catch (error) {
            console.error('Error deleting voice note:', error);
            setErrors(prev => ({
                ...prev,
                voice_note: 'Failed to delete voice note. Please try again.'
            }));
        }
    };

    // Handle personality selection
    const handlePersonalityToggle = (personalityId) => {
        setFormData(prev => {
            const currentPersonalities = prev.personality_ids || [];
            const isSelected = currentPersonalities.includes(personalityId);

            if (isSelected) {
                return {
                    ...prev,
                    personality_ids: currentPersonalities.filter(id => id !== personalityId)
                };
            } else {
                return {
                    ...prev,
                    personality_ids: [...currentPersonalities, personalityId]
                };
            }
        });

        // Clear any existing personality errors
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.personality_ids;
            return newErrors;
        });
    };

    // Service form handlers
    const handleServiceFormToggle = () => {
        setShowServiceForm(prev => !prev);
        setSuccessMessage('');
        if (!showServiceForm) {
            // Reset form when opening
            resetServiceForm(
                setServiceForm,
                setServiceStylePrices,
                setErrors,
                setServiceMessage
            );
        }
    };

    const handleServiceFieldChange = (field, value) => {
        setServiceForm(prev => ({
            ...prev,
            [field]: value
        }));
        setTouched(prev => ({ ...prev, [field]: true }));

        // Clear error for the changed field
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors[field];
            return newErrors;
        });
    };

    const handleServiceStylePriceChange = (styleId, value) => {
        // Clear any existing errors for service style
        setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.service_style;
            return newErrors;
        });

        // Update the style prices
        setServiceStylePrices(prev => ({
            ...prev,
            [styleId]: value
        }));

        // Update the service_style array in the form
        setServiceForm(prev => {
            const updatedStyles = [...prev.service_style];
            const styleIndex = updatedStyles.findIndex(style => style.service_style_id === styleId);

            if (styleIndex === -1 && value > 0) {
                // Add new style if it doesn't exist and has a price
                updatedStyles.push({
                    service_style_id: styleId,
                    price: value
                });
            } else if (styleIndex !== -1) {
                if (value > 0) {
                    // Update existing style price
                    updatedStyles[styleIndex].price = value;
                } else {
                    // Remove style if price is 0 or negative
                    updatedStyles.splice(styleIndex, 1);
                }
            }

            return {
                ...prev,
                service_style: updatedStyles
            };
        });
    };

    const submitNewService = async (elements = {}) => {
        const validationErrors = validateServiceForm(serviceForm, serviceStylePrices, elements);
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        const isOthersCategory =
            (serviceForm.service_category_slug || '')
                .toLowerCase() === 'others' ||
            String(serviceForm.service_category_id) === '2';

        const payload = {
            service_category_id: toNumberIfPossible(serviceForm.service_category_id),
            service_type_id: isOthersCategory ? null : toNumberIfPossible(serviceForm.service_type_id),
            pricing_option_id: serviceForm.pricing_option_id
                ? toNumberIfPossible(serviceForm.pricing_option_id)
                : null,
            price:
                serviceForm.price !== ''
                    ? parseFloat(serviceForm.price)
                    : null,
            service_elements: elements,
            service_style: serviceForm.service_style.map((s) => ({
                service_style_id: s.service_style_id,
                is_active: true,
                price: parseFloat(
                    serviceStylePrices[s.service_style_id] || s.price || 0
                )
            })),
        };

        if (isOthersCategory) {
            payload.service_type_title = serviceForm.service_type_title;
            payload.service_type_description = serviceForm.service_type_description;
        }

        try {
            const result = await userServiceApi.createService(payload);
            if (result.success) {
                const successMsg = result.data?.message || 'Service created successfully';
                setServiceMessage(successMsg);
                showSuccessToast(successMsg, 3000);
                resetServiceForm(
                    setServiceForm,
                    setServiceStylePrices,
                    setErrors,
                    setServiceMessage
                );
                setShowServiceForm(false);
            } else {
                const errorMsg = result.error || 'Failed to create service';
                setErrors({ general: errorMsg });
                showErrorToast(errorMsg, 4000);
            }
        } catch (error) {
            console.error('Error creating service:', error);
            const errorMsg = error.response?.data?.message || 'Failed to create service';
            setErrors({ general: errorMsg });
            showErrorToast(errorMsg, 4000);
        }
    };

    const handleEditService = async (service) => {
        try {
            const result = await userServiceApi.updateService(service);
            if (result.success) {
                setServiceMessage(result.data?.message || 'Service updated');
                queryClient.invalidateQueries(serviceKeys.user());
            } else {
                setErrors({ general: result.error || 'Failed to update service' });
            }
        } catch (error) {
            console.error('Error updating service:', error);
            setErrors({ general: 'Failed to update service' });
        }
    };

    const handleDeleteService = async (serviceId) => {
        try {
            const result = await userServiceApi.deleteService(serviceId);
            if (result.success) {
                setServiceMessage(result.data?.message || 'Service deleted');
                queryClient.invalidateQueries(serviceKeys.user());
            } else {
                setErrors({ general: result.error || 'Failed to delete service' });
            }
        } catch (error) {
            console.error('Error deleting service:', error);
            setErrors({ general: 'Failed to delete service' });
        }
    };

    // Remove the form's onSubmit, handleSubmit, and validateForm if not used
    // Remove any state or logic related to the main form submission that is now unused
    // The subcomponents (BasicInfoForm, MediaSettings, etc.) should handle their own save actions if needed

    // Handle ESC key press
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const handleDragStart = (e, index) => {
        setDraggedItem(index);
        setIsDragging(true);
        e.dataTransfer.effectAllowed = 'move';
        e.target.style.opacity = '0.5';

        // Add a custom drag image
        const dragImage = e.target.cloneNode(true);
        dragImage.style.position = 'absolute';
        dragImage.style.top = '-1000px';
        document.body.appendChild(dragImage);
        e.dataTransfer.setDragImage(dragImage, 0, 0);
        setTimeout(() => document.body.removeChild(dragImage), 0);
    };

    const handleDragEnd = (e) => {
        setIsDragging(false);
        setDraggedItem(null);
        e.target.style.opacity = '1';
    };

    const handleDragOver = (e, index) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        // Add visual feedback for drop target
        const target = e.currentTarget;
        if (target) {
            target.style.transform = 'scale(1.05)';
            target.style.transition = 'transform 0.2s ease';
        }
    };

    const handleDragLeave = (e) => {
        // Reset visual feedback
        const target = e.currentTarget;
        if (target) {
            target.style.transform = 'scale(1)';
        }
    };

    const handleDrop = async (e, dropIndex) => {
        e.preventDefault();
        if (draggedItem === null) return;

        // Reset visual feedback
        const target = e.currentTarget;
        if (target) {
            target.style.transform = 'scale(1)';
        }

        // Create a new array with the reordered media
        const newMediaOrder = [...mediaOrder];
        const [draggedMedia] = newMediaOrder.splice(draggedItem, 1);
        newMediaOrder.splice(dropIndex, 0, draggedMedia);

        // Update the order in state
        setMediaOrder(newMediaOrder);

        try {
            // Prepare the correct payload for the API
            const photos = newMediaOrder
                .filter(media => media.type === 'image')
                .map((media, newIdx) => ({
                    order: media.order,      // original order from backend
                    new_order: newIdx + 1    // new order after drag
                }));

            // Call API to update the order
            const response = await profileService.reorderMedia({ photos });

            if (!response.success) {
                console.error('Failed to reorder media:', response.error);
                // Revert the UI state on error
                setMediaOrder(mediaOrder);
                alert('Failed to reorder media. Please try again.');
                return;
            }

            // Update the current cover media if it was reordered
            if (currentCoverMedia && currentCoverMedia.id === draggedMedia.id) {
                setCurrentCoverMedia({
                    ...currentCoverMedia,
                    order: dropIndex + 1
                });
            }
        } catch (error) {
            console.error('Error reordering media:', error);
            // Revert the UI state on error
            setMediaOrder(mediaOrder);
            alert('Failed to reorder media. Please try again.');
        }
    };

    // Update the form close handler to use the reset function
    const handleCloseServiceForm = () => {
        resetServiceForm(
            setServiceForm,
            setServiceStylePrices,
            setErrors,
            setServiceMessage
        );
        setShowServiceForm(false);
    };

    // Place this after all useState declarations and before the return statement
    useEffect(() => {
        const cachedProfile = queryClient.getQueryData(profileKeys.detail());
        let coverMedia = [];
        if (cachedProfile?.success && cachedProfile?.data?.profile_media) {
            const cdnUrl = process.env.REACT_APP_CDN_URL || 'http://localhost:8001';
            const photos = cachedProfile.data.profile_media.photos || [];
            coverMedia = photos.map((photo, index) => ({
                id: photo.id || index,
                url: photo.path.startsWith('http') ? photo.path : `${cdnUrl}/${photo.path}`,
                type: 'image',
                order: photo.order || index + 1
            }));
            if (cachedProfile.data.profile_media.video) {
                coverMedia.push({
                    id: 'video',
                    url: cachedProfile.data.profile_media.video.startsWith('http')
                        ? cachedProfile.data.profile_media.video
                        : `${cdnUrl}/${cachedProfile.data.profile_media.video}`,
                    type: 'video',
                    order: coverMedia.length + 1
                });
            }
        }
        if (isOpen && coverMedia.length > 0) {
            setMediaOrder(coverMedia);
        }
    }, [isOpen, queryClient]);

    // New: handle section change and clear success message
    const handleSectionChange = (sectionId) => {
        setActiveSection(sectionId);
        setSuccessMessage('');
    };

    // Focus trap ref
    const modalRef = useRef(null);
    const firstFocusableRef = useRef(null);
    const lastFocusableRef = useRef(null);

    // Focus trap effect
    useEffect(() => {
        if (!isOpen) return;
        const modal = modalRef.current;
        if (!modal) return;
        // Get all focusable elements
        const focusableSelectors = [
            'a[href]', 'button:not([disabled])', 'textarea:not([disabled])',
            'input:not([type=hidden]):not([disabled])', 'select:not([disabled])', '[tabindex]:not([tabindex="-1"])'
        ];
        const focusableEls = modal.querySelectorAll(focusableSelectors.join(','));
        if (focusableEls.length > 0) {
            firstFocusableRef.current = focusableEls[0];
            lastFocusableRef.current = focusableEls[focusableEls.length - 1];
            // Focus the first element
            setTimeout(() => focusableEls[0].focus(), 0);
        }
        function handleTrap(e) {
            if (e.key !== 'Tab') return;
            if (focusableEls.length === 0) return;
            if (e.shiftKey) {
                if (document.activeElement === firstFocusableRef.current) {
                    e.preventDefault();
                    lastFocusableRef.current.focus();
                }
            } else {
                if (document.activeElement === lastFocusableRef.current) {
                    e.preventDefault();
                    firstFocusableRef.current.focus();
                }
            }
        }
        modal.addEventListener('keydown', handleTrap);
        return () => modal.removeEventListener('keydown', handleTrap);
    }, [isOpen]);

    // On modal open, set lastSavedPersonalities to the current personalities
    useEffect(() => {
        if (isOpen && formData.personality_ids) {
            setLastSavedPersonalities([...formData.personality_ids]);
        }
    }, [isOpen, formData.personality_ids]);

    if (!isOpen) return null;

    // Profile Details Save Logic
    const handleSaveProfileDetails = async () => {
        setSaving(true);
        setErrors({});

        // Debug: Log the current formData to see what's in it
        console.log('Current formData:', formData);
        console.log('Profile picture in formData:', formData.profile_picture);
        console.log('Is profile_picture a File?', formData.profile_picture instanceof File);

        // Collect all form data (basic info + media)
        const payload = {
            nickname: formData.nickname,
            gender: formData.gender,
            height: toNumberIfPossible(formData.height),
            weight: toNumberIfPossible(formData.weight),
            date_of_birth: formData.date_of_birth,
            email: formData.email,
            biography: formData.biography,
            race_id: toNumberIfPossible(formData.race_id),
            allow_3rd_party_access: formData.allow_3rd_party_access
        };

        // Only include profile_picture if it's a File object
        if (formData.profile_picture instanceof File) {
            payload.profile_picture = formData.profile_picture;
            console.log('✅ Profile picture included in payload:', formData.profile_picture);
        } else {
            console.log('❌ Profile picture not included - not a File object:', formData.profile_picture);
        }

        // Only include voice_note if it's a File object
        if (formData.voice_note instanceof File) {
            payload.voice_note = formData.voice_note;
            console.log('✅ Voice note included in payload:', formData.voice_note);
        } else {
            console.log('❌ Voice note not included - not a File object:', formData.voice_note);
        }

        // Debug: Log the payload being sent
        console.log('Payload being sent:', payload);
        console.log('Profile picture in payload:', payload.profile_picture);
        console.log('Is payload.profile_picture a File?', payload.profile_picture instanceof File);

        try {
            // Call POST /api/user/profile
            const result = await profileService.updateProfile(payload, (p) => setProfileUploadProgress(p));

            if (result && result.success) {
                queryClient.invalidateQueries(profileQueryKey);
                const successMsg = result.data?.message || 'Profile details saved successfully';
                setSuccessMessage(successMsg);
                showSuccessToast(successMsg, 3000);
                if (onProfileUpdated) {
                    onProfileUpdated(result.data);
                }
                setTimeout(() => onClose(), 2000);
            } else if (result && result.validationErrors) {
                setErrors(result.validationErrors);
                showErrorToast('Failed to save profile details', 4000);
            } else {
                const errorMsg = result.error || 'Failed to save profile details';
                setErrors({ general: errorMsg });
                showErrorToast(errorMsg, 4000);
            }
        } catch (error) {
            console.error('Error saving profile details:', error);
            const errorMsg = error.response?.data?.message || 'Failed to save profile details';
            setErrors({ general: errorMsg });
            showErrorToast(errorMsg, 4000);
        } finally {
            setSaving(false);
            setProfileUploadProgress(0);
        }
    };

    // Personalities Save Logic
    const handleSavePersonalities = async () => {
        setPersonalitySaving(true);
        setPersonalitySuccess('');
        setPersonalityError('');

        try {
            // Gather selected personality IDs
            const personalityIds = formData.personality_ids || [];

            // Call POST /api/users/personalities
            // profileAPI.updateUserPersonalities expects an array of IDs,
            // so we pass personalityIds directly rather than wrapping it
            // inside another object
            const result = await profileAPI.updateUserPersonalities(personalityIds);

            // Some API endpoints do not return a `success` flag. Treat a resolved
            // promise as success and use any provided message for feedback.
            const successMsg = result?.message || 'Personalities updated successfully!';

            setPersonalitySuccess(successMsg);
            setLastSavedPersonalities([...formData.personality_ids]);
            showSuccessToast(successMsg, 3000);

            setTimeout(() => {
                setPersonalitySuccess('');
            }, 3000);
        } catch (error) {
            console.error('Error updating personalities:', error);
            const errMsg = error.response?.data?.message || 'Failed to update personalities';
            setPersonalityError(errMsg);
            showErrorToast(errMsg, 4000);
        } finally {
            setPersonalitySaving(false);
            setTimeout(() => {
                setPersonalityError('');
            }, 3000);
        }
    };

    return (
        <AnimatePresence>
            <motion.div
                className="fixed inset-0 z-50 flex flex-col w-full h-full"
                initial="hidden"
                animate="visible"
                exit="hidden"
                style={{ pointerEvents: isOpen ? 'auto' : 'none' }}
                aria-modal="true"
                role="dialog"
                aria-labelledby="edit-profile-modal-title"
                ref={modalRef}
            >
                {/* Backdrop: solid black with less blur */}
                <div className="absolute inset-0 bg-black/90 backdrop-blur-md" />

                {/* Modal Content Container: glassy, high-contrast, with accent gradients */}
                <motion.div
                    className="relative flex flex-col flex-1 w-full h-full max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl 2xl:max-w-5xl mx-auto my-0 rounded-3xl shadow-2xl bg-gradient-to-br from-white/95 via-blue-50/90 to-indigo-100/90 border border-blue-200 shadow-blue-900/10 overflow-hidden dark:from-gray-900 dark:via-gray-900 dark:to-gray-950 dark:bg-gradient-to-br dark:border-gray-800"
                    variants={modalVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ type: isMobile ? 'spring' : 'spring', damping: 25, stiffness: 300 }}
                    role="document"
                    aria-modal="true"
                    aria-labelledby="edit-profile-modal-title"
                    onClick={(e) => {
                        if (e.target === e.currentTarget) {
                            onClose();
                        }
                    }}
                >
                    {/* Decorative glowing gradients (enhanced) */}
                    <div className="pointer-events-none absolute -top-16 -right-16 w-56 h-56 bg-gradient-to-br from-blue-400/30 via-indigo-400/20 to-purple-400/10 rounded-full blur-3xl animate-pulse" />
                    <div className="pointer-events-none absolute -bottom-16 -left-16 w-48 h-48 bg-gradient-to-tr from-indigo-400/30 via-blue-400/20 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
                    <div className="pointer-events-none absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 to-blue-400/10 rounded-full blur-2xl animate-pulse delay-700" />
                    <div className="pointer-events-none absolute -bottom-10 -right-10 w-32 h-32 bg-gradient-to-tr from-purple-400/20 to-indigo-400/10 rounded-full blur-2xl animate-pulse delay-500" />

                    {/* Sticky Header: blue-indigo gradient, white text, vibrant in dark mode */}
                    <div className="sticky top-0 z-20 flex items-center justify-between px-8 py-5 border-b border-blue-200 bg-gradient-to-r from-blue-600 via-indigo-700 to-blue-800 text-white shadow-lg rounded-t-3xl dark:from-indigo-400 dark:via-indigo-700 dark:to-blue-900 dark:bg-gradient-to-r">
                        <h2 id="edit-profile-modal-title" className="text-2xl font-extrabold tracking-tight drop-shadow-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent dark:from-indigo-200 dark:to-blue-200 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">{t('edit.title')}</h2>
                        <button
                            type="button"
                            onClick={onClose}
                            aria-label="Close modal"
                            className="p-2 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-200/50 transition-all duration-200"
                        >
                            <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Section Navigation: horizontal tab bar below header */}
                    <nav
                        className="flex gap-2 overflow-x-auto px-2 sm:px-4 py-3 bg-white/80 rounded-full shadow-md border border-blue-100 mx-2 sm:mx-4 my-4 scrollbar-thin scrollbar-thumb-indigo-200 scrollbar-track-transparent dark:bg-gray-900/80 dark:border-gray-800"
                        role="tablist"
                        aria-label="Edit Profile Sections"
                        style={{ minHeight: 56, WebkitOverflowScrolling: 'touch' }}
                    >
                        {sections.map((section, idx) => (
                            <motion.button
                                key={section.id}
                                role="tab"
                                aria-selected={activeSection === section.id}
                                aria-controls={`section-panel-${section.id}`}
                                tabIndex={activeSection === section.id ? 0 : -1}
                                onClick={() => handleSectionChange(section.id)}
                                aria-label={`Go to ${section.name} section`}
                                className={`relative px-6 py-2 rounded-full font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 whitespace-nowrap flex items-center
                                                     ${activeSection === section.id
                                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white shadow-lg border-0 dark:from-indigo-400 dark:to-blue-400 dark:bg-gradient-to-r dark:text-white'
                                        : 'bg-transparent text-indigo-700 hover:bg-indigo-50/80 hover:text-indigo-900 dark:text-indigo-200 dark:hover:bg-gray-800 dark:hover:text-white'}
                                `}
                                style={{ minWidth: 120, flex: '0 0 auto' }}
                                layout
                                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                            >
                                <span className="align-middle text-lg mr-2" aria-hidden="true">{section.icon}</span>
                                <span className="align-middle text-base">{section.name}</span>
                            </motion.button>
                        ))}
                    </nav>

                    {/* Main Content (scrollable) */}
                    <div className="flex-1 overflow-y-auto px-2 py-3 pb-8 sm:pb-10 md:pb-12">
                        {/* Place the form and section rendering here, unchanged for now */}
                        {loading ? (
                            <div className="flex items-center justify-center h-64">

                            </div>
                        ) : (
                            <form id="edit-profile-form" className="space-y-8 max-w-4xl mx-auto">
                                {/* Success/Error/Section Content as before */}
                                {successMessage && (
                                    <motion.div
                                        className="p-4 bg-green-100/80 border border-green-200 rounded-xl"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                    >
                                        <div className="flex items-center">
                                            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-green-800 font-medium">{successMessage}</span>
                                        </div>
                                    </motion.div>
                                )}
                                {errors.general && (
                                    <motion.div
                                        className="p-4 bg-red-100/80 border border-red-200 rounded-xl"
                                        initial={{ opacity: 0, y: -10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                    >
                                        <div className="flex items-center">
                                            <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-red-800 font-medium">{errors.general}</span>
                                        </div>
                                    </motion.div>
                                )}
                                {/* Section Content (unchanged) */}
                                <div className="space-y-8">
                                    {activeSection === 'profile' && (
                                        <section className="bg-white/80 backdrop-blur-md rounded-2xl shadow-lg border border-blue-100 p-8 mb-6 dark:bg-gray-900/90 dark:border-gray-800">
                                            <h3 className="text-xl font-bold text-indigo-800 mb-6 bg-gradient-to-r from-indigo-700 to-blue-700 bg-clip-text text-transparent dark:from-indigo-300 dark:to-blue-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">Profile Details</h3>

                                            {/* Basic Info Fields */}
                                            <div className="space-y-6 mb-8">
                                                <h4 className="text-lg font-semibold text-indigo-700 mb-4 bg-gradient-to-r from-indigo-500 to-blue-500 bg-clip-text text-transparent dark:from-indigo-200 dark:to-blue-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">Basic Information</h4>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                    <FloatingLabelInput
                                                        id="uid"
                                                        label="User ID (UID)"
                                                        value={formData.uid || ''}
                                                        onChange={(e) => handleInputChange('uid', e.target.value)}
                                                        error={errors?.uid}
                                                        maxLength={64}
                                                        className="col-span-1 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-700"
                                                    />
                                                    <FloatingLabelInput
                                                        id="nickname"
                                                        label="Nickname"
                                                        value={formData.nickname || ''}
                                                        onChange={(e) => handleInputChange('nickname', e.target.value)}
                                                        error={errors?.nickname}
                                                        maxLength={20}
                                                        required
                                                    />
                                                    <FloatingLabelInput
                                                        id="email"
                                                        label="Email Address"
                                                        type="email"
                                                        value={formData.email || ''}
                                                        onChange={(e) => handleInputChange('email', e.target.value)}
                                                        error={errors?.email}
                                                        required
                                                    />
                                                    <FloatingLabelInput
                                                        id="height"
                                                        label="Height (cm)"
                                                        type="number"
                                                        value={formData.height || ''}
                                                        onChange={(e) => handleInputChange('height', e.target.value)}
                                                        error={errors?.height}
                                                        min="0"
                                                        step="0.1"
                                                    />
                                                    <FloatingLabelInput
                                                        id="weight"
                                                        label="Weight (kg)"
                                                        type="number"
                                                        value={formData.weight || ''}
                                                        onChange={(e) => handleInputChange('weight', e.target.value)}
                                                        error={errors?.weight}
                                                        min="0"
                                                        step="0.1"
                                                    />
                                                    <FloatingLabelInput
                                                        id="date_of_birth"
                                                        label="Date of Birth"
                                                        type="date"
                                                        value={formData.date_of_birth || ''}
                                                        onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                                                        error={errors?.date_of_birth}
                                                        required
                                                    />
                                                    <div className="flex flex-col">
                                                        <label htmlFor="gender" className="text-base font-semibold text-indigo-800 mb-2 dark:text-white">Gender</label>
                                                        <select
                                                            id="gender"
                                                            value={formData.gender || ''}
                                                            onChange={(e) => handleInputChange('gender', e.target.value)}
                                                            className={`w-full px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-indigo-500/50 text-base bg-white/80 text-gray-900 transition-all duration-200 ${errors?.gender ? 'border-red-500/50' : 'border-blue-100'} dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-700`}
                                                            required
                                                        >
                                                            <option value="">Select gender</option>
                                                            <option value="Male">♂️ Male</option>
                                                            <option value="Female">♀️ Female</option>
                                                            <option value="Other">⚧️ Other</option>
                                                        </select>
                                                        {errors?.gender && (
                                                            <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
                                                        )}
                                                    </div>
                                                    <div className="flex flex-col">
                                                        <label htmlFor="race_id" className="text-base font-semibold text-indigo-800 mb-2 dark:text-white">Race</label>
                                                        <select
                                                            id="race_id"
                                                            value={formData.race_id || ''}
                                                            onChange={(e) => handleInputChange('race_id', e.target.value)}
                                                            className={`w-full px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-indigo-500/50 text-base bg-white/80 text-gray-900 transition-all duration-200 ${errors?.race_id ? 'border-red-500/50' : 'border-blue-100'} dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-700`}
                                                            required
                                                            disabled={racesLoading}
                                                        >
                                                            <option value="">
                                                                {racesLoading ? 'Loading races...' : 'Select race'}
                                                            </option>
                                                            {races.map(race => (
                                                                <option key={race.id} value={race.id}>{race.name}</option>
                                                            ))}
                                                        </select>
                                                        {errors?.race_id && (
                                                            <p className="mt-1 text-sm text-red-500">{errors.race_id}</p>
                                                        )}
                                                        {racesError && (
                                                            <p className="mt-1 text-sm text-red-500">{racesError}</p>
                                                        )}
                                                    </div>
                                                </div>
                                                <FloatingLabelInput
                                                    id="biography"
                                                    label="Biography"
                                                    multiline
                                                    value={formData.biography || ''}
                                                    onChange={(e) => handleInputChange('biography', e.target.value)}
                                                    error={errors?.biography}
                                                    maxLength={500}
                                                    placeholder="Tell us about yourself..."
                                                    className="md:col-span-2 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-700"
                                                />
                                            </div>

                                            {/* Media Components */}
                                            <div className="space-y-6 mb-8">
                                                <h4 className="text-lg font-semibold text-indigo-700 mb-4">Media</h4>
                                                <MediaSettings
                                                    profilePicturePreview={profilePicturePreview}
                                                    handleProfilePictureChange={handleProfilePictureChange}
                                                    errors={errors}
                                                    uploadProgress={profileUploadProgress}
                                                    mediaOrder={mediaOrder}
                                                    handleDragStart={handleDragStart}
                                                    handleDragEnd={handleDragEnd}
                                                    handleDragOver={handleDragOver}
                                                    handleDragLeave={handleDragLeave}
                                                    handleDrop={handleDrop}
                                                    setPendingDeleteOrder={setPendingDeleteOrder}
                                                    setShowDeleteCoverConfirm={setShowDeleteCoverConfirm}
                                                    showDeleteCoverConfirm={showDeleteCoverConfirm}
                                                    pendingDeleteOrder={pendingDeleteOrder}
                                                    handleDeleteCoverMedia={handleDeleteCoverMedia}
                                                    coverMediaPreview={coverMediaPreview}
                                                    handleCoverMediaChange={handleCoverMediaChange}
                                                    currentVoiceNote={currentVoiceNote}
                                                    handleVoiceNoteChange={handleVoiceNoteChange}
                                                    handleDeleteVoiceNote={handleDeleteVoiceNote}
                                                    validateMediaFiles={validateMediaFiles}
                                                    processValidFile={processValidFile}
                                                />
                                            </div>

                                            {/* Save Button */}
                                            <div className="flex justify-center pt-6 border-t border-blue-100">
                                                <button
                                                    type="button"
                                                    onClick={handleSaveProfileDetails}
                                                    disabled={saving}
                                                    className="px-8 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium shadow-lg shadow-blue-900/20 hover:shadow-xl hover:shadow-indigo-900/30 focus:outline-none focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-lg disabled:hover:shadow-blue-900/25 border border-white/20"
                                                >
                                                    {saving ? (
                                                        <div className="flex items-center space-x-2">
                                                            <InlineLoader size="small" />
                                                            <span>Saving Profile Details...</span>
                                                        </div>
                                                    ) : (
                                                        'Save Profile Details'
                                                    )}
                                                </button>
                                            </div>
                                        </section>
                                    )}
                                    {activeSection === 'personalities' && (
                                        <section className="bg-white/80 backdrop-blur-md rounded-2xl shadow-lg border border-blue-100 p-8 mb-6 dark:bg-gray-900/90 dark:border-gray-800">
                                            <h3 className="text-xl font-bold text-indigo-800 mb-6 bg-gradient-to-r from-purple-700 to-pink-700 bg-clip-text text-transparent dark:from-purple-300 dark:to-pink-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">Personalities</h3>
                                            <div className="space-y-6">
                                                {personalitiesLoading ? (
                                                    <div className="flex items-center justify-center py-8">
                                                        <div className="flex items-center space-x-2">
                                                            <InlineLoader size="small" />
                                                            <span className="text-gray-600 dark:text-gray-200">Loading personalities...</span>
                                                        </div>
                                                    </div>
                                                ) : personalitiesError ? (
                                                    <div className="p-4 bg-red-100/80 border border-red-200 rounded-xl dark:bg-red-900/80 dark:border-red-400">
                                                        <div className="flex items-center">
                                                            <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                                            </svg>
                                                            <span className="text-red-800 dark:text-red-300 font-medium">{personalitiesError}</span>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <div className="space-y-4">
                                                            <h4 className="text-lg font-semibold text-indigo-700 bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent dark:from-purple-200 dark:to-pink-200 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">Select Your Personalities</h4>
                                                            <p className="text-sm text-gray-600 dark:text-gray-200">
                                                                Choose the personalities that best describe you. Selected personalities will be highlighted.
                                                            </p>

                                                            {/* Personality Bubbles */}
                                                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                                                {availablePersonalities.map((personality) => {
                                                                    const isSelected = formData.personality_ids.includes(personality.id);
                                                                    return (
                                                                        <motion.button
                                                                            key={personality.id}
                                                                            type="button"
                                                                            onClick={() => handlePersonalityToggle(personality.id)}
                                                                            className={`relative p-4 rounded-2xl border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/50 ${isSelected
                                                                                    ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white border-indigo-500 shadow-lg shadow-indigo-900/20 dark:from-indigo-400 dark:to-purple-500 dark:text-white'
                                                                                    : 'bg-white/80 text-gray-700 border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/80 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 dark:hover:border-indigo-400 dark:hover:bg-gray-900'} cursor-pointer hover:scale-105`}
                                                                            whileHover={{ scale: 1.05 }}
                                                                            whileTap={{ scale: 0.95 }}
                                                                        >
                                                                            <div className="text-center">
                                                                                <div className="text-2xl mb-2">
                                                                                    {isSelected ? '✨' : '💭'}
                                                                                </div>
                                                                                <div className="font-medium text-sm">
                                                                                    {personality.name}
                                                                                </div>
                                                                                {personality.description && (
                                                                                    <div className="text-xs mt-1 opacity-80 dark:text-gray-300">
                                                                                        {personality.description}
                                                                                    </div>
                                                                                )}
                                                                            </div>

                                                                            {/* Selection indicator */}
                                                                            {isSelected && (
                                                                                <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 dark:bg-green-400 rounded-full flex items-center justify-center">
                                                                                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                                                    </svg>
                                                                                </div>
                                                                            )}
                                                                        </motion.button>
                                                                    );
                                                                })}
                                                            </div>

                                                            {/* Selection counter */}
                                                            <div className="flex justify-end text-sm">
                                                                <span className="text-gray-600 dark:text-gray-200">
                                                                    {formData.personality_ids.length} selected
                                                                </span>
                                                            </div>

                                                            {/* Error message */}
                                                            {errors?.personality_ids && (
                                                                <div className="p-3 bg-red-100/80 border border-red-200 rounded-xl dark:bg-red-900/80 dark:border-red-400">
                                                                    <div className="flex items-center">
                                                                        <svg className="w-4 h-4 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                                                        </svg>
                                                                        <span className="text-red-800 dark:text-red-300 text-sm">{errors.personality_ids}</span>
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </>
                                                )}
                                            </div>

                                            {/* Save Button */}
                                            <div className="flex justify-end pt-6 border-t border-blue-100 dark:border-gray-800">
                                                <button
                                                    type="button"
                                                    onClick={handleSavePersonalities}
                                                    disabled={personalitySaving || personalitiesLoading}
                                                    className="px-8 py-3 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white font-medium shadow-lg shadow-indigo-900/20 hover:shadow-xl hover:shadow-indigo-900/30 focus:outline-none focus:ring-2 focus:ring-indigo-200/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-lg disabled:hover:shadow-indigo-900/25 border border-white/20"
                                                >
                                                    {personalitySaving ? (
                                                        <div className="flex items-center space-x-2">
                                                            <InlineLoader size="small" />
                                                            <span>Saving Personalities...</span>
                                                        </div>
                                                    ) : (
                                                        'Save Personalities'
                                                    )}
                                                </button>
                                            </div>

                                            {/* Success/Error Messages */}
                                            {personalitySuccess && (
                                                <motion.div
                                                    className="p-4 bg-green-100/80 border border-green-200 rounded-xl dark:bg-green-900/80 dark:border-green-400"
                                                    initial={{ opacity: 0, y: -10 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                >
                                                    <div className="flex items-center">
                                                        <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                        </svg>
                                                        <span className="text-green-800 dark:text-green-300 font-medium">{personalitySuccess}</span>
                                                    </div>
                                                </motion.div>
                                            )}
                                            {personalityError && (
                                                <motion.div
                                                    className="p-4 bg-red-100/80 border border-red-200 rounded-xl dark:bg-red-900/80 dark:border-red-400"
                                                    initial={{ opacity: 0, y: -10 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                >
                                                    <div className="flex items-center">
                                                        <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                                        </svg>
                                                        <span className="text-red-800 dark:text-red-300 font-medium">{personalityError}</span>
                                                    </div>
                                                </motion.div>
                                            )}
                                        </section>
                                    )}
                                    {activeSection === 'services' && (
                                        <section className="bg-white/80 backdrop-blur-md rounded-2xl shadow-lg border border-blue-100 p-8 mb-6 dark:bg-gray-900/90 dark:border-gray-800">
                                            <h3 className="text-xl font-bold text-indigo-800 mb-6 bg-gradient-to-r from-green-700 to-blue-700 bg-clip-text text-transparent dark:from-green-300 dark:to-blue-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">Services</h3>
                                            <ServiceManagement
                                                showServiceForm={showServiceForm}
                                                serviceForm={serviceForm}
                                                serviceStylePrices={serviceStylePrices}
                                                errors={errors}
                                                handleServiceFieldChange={handleServiceFieldChange}
                                                handleServiceFormToggle={handleServiceFormToggle}
                                                handleServiceStylePriceChange={handleServiceStylePriceChange}
                                                handleCloseServiceForm={handleCloseServiceForm}
                                                submitNewService={submitNewService}
                                                handleEditService={handleEditService}
                                                handleDeleteService={handleDeleteService}
                                                validateServiceForm={validateServiceForm}
                                                resetServiceForm={resetServiceForm}
                                            />
                                        </section>
                                    )}
                                </div>
                                {/* Remove the sticky footer with Save/Cancel buttons */}
                            </form>
                        )}
                    </div>

                    {/* Toasts/Snackbars: For success/error, slide up from bottom, auto-dismiss */}
                    {(successMessage || errors.general) && (
                        <div className="fixed inset-x-0 bottom-4 z-[100] flex justify-center pointer-events-none">
                            <motion.div
                                initial={{ opacity: 0, y: 40 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 40 }}
                                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                                className={`max-w-md w-full mx-auto px-4 py-3 rounded-xl shadow-lg border pointer-events-auto
                                    ${successMessage ? 'bg-green-100/90 border-green-200' : ''}
                                    ${errors.general ? 'bg-red-100/90 border-red-200' : ''}
                                `}
                                style={{ zIndex: 1000 }}
                            >
                                <div className="flex items-center">
                                    {successMessage && (
                                        <>
                                            <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-green-800 font-medium">{successMessage}</span>
                                        </>
                                    )}
                                    {errors.general && (
                                        <>
                                            <svg className="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-red-800 font-medium">{errors.general}</span>
                                        </>
                                    )}
                                </div>
                            </motion.div>
                        </div>
                    )}
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default EditProfileModal;
