// OrderReviewModal.js
// Modern, animated, glassmorphic modal for submitting reviews for order participants/clients

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { InlineLoader } from '../ui/LoadingIndicator';

/**
 * OrderReviewModal
 *
 * @param {boolean} isOpen - Whether the modal is open
 * @param {function} onClose - Function to close the modal
 * @param {object} order - Order object with customer/participants
 * @param {function} onSubmitReview - Hand<PERSON> for submitting reviews
 */
const OrderReviewModal = ({
    isOpen = false,
    onClose,
    order = null,
    onSubmitReview
}) => {
    // State for reviews (one per participant/customer)
    const [reviews, setReviews] = useState([]);
    // Submission/loading state
    const [isSubmitting, setIsSubmitting] = useState(false);
    // Error message state
    const [error, setError] = useState(null);
    // Success state for feedback animation
    const [success, setSuccess] = useState(false);

    // Participants: support customer, client or participants array
    const participants =
        order?.participants ||
        (order?.customer
            ? [order.customer]
            : order?.client
            ? [
                  {
                      ...order.client,
                      avatar: order.client.profile_picture,
                  },
              ]
            : []);

    // Initialize reviews when modal opens or order changes
    useEffect(() => {
        if (!isOpen) return;

        if (order?.customer) {
            setReviews([
                {
                    child_id: order.customer.id || order.customer.user_id || order.customer.uid,
                    rating: 5,
                    review_text: '',
                    is_anonymous: false,
                },
            ]);
        } else if (order?.participants) {
            setReviews(
                order.participants.map((p) => ({
                    child_id: p.id || p.user_id || p.uid,
                    rating: 5,
                    review_text: '',
                    is_anonymous: false,
                }))
            );
        } else if (order?.client) {
            setReviews([
                {
                    child_id: order.client.id || order.client.user_id || order.client.uid,
                    rating: 5,
                    review_text: '',
                    is_anonymous: false,
                },
            ]);
        } else {
            setReviews([]);
        }
    }, [isOpen, order]);

    /**
     * Update a review for a participant
     * @param {number} index - Index of the participant
     * @param {object} changes - Changes to apply
     */
    const updateReview = (index, changes) => {
        setReviews(prev => prev.map((r, i) => (i === index ? { ...r, ...changes } : r)));
    };

    /**
     * Handle form submission
     * @param {Event} e - Form event
     */
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError(null);
        setIsSubmitting(true);
        try {
            await onSubmitReview(reviews);
            setSuccess(true);
            setTimeout(() => {
                setSuccess(false);
            onClose();
            }, 3000);
        } catch (error) {
            setError(error.message || 'Failed to submit review');
        } finally {
            setIsSubmitting(false);
        }
    };

    // Don't render if modal is closed
    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 z-50 flex items-center justify-center p-0 sm:p-4"
                    style={{
                        background: 'linear-gradient(120deg, rgba(255, 228, 230, 0.7) 0%, rgba(254, 215, 170, 0.7) 100%)',
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)'
                    }}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3, ease: 'easeOut' }}
                    onClick={onClose}
                    aria-modal="true"
                    role="dialog"
                    aria-labelledby="order-review-modal-title"
                    tabIndex={-1}
                    onKeyDown={(e) => { if (e.key === 'Escape') onClose(); }}
                >
                    <motion.div
                        className="relative rounded-3xl shadow-2xl border border-white/30 bg-white/80 backdrop-blur-2xl w-full max-w-full sm:max-w-3xl max-h-screen sm:max-h-[90vh] overflow-hidden flex flex-col"
                        style={{
                            background: 'linear-gradient(120deg, rgba(255,255,255,0.95) 0%, rgba(255,245,235,0.92) 100%)',
                            boxShadow: '0 8px 40px 0 rgba(255, 193, 7, 0.10), 0 1.5px 8px 0 rgba(255, 193, 7, 0.08)',
                            border: '1.5px solid rgba(255,255,255,0.25)',
                            backdropFilter: 'blur(16px)',
                            WebkitBackdropFilter: 'blur(16px)'
                        }}
                        initial={{ opacity: 0, scale: 0.95, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: 20 }}
                        transition={{ duration: 0.4, type: 'spring', stiffness: 200 }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <motion.div
                            className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.7, delay: 0.1 }}
                        />
                        <motion.div
                            className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-yellow-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.7, delay: 0.2 }}
                        />
                        {/* Header */}
                        <div className="relative z-10 px-4 sm:px-6 py-6 border-b border-white/20 bg-gradient-to-r from-yellow-50 to-orange-50">
                            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                                <div className="flex items-center space-x-4">
                                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-2xl bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center shadow-lg">
                                        {/* Star/Review Icon */}
                                        <svg className="w-6 h-6 sm:w-7 sm:h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 id="order-review-modal-title" className="text-2xl sm:text-3xl font-extrabold bg-gradient-to-r from-yellow-600 via-orange-500 to-orange-600 bg-clip-text text-transparent">
                                            Review Client
                                        </h2>
                                        <p className="text-gray-500 text-sm sm:text-base mt-1">
                                            Share your experience with this order
                                        </p>
                                    </div>
                                </div>
                                {/* Floating close button */}
                                <motion.button
                                    onClick={onClose}
                                    className="absolute top-4 right-4 p-3 bg-white/70 hover:bg-yellow-100 text-yellow-500 rounded-full shadow transition-colors duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-yellow-400"
                                    aria-label="Close review modal"
                                    tabIndex={0}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </motion.button>
                            </div>
                        </div>
                        {/* Content */}
                        <div className="p-4 sm:p-6 flex-1 overflow-y-auto w-full">
                            <form onSubmit={handleSubmit} className="space-y-8 max-w-2xl mx-auto" autoComplete="off">
                                {/* Review cards for each participant/customer */}
                                {participants.map((participant, index) => (
                                    <motion.div
                                        key={participant.id || index}
                                        className="relative flex flex-col sm:flex-row items-start max-w-3xl bg-gradient-to-br from-white/90 to-yellow-50/60 rounded-2xl shadow-lg border-l-4 border-yellow-300 p-6 mb-2"
                                        initial={{ opacity: 0, y: 30, scale: 0.97 }}
                                        animate={{ opacity: 1, y: 0, scale: 1 }}
                                        exit={{ opacity: 0, y: 30, scale: 0.97 }}
                                        transition={{ duration: 0.4, type: 'spring', delay: 0.05 * index }}
                                    >
                                        {/* Gradient Accent Bar */}
                                        <div className="absolute left-0 top-6 bottom-6 w-1.5 bg-gradient-to-b from-yellow-400 to-orange-400 rounded-full" />
                                        {/* Avatar & Name */}
                                        <div className="flex items-center space-x-4 z-10">
                                            <img
                                                src={
                                                    participant.avatar ||
                                                    participant.profile_picture ||
                                                    '/images/default-avatar.jpg'
                                                }
                                                alt={participant.nickname || participant.name || 'Participant'}
                                                className="w-14 h-14 rounded-full object-cover border-2 border-yellow-200 shadow-md bg-white"
                                                onError={(e) => {
                                                    e.target.src = '/images/default-avatar.jpg';
                                                }}
                                            />
                                            <span className="font-semibold text-lg text-gray-900 drop-shadow-sm">{participant.nickname || participant.name}</span>
                                        </div>
                                        <div className="flex-1 w-full mt-6 sm:mt-0 sm:ml-8 z-10">
                                            {/* Star Rating */}
                                            <label className="block text-base font-semibold text-gray-700 mb-2 mt-2">Rating</label>
                                            <div className="flex items-center space-x-2 mb-4">
                                                {[1,2,3,4,5].map(star => (
                                                    <motion.button
                                                        key={star}
                                                        type="button"
                                                        className={`p-1 rounded-full border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 ${star <= reviews[index]?.rating ? 'text-yellow-500 border-yellow-400 bg-yellow-50 shadow' : 'text-gray-300 border-gray-200 bg-white hover:bg-yellow-50'}`}
                                                        onClick={() => updateReview(index, { rating: star })}
                                                        whileHover={{ scale: 1.15 }}
                                                        whileTap={{ scale: 0.92 }}
                                                        aria-label={`Set rating to ${star}`}
                                                    >
                                                        <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                        </svg>
                                                    </motion.button>
                                                ))}
                                            </div>
                                            {/* Review Textarea */}
                                            <label className="block text-base font-semibold text-gray-700 mb-2">Review</label>
                                            <textarea
                                                value={reviews[index]?.review_text || ''}
                                                onChange={(e) => updateReview(index, { review_text: e.target.value })}
                                                className="w-full px-4 py-3 bg-white/70 border border-yellow-200 rounded-xl text-base focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 shadow-sm backdrop-blur-md transition-all duration-200"
                                                rows="3"
                                                required
                                            />
                                            {/* Anonymous Checkbox */}
                                            <div className="flex items-center mt-4">
                                            <input
                                                type="checkbox"
                                                id={`anon-${index}`}
                                                checked={reviews[index]?.is_anonymous || false}
                                                onChange={(e) => updateReview(index, { is_anonymous: e.target.checked })}
                                                    className="h-5 w-5 text-yellow-600 focus:ring-yellow-400 border-yellow-300 rounded shadow-sm bg-white/70 backdrop-blur-md transition-all duration-200"
                                            />
                                                <label htmlFor={`anon-${index}`} className="ml-3 block text-base text-gray-700 select-none">
                                                Submit anonymously
                                            </label>
                                        </div>
                                    </div>
                                    </motion.div>
                                ))}
                                {/* Error Message */}
                                {error && (
                                    <motion.div
                                        className="p-4 bg-gradient-to-br from-red-100 to-orange-100 border border-red-200 rounded-xl flex items-center space-x-3 mb-4"
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <svg className="w-6 h-6 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <p className="text-base font-semibold text-red-700">{typeof error === 'string' ? error : error.message}</p>
                                    </div>
                                    </motion.div>
                                )}
                                {/* Loading State */}
                                {isSubmitting && (
                                    <motion.div
                                        className="flex justify-center items-center py-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl shadow-inner mt-6"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <motion.div
                                            animate={{ rotate: 360 }}
                                            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                                            className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mr-4"
                                        />
                                        <span className="text-blue-700 text-lg font-semibold">Submitting...</span>
                                    </motion.div>
                                )}
                                {/* Success State */}
                                {success && (
                                    <motion.div
                                        className="text-center py-10"
                                        initial={{ opacity: 0, scale: 0.95 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                                    >
                                        <motion.div
                                            className="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
                                            initial={{ scale: 0, rotate: -180 }}
                                            animate={{ scale: 1, rotate: 0 }}
                                            transition={{ duration: 0.7, type: 'spring', stiffness: 200, delay: 0.1 }}
                                        >
                                            <motion.svg
                                                className="w-10 h-10 text-white"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                                initial={{ pathLength: 0 }}
                                                animate={{ pathLength: 1 }}
                                                transition={{ duration: 0.8, delay: 0.2 }}
                                            >
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                                            </motion.svg>
                                        </motion.div>
                                        <motion.div
                                            className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mx-auto max-w-md shadow"
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.3, duration: 0.5 }}
                                        >
                                            <h3 className="text-2xl font-extrabold text-green-700 mb-2">Review Submitted!</h3>
                                            <p className="text-green-700 text-lg font-medium">Thank you for sharing your feedback.</p>
                                        </motion.div>
                                    </motion.div>
                                )}
                                {/* Submit Button */}
                                <div className="sticky bottom-0 left-0 right-0 z-20 bg-gradient-to-r from-white/90 to-yellow-50/80 border-t border-yellow-100 px-0 sm:px-6 py-4 flex justify-center mt-8">
                                    <motion.button
                                        type="submit"
                                        disabled={isSubmitting || success}
                                        className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-extrabold text-lg rounded-2xl shadow-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 flex items-center justify-center space-x-3 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-400 disabled:opacity-50 disabled:cursor-not-allowed"
                                        whileHover={{ scale: 1.04 }}
                                        whileTap={{ scale: 0.97 }}
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <InlineLoader size="small" color="white" />
                                                <span>Submitting...</span>
                                            </>
                                        ) : (
                                            <>
                                                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                                <span>Submit Review</span>
                                            </>
                                        )}
                                    </motion.button>
                                </div>
                            </form>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default OrderReviewModal;
