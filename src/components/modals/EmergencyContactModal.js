import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import FloatingLabelInput from './editProfile/FloatingLabelInput';
import DeleteConfirmationDialog from './editProfile/DeleteConfirmationDialog';
import emergencyContactService from '../../services/emergencyContactService';
import phoneValidationService from '../../services/phoneValidationService';
import { UserIcon, PencilIcon, TrashIcon, StarIcon, PhoneIcon } from '@heroicons/react/24/solid';
import { useToast } from '../common/ToastProvider';

const defaultForm = {
  relationship_id: '',
  contact_person_name: '',
  phone_number: '',
  is_default: false
};

const EmergencyContactModal = ({ isOpen = false, onClose }) => {
  const [contacts, setContacts] = useState([]);
  const [relationships, setRelationships] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState(defaultForm);
  const [errors, setErrors] = useState({});
  const [editingId, setEditingId] = useState(null);
  const [showDelete, setShowDelete] = useState(null);
  const [apiError, setApiError] = useState(null);
  const { success: showSuccessToast, error: showErrorToast } = useToast();
  
  useEffect(() => {
    if (!isOpen) return;
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    document.addEventListener('keydown', handleEsc);
    document.body.style.overflow = 'hidden';
    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  const fetchData = async () => {
    setLoading(true);
    setApiError(null);
    try {
    const [contactsRes, relRes] = await Promise.all([
      emergencyContactService.getContacts(),
      emergencyContactService.getRelationships()
    ]);
      
      if (contactsRes.success) {
        setContacts(contactsRes.data || []);
      } else {
        setApiError(contactsRes.error || 'Failed to load contacts');
      }
      
      if (relRes.success) {
        setRelationships(relRes.data || []);
      } else {
        setApiError(relRes.error || 'Failed to load relationships');
      }
    } catch (error) {
      setApiError('An unexpected error occurred');
      console.error('Error fetching data:', error);
    } finally {
    setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchData();
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    setForm(defaultForm);
    setErrors({});
    setEditingId(null);
  };

  const validate = () => {
    const e = {};
    if (!form.relationship_id) e.relationship_id = 'Required';
    if (!form.contact_person_name.trim()) e.contact_person_name = 'Required';
    if (!phoneValidationService.isValidMalaysianPhone(form.phone_number)) {
      e.phone_number = 'Invalid Malaysian phone number format';
    }
    setErrors(e);
    return Object.keys(e).length === 0;
  };

  const handleSubmit = async () => {
    if (!validate()) return;
    setLoading(true);
    setApiError(null);
    
    const data = {
      relationship_id: form.relationship_id,
      contact_person_name: form.contact_person_name,
      phone_number: form.phone_number,
      is_default: form.is_default
    };
    
    try {
    let res;
    if (editingId) {
      res = await emergencyContactService.updateContact(editingId, data);
    } else {
      res = await emergencyContactService.createContact(data);
    }
      
    if (res.success) {
        await fetchData();
      resetForm();
      showSuccessToast({
        title: editingId ? 'Contact updated successfully!' : 'Contact added successfully!'
      }, 3000);
    } else {
        setApiError(res.error || 'Failed to save contact');
        showErrorToast({
          title: res.error || 'Failed to save contact'
        }, 4000);
      }
    } catch (error) {
      setApiError('An unexpected error occurred');
      showErrorToast({
        title: 'An unexpected error occurred'
      }, 4000);
      console.error('Error saving contact:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (contact) => {
    setEditingId(contact.id);
    setForm({
      relationship_id: contact.relationship?.id || '',
      contact_person_name: contact.contact_person_name || '',
      phone_number: contact.phone_number || '',
      is_default: !!contact.is_default
    });
  };

  const handleDelete = async () => {
    if (!showDelete) return;
    setLoading(true);
    setApiError(null);
    
    try {
    const res = await emergencyContactService.deleteContact(showDelete.id);
      if (res.success) {
        await fetchData();
        showSuccessToast({
          title: 'Contact deleted successfully!'
        }, 3000);
      } else {
        setApiError(res.error || 'Failed to delete contact');
        showErrorToast({
          title: res.error || 'Failed to delete contact'
        }, 4000);
      }
    } catch (error) {
      setApiError('An unexpected error occurred');
      showErrorToast({
        title: 'An unexpected error occurred'
      }, 4000);
      console.error('Error deleting contact:', error);
    } finally {
      setLoading(false);
    setShowDelete(null);
    }
  };

  const handleSetDefault = async (contact) => {
    setLoading(true);
    setApiError(null);
    
    try {
    const res = await emergencyContactService.setDefaultContact(contact);
      if (res.success) {
        await fetchData();
      } else {
        setApiError(res.error || 'Failed to set default contact');
      }
    } catch (error) {
      setApiError('An unexpected error occurred');
      console.error('Error setting default contact:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-900/90 border border-white/30 dark:border-gray-800 rounded-3xl shadow-2xl w-full max-w-md overflow-hidden"
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Animated background decorations */}
            <div className="absolute -top-10 -right-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-indigo-900/30 dark:to-purple-900/30 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-10 -left-10 w-20 h-20 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-indigo-900/30 dark:to-blue-900/30 rounded-full blur-xl animate-pulse delay-1000" />
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-900 dark:to-purple-900 p-6 text-white flex justify-between items-center relative z-10">
                <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  {/* Prominent phone/alert icon */}
                  <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold">Emergency Contacts</h3>
                </div>
              <motion.button
                  onClick={handleClose}
                  disabled={loading}
                className="p-2 hover:bg-white/20 dark:hover:bg-gray-800/40 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: 1.05, rotate: 90 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
              </motion.button>
            </div>
            {/* Content */}
            <div className="p-6 relative z-10 dark:bg-gray-900/80">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                </div>
              ) : apiError ? (
                <motion.div
                  className="bg-gradient-to-r from-red-100 to-yellow-100 dark:from-red-900/30 dark:to-yellow-900/30 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-xl px-4 py-3 mb-4 flex items-center shadow-md"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  <svg className="w-5 h-5 mr-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414" />
                  </svg>
                  <span>{apiError}</span>
                </motion.div>
              ) : (
                <div className="space-y-6 max-h-[60vh] overflow-y-auto pr-2">
                  {/* Contact List Section */}
                  <div className="mb-6">
                    <h4 className="font-bold text-lg text-gray-900 dark:text-gray-100 mb-2">Your Contacts</h4>
                    <div className="space-y-4">
                  {contacts.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 px-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                          <svg viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-24 h-24 mb-4">
                            <circle cx="48" cy="48" r="48" fill="#EEF2FF"/>
                            <rect x="28" y="36" width="40" height="28" rx="6" fill="#C7D2FE"/>
                            <rect x="36" y="44" width="24" height="12" rx="3" fill="#6366F1"/>
                            <circle cx="48" cy="50" r="2" fill="#A5B4FC"/>
                            <rect x="44" y="60" width="8" height="4" rx="2" fill="#A5B4FC"/>
                          </svg>
                          <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No emergency contacts</h4>
                          <p className="text-gray-500 dark:text-gray-300 text-center">Add your first emergency contact to get started.</p>
                        </div>
                  ) : (
                    contacts.map(c => (
                          <motion.div
                            className="p-4 rounded-xl bg-gradient-to-r from-blue-50/60 to-indigo-50/40 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-100/30 dark:border-blue-900/40 shadow-sm flex items-center justify-between"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5 }}
                          >
                            {/* Buddy Icon */}
                            <div className="flex-shrink-0 w-12 h-12 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center mr-4 border-2 border-indigo-200 dark:border-indigo-700">
                              <UserIcon className="w-7 h-7 text-indigo-400 dark:text-indigo-300" />
                            </div>
                            {/* Contact Info, stacked and organized */}
                            <div className="flex-1 min-w-0 flex flex-col justify-center">
                              <div className="flex items-center mb-1">
                                <span className="font-semibold text-gray-900 dark:text-gray-100 text-base truncate max-w-[10rem]">{c.contact_person_name}</span>
                            {c.is_default && (
                                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-semibold bg-indigo-600 text-white">
                                    <StarIcon className="w-3.5 h-3.5 mr-1 text-yellow-300" /> Default
                              </span>
                            )}
                          </div>
                              <span className="text-sm text-left text-gray-500 dark:text-gray-300 mb-0.5">{c.relationship?.name}</span>
                              <span className="text-sm text-left text-gray-700 dark:text-gray-200">{c.phone_number}</span>
                            </div>
                            {/* Action Buttons, vertically aligned */}
                            <div className="flex flex-col space-y-2 ml-4 items-end">
                            {!c.is_default && (
                                <motion.button
                                onClick={() => handleSetDefault(c)}
                                  className="rounded-full bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-900 dark:hover:bg-yellow-800 text-yellow-800 dark:text-yellow-200 p-2 flex items-center justify-center shadow transition-colors"
                                  whileHover={{ scale: 1.08 }}
                                  whileTap={{ scale: 0.95 }}
                                  title="Set as Default"
                                >
                                  <StarIcon className="w-4 h-4" />
                                </motion.button>
                              )}
                              <motion.button
                                onClick={() => handleEdit(c)}
                                className="rounded-full bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900 dark:hover:bg-indigo-800 text-indigo-700 dark:text-indigo-200 p-2 flex items-center justify-center shadow transition-colors"
                                whileHover={{ scale: 1.08 }}
                                whileTap={{ scale: 0.95 }}
                                title="Edit"
                              >
                                <PencilIcon className="w-4 h-4" />
                              </motion.button>
                              <motion.button
                              onClick={() => setShowDelete(c)}
                                className="rounded-full bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-300 p-2 flex items-center justify-center shadow transition-colors"
                                whileHover={{ scale: 1.08 }}
                                whileTap={{ scale: 0.95 }}
                                title="Delete"
                            >
                                <TrashIcon className="w-4 h-4" />
                              </motion.button>
                          </div>
                          </motion.div>
                        ))
                      )}
                        </div>
                      </div>
                  {/* Form Section */}
                  <div className="bg-white/80 dark:bg-gray-900/80 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-md">
                    <h4 className="font-bold text-lg text-gray-900 dark:text-gray-100 mb-4">{editingId ? 'Edit Contact' : 'Add New Contact'}</h4>
      <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
                        Relationship
                      </label>
        <select
                        className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 mb-5"
          value={form.relationship_id}
          onChange={e => setForm({ ...form, relationship_id: e.target.value })}
        >
                        <option value="">Select relationship</option>
          {relationships.map(r => (
            <option key={r.id} value={r.id}>{r.name}</option>
          ))}
        </select>
                      {errors.relationship_id && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.relationship_id}</p>
                      )}
      </div>
      <FloatingLabelInput
        id="contact_person_name"
        label="Contact Name"
        value={form.contact_person_name}
        onChange={e => setForm({ ...form, contact_person_name: e.target.value })}
        maxLength={255}
        error={errors.contact_person_name}
      />
      <FloatingLabelInput
        id="phone_number"
        label="Phone Number"
        value={form.phone_number}
        onChange={e => setForm({ ...form, phone_number: e.target.value })}
        error={errors.phone_number}
        placeholder="+60123456789"
      />
      <div className="flex space-x-2 pt-2">
        {editingId && (
          <button
            onClick={resetForm}
                          className="flex-1 px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            Cancel
          </button>
        )}
        <button
          onClick={handleSubmit}
                        disabled={loading}
                        className="flex-1 px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
        >
                        {editingId ? 'Update' : 'Add Contact'}
        </button>
      </div>
    </div>
                </div>
              )}
            </div>
          </motion.div>
          <DeleteConfirmationDialog
            isOpen={!!showDelete}
            onClose={() => setShowDelete(null)}
            onConfirm={handleDelete}
            message="Are you sure you want to delete this emergency contact?"
            dialogClassName="bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-900/90 border border-red-200 dark:border-red-700 rounded-2xl shadow-2xl p-6 max-w-sm mx-auto text-center"
            icon={<TrashIcon className="w-10 h-10 mx-auto mb-4 text-red-400" />}
            confirmButtonClassName="px-6 py-2 bg-red-600 text-white rounded-xl shadow hover:bg-red-700 transition-all duration-300"
            cancelButtonClassName="px-6 py-2 bg-gray-200 text-gray-700 rounded-xl shadow hover:bg-gray-300 transition-all duration-300"
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EmergencyContactModal;
