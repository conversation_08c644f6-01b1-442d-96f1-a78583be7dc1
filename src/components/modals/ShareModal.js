import React, { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../common/ToastProvider';
import {
  FaWhatsapp,
  FaFacebookF,
  FaLinkedinIn,
  FaEnvelope,
  FaQrcode,
  FaLink,
  FaShareAlt,
  FaTimes
} from 'react-icons/fa';

/**
 * ShareModal - A comprehensive, reusable sharing modal component
 * 
 * Features:
 * - Supports any shareable content (posts, referrals, profiles, etc.)
 * - Responsive design with mobile-first approach
 * - Comprehensive accessibility features
 * - Customizable share options and metadata
 * - Consistent with app's design system
 * 
 * Usage Examples:
 * 
 * // Basic usage
 * <ShareModal
 *   isOpen={isOpen}
 *   onClose={handleClose}
 *   shareUrl="https://example.com/post/123"
 * />
 * 
 * // With custom title and description
 * <ShareModal
 *   isOpen={isOpen}
 *   onClose={handleClose}
 *   shareUrl="https://example.com/post/123"
 *   title="Amazing Post"
 *   description="Check out this incredible post!"
 * />
 * 
 * // For referrals
 * <ShareModal
 *   isOpen={isOpen}
 *   onClose={handleClose}
 *   shareUrl="https://example.com/ref/user123"
 *   title="Join MissionX"
 *   description="I'm using MissionX and you should too!"
 * />
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when modal closes
 * @param {string} props.shareUrl - The URL to share
 * @param {string} [props.title] - Optional title for the shared content
 * @param {string} [props.description] - Optional description for the shared content
 * @param {Array} [props.customShareOptions] - Optional custom share options
 * @param {string} [props.modalTitle] - Custom modal title (defaults to "Share this item")
 * @param {string} [props.modalDescription] - Custom modal description
 * @param {boolean} [props.showQRCode] - Whether to show QR code option (default: true)
 * @param {Function} [props.onShare] - Callback when any share action is performed
 */
const ShareModal = ({ 
  isOpen, 
  onClose, 
  shareUrl, 
  title = '', 
  description = '', 
  customShareOptions = null,
  modalTitle = null,
  modalDescription = null,
  showQRCode = true,
  onShare = null
}) => {
  const { success: showSuccessToast } = useToast();
  const modalRef = useRef();
  const closeButtonRef = useRef();
  const firstFocusableRef = useRef();

  // Default share options - can be overridden by customShareOptions
  const defaultShareOptions = [
    {
      label: 'WhatsApp',
      icon: <FaWhatsapp className="w-6 h-6" />,
      color: 'bg-green-500 hover:bg-green-600 text-white',
      getUrl: (url, title) => `https://wa.me/?text=${encodeURIComponent(title ? `${title} ${url}` : url)}`
    },
    {
      label: 'Facebook',
      icon: <FaFacebookF className="w-6 h-6" />,
      color: 'bg-blue-600 hover:bg-blue-700 text-white',
      getUrl: (url) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
    },
    {
      label: 'X',
      icon: (
        <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      ),
      color: 'bg-black hover:bg-gray-800 text-white',
      getUrl: (url, title) => `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}${title ? `&text=${encodeURIComponent(title)}` : ''}`
    },
    {
      label: 'LinkedIn',
      icon: <FaLinkedinIn className="w-6 h-6" />,
      color: 'bg-blue-800 hover:bg-blue-900 text-white',
      getUrl: (url, title) => `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}${title ? `&title=${encodeURIComponent(title)}` : ''}`
    },
    {
      label: 'Email',
      icon: <FaEnvelope className="w-6 h-6" />,
      color: 'bg-amber-500 hover:bg-amber-600 text-white',
      getUrl: (url, title, description) => `mailto:?subject=${encodeURIComponent(title || 'Check this out!')}&body=${encodeURIComponent(description ? `${description}\n${url}` : url)}`
    }
  ];

  // Use custom share options if provided, otherwise use defaults
  const shareOptions = customShareOptions || defaultShareOptions;

  // Enhanced accessibility: trap focus and keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    // Store the element that had focus before modal opened
    const previouslyFocusedElement = document.activeElement;

    // Get all focusable elements within the modal
    const getFocusableElements = () => {
      const focusableSelectors = [
        'button:not([disabled])',
        'a[href]',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])'
      ];
      return modalRef.current?.querySelectorAll(focusableSelectors.join(', ')) || [];
    };

    const focusableElements = getFocusableElements();
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    // Focus the first element
    if (firstElement) {
      firstElement.focus();
      firstFocusableRef.current = firstElement;
    }

    // Enhanced keyboard navigation
    const handleKeyDown = (e) => {
      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case 'Tab':
          if (focusableElements.length === 0) return;
          
          if (e.shiftKey) {
            // Shift + Tab: move backwards
            if (document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            }
          } else {
            // Tab: move forwards
            if (document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
          break;
        case 'Enter':
        case ' ':
          // Handle Enter and Space for buttons
          if (document.activeElement.tagName === 'BUTTON') {
            e.preventDefault();
            document.activeElement.click();
          }
          break;
      }
    };

    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
      
      // Restore focus to previously focused element
      if (previouslyFocusedElement && previouslyFocusedElement.focus) {
        previouslyFocusedElement.focus();
      }
    };
  }, [isOpen, onClose]);

  // Handle copy link with callback
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      showSuccessToast('Link copied to clipboard!');
      
      // Call onShare callback if provided
      if (onShare) {
        onShare('copy', { url: shareUrl, title, description });
      }
    } catch {
      showSuccessToast('Failed to copy link');
    }
  };

  // Handle share action with callback
  const handleShareAction = (platform, url) => {
    // Call onShare callback if provided
    if (onShare) {
      onShare(platform, { url, title, description });
    }
  };

  // Generate modal title and description
  const getModalTitle = () => {
    if (modalTitle) return modalTitle;
    return `Share this ${title ? title : 'item'}`;
  };

  const getModalDescription = () => {
    if (modalDescription) return modalDescription;
    return "Choose how you'd like to share";
  };

  // Animation variants matching ServiceSelectionModal.js
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, transition: { duration: 0.2, ease: "easeIn" } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1, 
      transition: { 
        duration: 0.4, 
        ease: "easeOut",
        type: "spring",
        stiffness: 300,
        damping: 30
      } 
    },
    exit: { 
      opacity: 0, 
      y: 50, 
      scale: 0.95, 
      transition: { 
        duration: 0.3, 
        ease: "easeIn" 
      } 
    }
  };

  const buttonVariants = {
    hover: { 
      scale: 1.05, 
      transition: { 
        duration: 0.2, 
        ease: "easeOut" 
      } 
    },
    tap: { 
      scale: 0.95, 
      transition: { 
        duration: 0.1, 
        ease: "easeIn" 
      } 
    }
  };

  const cardVariants = {
    hover: { 
      scale: 1.02, 
      y: -2, 
      transition: { 
        duration: 0.2, 
        ease: "easeOut" 
      } 
    },
    tap: { 
      scale: 0.98, 
      transition: { 
        duration: 0.1, 
        ease: "easeIn" 
      } 
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-2 sm:p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={onClose}
          aria-modal="true"
          role="dialog"
          aria-labelledby="share-modal-title"
          aria-describedby="share-modal-description"
        >
          <motion.div
            ref={modalRef}
            className="bg-white rounded-2xl w-full max-w-full sm:max-w-2xl md:max-w-3xl lg:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden shadow-2xl border border-gray-100 relative"
            variants={modalVariants}
            onClick={e => e.stopPropagation()}
            role="document"
            aria-label="Share options modal"
          >
            {/* Header with gradient background */}
            <div className="relative px-4 sm:px-6 lg:px-8 py-4 sm:py-6 bg-gradient-to-br from-indigo-500 to-blue-600 border-b border-indigo-400">
              {/* Close Button */}
              <motion.button
                ref={closeButtonRef}
                onClick={onClose}
                className="absolute top-2 sm:top-4 right-2 sm:right-4 p-3 text-white bg-red-500 hover:text-gray-600 hover:bg-white/50 rounded-full transition-all duration-200 touch-manipulation select-none min-w-[44px] min-h-[44px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                aria-label="Close share modal"
                tabIndex={0}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <FaTimes className="w-5 h-5" />
              </motion.button>

              {/* Header Content */}
              <div className="flex items-center space-x-3 sm:space-x-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-2xl bg-gradient-to-br from-white/20 to-white/10 flex items-center justify-center shadow-lg flex-shrink-0" aria-hidden="true">
                  <FaShareAlt className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h1 id="share-modal-title" className="text-lg sm:text-xl lg:text-2xl text-left font-bold text-white mb-1 truncate">
                    {getModalTitle()}
                  </h1>
                  <p id="share-modal-description" className="text-xs sm:text-sm text-white/80 font-medium">
                    {getModalDescription()}
                  </p>
                </div>
              </div>
            </div>

            {/* Content Area */}
            <div className="px-2 sm:px-6 lg:px-8 py-3 sm:py-6 max-h-[60vh] sm:max-h-[60vh] overflow-y-auto">
              {/* Share Options Grid - Responsive and Accessible */}
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 mb-6" role="group" aria-label="Share options">
                {shareOptions.map((opt, index) => (
                  <motion.a
                    key={opt.label}
                    href={opt.getUrl(shareUrl, title, description)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`relative group cursor-pointer transition-all duration-300 rounded-xl p-4 sm:p-6 border-2 border-transparent hover:border-white/20 ${opt.color} shadow-lg hover:shadow-xl min-h-[80px] sm:min-h-[100px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white/50`}
                    tabIndex={0}
                    aria-label={`Share via ${opt.label}`}
                    role="button"
                    variants={cardVariants}
                    whileHover="hover"
                    whileTap="tap"
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ 
                      delay: index * 0.1, 
                      duration: 0.4,
                      type: "spring",
                      stiffness: 300,
                      damping: 30
                    }}
                    onClick={() => handleShareAction(opt.label.toLowerCase(), opt.getUrl(shareUrl, title, description))}
                  >
                    {/* Icon and Label */}
                    <div className="flex flex-col items-center justify-center text-center">
                      <div className="mb-2 sm:mb-3" aria-hidden="true">
                        {opt.icon}
                      </div>
                      <span className="text-xs sm:text-sm font-semibold">{opt.label}</span>
                    </div>

                    {/* Hover Effect Overlay */}
                    <div className="absolute inset-0 rounded-xl transition-opacity duration-300 bg-white/10 opacity-0 group-hover:opacity-100" aria-hidden="true" />
                  </motion.a>
                ))}

                {/* Copy Link Button */}
                <motion.button
                  onClick={handleCopy}
                  className="relative group cursor-pointer transition-all duration-300 rounded-xl p-4 sm:p-6 border-2 border-gray-200 hover:border-indigo-300 bg-gradient-to-br from-gray-50 to-gray-100 hover:from-indigo-50 hover:to-blue-50 text-gray-700 hover:text-indigo-700 shadow-lg hover:shadow-xl min-h-[80px] sm:min-h-[100px] flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  tabIndex={0}
                  aria-label="Copy link to clipboard"
                  role="button"
                  variants={cardVariants}
                  whileHover="hover"
                  whileTap="tap"
                  initial={{ opacity: 0, y: 20, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ 
                    delay: shareOptions.length * 0.1, 
                    duration: 0.4,
                    type: "spring",
                    stiffness: 300,
                    damping: 30
                  }}
                >
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="mb-2 sm:mb-3" aria-hidden="true">
                      <FaLink className="w-6 h-6" />
                    </div>
                    <span className="text-xs sm:text-sm font-semibold">Copy Link</span>
                  </div>

                  {/* Hover Effect Overlay */}
                  <div className="absolute inset-0 rounded-xl transition-opacity duration-300 bg-indigo-500/5 opacity-0 group-hover:opacity-100" aria-hidden="true" />
                </motion.button>
              </div>

              {/* Share URL Display */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.4 }}
                className="bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-3 sm:p-4 mb-4"
                role="region"
                aria-label="Share URL information"
              >
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm font-medium text-gray-600 mr-3">Share URL:</span>
                  <span className="text-xs sm:text-sm text-gray-800 font-mono truncate flex-1" aria-label={`URL to share: ${shareUrl}`}>{shareUrl}</span>
                </div>
              </motion.div>

              {/* Description (if provided) */}
              {description && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.4 }}
                  className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-3 sm:p-4 mb-4"
                  role="region"
                  aria-label="Share description"
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0" aria-hidden="true">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-2 sm:ml-3">
                      <h3 className="text-xs sm:text-sm text-left font-medium text-blue-900">Description</h3>
                      <p className="text-xs sm:text-sm text-left text-blue-700 mt-1 leading-relaxed">{description}</p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Footer */}
            <div className="px-2 sm:px-6 lg:px-8 py-3 sm:py-6 bg-gray-50 border-t border-gray-100">
              <div className="flex flex-col sm:flex-row justify-end items-center space-y-2 sm:space-y-0">
                <motion.button
                  onClick={onClose}
                  variants={buttonVariants}
                  whileHover="hover"
                  whileTap="tap"
                  className="w-full sm:w-auto px-4 sm:px-6 py-3 sm:py-3 border border-gray-300 rounded-xl text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 touch-manipulation min-h-[44px] flex items-center justify-center"
                  aria-label="Close share modal"
                  tabIndex={0}
                >
                  <span className="flex items-center justify-center sm:justify-start">
                    <FaTimes className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    Close
                  </span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ShareModal; 