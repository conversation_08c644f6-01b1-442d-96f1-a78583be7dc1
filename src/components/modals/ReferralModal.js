import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { InlineLoader } from '../ui/LoadingIndicator';
import { useTranslation } from 'react-i18next';
import { cn } from '../../lib/utils';
import ReferralStats from '../referral/ReferralStats';
import { FaWhatsapp, FaFacebook, FaTwitter, FaLinkedin, FaEnvelope, FaDownload, FaStar, FaShareAlt, FaUserFriends } from 'react-icons/fa';
import { GiftIcon } from '@heroicons/react/24/outline';
import giftAPI from '../../services/giftService';
import { getCdnUrl } from '../../utils/cdnUtils';
import referralService from '../../services/referralService';
import api from '../../services/api';

const ReferralModal = ({
    isOpen = false,
    onClose,
    loading = false,
    error = null,
    onRefresh,
    stats = {}
}) => {
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState('statistics');
    const [referralLink, setReferralLink] = useState('');
    const [copied, setCopied] = useState(false);
    const [userReferralCode, setUserReferralCode] = useState(null);
    const [loadingReferral, setLoadingReferral] = useState(false);

    // Rewards tab state
    const [availableGifts, setAvailableGifts] = useState([]);
    const [loadingGifts, setLoadingGifts] = useState(false);
    const [errorGifts, setErrorGifts] = useState(null);
    const [selectedGift, setSelectedGift] = useState(null);
    const [showRedeemModal, setShowRedeemModal] = useState(false);
    const [redeemQuantity, setRedeemQuantity] = useState(1);
    const [isRedeeming, setIsRedeeming] = useState(false);

    // Add state for stats
    const [referralStats, setReferralStats] = useState({
        totalReferrals: 0,
        activeReferrals: 0,
        pointsEarned: 0,
        conversionRate: 0,
        recentActivity: []
    });
    const userPoints = referralStats.pointsEarned || stats.pointsEarned || 0;

    // Animation state for points burst/confetti
    const [pointsBurst, setPointsBurst] = useState(null); // { amount: number, key: number }
    const [showConfetti, setShowConfetti] = useState(false);
    const prevPointsRef = useRef(userPoints);

    // Animate points increase and show burst
    useEffect(() => {
        if (userPoints > prevPointsRef.current) {
            const diff = userPoints - prevPointsRef.current;
            setPointsBurst({ amount: diff, key: Date.now() });
            setShowConfetti(true);
            setTimeout(() => setPointsBurst(null), 1200);
            setTimeout(() => setShowConfetti(false), 1800);
        }
        prevPointsRef.current = userPoints;
    }, [userPoints]);

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    const tabs = [
        { id: 'statistics', label: t('referral.tabs.statistics', 'My Points'), icon: FaStar },
        { id: 'collect', label: t('referral.tabs.collect', 'Collect Points'), icon: FaDownload },
        { id: 'rewards', label: t('referral.tabs.rewards', 'Rewards'), icon: GiftIcon },
        { id: 'myreferrals', label: 'My Referrals', icon: FaUserFriends },
        { id: 'share', label: t('referral.tabs.share', 'Share'), icon: FaShareAlt },
    ];

    // Fetch referral link from backend endpoint
    useEffect(() => {
        if (isOpen) {
            const fetchReferralLink = async () => {
                setLoadingReferral(true);
                try {
                    const response = await api.get('/referrals/link');
                    if (response.data && response.data.url) {
                        setReferralLink(response.data.url);
                    }
                } catch (err) {
                    console.error('Failed to fetch referral link:', err);
                } finally {
                    setLoadingReferral(false);
                }
            };

            fetchReferralLink();
        }
    }, [isOpen]);

    // Fetch available gifts when rewards tab is active
    useEffect(() => {
        if (activeTab === 'rewards') {
            setLoadingGifts(true);
            setErrorGifts(null);
            giftAPI.getGiftItems()
                .then((response) => {
                    setAvailableGifts(response.data.gifts || []);
                })
                .catch((err) => {
                    setErrorGifts(err.message || 'Failed to load rewards');
                })
                .finally(() => setLoadingGifts(false));
        }
    }, [activeTab]);

    const [referrals, setReferrals] = useState([]);
    const [referralsLoading, setReferralsLoading] = useState(false);
    const [referralsError, setReferralsError] = useState(null);
    const [referralsPage, setReferralsPage] = useState(1);
    const [referralsTotal, setReferralsTotal] = useState(0);
    const [referralsLastPage, setReferralsLastPage] = useState(1);

    useEffect(() => {
        if (activeTab === 'myreferrals') {
            setReferralsLoading(true);
            setReferralsError(null);
            referralService.getReferredUsers(referralsPage)
                .then((resp) => {
                    if (resp.success) {
                        // resp.data is the referred_users object
                        const data = resp.data;
                        setReferrals(data.data || []);
                        setReferralsTotal(data.total || 0);
                        setReferralsLastPage(data.last_page || 1);
                    } else {
                        setReferralsError(resp.error || 'Failed to load referrals');
                    }
                })
                .catch((err) => {
                    setReferralsError(err.message || 'Failed to load referrals');
                })
                .finally(() => setReferralsLoading(false));
        }
    }, [activeTab, referralsPage]);

    // Handle redeem modal open
    const openRedeemModal = (gift) => {
        setSelectedGift(gift);
        setRedeemQuantity(1);
        setShowRedeemModal(true);
    };
    // Handle redeem modal close
    const closeRedeemModal = () => {
        setShowRedeemModal(false);
        setSelectedGift(null);
        setRedeemQuantity(1);
    };
    // Handle redeem action
    const handleRedeemGift = async () => {
        if (!selectedGift || redeemQuantity <= 0) return;
        setIsRedeeming(true);
        try {
            await giftAPI.redeemGift(selectedGift.id, redeemQuantity);
            alert(`Successfully redeemed ${redeemQuantity} ${selectedGift.name}!`);
            setShowConfetti(true);
            setTimeout(() => setShowConfetti(false), 1800);
            closeRedeemModal();
        } catch (err) {
            alert(err.message || 'Failed to redeem gift. Please check your points and try again.');
        } finally {
            setIsRedeeming(false);
        }
    };

    // Add console log to check state values
    useEffect(() => {
        console.log('Current referral link:', referralLink);
        console.log('Current user referral code:', userReferralCode);
    }, [referralLink, userReferralCode]);

    const handleCopyLink = async () => {
        try {
            await navigator.clipboard.writeText(referralLink);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy link:', err);
        }
    };

    const handleSocialShare = (platform) => {
        const shareText = 'Join me on MissionX! Use my referral link to sign up and get exclusive rewards.';
        const encodedText = encodeURIComponent(shareText);
        const encodedLink = encodeURIComponent(referralLink);

        let shareUrl = '';
        switch (platform) {
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=${encodedText}%20${encodedLink}`;
                break;
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedLink}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedLink}`;
                break;
            case 'linkedin':
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedLink}`;
                break;
            case 'email':
                shareUrl = `mailto:?subject=Join me on MissionX&body=${encodedText}%20${encodedLink}`;
                break;
            default:
                return;
        }

        window.open(shareUrl, '_blank', 'noopener,noreferrer');
    };

    const handleDownloadQR = () => {
        const canvas = document.querySelector('canvas');
        if (canvas) {
            const pngUrl = canvas.toDataURL('image/png');
            const downloadLink = document.createElement('a');
            downloadLink.href = pngUrl;
            downloadLink.download = 'missionx-referral-qr.png';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-2 sm:p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900 dark:to-gray-950 backdrop-blur-2xl border border-white/30 dark:border-gray-800 rounded-3xl shadow-2xl w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl mx-2 sm:mx-4 md:mx-0 max-h-[90vh] overflow-hidden overflow-y-auto custom-scrollbar p-2 sm:p-4 md:p-6"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-gold-200/40 to-emerald-200/40 dark:from-yellow-900/30 dark:to-emerald-900/30 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-amethyst-200/40 to-sapphire-200/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-2 sm:p-4 md:p-6 border-b border-white/20 dark:border-gray-800">
                            <div className="flex flex-col sm:flex-row items-center justify-between gap-2 sm:gap-4">
                                <div className="flex items-center gap-2 sm:gap-3">
                                    <div className="p-3 bg-gradient-to-br from-gold-400 to-emerald-400 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-gold-600 via-emerald-600 to-amethyst-600 text-left bg-clip-text text-transparent dark:from-yellow-200 dark:via-emerald-200 dark:to-purple-200 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                                            {t('referral.title', 'My Points & Referral')}
                                        </h2>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {t('referral.subtitle', 'View your referral statistics and rewards')}
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 dark:hover:bg-gray-800/40 bg-transparent shadow-lg rounded-xl transition-colors"
                                >
                                    <svg className="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            {/* Hero Banner Section */}
                            <div className="mt-4 sm:mt-6 mb-2 flex flex-col md:flex-row md:items-center gap-4 md:gap-8 items-center justify-center bg-gradient-to-r from-gold-50 via-emerald-50 to-amethyst-50 dark:from-yellow-900/20 dark:via-emerald-900/20 dark:to-purple-900/20 rounded-2xl shadow-inner px-3 sm:px-6 py-4 sm:py-5 relative overflow-visible">
                                {/* Trophy/Star Icon */}
                                <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-gradient-to-br from-gold-200 via-emerald-100 to-amethyst-100 shadow-lg mr-0 md:mr-6 mb-2 md:mb-0">
                                    <FaStar className="w-8 h-8 sm:w-10 sm:h-10 text-gold-500 drop-shadow-lg animate-pulse" />
                                </div>
                                {/* Points Counter & Level */}
                                <div className="flex flex-col items-center md:items-start">
                                    <div className="flex items-center space-x-4 relative">
                                        {/* Animated Points Counter */}
                                        <motion.span
                                            key={userPoints}
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            animate={{ scale: 1, opacity: 1 }}
                                            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                                            className="text-2xl sm:text-3xl md:text-4xl font-extrabold text-gold-600 dark:text-yellow-200 drop-shadow"
                                        >
                                            <AnimatedNumber value={userPoints} />
                                        </motion.span>
                                        {/* Points Burst Animation */}
                                        {pointsBurst && (
                                            <motion.span
                                                key={pointsBurst.key}
                                                initial={{ y: 0, opacity: 1, scale: 1 }}
                                                animate={{ y: -40, opacity: 0, scale: 1.5 }}
                                                transition={{ duration: 1.1, type: 'spring' }}
                                                className="absolute left-full ml-2 text-2xl font-bold text-emerald-500 dark:text-emerald-200 drop-shadow animate-bounce"
                                            >
                                                +{pointsBurst.amount}
                                            </motion.span>
                                        )}
                                        {/* Level Badge */}
                                        <span className="px-3 py-1 sm:px-4 sm:py-2 bg-gradient-to-r from-emerald-400 to-amethyst-400 text-white text-base sm:text-lg font-bold rounded-2xl shadow border border-white ml-2">
                                            {t('referral.level', 'Level')} {Math.floor(userPoints / 1000) + 1}
                                        </span>
                                    </div>
                                    {/* Motivational Message */}
                                    <span className="mt-2 text-emerald-700 dark:text-emerald-200 font-medium text-base sm:text-lg text-center md:text-left">
                                        {t('referral.motivation', "You're a Points Pro! Keep going!")}
                                    </span>
                                </div>
                                {/* Confetti/Fireworks Animation */}
                                {showConfetti && (
                                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
                                        <span className="animate-confetti-burst text-5xl select-none">🎊🎉✨</span>
                                    </div>
                                )}
                            </div>

                            {/* Tab Navigation */}
                            <div className="mt-4 sm:mt-6 flex flex-wrap gap-2 border-b border-white/20 dark:border-gray-800">
                                {tabs.map((tab) => {
                                    const Icon = tab.icon;
                                    return (
                                    <button
                                        key={tab.id}
                                        onClick={() => setActiveTab(tab.id)}
                                        className={cn(
                                                "px-4 py-2 text-sm font-medium rounded-t-lg transition-all duration-200 relative flex items-center gap-2",
                                            activeTab === tab.id
                                                    ? "text-gold-600 dark:text-yellow-200 bg-white/80 dark:bg-gray-900/80"
                                                    : "text-gray-600 dark:text-gray-300 bg-gray-100/50 dark:bg-gray-800/50 hover:text-emerald-600 dark:hover:text-emerald-200 hover:bg-gray-200/50 dark:hover:bg-gray-900/50"
                                        )}
                                    >
                                            <Icon className={activeTab === tab.id ? "w-4 h-4 text-gold-500 dark:text-yellow-200" : "w-4 h-4 text-gray-400 dark:text-gray-500"} />
                                        {tab.label}
                                        {activeTab === tab.id && (
                                            <motion.div
                                                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-gold-400 via-emerald-400 to-amethyst-400 dark:from-yellow-200 dark:via-emerald-200 dark:to-purple-200"
                                                layoutId="activeTab"
                                                transition={{ type: "spring", duration: 0.5 }}
                                            />
                                        )}
                                    </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-2 sm:p-4 md:p-6 dark:bg-gray-900">
                            {/* Loading State */}
                            {loading && (
                                <div className="text-center py-12">
                                    <InlineLoader size="large" color="gold" />
                                    <p className="text-gray-600 dark:text-gray-200 mt-4">{t('common.loading', 'Loading...')}</p>
                                </div>
                            )}

                            {/* Error State */}
                            {error && (
                                <div className="text-center py-12">
                                    <div className="p-6 bg-gold-50 dark:bg-yellow-900/40 rounded-2xl border border-gold-100 dark:border-yellow-700 max-w-md mx-auto">
                                        <svg className="w-12 h-12 text-gold-400 dark:text-yellow-200 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-gold-600 dark:text-yellow-200 font-medium mb-3">{error}</p>
                                        {onRefresh && (
                                            <button
                                                onClick={onRefresh}
                                                className="px-4 py-2 bg-gold-500 text-white rounded-lg hover:bg-gold-600 dark:bg-yellow-700 dark:hover:bg-yellow-800 transition-colors"
                                            >
                                                {t('common.retry', 'Retry')}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Tab Content */}
                            <AnimatePresence mode="wait">
                                <motion.div
                                    key={activeTab}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -20 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    {activeTab === 'statistics' && (
                                        <ReferralStats
                                            loading={loading}
                                            error={error}
                                            stats={referralStats}
                                        />
                                    )}
                                    {activeTab === 'collect' && (
                                        <div className="max-w-xl mx-auto">
                                            <div className="flex flex-col sm:flex-row items-center mb-4 sm:mb-6 gap-2 sm:gap-4">
                                                <FaDownload className="w-6 h-6 sm:w-8 sm:h-8 text-emerald-400 animate-bounce" />
                                                <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-emerald-700">Collect Points</h3>
                                            </div>
                                            <ul className="space-y-2 sm:space-y-4">
                                                {/* Missions/Challenges List */}
                                                {[
                                                    { desc: 'Referral Profile Completion', points: 20 },
                                                    { desc: 'Referral Order Completion', points: 15 },
                                                    { desc: 'Referral Mission Completion', points: 15 },
                                                    { desc: 'Referral Talent Certified', points: 30 },
                                                    { desc: 'Referral Registration', points: 30 },
                                                    { desc: 'Referral Top-up', points: 5 },
                                                ].map((item, idx) => (
                                                    <li key={item.desc} className="flex flex-col sm:flex-row items-center justify-between bg-white/70 rounded-xl shadow border border-emerald-100 px-3 sm:px-5 py-3 sm:py-4 gap-2 sm:gap-0">
                                                        <div className="flex items-center gap-2 sm:gap-3">
                                                            <span className="inline-flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gradient-to-br from-gold-100 via-emerald-100 to-amethyst-100">
                                                                <FaStar className="w-4 h-4 sm:w-5 sm:h-5 text-gold-400" />
                                                            </span>
                                                            <span className="text-sm sm:text-base font-medium text-gray-800">{item.desc}</span>
                                                </div>
                                                        <div className="flex items-center gap-1 sm:gap-2">
                                                            <span className="px-2 sm:px-3 py-1 rounded-full bg-gold-100 text-gold-700 font-semibold text-xs sm:text-sm border border-gold-200">+{item.points} pts</span>
                                                            {/* Placeholder for progress/checkmark */}
                                                            <span className="w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 border-gray-300 flex items-center justify-center bg-gray-100">
                                                                <svg className="w-3 h-3 sm:w-4 sm:h-4 text-gray-300" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                                                            </span>
                                            </div>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                    {activeTab === 'rewards' && (
                                        <div className="space-y-8">
                                            {/* Rewards Grid */}
                                                    <div>
                                                <h4 className="text-lg font-semibold text-gray-900 mb-4">{t('referral.rewards.redeemGifts', 'Redeem Gifts from Points!')}</h4>
                                                {loadingGifts ? (
                                                    <div className="flex items-center justify-center py-8">
                                                        <InlineLoader size="large" color="gold" />
                                                    </div>
                                                ) : errorGifts ? (
                                                    <div className="text-center py-8">
                                                        <p className="text-red-500">{errorGifts}</p>
                                                    </div>
                                                ) : (
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
                                                        {availableGifts.map((gift) => {
                                                            const pointsPrice = gift.redeem_point || gift.points_price || gift.credit_price || 0;
                                                            const imageUrl = gift.image_url || gift.icon_path;
                                                            return (
                                                                <div key={gift.id} className={`relative group bg-white/80 rounded-2xl border border-gold-100 shadow-lg p-4 flex flex-col items-center transition-all duration-300 hover:shadow-xl ${selectedGift && selectedGift.id === gift.id ? 'ring-2 ring-gold-400' : ''}`}>
                                                                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-gold-100 via-emerald-100 to-amethyst-100 flex items-center justify-center mb-3 overflow-hidden">
                                                                        {imageUrl ? (
                                                                            <img src={getCdnUrl(imageUrl)} alt={gift.name} className="w-full h-full object-cover" />
                                                                        ) : (
                                                                            <GiftIcon className="w-10 h-10 text-gold-400" />
                                                                        )}
                                                                    </div>
                                                                    <div className="text-center">
                                                                        <h5 className="font-bold text-gray-800 mb-1 truncate">{gift.name}</h5>
                                                                        <div className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 rounded-full text-sm font-semibold mb-2">{pointsPrice} pts</div>
                                                                    </div>
                                                                    <button
                                                                        className={`mt-2 w-full px-4 py-2 rounded-lg font-semibold transition-all duration-200 ${userPoints >= pointsPrice ? 'bg-gradient-to-r from-gold-500 to-emerald-500 text-white hover:from-gold-600 hover:to-emerald-600' : 'bg-gray-200 text-gray-400 cursor-not-allowed'}`}
                                                                        disabled={userPoints < pointsPrice}
                                                                        onClick={() => openRedeemModal({ ...gift, points_price: pointsPrice, image_url: imageUrl })}
                                                                    >
                                                                        {userPoints >= pointsPrice ? t('referral.rewards.redeem', 'Redeem') : t('referral.rewards.notEnough', 'Not enough points')}
                                                                    </button>
                                                                </div>
                                                            );
                                                        })}
                                                </div>
                                                )}
                                            </div>

                                            {/* Redeem Modal */}
                                            <AnimatePresence>
                                                {showRedeemModal && selectedGift && (
                                                    <motion.div
                                                        initial={{ opacity: 0 }}
                                                        animate={{ opacity: 1 }}
                                                        exit={{ opacity: 0 }}
                                                        className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
                                                        onClick={closeRedeemModal}
                                                    >
                                                        <motion.div
                                                            initial={{ scale: 0.9, opacity: 0 }}
                                                            animate={{ scale: 1, opacity: 1 }}
                                                            exit={{ scale: 0.9, opacity: 0 }}
                                                            className="bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl max-w-md w-full p-6 border border-white/20"
                                                            onClick={(e) => e.stopPropagation()}
                                                        >
                                                            <h3 className="text-xl font-bold text-gray-800 mb-4">{t('referral.rewards.redeemGift', 'Redeem Gift')}</h3>
                                                            <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-xl">
                                                                <div className="w-16 h-16 bg-gradient-to-br from-gold-100 to-emerald-100 rounded-xl flex items-center justify-center">
                                                                    {selectedGift.image_url ? (
                                                                        <img src={getCdnUrl(selectedGift.image_url)} alt={selectedGift.name} className="w-full h-full object-cover rounded-xl" />
                                                                    ) : (
                                                                        <GiftIcon className="w-8 h-8 text-gold-400" />
                                                                    )}
                                                                </div>
                                                                <div className="flex-1">
                                                                    <h4 className="font-semibold text-gray-800">{selectedGift.name}</h4>
                                                                    <div className="text-sm text-gray-600">{selectedGift.points_price} points each</div>
                                                                </div>
                                                            </div>
                                                            {/* Quantity Selection */}
                                                            <div className="mb-4">
                                                                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                                                                <input
                                                                    type="number"
                                                                    id="quantity"
                                                                    min="1"
                                                                    max={selectedGift.max_quantity || 10}
                                                                    value={redeemQuantity}
                                                                    onChange={(e) => setRedeemQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                                                                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
                                                                />
                                                            </div>
                                                            {/* Total Cost */}
                                                            <div className="mb-6 p-3 bg-gold-50 rounded-lg">
                                                                <div className="flex justify-between items-center">
                                                                    <span className="font-medium text-gray-700">Total Cost:</span>
                                                                    <span className="font-bold text-gold-600">{(selectedGift.points_price || 0) * redeemQuantity} points</span>
                                                                </div>
                                                            </div>
                                                            {/* Action Buttons */}
                                                            <div className="flex space-x-3">
                                                                <button
                                                                    onClick={closeRedeemModal}
                                                                    disabled={isRedeeming}
                                                                    className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
                                                                >
                                                                    {t('common.cancel', 'Cancel')}
                                                                </button>
                                                                <motion.button
                                                                    onClick={handleRedeemGift}
                                                                    disabled={isRedeeming || userPoints < (selectedGift.points_price || 0) * redeemQuantity}
                                                                    className="flex-1 px-4 py-2 bg-gradient-to-r from-gold-500 to-emerald-600 text-white rounded-lg hover:from-gold-600 hover:to-emerald-700 transition-all duration-300 disabled:opacity-50"
                                                                    whileHover={!isRedeeming ? { scale: 1.02 } : {}}
                                                                    whileTap={!isRedeeming ? { scale: 0.98 } : {}}
                                                                >
                                                                    {isRedeeming ? t('referral.rewards.redeeming', 'Redeeming...') : t('referral.rewards.redeem', 'Redeem')}
                                                                </motion.button>
                                                            </div>
                                                        </motion.div>
                                                    </motion.div>
                                                )}
                                            </AnimatePresence>

                                            {/* Reward History (already present, styled) */}
                                            <div className="bg-white/50 backdrop-blur-sm rounded-xl p-6 border border-gray-100 mt-8">
                                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                                    {t('referral.rewards.rewardHistory', 'Reward History')}
                                                </h3>
                                                {loading ? (
                                                    <div className="flex items-center justify-center py-8">
                                                        <InlineLoader size="large" color="gold" />
                                                    </div>
                                                ) : stats.recentActivity?.length > 0 ? (
                                                    <div className="space-y-4">
                                                        {stats.recentActivity.map((activity, index) => (
                                                            <motion.div
                                                                key={activity.id}
                                                                initial={{ opacity: 0, y: 20 }}
                                                                animate={{ opacity: 1, y: 0 }}
                                                                transition={{ delay: index * 0.1 }}
                                                                className="p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/50 hover:border-emerald-200 transition-all duration-300"
                                                            >
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center space-x-3">
                                                                        <div className="p-2 bg-gold-100 rounded-lg">
                                                                            <GiftIcon className="w-5 h-5 text-gold-600" />
                                                                        </div>
                                                                        <div>
                                                                            <p className="font-medium text-gray-800">{activity.description}</p>
                                                                            <p className="text-sm text-gray-500">{activity.date}</p>
                                                                        </div>
                                                                    </div>
                                                                    <span className="text-sm font-medium text-gold-600">+{activity.points} points</span>
                                                                </div>
                                                            </motion.div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <div className="text-center py-8 bg-gray-50 rounded-xl">
                                                        <p className="text-gray-500">{t('referral.rewards.noRewards', 'No rewards earned yet')}</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                    {activeTab === 'myreferrals' && (
                                        <div className="py-0">
                                            <h3 className="text-2xl font-bold text-gold-700 mb-6 text-center">My Referrals</h3>
                                            {referralsLoading ? (
                                                <div className="flex justify-center py-12"><InlineLoader size="large" color="gold" /></div>
                                            ) : referralsError ? (
                                                <div className="text-center text-red-500 py-8">{referralsError}</div>
                                            ) : referrals.length === 0 ? (
                                                <div className="text-center py-12 text-gray-500">
                                                    <div className="text-5xl mb-4">🤝</div>
                                                    <div>No referrals yet. Invite friends to earn rewards!</div>
                                                </div>
                                            ) : (
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                                                    {referrals.map((user) => {
                                                        const completed = user.referrer_Actions.filter(a => a.is_Completed);
                                                        const totalPoints = completed.reduce((sum, a) => sum + a.points, 0);
                                                        const totalPossible = user.referrer_Actions.reduce((sum, a) => sum + a.points, 0);
                                                        const progress = totalPossible ? Math.round((totalPoints / totalPossible) * 100) : 0;
                                                        return (
                                                            <div key={user.id} className="bg-white/80 rounded-2xl shadow-lg border border-gold-100 p-6 flex flex-col gap-3 relative">
                                                                <div className="flex items-center gap-4 mb-2">
                                                                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-gold-100 to-emerald-100 flex items-center justify-center overflow-hidden border-2 border-gold-200">
                                                                        {user.profile_picture ? (
                                                                            <img src={getCdnUrl(user.profile_picture)} alt={user.name} className="w-full h-full object-cover" />
                                                                        ) : (
                                                                            <span className="text-2xl text-gold-500 font-bold">{user.name?.[0] || '?'}</span>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex-1">
                                                                        <div className="font-bold text-lg text-gray-800">{user.name} <span className="text-gray-400 font-normal">({user.nickname})</span></div>
                                                                        <div className="text-xs text-gray-500">UID: <span className="font-mono bg-gray-100 px-2 py-0.5 rounded">{user.uid}</span></div>
                                                                        <div className="text-xs text-gray-400">Referred on: {new Date(user.referred_at).toLocaleDateString()}</div>
                                                                    </div>
                                                                </div>
                                                                {/* Actions Checklist */}
                                                                <div className="flex flex-wrap gap-2 mb-2">
                                                                    {user.referrer_Actions.map((action) => (
                                                                        <div key={action.action} className={`flex items-center gap-1 px-3 py-1 rounded-full border text-xs font-semibold ${action.is_Completed ? 'bg-emerald-100 border-emerald-300 text-emerald-700' : 'bg-gray-100 border-gray-300 text-gray-400'}`}>
                                                                            {action.is_Completed ? (
                                                                                <span className="text-emerald-500">✔</span>
                                                                            ) : (
                                                                                <span className="text-gray-300">○</span>
                                                                            )}
                                                                            {action.action_name}
                                                                            <span className="ml-1 px-2 py-0.5 rounded bg-gold-100 text-gold-700 font-bold">+{action.points}</span>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                                {/* Progress Bar & Points */}
                                                                <div className="flex items-center gap-3 mt-2">
                                                                    <div className="flex-1 h-3 bg-gray-200 rounded-full overflow-hidden">
                                                                        <div className="h-full bg-gradient-to-r from-gold-400 to-emerald-400" style={{ width: `${progress}%` }} />
                                                                    </div>
                                                                    <div className="text-sm font-bold text-gold-700">{totalPoints} / {totalPossible} pts</div>
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            )}
                                            {/* Pagination Controls */}
                                            {referralsLastPage > 1 && (
                                                <div className="flex justify-center mt-6 gap-2 flex-wrap">
                                                    <button
                                                        onClick={() => setReferralsPage(p => Math.max(1, p - 1))}
                                                        disabled={referralsPage === 1}
                                                        className="px-3 py-1 rounded bg-gray-100 text-gray-500 font-bold disabled:opacity-50"
                                                    >Prev</button>
                                                    <span className="px-3 py-1 font-bold text-gold-700">Page {referralsPage} / {referralsLastPage}</span>
                                                    <button
                                                        onClick={() => setReferralsPage(p => Math.min(referralsLastPage, p + 1))}
                                                        disabled={referralsPage === referralsLastPage}
                                                        className="px-3 py-1 rounded bg-gray-100 text-gray-500 font-bold disabled:opacity-50"
                                                    >Next</button>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                    {activeTab === 'share' && (
                                        <div className="flex flex-col items-center justify-center py-6 sm:py-10 space-y-6 sm:space-y-8">
                                            {/* Referral Link Section */}
                                            <div className="relative flex flex-col items-center w-full">
                                                <div className="w-full flex flex-col sm:flex-row items-center px-4 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-gold-400 via-gold-200 to-gold-500 rounded-2xl shadow-xl border-4 border-gold-300 space-y-2 sm:space-y-0 sm:space-x-4 animate-pulse">
                                                    <span className="flex-1 text-sm sm:text-base font-bold text-gold-900 tracking-widest drop-shadow break-all text-center sm:text-left">{referralLink || '------'}</span>
                                                    <button
                                                        onClick={handleCopyLink}
                                                        className="mt-2 sm:mt-0 px-3 py-1 bg-white/80 hover:bg-gold-100 text-gold-700 font-bold rounded-lg shadow transition-colors border border-gold-200"
                                                        aria-label="Copy referral link"
                                                    >
                                                        {copied ? t('referral.share.copied', 'Copied!') : t('referral.share.copy', 'Copy')}
                                                    </button>
                                                </div>
                                                {/* Confetti burst animation on copy */}
                                                {copied && (
                                                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                                        <span className="animate-confetti-burst text-3xl">🎉</span>
                                                    </div>
                                                )}
                                            </div>
                                            {/* Social Share Buttons */}
                                            <div className="flex flex-wrap gap-2 sm:gap-4 justify-center w-full">
                                                <button
                                                    onClick={() => handleSocialShare('whatsapp')}
                                                    className="flex items-center gap-2 px-4 sm:px-5 py-2 rounded-lg font-bold bg-green-500 hover:bg-green-600 text-white shadow transition-colors text-xs sm:text-sm"
                                                >
                                                    <FaWhatsapp className="w-4 h-4 sm:w-5 sm:h-5" /> WhatsApp
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('facebook')}
                                                    className="flex items-center gap-2 px-4 sm:px-5 py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white shadow transition-colors text-xs sm:text-sm"
                                                >
                                                    <FaFacebook className="w-4 h-4 sm:w-5 sm:h-5" /> Facebook
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('twitter')}
                                                    className="flex items-center gap-2 px-4 sm:px-5 py-2 rounded-lg font-bold bg-sky-400 hover:bg-sky-500 text-white shadow transition-colors text-xs sm:text-sm"
                                                >
                                                    <FaTwitter className="w-4 h-4 sm:w-5 sm:h-5" /> Twitter
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('linkedin')}
                                                    className="flex items-center gap-2 px-4 sm:px-5 py-2 rounded-lg font-bold bg-blue-800 hover:bg-blue-900 text-white shadow transition-colors text-xs sm:text-sm"
                                                >
                                                    <FaLinkedin className="w-4 h-4 sm:w-5 sm:h-5" /> LinkedIn
                                                </button>
                                                <button
                                                    onClick={() => handleSocialShare('email')}
                                                    className="flex items-center gap-2 px-4 sm:px-5 py-2 rounded-lg font-bold bg-gradient-to-r from-amber-400 to-pink-400 hover:from-amber-500 hover:to-pink-500 text-white shadow transition-colors text-xs sm:text-sm"
                                                >
                                                    <FaEnvelope className="w-4 h-4 sm:w-5 sm:h-5" /> Email
                                                </button>
                                            </div>
                                            {/* QR Code section is now disabled */}
                                        </div>
                                    )}
                                </motion.div>
                            </AnimatePresence>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

// AnimatedNumber component for smooth number transitions
function AnimatedNumber({ value }) {
    const ref = useRef();
    const [display, setDisplay] = useState(value);
    useEffect(() => {
        let frame;
        let start = ref.current || value;
        let end = value;
        let duration = 800;
        let startTime = null;
        function animate(ts) {
            if (!startTime) startTime = ts;
            const progress = Math.min((ts - startTime) / duration, 1);
            setDisplay(Math.floor(start + (end - start) * progress));
            if (progress < 1) {
                frame = requestAnimationFrame(animate);
            } else {
                setDisplay(end);
            }
        }
        frame = requestAnimationFrame(animate);
        ref.current = value;
        return () => cancelAnimationFrame(frame);
    }, [value]);
    return <span>{display}</span>;
}

export default ReferralModal; 

// Custom scrollbar styles
// Place this outside the component to avoid linter errors
if (typeof window !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
      background: transparent;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #fcd34d 40%, #d1fae5 100%);
      border-radius: 8px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: transparent;
    }
    .custom-scrollbar {
      scrollbar-width: thin;
      scrollbar-color: #fcd34d #ffffff00;
    }
  `;
  document.head.appendChild(style);
} 