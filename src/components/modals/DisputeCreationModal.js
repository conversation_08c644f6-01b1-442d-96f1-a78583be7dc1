/**
 * Dispute Creation Modal Component
 *
 * A comprehensive modal for creating new disputes with:
 * - Dispute type selection
 * - Description input with character limit
 * - Media upload (images and video)
 * - Form validation and error handling
 * - Glassmorphism styling
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import disputeService from '../../services/disputeService';
import { InlineLoader } from '../ui/LoadingIndicator';

const DisputeCreationModal = ({
    isOpen = false,
    onClose,
    onSuccess,
    orderId = null,
    orderType = 'immediate'
}) => {
    const [disputeTypes, setDisputeTypes] = useState([]);
    const [loadingTypes, setLoadingTypes] = useState(false);
    const [userOrders, setUserOrders] = useState([]);
    const [loadingOrders, setLoadingOrders] = useState(false);
    const [formData, setFormData] = useState({
        order_id: orderId || '',
        order_type: orderType,
        dispute_type_id: '',
        description: '',
        images: [],
        video: null
    });
    const [errors, setErrors] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [dragOver, setDragOver] = useState(false);

    const fileInputRef = useRef(null);
    const videoInputRef = useRef(null);

    const maxImages = 3;
    const maxDescriptionLength = 1000;

    // Fetch dispute types and orders when modal opens
    useEffect(() => {
        if (isOpen) {
            fetchDisputeTypes();
            if (!orderId) {
                fetchUserOrders();
            }
            resetForm();
        }
    }, [isOpen, orderId]);

    const fetchDisputeTypes = async () => {
        setLoadingTypes(true);
        try {
            const response = await disputeService.getDisputeTypes();
            if (response.success && response.data) {
                // Ensure we always set an array
                const types = Array.isArray(response.data) ? response.data : [];
                setDisputeTypes(types);
            } else {
                setErrors({ general: response.error || 'Failed to load dispute types' });
                setDisputeTypes([]); // Set empty array on error
            }
        } catch (error) {
            console.error('Error fetching dispute types:', error);
            setErrors({ general: 'Failed to load dispute types' });
            setDisputeTypes([]); // Set empty array on error
        } finally {
            setLoadingTypes(false);
        }
    };

    const fetchUserOrders = async () => {
        setLoadingOrders(true);
        try {
            // Fetch user orders that can have disputes (accepted but not completed)
            const response = await fetch(`${process.env.REACT_APP_API_URL}/orders?status=accepted`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                // Ensure we always set an array, even if data is null or undefined
                let orders = [];
                if (data) {
                    orders = Array.isArray(data) ? data : (Array.isArray(data.data) ? data.data : []);
                }
                setUserOrders(orders);
            } else {
                setErrors({ general: 'Failed to load orders' });
                setUserOrders([]); // Set empty array on error
            }
        } catch (error) {
            console.error('Error fetching user orders:', error);
            setErrors({ general: 'Failed to load orders' });
            setUserOrders([]); // Set empty array on error
        } finally {
            setLoadingOrders(false);
        }
    };

    const resetForm = () => {
        setFormData({
            order_id: orderId || '',
            order_type: orderType,
            dispute_type_id: '',
            description: '',
            images: [],
            video: null
        });
        setErrors({});
        setIsSubmitting(false);
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.order_id) {
            newErrors.order_id = 'Please select an order';
        }

        if (!formData.dispute_type_id) {
            newErrors.dispute_type_id = 'Please select a dispute type';
        }

        if (!formData.description.trim()) {
            newErrors.description = 'Please provide a description';
        } else if (formData.description.length > maxDescriptionLength) {
            newErrors.description = `Description must be ${maxDescriptionLength} characters or less`;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    const handleImageUpload = (files) => {
        const fileArray = Array.from(files);
        const validImages = [];
        const newErrors = {};

        fileArray.forEach((file, index) => {
            const validation = disputeService.validateFile(file, 'image');
            if (validation.valid) {
                if (formData.images.length + validImages.length < maxImages) {
                    validImages.push(file);
                }
            } else {
                newErrors[`image_${index}`] = validation.error;
            }
        });

        if (formData.images.length + validImages.length > maxImages) {
            newErrors.images = `Maximum ${maxImages} images allowed`;
        } else {
            setFormData(prev => ({
                ...prev,
                images: [...prev.images, ...validImages]
            }));
        }

        if (Object.keys(newErrors).length > 0) {
            setErrors(prev => ({ ...prev, ...newErrors }));
        }
    };

    const handleVideoUpload = (file) => {
        const validation = disputeService.validateFile(file, 'video');
        if (validation.valid) {
            setFormData(prev => ({
                ...prev,
                video: file
            }));
            setErrors(prev => ({ ...prev, video: null }));
        } else {
            setErrors(prev => ({ ...prev, video: validation.error }));
        }
    };

    const removeImage = (index) => {
        setFormData(prev => ({
            ...prev,
            images: prev.images.filter((_, i) => i !== index)
        }));
    };

    const removeVideo = () => {
        setFormData(prev => ({
            ...prev,
            video: null
        }));
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setDragOver(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setDragOver(false);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setDragOver(false);

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsSubmitting(true);
        setErrors({});

        try {
            const selectedOrderId = formData.order_id || orderId;
            const selectedOrderType = formData.order_type || orderType;

            const response = await disputeService.createDispute(selectedOrderId, formData, selectedOrderType);

            if (response.success) {
                if (onSuccess) {
                    onSuccess(response.data);
                }
                onClose();
            } else {
                setErrors({ general: response.error });
            }
        } catch (error) {
            setErrors({ general: 'Failed to create dispute. Please try again.' });
        } finally {
            setIsSubmitting(false);
        }
    };

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-red-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-6 border-b border-white/20">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-red-700 to-orange-700 bg-clip-text text-transparent">
                                            Create Dispute
                                        </h2>
                                        <p className="text-gray-600 text-sm">
                                            Report an issue with your order
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 rounded-xl transition-colors"
                                    disabled={isSubmitting}
                                >
                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                            {/* General Error */}
                            {errors.general && (
                                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-red-700 font-medium">{errors.general}</p>
                                    </div>
                                </div>
                            )}

                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Order Selection (only if no orderId provided) */}
                                {!orderId && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Order *
                                        </label>
                                        {loadingOrders ? (
                                            <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-xl">
                                                <InlineLoader size="small" color="gray" />
                                                <span className="text-gray-600">Loading orders...</span>
                                            </div>
                                        ) : (
                                            <select
                                                value={formData.order_id}
                                                onChange={(e) => handleInputChange('order_id', e.target.value)}
                                                className={`w-full px-4 py-3 bg-white/70 border rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-400 transition-colors ${
                                                    errors.order_id ? 'border-red-300 bg-red-50/50' : 'border-gray-300'
                                                }`}
                                                disabled={isSubmitting}
                                            >
                                                <option value="">Select an order to dispute</option>
                                                {Array.isArray(userOrders) && userOrders.map((order) => (
                                                    <option key={order.id} value={order.id}>
                                                        Order #{order.id} - {order.service?.name || 'Service'}
                                                        {order.total_amount && ` ($${order.total_amount})`}
                                                    </option>
                                                ))}
                                            </select>
                                        )}
                                        {errors.order_id && (
                                            <p className="mt-1 text-sm text-red-600">{errors.order_id}</p>
                                        )}
                                    </div>
                                )}

                                {/* Dispute Type Selection */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Dispute Type *
                                    </label>
                                    {loadingTypes ? (
                                        <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-xl">
                                            <InlineLoader size="small" color="gray" />
                                            <span className="text-gray-600">Loading dispute types...</span>
                                        </div>
                                    ) : (
                                        <select
                                            value={formData.dispute_type_id}
                                            onChange={(e) => handleInputChange('dispute_type_id', e.target.value)}
                                            className={`w-full px-4 py-3 bg-white/70 border rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-400 transition-colors ${
                                                errors.dispute_type_id ? 'border-red-300 bg-red-50/50' : 'border-gray-300'
                                            }`}
                                            disabled={isSubmitting}
                                        >
                                            <option value="">Select a dispute type</option>
                                            {Array.isArray(disputeTypes) && disputeTypes.map((type) => (
                                                <option key={type.id} value={type.id}>
                                                    {type.name}
                                                </option>
                                            ))}
                                        </select>
                                    )}
                                    {errors.dispute_type_id && (
                                        <p className="mt-1 text-sm text-red-600">{errors.dispute_type_id}</p>
                                    )}
                                </div>

                                {/* Description */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Description *
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        placeholder="Please describe the issue in detail..."
                                        rows={4}
                                        maxLength={maxDescriptionLength}
                                        className={`w-full px-4 py-3 bg-white/70 border rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:border-red-400 transition-colors resize-none ${
                                            errors.description ? 'border-red-300 bg-red-50/50' : 'border-gray-300'
                                        }`}
                                        disabled={isSubmitting}
                                    />
                                    <div className="flex justify-between items-center mt-1">
                                        {errors.description ? (
                                            <p className="text-sm text-red-600">{errors.description}</p>
                                        ) : (
                                            <div />
                                        )}
                                        <p className="text-sm text-gray-500">
                                            {formData.description.length}/{maxDescriptionLength}
                                        </p>
                                    </div>
                                </div>

                                {/* Image Upload */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Images (Optional - Max {maxImages})
                                    </label>
                                    <div
                                        className={`border-2 border-dashed rounded-xl p-6 text-center transition-colors ${
                                            dragOver ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-red-300'
                                        }`}
                                        onDragOver={handleDragOver}
                                        onDragLeave={handleDragLeave}
                                        onDrop={handleDrop}
                                    >
                                        <input
                                            ref={fileInputRef}
                                            type="file"
                                            multiple
                                            accept="image/jpeg,image/jpg,image/png,image/heic,image/heif"
                                            onChange={(e) => handleImageUpload(e.target.files)}
                                            className="hidden"
                                            disabled={isSubmitting || (Array.isArray(formData.images) && formData.images.length >= maxImages)}
                                        />

                                        {Array.isArray(formData.images) && formData.images.length < maxImages && (
                                            <div>
                                                <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p className="text-gray-600 mb-2">
                                                    Drag and drop images here, or{' '}
                                                    <button
                                                        type="button"
                                                        onClick={() => fileInputRef.current?.click()}
                                                        className="text-red-600 hover:text-red-700 font-medium"
                                                        disabled={isSubmitting}
                                                    >
                                                        browse
                                                    </button>
                                                </p>
                                                <p className="text-sm text-gray-500">
                                                    JPEG, PNG, JPG, HEIC, HEIF (Max 10MB each)
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {/* Image Previews */}
                                    {Array.isArray(formData.images) && formData.images.length > 0 && (
                                        <div className="mt-4 grid grid-cols-3 gap-4">
                                            {formData.images.map((image, index) => (
                                                <div key={index} className="relative group">
                                                    <img
                                                        src={URL.createObjectURL(image)}
                                                        alt={`Preview ${index + 1}`}
                                                        className="w-full h-24 object-cover rounded-lg border border-gray-200"
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => removeImage(index)}
                                                        className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                                                        disabled={isSubmitting}
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {errors.images && (
                                        <p className="mt-1 text-sm text-red-600">{errors.images}</p>
                                    )}
                                </div>

                                {/* Video Upload */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Video (Optional)
                                    </label>

                                    {!formData.video ? (
                                        <div>
                                            <input
                                                ref={videoInputRef}
                                                type="file"
                                                accept="video/mp4,video/mov,video/avi,video/flv"
                                                onChange={(e) => e.target.files[0] && handleVideoUpload(e.target.files[0])}
                                                className="hidden"
                                                disabled={isSubmitting}
                                            />
                                            <button
                                                type="button"
                                                onClick={() => videoInputRef.current?.click()}
                                                className="w-full p-4 border-2 border-dashed border-gray-300 rounded-xl hover:border-red-300 transition-colors text-gray-600 hover:text-red-600"
                                                disabled={isSubmitting}
                                            >
                                                <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                </svg>
                                                <p>Click to upload video</p>
                                                <p className="text-sm text-gray-500 mt-1">
                                                    MP4, MOV, AVI, FLV (Max 20MB)
                                                </p>
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="p-4 bg-gray-50 rounded-xl border border-gray-200">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-3">
                                                    <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                    </svg>
                                                    <div>
                                                        <p className="font-medium text-gray-800">{formData.video.name}</p>
                                                        <p className="text-sm text-gray-500">
                                                            {(formData.video.size / (1024 * 1024)).toFixed(2)} MB
                                                        </p>
                                                    </div>
                                                </div>
                                                <button
                                                    type="button"
                                                    onClick={removeVideo}
                                                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                                    disabled={isSubmitting}
                                                >
                                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    )}

                                    {errors.video && (
                                        <p className="mt-1 text-sm text-red-600">{errors.video}</p>
                                    )}
                                </div>

                                {/* Submit Button */}
                                <div className="flex justify-end space-x-4 pt-4">
                                    <button
                                        type="button"
                                        onClick={onClose}
                                        className="px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 transition-colors"
                                        disabled={isSubmitting}
                                    >
                                        Cancel
                                    </button>
                                    <motion.button
                                        type="submit"
                                        className="px-8 py-3 bg-gradient-to-r from-red-500 to-orange-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-orange-700 transition-all duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                                        disabled={isSubmitting}
                                        whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                                        whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                                    >
                                        {isSubmitting && <InlineLoader size="small" color="white" />}
                                        <span>{isSubmitting ? 'Creating Dispute...' : 'Create Dispute'}</span>
                                    </motion.button>
                                </div>
                            </form>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default DisputeCreationModal;
