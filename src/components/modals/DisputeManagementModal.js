/**
 * Dispute Management Modal Component
 * 
 * A comprehensive modal for viewing and managing user disputes with:
 * - Dispute history list with filtering
 * - Status badges and visual indicators
 * - Pagination support
 * - Glassmorphism styling
 * - Integration with dispute creation
 */

import React, { useState, useEffect, useMemo } from 'react';
import { getCdnUrl } from '../../utils/cdnUtils';
import { motion, AnimatePresence } from 'framer-motion';
import disputeService from '../../services/disputeService';
import { InlineLoader } from '../ui/LoadingIndicator';
import useTranslation from '../../hooks/useTranslation';

const DisputeManagementModal = ({
    isOpen = false,
    onClose,
    disputes = [],
    loading = false,
    error = null,
    onRefresh
}) => {
    const { t } = useTranslation('profile');
    // Ensure disputes is always an array and memoize to avoid reference changes
    const safeDisputes = useMemo(() => (Array.isArray(disputes) ? disputes : []), [disputes]);
    const [filteredDisputes, setFilteredDisputes] = useState(safeDisputes);
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedDispute, setSelectedDispute] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Filter disputes based on status
    useEffect(() => {
        if (statusFilter === 'all') {
            setFilteredDisputes(safeDisputes);
        } else {
            setFilteredDisputes(safeDisputes.filter(dispute => dispute.status === statusFilter));
        }
        setCurrentPage(1); // Reset to first page when filter or data changes
    }, [safeDisputes, statusFilter]);

    // Pagination calculations
    const totalPages = Math.ceil(filteredDisputes.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentDisputes = Array.isArray(filteredDisputes) ? 
        filteredDisputes.slice(startIndex, endIndex) : [];

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    const handleStatusFilterChange = (status) => {
        setStatusFilter(status);
    };

    const handleDisputeClick = (dispute) => {
        setSelectedDispute(dispute);
    };

    const handleCloseDetailsModal = () => {
        setSelectedDispute(null);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusBadge = (status) => {
        return disputeService.getStatusBadge(status);
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {/* Main Modal */}
            {isOpen && (
                <motion.div
                    key="main-modal"
                    className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-800/90 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-3xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 dark:from-red-900/20 dark:to-orange-900/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-red-400/20 to-pink-400/20 dark:from-red-900/20 dark:to-pink-900/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-700">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-red-500 to-orange-600 dark:from-red-700 dark:to-orange-700 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-red-700 to-orange-700 dark:from-red-300 dark:to-orange-300 bg-clip-text text-transparent">
                                            {t('modals.disputes.title')}
                                        </h2>
                                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                                            {t('modals.disputes.description')}
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 dark:hover:bg-gray-800 rounded-xl transition-colors"
                                >
                                    <svg className="w-6 h-6 text-gray-600 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)] bg-white dark:bg-gray-900">
                            {/* Filter and Actions Bar */}
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                                {/* Status Filter */}
                                <div className="flex flex-wrap gap-2">
                                    {['all', 'submitted', 'in_review', 'resolved', 'rejected'].map((status) => (
                                        <button
                                            key={status}
                                            onClick={() => handleStatusFilterChange(status)}
                                            className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                                                statusFilter === status
                                                    ? 'bg-gradient-to-r from-red-500 to-orange-600 dark:from-red-700 dark:to-orange-700 text-white shadow-lg'
                                                    : 'bg-white/50 dark:bg-gray-900/60 text-gray-700 dark:text-gray-200 hover:bg-white/70 dark:hover:bg-gray-800 border border-gray-200 dark:border-gray-700'
                                            }`}
                                        >
                                            {status === 'all' ? t('modals.disputes.status.all') : t(`modals.disputes.status.${status}`)}
                                        </button>
                                    ))}
                                </div>

                            </div>

                            {/* Loading State */}
                            {loading && (
                                <div className="text-center py-12">
                                    <InlineLoader size="large" color="red" />
                                    <p className="text-gray-600 dark:text-gray-300 mt-4">Loading disputes...</p>
                                </div>
                            )}

                            {/* Error State */}
                            {error && !loading && (
                                <div className="text-center py-12">
                                    <div className="p-6 bg-red-50 dark:bg-red-900 rounded-2xl border border-red-100 dark:border-red-700 max-w-md mx-auto">
                                        <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-red-600 dark:text-red-300 font-medium mb-3">{error}</p>
                                        {onRefresh && (
                                            <button
                                                onClick={onRefresh}
                                                className="px-4 py-2 bg-red-500 dark:bg-red-700 text-white rounded-lg hover:bg-red-600 dark:hover:bg-red-800 transition-colors"
                                            >
                                                {t('modals.disputes.retry')}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Empty State */}
                            {!loading && !error && (!Array.isArray(filteredDisputes) || filteredDisputes.length === 0) && (
                                <div className="text-center py-12">
                                    <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
                                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p className="text-gray-600 dark:text-gray-300 font-medium text-lg mb-2">
                                            {statusFilter === 'all'
                                                ? t('modals.disputes.noDisputes')
                                                : t('modals.disputes.noDisputesStatus', { status: t(`modals.disputes.status.${statusFilter}`) })}
                                        </p>
                                        <p className="text-gray-500 dark:text-gray-400 text-sm">
                                            {statusFilter === 'all'
                                                ? t('modals.disputes.noDisputesDesc')
                                                : t('modals.disputes.noDisputesStatusDesc')
                                            }
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Disputes List */}
                            {!loading && !error && Array.isArray(currentDisputes) && currentDisputes.length > 0 && (
                                <div className="space-y-4">
                                    {currentDisputes.map((dispute, index) => {
                                        const statusBadge = getStatusBadge(dispute.status);
                                        // Step 4: Enhanced layout for submitted, in_review, resolved
                                        if (["submitted", "in_review", "resolved"].includes(dispute.status)) {
                                            return (
                                                <motion.div
                                                    key={dispute.id}
                                                    className="relative p-7 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900 dark:to-blue-900 backdrop-blur-sm rounded-xl border border-indigo-200 dark:border-indigo-700 shadow-md transition-all duration-300 group"
                                                    initial={{ opacity: 0, y: 20 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ duration: 0.3, delay: index * 0.1 }}
                                                >
                                                    {/* Section 1: Dispute type (left) and status (right) */}
                                                    <div className="flex items-center justify-between mb-4">
                                                        <div className="text-xl font-bold text-indigo-700 dark:text-indigo-300 truncate text-left">
                                                            {dispute.dispute_type?.name || 'Dispute'}
                                                        </div>
                                                        <span className={`inline-flex px-4 py-2 text-base font-bold rounded-full border ${statusBadge.className} bg-white/80 ml-4`}> 
                                                            {statusBadge.icon} <span className="ml-2">{statusBadge.label}</span>
                                                        </span>
                                                    </div>
                                                    {/* Section 2: Details grid */}
                                                    <div className="grid grid-cols-2 gap-x-16 gap-y-2 mb-2">
                                                        <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Order ID</div>
                                                        <div className="text-base text-gray-800 dark:text-gray-100 font-semibold text-right">{dispute.order_id || dispute.order?.id || 'N/A'}</div>
                                                        <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Submitted On</div>
                                                        <div className="text-base text-gray-800 dark:text-gray-100 text-right">{formatDate(dispute.created_at)}</div>
                                                        {dispute.status === 'resolved' && (
                                                          <>
                                                            <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Resolved On</div>
                                                            <div className="text-base text-gray-800 dark:text-gray-100 text-right">{dispute.resolved_at ? formatDate(dispute.resolved_at) : 'N/A'}</div>
                                                          </>
                                                        )}
                                                        <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Dispute Type</div>
                                                        <div className="text-base text-gray-800 dark:text-gray-100 text-right">{dispute.dispute_type?.name || 'N/A'}</div>
                                                        <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Description</div>
                                                        <div className="text-base text-gray-800 dark:text-gray-100 text-right whitespace-pre-line">{dispute.description || 'N/A'}</div>
                                                    </div>
                                                    {/* Section 3: Evidence */}
                                                    {dispute.status !== 'submitted' &&
                                                        dispute.media &&
                                                        dispute.media.length > 0 && (
                                                        <div className="mb-2">
                                                            <div className="text-base font-semibold text-indigo-700 dark:text-indigo-300 mb-1">Evidence</div>
                                                            <div className="grid grid-cols-2 gap-3">
                                                                {dispute.media.map((media, idx) => (
                                                                    <div key={media.id ? `evidence-${media.id}` : `evidence-${idx}`} className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                                                                        <img
                                                                            src={getCdnUrl(media.optimized_path)}
                                                                            alt={`Evidence ${idx + 1}`}
                                                                            className="object-cover w-full h-32"
                                                                        />
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    )}
                                                    {/* Dispute Details Button */}
                                                    <div className="flex justify-center mt-6">
                                                        <button
                                                            className="flex items-center gap-2 px-10 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 dark:from-indigo-700 dark:to-blue-700 text-white font-semibold rounded-lg shadow hover:from-indigo-600 hover:to-blue-600 dark:hover:from-indigo-800 dark:hover:to-blue-800 transition-all text-lg w-full max-w-xs"
                                                            onClick={() => handleDisputeClick(dispute)}
                                                        >
                                                            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                                            </svg>
                                                            {t('modals.disputes.details')}
                                                        </button>
                                                    </div>
                                                </motion.div>
                                            );
                                        }
                                        // Default layout for other statuses
                                        return (
                                            <motion.div
                                                key={dispute.id}
                                                className="relative p-6 bg-gradient-to-r from-white/80 to-white/60 dark:from-gray-900/80 dark:to-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/50 dark:border-gray-700 hover:border-red-200 dark:hover:border-red-700 shadow-lg transition-all duration-300 group cursor-pointer"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                whileHover={{ scale: 1.01, y: -2 }}
                                                onClick={() => handleDisputeClick(dispute)}
                                            >
                                                <div className="flex items-start justify-between mb-2">
                                                    <div className="flex items-center space-x-3">
                                                        {/* Status Icon/Accent */}
                                                        <span className="text-2xl">{statusBadge.icon}</span>
                                                        <div>
                                                            <h3 className="font-bold text-gray-800 dark:text-gray-100 text-lg group-hover:text-red-700 dark:group-hover:text-red-300 transition-colors">
                                                                Dispute #{dispute.id}
                                                            </h3>
                                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                                Order #{dispute.order_id || dispute.order?.id || 'N/A'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    {/* Status Badge */}
                                                    <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full border ${statusBadge.className}`}>
                                                        {statusBadge.label}
                                                    </span>
                                                </div>
                                                {/* Dispute Type and Created Date */}
                                                <div className="flex flex-wrap items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1 mb-2">
                                                    {dispute.dispute_type && <span>Type: {dispute.dispute_type.name || dispute.dispute_type}</span>}
                                                    <span>Created: {formatDate(dispute.created_at)}</span>
                                                </div>
                                                {/* Description */}
                                                <p className="text-gray-700 dark:text-gray-200 my-3 line-clamp-2">
                                                    {dispute.description || 'No description provided'}
                                                </p>
                                                {/* Attachments */}
                                                {dispute.media && dispute.media.length > 0 && (
                                                    <div className="flex items-center text-xs text-gray-500 mt-2">
                                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z" />
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                                                        </svg>
                                                        {dispute.media.length} attachment{dispute.media.length > 1 ? 's' : ''}
                                                    </div>
                                                )}
                                            </motion.div>
                                        );
                                    })}
                                </div>
                            )}

                            {/* Pagination */}
                            {!loading && !error && totalPages > 1 && Array.isArray(filteredDisputes) && (
                                <div className="flex justify-center items-center space-x-2 mt-8">
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={currentPage === 1}
                                        className="px-4 py-2 bg-white/50 dark:bg-gray-900/60 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-white/70 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Previous
                                    </button>
                                    
                                    <div className="flex space-x-1">
                                        {[...Array(Math.max(0, totalPages))].map((_, index) => (
                                            <button
                                                key={index + 1}
                                                onClick={() => setCurrentPage(index + 1)}
                                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                                                    currentPage === index + 1
                                                        ? 'bg-gradient-to-r from-red-500 to-orange-600 dark:from-red-700 dark:to-orange-700 text-white'
                                                        : 'bg-white/50 dark:bg-gray-900/60 text-gray-700 dark:text-gray-200 hover:bg-white/70 dark:hover:bg-gray-800'
                                                }`}
                                            >
                                                {index + 1}
                                            </button>
                                        ))}
                                    </div>
                                    
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                        className="px-4 py-2 bg-white/50 dark:bg-gray-900/60 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-white/70 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
            )}
            {/* Dispute Details Modal for submitted, in_review, resolved */}
            {selectedDispute && ["submitted", "in_review", "resolved"].includes(selectedDispute.status) && (
                <motion.div
                    key="details-modal"
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 dark:bg-black/80 backdrop-blur-sm p-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={handleCloseDetailsModal}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-blue-50/90 dark:from-gray-900/95 dark:to-blue-900/90 backdrop-blur-2xl border border-indigo-200 dark:border-indigo-700 rounded-3xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto"
                        initial={{ scale: 0.95, opacity: 0, y: 20 }}
                        animate={{ scale: 1, opacity: 1, y: 0 }}
                        exit={{ scale: 0.95, opacity: 0, y: 20 }}
                        onClick={e => e.stopPropagation()}
                    >
                        {/* Close Button */}
                        <button
                            onClick={handleCloseDetailsModal}
                            className="absolute top-4 right-4 p-2 bg-white/40 dark:bg-gray-900/60 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
                        >
                            <svg className="w-5 h-5 text-gray-600 dark:text-gray-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        <div className="p-8">
                            {/* Section 1: Status */}
                            <h1 className="text-2xl font-bold text-indigo-700 dark:text-indigo-300 mb-1 capitalize">{selectedDispute.status.replace('_', ' ')}</h1>
                            <p className="text-sm text-indigo-700 dark:text-indigo-300 mb-6">
                                {selectedDispute.status === 'submitted' && t('modals.disputes.statusMessages.submitted')}
                                {selectedDispute.status === 'in_review' && t('modals.disputes.statusMessages.in_review')}
                                {selectedDispute.status === 'resolved' && t('modals.disputes.statusMessages.resolved')}
                            </p>
                            {/* Section 2: Dispute Details */}
                            <h2 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">{t('modals.disputes.details')}</h2>
                            <div className="mb-4 grid grid-cols-2 gap-x-10 gap-y-2">
                                <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Submitted On</div>
                                <div className="text-base text-gray-800 dark:text-gray-100 text-right">{formatDate(selectedDispute.created_at)}</div>
                                <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Dispute Type</div>
                                <div className="text-base text-gray-800 dark:text-gray-100 text-right">{selectedDispute.dispute_type?.name || 'N/A'}</div>
                                <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Description</div>
                                <div className="text-base text-gray-800 dark:text-gray-100 text-right whitespace-pre-line">{selectedDispute.description || 'N/A'}</div>
                                {selectedDispute.status === 'resolved' && (
                                    <>
                                        <div className="text-base text-gray-500 dark:text-gray-400 font-medium text-left">Resolved On</div>
                                        <div className="text-base text-gray-800 dark:text-gray-100 text-right">{selectedDispute.resolved_at ? formatDate(selectedDispute.resolved_at) : 'N/A'}</div>
                                    </>
                                )}
                            </div>
                            {/* Section 3: Evidence */}
                            <h2 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">Evidence</h2>
                            {selectedDispute.media && selectedDispute.media.length > 0 ? (
                                <div className="grid grid-cols-2 gap-3 mb-4">
                                    {selectedDispute.media.map((media, idx) => (
                                        <div key={media.id ? `evidence-${media.id}` : `evidence-${idx}`} className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                                            <img
                                                src={getCdnUrl(media.optimized_path)}
                                                alt={`Evidence ${idx + 1}`}
                                                className="object-cover w-full h-32"
                                            />
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">No evidence provided.</div>
                            )}
                            {/* Section 4: Service Ordered */}
                            <h2 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">Service Ordered</h2>
                            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700 mb-2">
                                <div className="grid grid-cols-2 gap-y-2 text-base">
                                    {selectedDispute.order_id && (
                                        <>
                                            <div className="text-gray-500 dark:text-gray-400 font-medium">Order ID</div>
                                            <div className="text-gray-800 dark:text-gray-100 text-right">{selectedDispute.order_id}</div>
                                            <div className="text-gray-500 dark:text-gray-400 font-medium">Order Type</div>
                                            <div className="text-gray-800 dark:text-gray-100 text-right capitalize">{selectedDispute.order_type || 'N/A'}</div>
                                        </>
                                    )}
                                    {selectedDispute.mission_id && (
                                        <>
                                            <div className="text-gray-500 dark:text-gray-400 font-medium">Mission ID</div>
                                            <div className="text-gray-800 dark:text-gray-100 text-right">{selectedDispute.mission_id}</div>
                                        </>
                                    )}
                                    <div className="text-gray-500 dark:text-gray-400 font-medium">Requestor Role</div>
                                    <div className="text-gray-800 dark:text-gray-100 text-right capitalize">{selectedDispute.requestor_role || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default DisputeManagementModal;
