import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import orderAPI from '../../services/orderService';
import { getCdnUrl } from '../../utils/cdnUtils';
import useTranslation from '../../hooks/useTranslation';
import OrderReviewModal from './OrderReviewModal';
import OrderDisputeModal from './OrderDisputeModal';

const TABS = [
  { key: 'toaccept', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', status: 'pending' },
  { key: 'accepted', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z', status: 'accepted' },
  { key: 'rejected', icon: 'M6 18L18 6M6 6l12 12', status: 'rejected' },
  { key: 'inprogress', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', status: 'in_progress' },
  { key: 'completed', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z', status: 'completed' },
  { key: 'expired', icon: 'M18 12H6m6 6V6', status: 'expired' },
  { key: 'cancelled', icon: 'M18 12H6m6 6V6', status: 'cancelled' },
];

const statusQueryMap = {
  toaccept: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  completed: 'completed',
  cancelled: 'cancelled',
  inprogress: 'in_progress'
};

const getStatusColor = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'accepted':
      return 'bg-blue-100 text-blue-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'in_progress':
      return 'bg-indigo-100 text-indigo-800';
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'expired':
      return 'bg-gray-100 text-gray-800';
    case 'cancelled':
      return 'bg-pink-100 text-pink-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return 'Not set';
  const date = new Date(dateTimeString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Helper to get service icon (copied from MyClientModal)
const getServiceIcon = (service) => {
  if (service?.icon) {
    return getCdnUrl(service.icon);
  }
  // Default service icons based on service name
  const serviceIcons = {
    'League of Legends': '🎮',
    'Valorant': '🔫',
    'CS2': '🎯',
    'Dota 2': '⚔️',
    'Mobile Legends': '📱'
  };
  return serviceIcons[service?.name] || '🎮';
};

const MyOrderModal = ({ isOpen = false, onClose }) => {
  const { t } = useTranslation('profile');
  const [activeTab, setActiveTab] = useState('toaccept');
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isDisputeModalOpen, setIsDisputeModalOpen] = useState(false);
  const refreshIntervalRef = useRef(null);
  const ordersRef = useRef([]);
  const statusCounts = React.useMemo(() => {
    const counts = {};
    orders.forEach((o) => {
      counts[o.status] = (counts[o.status] || 0) + 1;
    });
    return counts;
  }, [orders]);

  useEffect(() => {
    if (isOpen) {
      fetchOrders(statusQueryMap[activeTab]);
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      refreshIntervalRef.current = setInterval(() => {
        updateOrdersStatus(statusQueryMap[activeTab]);
      }, 10000);
    } else {
      setOrders([]);
      setError(null);
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [isOpen, activeTab]);

  useEffect(() => {
    ordersRef.current = orders;
  }, [orders]);

  const fetchOrders = async (status) => {
    setLoading(true);
    setError(null);
    try {
      const response = await orderAPI.getOrders({ status });
      const raw = response?.data?.data?.data || response?.data?.data || response?.data || [];
      const normalized = Array.isArray(raw) ? raw : [];
      // ensure consistent type format
      const ordersData = normalized.map((o) => ({ ...o, type: o.type?.replace('_', '-') || 'orders' }));
      setOrders(ordersData);
      ordersRef.current = ordersData;
    } catch (err) {
      console.error('Failed to load orders', err);
      setOrders([]);
      setError('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const updateOrdersStatus = async (status) => {
    try {
      const response = await orderAPI.getOrders({ status });
      const raw = response?.data?.data?.data || response?.data?.data || response?.data || [];
      const normalized = Array.isArray(raw) ? raw : [];
      const ordersData = normalized.map((o) => ({ ...o, type: o.type?.replace('_', '-') || 'orders' }));
      setOrders(ordersData);
      ordersRef.current = ordersData;
    } catch (err) {
      console.error('Failed to refresh orders', err);
    }
  };

  const currentTab = TABS.find(tab => tab.key === activeTab);
  const filteredOrders = orders;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900 dark:to-gray-950 backdrop-blur-2xl border border-white/30 dark:border-gray-800 rounded-3xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={e => e.stopPropagation()}
        >
          {/* Animated background decorations */}
          <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-emerald-900/30 dark:to-purple-900/30 rounded-full blur-2xl animate-pulse" />
          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-emerald-900/30 dark:to-blue-900/30 rounded-full blur-xl animate-pulse delay-1000" />

          {/* Header */}
          <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-green-700 to-emerald-700 bg-clip-text text-transparent dark:from-emerald-300 dark:to-green-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                    {t('modals.myOrders.title')}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {t('modals.myOrders.description')}
                  </p>
                </div>
              </div>
              {/* Close Button */}
              <motion.button
                onClick={onClose}
                className="p-3 bg-white/20 dark:bg-gray-800/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:bg-white/30 dark:hover:bg-gray-900/40 transition-all duration-300 shadow-sm hover:shadow-md"
                whileHover={{ scale: 1.05, rotate: 90, backgroundColor: 'rgba(255,255,255,0.3)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-5 h-5 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>
          </div>

          {/* Tabs */}
          <div className="relative z-10 px-6 py-4 border-b border-white/30 dark:border-gray-800">
            <div className="flex space-x-1 bg-white/50 dark:bg-gray-900/70 backdrop-blur-sm rounded-xl p-1 border border-white/30 dark:border-gray-700">
              {TABS.map(tab => (
                <motion.button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 relative ${
                    activeTab === tab.key
                      ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg dark:from-emerald-400 dark:to-green-500 dark:bg-gradient-to-r dark:text-white'
                      : 'text-gray-700 dark:text-gray-200 bg-transparent hover:bg-transparent dark:hover:bg-gray-800'
                  }`}
                  whileHover={{ scale: activeTab === tab.key ? 1 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={tab.icon} />
                  </svg>
                  <span>{t(`modals.myOrders.tabs.${tab.key}`)}</span>
                  <span className="ml-1 text-xs font-semibold">{statusCounts[tab.status] || 0}</span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-12rem)] dark:bg-gray-900">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <svg className="animate-spin h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
                <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-200">{t('modals.myOrders.loading')}</span>
              </div>
            ) : error ? (
              <div className="text-center py-16">
                <div className="p-8 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900 dark:to-red-800 rounded-2xl border border-red-200 dark:border-red-700 max-w-md mx-auto">
                  <div className="p-4 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-800 dark:to-red-900 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                    <svg className="w-10 h-10 text-red-400 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-red-900 dark:text-red-200 mb-2">{error}</h3>
                  <p className="text-red-600 dark:text-red-300">Please try again later.</p>
                </div>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-16">
                <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
                  <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                    <svg className="w-10 h-10 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('modals.myOrders.empty', { status: t(`modals.myOrders.tabs.${currentTab.key}`) })}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{t('modals.myOrders.emptyDesc', { status: t(`modals.myOrders.tabs.${currentTab.key}`).toLowerCase() })}</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredOrders.map((order, index) => (
                  <motion.div
                    key={order.id || `order-${index}`}
                    className={`relative p-6 backdrop-blur-sm rounded-xl border transition-all duration-300 shadow-sm hover:shadow-md ${getStatusColor(order.status)} ${order.status === 'accepted' ? 'bg-gradient-to-r from-green-50/60 to-emerald-50/40 dark:from-green-900/40 dark:to-emerald-900/30 border-green-200/50 dark:border-green-700' : 'bg-gradient-to-r from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 border-white/30 dark:border-gray-700 hover:border-indigo-200 dark:hover:border-indigo-700'}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    {/* Success Badge for Accepted Orders */}
                    {order.status === 'accepted' && (
                      <motion.div
                        className="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg dark:from-emerald-400 dark:to-green-500"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                      >
                        ✓ Accepted
                      </motion.div>
                    )}

                    {/* Order Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-3 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-emerald-900 dark:to-purple-900 rounded-xl shadow-sm">
                          {order.ordered_service_style?.icon ? (
                            <img 
                              src={getServiceIcon(order.ordered_service_style)} 
                              alt={order.ordered_service_style.name}
                              className="w-20 h-20 object-cover rounded"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          ) : (
                            <span className="text-2xl">{getServiceIcon(order.ordered_service_style)}</span>
                          )}
                        </div>
                        <div>
                          <h3 className="text-xl text-left pb-2 font-semibold text-gray-900 dark:text-white">
                            {order.ordered_service_style?.name || order.user_service?.service_type_title || order.user_service?.service_type?.name || 'Service'}
                          </h3>
                          <h3 className="text-sm text-left font-semibold text-gray-900 dark:text-gray-200">
                            {order.ordered_service_style?.name} {order.user_service?.pricing_option_type?.name ? `| ${order.user_service?.pricing_option_type?.name}` : ''}
                          </h3>
                          <p className="text-sm text-left text-gray-600 dark:text-gray-300">
                            Order #{order.id} • {order.customer?.nickname || order.customer?.name}
                          </p>
                        </div>
                      </div>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>

                    {/* Order Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="p-4 bg-white/50 dark:bg-gray-800/70 rounded-lg border border-white/30 dark:border-gray-700">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Date & Time</h4>
                        <p className="text-sm text-gray-900 dark:text-gray-100">{formatDateTime(order.created_at)}</p>
                      </div>
                      <div className="p-4 bg-white/50 dark:bg-gray-800/70 rounded-lg border border-white/30 dark:border-gray-700">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Credits</h4>
                        <p className="text-lg font-bold text-green-700 dark:text-green-400">{order.credit_amount} Credits</p>
                      </div>
                    </div>

                    {/* Customer Info */}
                    <div className="mb-4 p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-emerald-900/30 dark:to-indigo-900/30 rounded-lg border border-blue-100/30 dark:border-blue-700">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-sm">
                          <img
                            src={getCdnUrl(order.customer?.profile_picture)}
                            alt={order.customer?.nickname}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(order.customer?.nickname || order.customer?.name)}&background=6366f1&color=ffffff&size=100`;
                            }}
                          />
                        </div>
                        <div>
                          <p className="text-sm text-left font-medium text-gray-900 dark:text-white">
                            {order.customer?.nickname || order.customer?.name}
                          </p>
                          <p className="text-sm text-left text-gray-600 dark:text-gray-300">
                            ID: {order.customer?.uid} {order.customer?.level ? `• Level ${order.customer?.level}` : ''}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Remarks */}
                    {order.remarks && (
                      <div className="mt-2 text-sm text-gray-600 dark:text-gray-300"><span className="font-medium">Remarks:</span> {order.remarks}</div>
                    )}

                    {currentTab.key === 'completed' && (
                      <div className="mt-4 flex justify-end">
                        <motion.button
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsReviewModalOpen(true);
                          }}
                          className="px-4 py-2 bg-indigo-600 text-white rounded-xl shadow hover:bg-indigo-700 dark:bg-indigo-800 dark:hover:bg-indigo-900"
                          whileHover={{ scale: 1.03 }}
                          whileTap={{ scale: 0.97 }}
                        >
                          Review
                        </motion.button>
                      </div>
                    )}

                    {currentTab.key === 'inprogress' && (
                      <div className="mt-4 flex justify-end">
                        <motion.button
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsDisputeModalOpen(true);
                          }}
                          className="px-4 py-2 bg-red-600 text-white rounded-xl shadow hover:bg-red-700 dark:bg-red-800 dark:hover:bg-red-900"
                          whileHover={{ scale: 1.03 }}
                          whileTap={{ scale: 0.97 }}
                        >
                          Dispute
                        </motion.button>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
      <OrderReviewModal
        key="review-modal"
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        order={selectedOrder}
        onSubmitReview={() => {}}
      />
      <OrderDisputeModal
        key="dispute-modal"
        isOpen={isDisputeModalOpen}
        onClose={() => setIsDisputeModalOpen(false)}
        order={selectedOrder}
        onDisputeCreated={() => {}}
      />
    </AnimatePresence>
  );
};export default MyOrderModal; 