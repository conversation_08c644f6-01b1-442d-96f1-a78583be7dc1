import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import clientAPI from '../../services/clientService';
import orderAPI from '../../services/orderService';
import { InlineLoader } from '../ui/LoadingIndicator';
import { getCdnUrl } from '../../utils/cdnUtils';
import { ToastContainer } from 'react-toastify';
import useTranslation from '../../hooks/useTranslation';
import OrderReviewModal from './OrderReviewModal';
import OrderDisputeModal from './OrderDisputeModal';

const MyClientModal = ({ isOpen = false, onClose }) => {
  const { t } = useTranslation('profile');
  const statusQueryMap = {
    'to-accept': 'pending',
    accepted: 'accepted',
    'in-progress': 'in_progress',
    rejected: 'rejected',
    cancelled: 'cancelled',
    completed: 'completed',
    expired: 'expired'
  };

  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('to-accept');
  const [processingOrder, setProcessingOrder] = useState(null);
  const [acceptedOrder, setAcceptedOrder] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [isDisputeModalOpen, setIsDisputeModalOpen] = useState(false);
  const refreshIntervalRef = useRef(null);
  const ordersRef = useRef([]);

  const transformClientData = (client) => {
    const transformed = {
      order_id: client.service?.id,
      client_id: client.client_id,
      type: client.type || client.order_type || 'order',
      service: {
        name: client.service?.user_service?.service_type?.name || 'Unknown Service',
        icon: client.service?.user_service?.service_type?.icon_path,
        category: client.service?.service_category?.name,
        pricing_option_type: client.service?.pricing_option_type,
        quantity: client.service?.quantity,
        order_type: client.service?.order_type
      },
      ordered_service_style: client.ordered_service_style,
      bounty: client.service?.credit_amount || 0,
      order_time: client.order_time || client.service?.created_at,
      order_accept_time: client.order_accept_time || client.service?.responded_at,
      service_start_time: client.service_start_time || client.service?.service_start_time,
      service_end_time: client.service_end_time || client.service?.service_end_time,
      order_complete_time: client.order_complete_time || client.service?.completed_at,
      client: {
        name: client.name,
        nickname: client.nickname,
        profile_picture: client.profile_picture,
        uid: client.id || client.client_id,
        level: client.level?.level || 1
      },
      status: client.service?.status || client.status
    };

    return transformed;
  };

  useEffect(() => {
    if (isOpen) {
      fetchOrders(statusQueryMap[activeTab]);
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      refreshIntervalRef.current = setInterval(() => {
        updateOrdersByClientId(statusQueryMap[activeTab]);
      }, 10000);
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [isOpen, activeTab]);

  useEffect(() => {
    ordersRef.current = orders;
  }, [orders]);

  const fetchOrders = async (status) => {
    setLoading(true);
    try {
      const response = await clientAPI.getClients({ status });

      // Transform the API response to match component expectations
      const transformedOrders = (response?.data?.data || []).map(transformClientData);
      
      setOrders(transformedOrders);
      ordersRef.current = transformedOrders;
    } catch (err) {
      console.error('Failed to fetch orders', err);
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const updateOrdersByClientId = async (status) => {
    try {
      const response = await clientAPI.getClients({ status });
      const transformedOrders = (response?.data?.data || []).map(transformClientData);
      setOrders(transformedOrders);
      ordersRef.current = transformedOrders;
    } catch (err) {
      console.error('Failed to refresh orders', err);
    }
  };

  const handleAcceptOrder = async (orderId) => {
    setProcessingOrder(orderId);
    const targetOrder = orders.find((o) => o.order_id === orderId);
    try {
      // Make API call to accept the order
      await orderAPI.respondToOrder(orderId, { action: 'accept' });
      
      // Find the order details for celebration
      const orderToAccept = orders.find(order => order.order_id === orderId);
      
      // Update local state
      setOrders(prev => prev.map(order => 
        order.order_id === orderId 
          ? { ...order, status: 'accepted', order_accept_time: new Date().toISOString() }
          : order
      ));
      
      // Set accepted order for celebration and show modal only once
      setAcceptedOrder(orderToAccept);
      setShowSuccessModal(true);
      
      // Show celebration toast
      toast.success(
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <div className="font-semibold text-green-800">Order Accepted! 🎉</div>
            <div className="text-sm text-green-700">
              {orderToAccept?.client?.nickname || orderToAccept?.client?.name} • {orderToAccept?.bounty} Credits
            </div>
          </div>
        </div>,
        {
          position: 'top-center',
          autoClose: 4000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: 'colored',
          style: {
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)',
          }
        }
      );
      
    } catch (error) {
      console.error('Failed to accept order:', error);
      toast.error('Failed to accept order. Please try again.');
    } finally {
      setProcessingOrder(null);
      updateOrdersByClientId(statusQueryMap[activeTab]);
    }
  };

  const handleRejectOrder = async (orderId) => {
    setProcessingOrder(orderId);
    const targetOrder = orders.find((o) => o.order_id === orderId);
    try {
      // Make API call to reject the order
      await orderAPI.respondToOrder(orderId, { action: 'reject' });
      
      // Update local state
      setOrders(prev => prev.map(order => 
        order.order_id === orderId 
          ? { ...order, status: 'rejected' }
          : order
      ));
    } catch (error) {
      console.error('Failed to reject order:', error);
      toast.error('Failed to reject order. Please try again.');
    } finally {
      setProcessingOrder(null);
      updateOrdersByClientId(statusQueryMap[activeTab]);
    }
  };

  const getOrdersByStatus = (statusKey) => {
    const queryStatus = statusQueryMap[statusKey] || statusKey;
    return orders.filter((order) => order.status === queryStatus);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return t('modals.myClients.notSet');
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getServiceIcon = (service) => {
    if (service?.icon) {
      return getCdnUrl(service.icon);
    }
    // Default service icons based on service name
    const serviceIcons = {
      'League of Legends': '🎮',
      'Valorant': '🔫',
      'CS2': '🎯',
      'Dota 2': '⚔️',
      'Mobile Legends': '📱'
    };
    return serviceIcons[service?.name] || '🎮';
  };

  // Success Modal close handler
  const handleCloseSuccessModal = (goToAcceptedTab = false) => {
    setShowSuccessModal(false);
    setAcceptedOrder(null);
    if (goToAcceptedTab) setActiveTab('accepted');
  };

  if (!isOpen) return null;

  // Success Modal Component
  const SuccessModal = () => (
    <AnimatePresence>
      {showSuccessModal && acceptedOrder && (
        <motion.div
          key="success-modal"
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="relative bg-gradient-to-br from-white to-green-50/30 backdrop-blur-2xl border border-green-200/50 rounded-3xl shadow-2xl w-full max-w-md overflow-hidden"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
          >
            {/* Confetti Background */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={`confetti-${i}`}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                  initial={{ 
                    x: Math.random() * window.innerWidth, 
                    y: -20,
                    rotate: 0,
                    scale: 0
                  }}
                  animate={{ 
                    y: window.innerHeight + 20,
                    rotate: 360,
                    scale: [0, 1, 0]
                  }}
                  transition={{ 
                    duration: 3 + Math.random() * 2,
                    delay: Math.random() * 0.5,
                    ease: "easeOut"
                  }}
                />
              ))}
            </div>

            {/* Content */}
            <div className="relative z-10 p-8 text-center">
              {/* Success Icon */}
              <motion.div
                className="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </motion.div>

              {/* Title */}
              <motion.h2
                className="text-2xl font-bold text-gray-900 mb-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Congratulations! 🎉
              </motion.h2>

              {/* Subtitle */}
              <motion.p
                className="text-gray-600 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                You've successfully accepted an order!
              </motion.p>

              {/* Order Details */}
              <motion.div
                className="bg-white rounded-xl p-4 mb-6 border border-green-200 shadow-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-green-200">
                    <img
                      src={getCdnUrl(acceptedOrder.client?.profile_picture)}
                      alt={acceptedOrder.client?.nickname}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(acceptedOrder.client?.nickname || acceptedOrder.client?.name)}&background=10b981&color=ffffff&size=100`;
                      }}
                    />
                  </div>
                  <div className="text-left">
                    <p className="font-semibold text-gray-900">
                      {acceptedOrder.client?.nickname || acceptedOrder.client?.name}
                    </p>
                    <p className="text-sm text-gray-600">
                      {acceptedOrder.service?.name}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{t('modals.myClients.earnings')}</span>
                  <span className="text-lg font-bold text-green-600">
                    {acceptedOrder.bounty} Credits
                  </span>
                </div>
              </motion.div>

              {/* Next Steps */}
              <motion.div
                className="bg-green-50 rounded-xl p-4 mb-6 border border-green-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <h3 className="font-semibold text-green-800 mb-2">{t('modals.myClients.whatNext')}</h3>
                <ul className="text-sm text-green-700 space-y-1 text-left">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    {t('modals.myClients.steps.notified')}
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    {t('modals.myClients.steps.creditsHeld')}
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    {t('modals.myClients.steps.startService')}
                  </li>
                </ul>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                className="flex space-x-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <button
                  onClick={() => {
                    handleCloseSuccessModal(true);
                  }}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg"
                >
                  {t('modals.myClients.viewAcceptedOrders')}
                </button>
                <button
                  onClick={() => {
                    handleCloseSuccessModal();
                  }}
                  className="px-6 py-3 bg-white text-gray-700 border border-gray-300 font-medium rounded-xl hover:bg-gray-50 transition-all duration-300"
                >
                  {t('modals.myClients.continue')}
                </button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <AnimatePresence>
      <motion.div
        key="client-modal"
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900 dark:to-gray-950 backdrop-blur-2xl border border-white/30 dark:border-gray-800 rounded-3xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Animated background decorations */}
          <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-indigo-900/30 dark:to-purple-900/30 rounded-full blur-2xl animate-pulse" />
          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-indigo-900/30 dark:to-blue-900/30 rounded-full blur-xl animate-pulse delay-1000" />

          {/* Header */}
          <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent dark:from-indigo-300 dark:to-purple-300 dark:bg-gradient-to-r dark:bg-clip-text dark:text-transparent">
                    {t('modals.myClients.title')}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    {t('modals.myClients.description')}
                  </p>
                </div>
              </div>
              
              {/* Close Button */}
              <motion.button
                onClick={onClose}
                className="p-3 bg-white/20 dark:bg-gray-800/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:bg-white/30 dark:hover:bg-gray-900/40 transition-all duration-300 shadow-sm hover:shadow-md"
                whileHover={{ 
                  scale: 1.05,
                  rotate: 90,
                  backgroundColor: "rgba(255, 255, 255, 0.3)"
                }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-5 h-5 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>
          </div>

          {/* Tabs */}
          <div className="relative z-10 px-6 py-4 border-b border-white/30 dark:border-gray-800">
            <div className="flex space-x-1 bg-white/50 dark:bg-gray-900/70 backdrop-blur-sm rounded-xl p-1 border border-white/30 dark:border-gray-700">
              {[
                { key: 'to-accept', label: t('modals.myClients.tabs.toAccept'), icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', count: getOrdersByStatus('to-accept').length },
                { key: 'accepted', label: t('modals.myClients.tabs.accepted'), icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z', count: getOrdersByStatus('accepted').length },
                { key: 'in-progress', label: t('modals.myClients.tabs.inProgress'), icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', count: getOrdersByStatus('in-progress').length },
                { key: 'rejected', label: t('modals.myClients.tabs.rejected'), icon: 'M6 18L18 6M6 6l12 12', count: getOrdersByStatus('rejected').length },
                { key: 'cancelled', label: t('modals.myClients.tabs.cancelled'), icon: 'M18 12H6m6 6V6', count: getOrdersByStatus('cancelled').length },
                { key: 'completed', label: t('modals.myClients.tabs.completed'), icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z', count: getOrdersByStatus('completed').length },
                { key: 'expired', label: t('modals.myClients.tabs.expired'), icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z', count: getOrdersByStatus('expired').length }
              ].map((tab) => (
                <motion.button
                  key={`tab-${tab.key}`}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 relative ${
                    activeTab === tab.key
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg dark:from-indigo-400 dark:to-purple-500 dark:bg-gradient-to-r dark:text-white'
                      : 'text-gray-700 dark:text-gray-200 bg-transparent hover:bg-transparent dark:hover:bg-gray-800'
                  }`}
                  whileHover={{ scale: activeTab === tab.key ? 1 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={tab.icon} />
                  </svg>
                  <span>{tab.label}</span>
                  {tab.count > 0 && (
                    <span className={`px-2 py-0.5 text-xs rounded-full ${
                      activeTab === tab.key 
                        ? 'bg-white/20 text-white dark:bg-indigo-900/40 dark:text-indigo-200' 
                        : 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </motion.button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-12rem)] dark:bg-gray-900">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <InlineLoader size="large" color="indigo" />
                <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-200">{t('modals.myClients.loading')}</span>
              </div>
            ) : (
              <div className="space-y-4">
                {getOrdersByStatus(activeTab).length === 0 ? (
                  <div className="text-center py-16">
                    <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 max-w-md mx-auto">
                      <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                        <svg className="w-10 h-10 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('modals.myClients.empty', { status: activeTab.replace('-', ' ') })}</h3>
                      <p className="text-gray-600 dark:text-gray-300">{t('modals.myClients.emptyDesc', { status: activeTab.replace('-', ' ') })}</p>
                    </div>
                  </div>
                ) : (
                  getOrdersByStatus(activeTab).map((order, index) => (
                    <motion.div
                      key={`order-${order.order_id || index}`}
                      className={`p-6 backdrop-blur-sm rounded-xl border transition-all duration-300 shadow-sm hover:shadow-md ${
                        order.status === 'accepted'
                          ? 'bg-gradient-to-r from-green-50/60 to-emerald-50/40 dark:from-green-900/40 dark:to-emerald-900/30 border-green-200/50 dark:border-green-700'
                          : 'bg-gradient-to-r from-white/60 to-white/40 dark:from-gray-900/60 dark:to-gray-800/40 border-white/30 dark:border-gray-700 hover:border-indigo-200 dark:hover:border-indigo-700'
                      }`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      {/* Success Badge for Accepted Orders */}
                      {order.status === 'accepted' && (
                        <motion.div
                          className="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg dark:from-green-400 dark:to-emerald-500"
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{ duration: 0.5, delay: 0.3 }}
                        >
                          ✓ Accepted
                        </motion.div>
                      )}

                      {/* Order Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="p-3 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl shadow-sm dark:from-indigo-900 dark:to-purple-900">
                            {order.service?.icon ? (
                              <img 
                                src={getServiceIcon(order.service)} 
                                alt={order.service.name}
                                className="w-20 h-20 object-cover rounded"
                                onError={(e) => {
                                  e.target.style.display = 'none';
                                  e.target.nextSibling.style.display = 'block';
                                }}
                              />
                            ) : (
                              <span className="text-2xl">{getServiceIcon(order.service)}</span>
                )}
              </div>
              <div>
                            <h3 className="text-xl text-left pb-2 font-semibold text-gray-900 dark:text-white">
                              {order.service?.name}
                            </h3>
                            <h3 className="text-sm text-left font-semibold text-gray-900 dark:text-gray-200">
                              {order.service?.category} | {order.ordered_service_style?.name} | {order.service?.pricing_option_type?.name} | Qty {order.service?.quantity}
                            </h3>
                            <p className="text-sm text-left text-gray-600 dark:text-gray-300">
                              Order #{order.order_id} • {order.client?.nickname || order.client?.name}
                            </p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}> 
                          {order.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>

                      {/* Order Details */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="p-4 bg-white/50 dark:bg-gray-800/70 rounded-lg border border-white/30 dark:border-gray-700">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">{t('modals.myClients.dateTime')}</h4>
                          <p className="text-sm text-gray-900 dark:text-gray-100">{formatDateTime(order.order_time)}</p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/70 rounded-lg border border-white/30 dark:border-gray-700">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">{t('modals.myClients.creditsToReceive')}</h4>
                          <p className="text-lg font-bold text-green-700 dark:text-green-400">{order.bounty} Credits</p>
                        </div>
                        <div className="p-4 bg-white/50 dark:bg-gray-800/70 rounded-lg border border-white/30 dark:border-gray-700">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Order Type</h4>
                          <p className="text-sm text-gray-900 dark:text-gray-100">{order.service?.order_type || '-'}</p>
                        </div>
                      </div>

                      {/* Client Info */}
                      <div className="mb-4 p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-lg border border-blue-100/30 dark:border-blue-700">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-sm">
                            <img
                              src={getCdnUrl(order.client?.profile_picture)}
                              alt={order.client?.nickname}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(order.client?.nickname || order.client?.name)}&background=6366f1&color=ffffff&size=100`;
                              }}
                            />
                          </div>
                          <div>
                            <p className="text-sm text-left font-medium text-gray-900 dark:text-white">
                              {order.client?.nickname || order.client?.name}
                            </p>
                            <p className="text-sm text-left text-gray-600 dark:text-gray-300">
                              ID: {order.client?.uid} • Level {order.client?.level}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      {activeTab === 'to-accept' && (
                        <div className="flex space-x-3">
                          <motion.button
                            onClick={() => handleAcceptOrder(order.order_id)}
                            disabled={processingOrder === order.order_id}
                            className={`flex-1 px-6 py-3 font-semibold rounded-xl transition-all duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed ${
                              processingOrder === order.order_id
                                ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white'
                                : order.status === 'accepted'
                                ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white'
                                : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700'
                            }`}
                            whileHover={{ scale: processingOrder === order.order_id ? 1 : 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {processingOrder === order.order_id ? (
                              <div className="flex items-center justify-center space-x-2">
                                <InlineLoader size="small" color="white" />
                                <span>{t('modals.myClients.accepting')}</span>
                              </div>
                            ) : order.status === 'accepted' ? (
                              <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Accepted!</span>
                              </div>
                            ) : (
                              <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>{t('modals.myClients.acceptOffer')}</span>
                              </div>
                            )}
                          </motion.button>
                          <motion.button
                            onClick={() => handleRejectOrder(order.order_id)}
                            disabled={processingOrder === order.order_id || order.status === 'accepted'}
                            className="flex-1 px-6 py-3 bg-gradient-to-r from-red-500 to-pink-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-pink-700 dark:from-red-700 dark:to-pink-700 transition-all duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                            whileHover={{ scale: processingOrder === order.order_id ? 1 : 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {processingOrder === order.order_id ? (
                              <div className="flex items-center justify-center space-x-2">
                                <InlineLoader size="small" color="white" />
                                <span>{t('modals.myClients.rejecting')}</span>
                              </div>
                            ) : (
                              <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>{t('modals.myClients.rejectOffer')}</span>
                              </div>
                            )}
                          </motion.button>
                        </div>
                      )}

                      {/* Additional Info for other tabs */}
                      {activeTab !== 'to-accept' && (
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {order.order_accept_time && (
                            <p>Accepted: {formatDateTime(order.order_accept_time)}</p>
                          )}
                          {order.order_complete_time && (
                            <p>Completed: {formatDateTime(order.order_complete_time)}</p>
                          )}
                          {order.service_start_time && (
                            <p>Started: {formatDateTime(order.service_start_time)}</p>
                          )}
                          {order.service_end_time && (
                            <p>Ended: {formatDateTime(order.service_end_time)}</p>
                          )}
                        </div>
                      )}

                      {activeTab === 'completed' && (
                        <div className="mt-4 flex justify-end">
                          <motion.button
                            onClick={() => {
                              setSelectedOrder(order);
                              setIsReviewModalOpen(true);
                            }}
                            className="px-4 py-2 bg-indigo-600 text-white rounded-xl shadow hover:bg-indigo-700 dark:bg-indigo-800 dark:hover:bg-indigo-900"
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                          >
                            Review
                          </motion.button>
                        </div>
                      )}

                      {activeTab === 'in-progress' && (
                        <div className="mt-4 flex justify-end">
                          <motion.button
                            onClick={() => {
                              setSelectedOrder(order);
                              setIsDisputeModalOpen(true);
                            }}
                            className="px-4 py-2 bg-red-600 text-white rounded-xl shadow hover:bg-red-700 dark:bg-red-800 dark:hover:bg-red-900"
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                          >
                            Dispute
                          </motion.button>
                        </div>
                      )}
                    </motion.div>
                  ))
              )}
            </div>
            )}
          </div>
        </motion.div>
      </motion.div>
      <SuccessModal key="success-container" />
      <OrderReviewModal
        key="review-modal"
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        order={selectedOrder}
        onSubmitReview={() => {}}
      />
      <OrderDisputeModal
        key="dispute-modal"
        isOpen={isDisputeModalOpen}
        onClose={() => setIsDisputeModalOpen(false)}
        order={selectedOrder}
        onDisputeCreated={() => {}}
      />
      {/* Provide a custom containerId to avoid duplicate key warnings */}
      <ToastContainer key="toast" containerId="my-client-modal" />
    </AnimatePresence>
  );
};

export default MyClientModal;
