import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCdnUrl } from '../../utils/cdnUtils';
import useTranslation from '../../hooks/useTranslation';

const BookmarkedMissionsModal = ({ isOpen, onClose, missions = [] }) => {
  const { t } = useTranslation('profile');
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900 dark:to-gray-800 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={e => e.stopPropagation()}
        >
          {/* Animated background decorations */}
          <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl animate-pulse" />
          <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />

          {/* Header */}
          <div className="relative z-10 p-6 border-b border-white/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-blue-700 to-indigo-700 dark:from-blue-300 dark:to-indigo-300 bg-clip-text text-transparent">
                    {t('view.bookmarkedMissions')}
                  </h2>
                  <p className="text-gray-600 text-left dark:text-white text-sm">
                    All your saved missions in one place
                  </p>
                </div>
              </div>
              {/* Close Button */}
              <motion.button
                onClick={onClose}
                className="p-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 hover:bg-white/30 transition-all duration-300 shadow-sm hover:shadow-md"
                whileHover={{ scale: 1.05, rotate: 90, backgroundColor: 'rgba(255,255,255,0.3)' }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                <svg className="w-5 h-5 text-gray-600 dark:text-white hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            </div>
          </div>

          {/* Content */}
          <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
            {missions.length === 0 ? (
              <div className="text-center py-16">
                <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-2xl border border-gray-200 max-w-md mx-auto">
                  <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-900 dark:to-gray-800 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                    <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('view.noBookmarkedMissions')}</h3>
                  <p className="text-gray-600">You haven't bookmarked any missions yet.</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {missions.map((mission, index) => (
                  <motion.div
                    key={mission.id}
                    className="group relative bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                    whileHover={{ scale: 1.02 }}
                  >
                    {/* Mission Image - fixed aspect ratio */}
                    <div className="relative w-full h-48 overflow-hidden rounded-lg">
                      {mission.images && mission.images.length > 0 ? (
                        <img
                          src={getCdnUrl(mission.images[0])}
                          alt={mission.title || 'Mission image'}
                          className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                          onError={e => { e.target.src = '/AuthLogo.png'; e.target.onerror = null; }}
                        />
                      ) : (
                        <img
                          src={'/AuthLogo.png'}
                          alt="MissionX Logo"
                          className="w-full h-full object-contain bg-gray-100 p-8"
                        />
                      )}
                    </div>
                    {/* Card Content */}
                    <div className="p-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {mission.title || mission.name || 'Untitled Mission'}
                      </h4>
                      <p className="text-gray-600 dark:text-white text-sm line-clamp-3 mb-3">
                        {mission.description ? mission.description.substring(0, 80) + '...' : 'No description available'}
                      </p>
                      {/* Meta */}
                      <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                          </svg>
                          <span className="text-xs text-gray-600">{mission.reward ? `${mission.reward} Credits` : ''}</span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-white">
                          {mission.created_at ? new Date(mission.created_at).toLocaleDateString() : 'Recent'}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BookmarkedMissionsModal; 