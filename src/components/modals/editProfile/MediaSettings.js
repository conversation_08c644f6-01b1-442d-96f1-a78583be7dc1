import React, { useMemo, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useDropzone } from 'react-dropzone';
import { Progress } from '../../ui/progress';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import { profileKeys } from '../../../queryKeys/profileKeys';

export const validateMediaFiles = (file, type) => {
  const errors = {};

  if (type === 'profile') {
    const validTypes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'image/gif',
      'image/heic',
      'image/heif',
      'image/webp'
    ];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      errors.profile_picture =
        'Please select a valid image file (JPEG, PNG, JPG, HEIC, HEIF, WebP)';
      return errors;
    }

    if (file.size > maxSize) {
      errors.profile_picture = 'Image size must be less than 5MB';
      return errors;
    }
  } else if (type === 'cover') {
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    const allowedImageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/heic',
      'image/heif',
      'image/webp'
    ];
    const allowedVideoTypes = [
      'video/mp4',
      'video/mov',
      'video/avi',
      'video/flv',
      'video/wmv',
      'video/3gp',
      'video/mkv'
    ];
    const maxSize = isVideo ? 50 * 1024 * 1024 : 20 * 1024 * 1024;

    if (!isImage && !isVideo) {
      errors.cover_media = 'Please select a valid image or video file';
      return errors;
    }

    if (isImage && !allowedImageTypes.includes(file.type)) {
      errors.cover_media =
        'Image must be JPEG, PNG, HEIC, HEIF, or WebP format';
      return errors;
    }

    if (isVideo && !allowedVideoTypes.includes(file.type)) {
      errors.cover_media =
        'Video must be MP4, MOV, AVI, FLV, WMV, 3GP, or MKV format';
      return errors;
    }

    if (file.size > maxSize) {
      errors.cover_media = `File size must be less than ${isVideo ? '50MB' : '20MB'}`;
      return errors;
    }
  }

  return errors;
};

export const processValidFile = (
  file,
  isVideo,
  setErrors,
  setCoverMediaFile,
  setCoverMediaType,
  setCoverMediaPreview
) => {
  setErrors((prev) => {
    const newErrors = { ...prev };
    delete newErrors.cover_media;
    return newErrors;
  });

  const mediaType = isVideo ? 'video' : 'image';
  setCoverMediaFile(file);
  setCoverMediaType(mediaType);

  const reader = new FileReader();
  reader.onloadend = () => {
    setCoverMediaPreview(reader.result);
  };
  reader.readAsDataURL(file);
};

const MediaSettings = ({
  profilePicturePreview,
  handleProfilePictureChange,
  errors,
  uploadProgress,
  mediaOrder,
  handleDragStart,
  handleDragEnd,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  setPendingDeleteOrder,
  setShowDeleteCoverConfirm,
  showDeleteCoverConfirm,
  pendingDeleteOrder,
  handleDeleteCoverMedia,
  coverMediaPreview,
  handleCoverMediaChange,
  currentVoiceNote,
  handleVoiceNoteChange,
  handleDeleteVoiceNote,
  validateMediaFiles,
  processValidFile
}) => {
  const queryClient = useQueryClient();

  // Get cached profile data
  const cachedProfile = queryClient.getQueryData(profileKeys.detail());

  // Initialize current media from cached profile
  const currentMedia = useMemo(() => {
    if (!cachedProfile?.success || !cachedProfile?.data) {
      return {
        profilePicture: null,
        voiceNote: null,
        coverMedia: []
      };
    }

    const userData = cachedProfile.data;
    
    // Profile picture
    let profilePicture = null;
    if (userData.profile_picture) {
      profilePicture = userData.profile_picture.startsWith('http')
        ? userData.profile_picture
        : `${process.env.REACT_APP_CDN_URL}/${userData.profile_picture}`;
    }

    // Voice note
    let voiceNote = null;
    if (userData.voice_note) {
      voiceNote = userData.voice_note.startsWith('http')
        ? userData.voice_note
        : `${process.env.REACT_APP_CDN_URL}/${userData.voice_note}`;
    }

    // Cover media from profile_media field
    let coverMedia = [];
    if (userData.profile_media) {
      
      // Get CDN URL with fallback
      const cdnUrl = process.env.REACT_APP_CDN_URL || 'http://localhost:8001';
      
      // Handle photos array from profile_media
      if (userData.profile_media.photos && Array.isArray(userData.profile_media.photos)) {
        coverMedia = userData.profile_media.photos.map((photo, index) => {
          const mediaItem = {
            id: photo.id || index,
            url: photo.path.startsWith('http') 
              ? photo.path 
              : `${cdnUrl}/${photo.path}`,
            type: 'image',
            order: photo.order || index + 1
          };
          return mediaItem;
        });
      }
      
      // Handle video if present
      if (userData.profile_media.video) {
        const videoItem = {
          id: 'video',
          url: userData.profile_media.video.startsWith('http')
            ? userData.profile_media.video
            : `${cdnUrl}/${userData.profile_media.video}`,
          type: 'video',
          order: coverMedia.length + 1
        };
        coverMedia.push(videoItem);
      }
    } else {

    }
    
    return {
      profilePicture,
      voiceNote,
      coverMedia
    };
  }, [cachedProfile]);

  // Update parent component's state when current media changes
  useEffect(() => {
    if (currentMedia.profilePicture && !profilePicturePreview) {
    }
    if (currentMedia.voiceNote && !currentVoiceNote) {
    }
  }, [currentMedia, profilePicturePreview, currentVoiceNote]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (accepted) => {
      if (accepted.length > 0) {
        handleProfilePictureChange({ target: { files: accepted } });
      }
    },
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/jpg': [],
      'image/gif': [],
      'image/webp': []
    },
    maxSize: 5 * 1024 * 1024,
    multiple: false
  });

  // Choose which media array to display
  const displayMedia = (mediaOrder && mediaOrder.length > 0) ? mediaOrder : currentMedia.coverMedia;

  return (
    <div className="space-y-8">
      {/* Profile Picture Section */}
      <div className="space-y-4">
        <h4 className="text-base font-semibold text-indigo-800 dark:text-white">Profile Picture</h4>
        <div className="flex flex-col items-center">
          <div className="relative w-36 h-36 rounded-full mx-auto overflow-hidden border-4 border-blue-100 shadow-lg bg-gradient-to-br from-gray-100 to-blue-50 dark:from-gray-900 dark:to-gray-800">
            {(profilePicturePreview || currentMedia.profilePicture) ? (
              <img 
                src={profilePicturePreview || currentMedia.profilePicture} 
                alt="Profile preview" 
                className="w-full h-full object-cover" 
              />
            ) : (
              <div className="w-full h-full flex justify-center items-center bg-gradient-to-br from-gray-200 to-blue-100 dark:from-gray-800 dark:to-gray-900">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            )}
            {/* Edit icon overlay */}
            <button
              type="button"
              onClick={() => document.getElementById('profile-picture-input').click()}
              className="absolute bottom-1 right-1 p-1.5 bg-white/90 border border-blue-200 rounded-full shadow hover:bg-indigo-100 transition-all dark:bg-gray-900 dark:border-gray-700 dark:hover:bg-gray-800"
              aria-label="Change profile picture"
            >
              <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536M9 13h3l8-8a2.828 2.828 0 10-4-4l-8 8v3z" />
              </svg>
            </button>
            <input
              id="profile-picture-input"
              type="file"
              className="hidden"
              accept="image/jpeg,image/png,image/jpg,image/gif,image/heic,image/heif,image/webp"
              onChange={handleProfilePictureChange}
            />
          </div>
          {errors.profile_picture && <p className="mt-2 text-sm text-red-500 dark:text-red-400">{errors.profile_picture}</p>}
          {uploadProgress > 0 && (
            <div className="mt-2 w-24 mx-auto">
              <Progress value={uploadProgress} />
              <p className="text-xs text-gray-500 dark:text-gray-300 mt-1 text-center">{uploadProgress}%</p>
            </div>
          )}
        </div>
      </div>

      {/* Cover Media Section */}
      <div className="space-y-4">
        <h4 className="text-base font-semibold text-indigo-800 dark:text-white">Cover Media</h4>
        <div className="grid grid-cols-1 gap-4">
          {displayMedia && displayMedia.length > 0 ? (
            displayMedia.map((media, index) => (
              <div
                key={media.id}
                className="relative w-full h-48 rounded-xl overflow-hidden border-2 border-blue-100 shadow bg-gradient-to-br from-gray-100 to-blue-50 dark:from-gray-900 dark:to-gray-800 dark:border-gray-700"
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
              >
                {media.type === 'video' ? (
                  <video src={media.url} className="w-full h-full object-cover" controls muted />
                ) : (
                  <img src={media.url} alt={`Cover ${index + 1}`} className="w-full h-full object-cover" />
                )}
                {/* Delete icon overlay */}
                <button
                  type="button"
                  onClick={() => {
                    setPendingDeleteOrder({ order: media.order, type: media.type });
                    setShowDeleteCoverConfirm(true);
                  }}
                  className="absolute top-1 right-1 p-1 bg-white/90 border border-red-200 rounded-full shadow hover:bg-red-100 transition-all dark:bg-gray-900 dark:border-red-400 dark:hover:bg-gray-800"
                  aria-label="Delete cover media"
                >
                  <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                {/* Optionally: Add reorder handle icon at bottom-left */}
                <span className="absolute bottom-1 left-1 p-1 bg-white/80 border border-blue-100 rounded-full shadow text-indigo-400 cursor-move dark:bg-gray-900 dark:border-blue-400">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8h16M4 16h16" />
                  </svg>
                </span>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-400 dark:text-gray-500 w-full">No cover media uploaded yet</div>
          )}
        </div>
        <div
          className="relative border-2 border-dashed rounded-xl p-6 transition-all duration-200 border-blue-100 hover:border-indigo-500/50 hover:bg-indigo-50 cursor-pointer bg-white/60 dark:bg-gray-900 dark:border-gray-700 dark:hover:bg-gray-800"
          onClick={() => document.getElementById('cover-media-input').click()}
        >
          <input
            id="cover-media-input"
            type="file"
            className="hidden"
            accept="image/jpeg,image/png,image/jpg,image/heic,image/heif,image/webp,video/mp4,video/mov,video/avi,video/flv,video/wmv,video/3gp,video/mkv"
            onChange={handleCoverMediaChange}
          />
          <div className="flex flex-col items-center">
            <svg className="w-8 h-8 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <p className="mt-2 text-sm font-medium text-indigo-700 dark:text-white">Click to upload or drag and drop</p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">Supports JPG, PNG, HEIC, WebP, MP4, MOV, AVI, FLV, WMV, 3GP, MKV</p>
          </div>
        </div>
        {errors.cover_media && <p className="mt-2 text-sm text-red-500 dark:text-red-400">{errors.cover_media}</p>}
      </div>

      {/* Voice Note Section */}
      <div className="space-y-4">
        <h4 className="text-base font-semibold text-indigo-800 dark:text-white">Voice Note</h4>
        <div className="flex flex-col items-center gap-4">
          {(currentVoiceNote || currentMedia.voiceNote) ? (
            <div className="flex flex-col items-center w-full">
              <audio controls src={currentVoiceNote || currentMedia.voiceNote} className="w-full max-w-xs" />
              <button
                type="button"
                onClick={handleDeleteVoiceNote}
                className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded-full font-medium shadow hover:bg-red-200 transition-all dark:bg-gray-900 dark:text-red-400 dark:hover:bg-gray-800"
              >
                Delete Voice Note
              </button>
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-300">No voice note uploaded</p>
          )}
          <div
            className="relative border-2 border-dashed rounded-xl p-6 transition-all duration-200 border-blue-100 hover:border-indigo-500/50 hover:bg-indigo-50 cursor-pointer bg-white/60 w-full max-w-xs dark:bg-gray-900 dark:border-gray-700 dark:hover:bg-gray-800"
            onClick={() => document.getElementById('voice-note-input').click()}
          >
            <input
              id="voice-note-input"
              type="file"
              className="hidden"
              accept="audio/mp3,audio/wav,audio/m4a,audio/aac,audio/ogg"
              onChange={handleVoiceNoteChange}
            />
            <div className="flex flex-col items-center">
              <svg className="w-8 h-8 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <p className="mt-2 text-sm font-medium text-indigo-700 dark:text-white">Click to upload</p>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-300">MP3, WAV, M4A, AAC, OGG (max 5MB)</p>
            </div>
          </div>
          {errors.voice_note && <p className="mt-2 text-sm text-red-500 dark:text-red-400">{errors.voice_note}</p>}
        </div>
      </div>

      <DeleteConfirmationDialog
        isOpen={showDeleteCoverConfirm}
        onClose={() => setShowDeleteCoverConfirm(false)}
        onConfirm={() => {
          handleDeleteCoverMedia(pendingDeleteOrder);
          setShowDeleteCoverConfirm(false);
        }}
        message="Delete this media?"
      />
    </div>
  );
};

export default MediaSettings;
