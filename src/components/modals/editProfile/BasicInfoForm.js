import React, { useMemo, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { profileAPI } from '../../../services/api';
import profileService from '../../../services/profileService';
import { motion } from 'framer-motion';
import { InlineLoader } from '../../ui/LoadingIndicator';
import FloatingLabelInput from './FloatingLabelInput';
import { profileKeys } from '../../../queryKeys/profileKeys';

export const validateBasicInfo = (formData) => {
  const errors = {};

  if (!formData.nickname?.trim()) {
    errors.nickname = 'Nickname is required';
  } else if (formData.nickname.length < 2) {
    errors.nickname = 'Nickname must be at least 2 characters';
  }

  if (!formData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Invalid email format';
  }

  if (!formData.gender) {
    errors.gender = 'Gender is required';
  }

  if (!formData.date_of_birth) {
    errors.date_of_birth = 'Date of birth is required';
  } else {
    const dob = new Date(formData.date_of_birth);
    const today = new Date();
    const age = today.getFullYear() - dob.getFullYear();
    if (age < 18) {
      errors.date_of_birth = 'Must be at least 18 years old';
    }
  }

  if (!formData.race_id) {
    errors.race_id = 'Race is required';
  }

  if (!formData.personality_ids?.length) {
    errors.personality_ids = 'At least one personality must be selected';
  } else if (formData.personality_ids.length > 5) {
    errors.personality_ids = 'Maximum 5 personalities can be selected';
  }

  return errors;
};

// Query keys for races and personalities
const raceKeys = {
  all: ['races'],
  list: () => [...raceKeys.all, 'list'],
};

const personalityKeys = {
  all: ['personalities'],
  list: () => [...personalityKeys.all, 'list'],
};

const BasicInfoForm = ({
  formData,
  setFormData,
  errors,
  handleInputChange,
  handlePersonalityToggle,
  validateBasicInfo
}) => {
  const queryClient = useQueryClient();
  
  // Get cached profile data
  const cachedProfile = queryClient.getQueryData(profileKeys.detail());

  // Fetch races using TanStack Query
  const { 
    data: racesData, 
    isLoading: loadingRaces, 
    error: racesError 
  } = useQuery({
    queryKey: raceKeys.list(),
    queryFn: async () => {
      const response = await profileService.getRaces();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch races');
      }
      return response.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Fetch personalities using TanStack Query
  const { 
    data: personalitiesData, 
    isLoading: loadingPersonalities, 
    error: personalitiesError 
  } = useQuery({
    queryKey: personalityKeys.list(),
    queryFn: async () => {
      const response = await profileAPI.getAvailablePersonalities();
      if (response && response.data) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      } else {
        throw new Error('Invalid personalities response format');
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Initialize form data from cached profile if available
  useEffect(() => {
    if (cachedProfile?.success && cachedProfile?.data && 
        (!formData.nickname || formData.nickname === '') && 
        typeof setFormData === 'function') {
      const userData = cachedProfile.data;
      const newFormData = {
        nickname: userData.nickname || '',
        email: userData.email || '',
        height: userData.height || '',
        weight: userData.weight || '',
        gender: userData.gender || '',
        date_of_birth: userData.date_of_birth
          ? new Date(userData.date_of_birth).toISOString().split('T')[0]
          : '',
        biography: userData.biography || '',
        race_id: (
          userData.race_id ??
          (typeof userData.race === 'object' ? userData.race?.id : userData.race)
        )
          ? (userData.race_id ??
              (typeof userData.race === 'object'
                ? userData.race?.id
                : userData.race)
            ).toString()
          : '',
        personality_ids: (userData.personalities || []).map(p => p.id),
        allow_3rd_party_access: userData.allow_3rd_party_access ?? true
      };
      setFormData(newFormData);
    }
  }, [cachedProfile, formData.nickname, setFormData]);

  // Use safe form data with fallback to cached data
  const safeFormData = useMemo(() => {
    if (formData && Object.values(formData).some(v => v !== null && v !== undefined && v !== '')) {
      return formData;
    }

    if (cachedProfile?.success && cachedProfile?.data) {
      const userData = cachedProfile.data;
      return {
        nickname: userData.nickname || '',
        email: userData.email || '',
        height: userData.height || '',
        weight: userData.weight || '',
        gender: userData.gender || '',
        date_of_birth: userData.date_of_birth
          ? new Date(userData.date_of_birth).toISOString().split('T')[0]
          : '',
        biography: userData.biography || '',
        race_id: (
          userData.race_id ??
          (typeof userData.race === 'object' ? userData.race?.id : userData.race)
        )
          ? (userData.race_id ??
              (typeof userData.race === 'object'
                ? userData.race?.id
                : userData.race)
            ).toString()
          : '',
        personality_ids: (userData.personalities || []).map(p => p.id),
        allow_3rd_party_access: userData.allow_3rd_party_access ?? true
      };
    }

    return formData || {};
  }, [formData, cachedProfile]);

  // Get selected personalities from form data
  const selectedPersonalities = useMemo(() => {
    return safeFormData.personality_ids || [];
  }, [safeFormData.personality_ids]);

  return (
  <div className="space-y-6 p-6 bg-white/5 backdrop-blur-sm rounded-xl">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* UID (editable) */}
      <FloatingLabelInput
        id="uid"
        label="User ID (UID)"
        value={safeFormData.uid || (cachedProfile?.success && cachedProfile?.data?.uid) || ''}
        onChange={(e) => handleInputChange('uid', e.target.value)}
        error={errors?.uid}
        maxLength={64}
        className="col-span-1"
      />
      {/* Name */}
      <FloatingLabelInput
        id="nickname"
        label="Nickname"
          value={safeFormData.nickname || ''}
        onChange={(e) => handleInputChange('nickname', e.target.value)}
          error={errors?.nickname}
        maxLength={20}
        required
      />
      <FloatingLabelInput
        id="email"
        label="Email Address"
        type="email"
          value={safeFormData.email || ''}
        onChange={(e) => handleInputChange('email', e.target.value)}
          error={errors?.email}
        required
      />
      <FloatingLabelInput
        id="height"
        label="Height (cm)"
        type="number"
          value={safeFormData.height || ''}
        onChange={(e) => handleInputChange('height', e.target.value)}
          error={errors?.height}
        min="0"
        step="0.1"
      />
      <FloatingLabelInput
        id="weight"
        label="Weight (kg)"
        type="number"
          value={safeFormData.weight || ''}
        onChange={(e) => handleInputChange('weight', e.target.value)}
          error={errors?.weight}
        min="0"
        step="0.1"
      />
      <FloatingLabelInput
        id="date_of_birth"
        label="Date of Birth"
        type="date"
          value={safeFormData.date_of_birth || ''}
        onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
          error={errors?.date_of_birth}
        required
      />
      {/* Gender */}
      <div className="flex flex-col md:col-span-1">
        <label htmlFor="gender" className="text-base font-semibold text-indigo-800 mb-2">Gender</label>
        <select
          id="gender"
            value={safeFormData.gender || ''}
          onChange={(e) => handleInputChange('gender', e.target.value)}
          className={`w-full px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-indigo-500/50 text-base bg-white/80 text-gray-900 transition-all duration-200 ${errors?.gender ? 'border-red-500/50' : 'border-blue-100'}`}
          required
        >
          <option value="">Select gender</option>
          <option value="Male">♂️ Male</option>
          <option value="Female">♀️ Female</option>
          <option value="Other">⚧️ Other</option>
        </select>
          {errors?.gender && (
          <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
        )}
      </div>
      {/* Race */}
      <div className="flex flex-col md:col-span-1">
        <label htmlFor="race_id" className="text-base font-semibold text-indigo-800 mb-2">Race</label>
        <select
          id="race_id"
            value={safeFormData.race_id || ''}
          onChange={(e) => handleInputChange('race_id', e.target.value)}
          className={`w-full px-4 py-2 rounded-full border focus:outline-none focus:ring-2 focus:ring-indigo-500/50 text-base bg-white/80 text-gray-900 transition-all duration-200 ${errors?.race_id ? 'border-red-500/50' : 'border-blue-100'}`}
          required
        >
          <option value="">Select race</option>
          {Array.isArray(racesData) && racesData.map((race) => (
            <option key={race.id} value={race.id}>{race.name}</option>
          ))}
        </select>
          {errors?.race_id && (
          <p className="mt-1 text-sm text-red-500">{errors.race_id}</p>
        )}
      </div>
      {/* Biography */}
      <FloatingLabelInput
        id="biography"
        label="Biography"
        multiline
        value={safeFormData.biography || ''}
        onChange={(e) => handleInputChange('biography', e.target.value)}
        error={errors?.biography}
        maxLength={500}
        placeholder="Tell us about yourself..."
        className="md:col-span-2"
      />
      {/* Personality */}
      <div className="flex flex-col md:col-span-2">
        <label className="text-base font-semibold text-indigo-800 mb-2">Personality</label>
        <div className="flex flex-wrap gap-2 mt-2">
          {Array.isArray(personalitiesData) && personalitiesData.map((personality) => {
            const isSelected = selectedPersonalities.includes(personality.id);
            return (
              <button
                type="button"
                key={personality.id}
                onClick={() => handlePersonalityToggle(personality.id)}
                className={`inline-flex items-center px-3 py-1 rounded-full border text-base font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/50
                  ${isSelected ? 'bg-indigo-100 text-indigo-700 border-indigo-300' : 'bg-white text-gray-500 border-blue-100 hover:bg-indigo-50 hover:text-indigo-600'}`}
              >
                <span className="mr-1">{personality.emoji || '💡'}</span>
                {personality.name}
              </button>
            );
          })}
        </div>
          {errors?.personality_ids && (
          <p className="mt-1 text-sm text-red-500">{errors.personality_ids}</p>
        )}
      </div>
    </div>
  </div>
);
};

export default BasicInfoForm;
