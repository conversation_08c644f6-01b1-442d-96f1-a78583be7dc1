import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const DeleteConfirmationDialog = ({ isOpen, onClose, onConfirm, message }) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        className="fixed inset-0 flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className="absolute inset-0 bg-black/40 backdrop-blur-sm"
          onClick={onClose}
        />
        <motion.div
          initial={{ y: -20, opacity: 0, scale: 0.95 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={{ y: 20, opacity: 0, scale: 0.95 }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className="relative w-full max-w-sm p-6 rounded-2xl bg-gradient-to-br from-white/90 to-indigo-50/30 shadow-xl shadow-indigo-500/10 border border-white/30 backdrop-blur-xl space-y-6 mx-4"
        >
          <p className="text-center text-gray-800">{message}</p>
          <div className="grid grid-cols-2 gap-4 pt-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onClose}
              className="px-4 py-2.5 rounded-xl bg-white/60 hover:bg-white/80 text-gray-700 font-medium transition-all duration-200 border border-white/50 hover:border-white/70 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-gray-300/50"
            >
              Cancel
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onConfirm}
              className="px-4 py-2.5 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-medium shadow-md shadow-red-500/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60"
            >
              Delete
            </motion.button>
          </div>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
);

export default DeleteConfirmationDialog;
