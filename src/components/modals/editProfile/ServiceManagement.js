import React, { useState, useEffect, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import FloatingLabelInput from './FloatingLabelInput';
import serviceConfigApi from '../../../services/serviceConfigurationApi';
import userServiceApi from '../../../services/userServiceApi';
import ReactDOM from "react-dom";
import { motion } from 'framer-motion';
import { SectionLoader } from '../../ui/LoadingIndicator';
import Switch from '@mui/material/Switch';

// Query keys for service data
export const serviceKeys = {
  all: ['services'],
  user: () => [...serviceKeys.all, 'user'],
  categories: () => [...serviceKeys.all, 'categories'],
  types: (categoryId) => [...serviceKeys.all, 'types', categoryId],
  styles: (typeId) => [...serviceKeys.all, 'styles', typeId],
  pricingOptionTypes: () => [...serviceKeys.all, 'pricing-option-types'],
};

// Add ServiceElementsForm component
const ServiceElementsForm = ({ category, elements, onChange, errors }) => {
  if (category === '1') {
    return (
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-300">Game Service Details</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <FloatingLabelInput
              label="Game Title"
              type="text"
              value={elements.game_title || ''}
              onChange={(e) => onChange('game_title', e.target.value)}
              error={errors?.game_title}
              placeholder="e.g., Valorant, League of Legends"
            />
          </div>
          <div>
            <FloatingLabelInput
              label="Rank"
              type="text"
              value={elements.rank || ''}
              onChange={(e) => onChange('rank', e.target.value)}
              error={errors?.rank}
              placeholder="e.g., Radiant, Diamond"
            />
          </div>
        </div>
      </div>
    );
  }

  if (category === '2') {
    return (
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-300">Talent Service Details</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <FloatingLabelInput
              label="Equipment"
              type="text"
              value={elements.equipment || ''}
              onChange={(e) => onChange('equipment', e.target.value)}
              error={errors?.equipment}
              placeholder="e.g., Professional DSLR, Studio Equipment"
            />
          </div>
          <div>
            <FloatingLabelInput
              label="Experience"
              type="text"
              value={elements.experience || ''}
              onChange={(e) => onChange('experience', e.target.value)}
              error={errors?.experience}
              placeholder="e.g., 5+ years, Certified Professional"
            />
          </div>
        </div>
      </div>
    );
  }

  return null;
};

// Update section headers, field labels, and important text for higher contrast in the Service Form Portal modal content
// Example changes:
// - Section headers: text-indigo-900 font-bold
// - Field labels: text-gray-900 font-medium
// - Helper text: text-blue-800 or text-gray-700
// - Descriptions: text-blue-800 or text-gray-700
// Update validateServiceForm to include service elements validation
export const validateServiceForm = (formData, serviceStylePrices, serviceElements) => {
  const errors = {};
  const selectedCategoryId = formData.service_category_id;
  const selectedCategorySlug = (formData.service_category_slug || '').toLowerCase();
  const isOthers =
    selectedCategorySlug === 'others' || selectedCategoryId === '2';

  // Only validate category if it's been touched or submitted
  if (selectedCategoryId === '' || selectedCategoryId === null) {
    errors.service_category_id = 'Please select a valid service category';
  }

  // Only validate service type if category is selected and it's been touched or submitted
  if (selectedCategoryId) {
    if (isOthers) {
      if (!formData.service_type_title) {
        errors.service_type_title = 'Service type is required';
      }
    } else if (formData.service_type_id === '' || formData.service_type_id === null) {
      errors.service_type_id = 'Please select a valid service type';
    }
  }

  // Require pricing option type
  if (selectedCategoryId && (formData.pricing_option_id === '' || formData.pricing_option_id === null)) {
    errors.pricing_option_id = 'Please select a pricing option';
  }

  // Validate service elements based on category
  if (selectedCategoryId === '1') {
    const stylePrices = (formData.service_style || []).map((s) =>
      parseFloat(serviceStylePrices[s.service_style_id] || s.price || 0)
    );
    if (stylePrices.length === 0 || stylePrices.every((p) => p <= 0)) {
      errors.service_style = 'At least one service style must have a price greater than 0';
    }
  } else if (isOthers) {
    if (!formData.service_type_description) {
      errors.service_type_description = 'Description is required';
    }
    const parsedPrice = parseFloat(formData.price);
    if (isNaN(parsedPrice) || parsedPrice <= 0) {
      errors.price = 'Price must be greater than 0';
    }
  }

  return errors;
};

export const resetServiceForm = (
  setServiceForm,
  setServiceStylePrices,
  setErrors,
  setServiceMessage
) => {
  setServiceForm({
    service_category_id: '',
    service_category_slug: '',
    service_type_id: '',
    service_type_title: '',
    service_type_description: '',
    pricing_option_id: '',
    price: '',
    service_style: []
  });
  setServiceStylePrices({});
  setErrors({});
  setServiceMessage('');
};

// Status Badge Component
const ServiceStatusBadge = ({ status, rejectionReason, onShowRejectionReason }) => {
  const statusConfig = {
    pending: {
      color: 'yellow',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      label: 'Pending Review'
    },
    approved: {
      color: 'green',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
      ),
      label: 'Approved'
    },
    rejected: {
      color: 'red',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      label: 'Rejected'
    },
    disabled: {
      color: 'gray',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
        </svg>
      ),
      label: 'Disabled'
    }
  };

  const config = statusConfig[status] || statusConfig.pending;

  return (
    <div className="flex items-center space-x-2">
      <span className={`px-3 py-1 rounded-full text-sm font-medium
        ${config.color === 'yellow' ? 'bg-yellow-500/10 text-yellow-400' : ''}
        ${config.color === 'green' ? 'bg-green-500/10 text-green-400' : ''}
        ${config.color === 'red' ? 'bg-red-500/10 text-red-400' : ''}
        ${config.color === 'gray' ? 'bg-gray-500/10 text-gray-400' : ''}
      `}>
        <span className="mr-1">{config.icon}</span>
        {config.label}
      </span>
      {rejectionReason && (
        <button
          type="button"
          onClick={onShowRejectionReason}
          className="text-gray-400 hover:text-gray-300"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
      )}
    </div>
  );
};

// Revision Timeline Item Component
const RevisionTimelineItem = ({ revision, isLatest }) => {
  return (
    <div className="flex items-start space-x-3">
      <div className="flex-shrink-0">
        <div className={`w-2 h-2 rounded-full mt-2
          ${revision.status === 'approved' ? 'bg-green-400' : ''}
          ${revision.status === 'rejected' ? 'bg-red-400' : ''}
          ${revision.status === 'pending' ? 'bg-yellow-400' : ''}
        `} />
      </div>
      <div className="flex-1">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-300">
            {new Date(revision.created_at).toLocaleDateString()}
          </span>
          <ServiceStatusBadge status={revision.status} />
        </div>
        {revision.rejection_reason && (
          <p className="text-sm text-red-400 mt-1">{revision.rejection_reason}</p>
        )}
        {isLatest && (
          <div className="mt-2 p-2 bg-white/5 rounded-lg">
            <h6 className="text-sm font-medium text-gray-300">Changes in this revision:</h6>
            <ul className="mt-1 space-y-1 text-sm text-gray-400">
              {revision.changes?.map((change, index) => (
                <li key={index}>{change}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

// Service Action Buttons Component
const ServiceActionButtons = ({ status, onEdit, onDelete, onViewRevisions }) => {
  return (
    <div className="flex space-x-2">
      {status === 'approved' && (
        <button
          type="button"
          onClick={onEdit}
          className="p-2 text-gray-400 hover:text-white transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
      )}
      {status !== 'pending' && (
        <button
          type="button"
          onClick={onDelete}
          className="p-2 text-gray-400 hover:text-red-400 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
      <button
        type="button"
        onClick={onViewRevisions}
        className="p-2 text-gray-400 hover:text-white transition-colors"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </button>
    </div>
  );
};

// Type Card Component
const TypeCard = ({ type, selected, onSelect }) => {
  return (
    <button
      type="button"
      onClick={onSelect}
      className={`p-4 rounded-xl border-2 transition-all w-full
        ${selected
          ? 'border-indigo-500 bg-indigo-500/10'
          : 'border-white/10 hover:border-white/20'
        }`}
    >
      <div className="text-left">
        <h5 className={`font-medium ${selected ? 'text-indigo-400' : 'text-gray-200'}`}>
          {type.name}
        </h5>
        <p className="text-sm text-gray-400">{type.description}</p>
      </div>
    </button>
  );
};

// Style Card Component
const StyleCard = ({ style, selected, onSelect, onPriceChange, price, pricingSelected, styleName }) => {
  return (
    <div className={`p-4 rounded-xl border-2 transition-all
      ${selected 
        ? 'border-indigo-500 bg-indigo-500/10' 
        : 'border-white/10 bg-transparent hover:border-white/20'
      }`}
    >
      <div className="flex items-start justify-between">
        <button
          type="button"
          onClick={onSelect}
          className="flex-1 bg-transparent text-left"
        >
          <h5 className={`font-medium ${selected ? 'text-indigo-900 dark:text-blue-400' : 'text-gray-900 dark:text-blue-300'}`}>{styleName}</h5>
          <p className="text-sm text-gray-900 dark:text-blue-300">{style.description}</p>
        </button>
        <div className="ml-4">
          <input
            type="checkbox"
            checked={selected}
            onChange={onSelect}
            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
        </div>
      </div>
      {selected && pricingSelected && (
        <div className="mt-4 space-y-2">
          <div className="text-sm text-blue-900 dark:text-blue-400 mb-8 ml-2">
            <p>Recommended Price: {style.recommended_price} Credits</p>
            <p>Preset Price: {style.preset_price} Credits</p>
          </div>
          <FloatingLabelInput
            label="Your Price (Credits)"
            type="number"
            value={price ?? style.preset_price ?? ''}
            onChange={(e) => onPriceChange(e.target.value)}
            min="0"
            step="0.01"
            required
          />
        </div>
      )}
    </div>
  );
};

const ServiceManagement = ({
  showServiceForm,
  serviceForm,
  serviceStylePrices,
  errors,
  handleServiceFieldChange,
  handleServiceFormToggle,
  handleServiceStylePriceChange,
  handleCloseServiceForm,
  submitNewService,
  handleEditService,
  handleDeleteService,
  validateServiceForm,
  resetServiceForm
}) => {
  const queryClient = useQueryClient();
  const [serviceElements, setServiceElements] = useState({});
  const [showRevisions, setShowRevisions] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);
  const [revisions, setRevisions] = useState([]);
  const [touched, setTouched] = useState({});
  const [isSubmittingService, setIsSubmittingService] = useState(false);
  const [togglingStyles, setTogglingStyles] = useState({});
  const [serviceTypeStyles, setServiceTypeStyles] = useState({});
  const [optimisticToggles, setOptimisticToggles] = useState({});

  // Fetch user services using TanStack Query
  const { 
    data: userServicesData, 
    isLoading: loadingUserServices, 
    error: userServicesError,
    refetch: refetchUserServices
  } = useQuery({
    queryKey: serviceKeys.user(),
    queryFn: async () => {
      const response = await userServiceApi.getUserServices();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch user services');
      }
      return response.data || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Fetch styles for all service types in user's services to map names
  useEffect(() => {
    const loadStylesForServices = async () => {
      if (!userServicesData) return;
      const typeIds = [...new Set(userServicesData.map(s => s.service_type_id))];
      const result = {};
      for (const id of typeIds) {
        try {
          const styles = await serviceConfigApi.getStyles(id);
          if (Array.isArray(styles)) {
            result[id] = styles;
          }
        } catch (err) {
          console.error('Failed to fetch styles for type', id, err);
        }
      }
      setServiceTypeStyles(result);
    };
    loadStylesForServices();
  }, [userServicesData]);

  // Fetch service categories using TanStack Query
  const { 
    data: serviceCategoriesData, 
    isLoading: loadingCategories, 
    error: categoriesError 
  } = useQuery({
    queryKey: serviceKeys.categories(),
    queryFn: async () => {
      const response = await serviceConfigApi.getCategories();
      if (!response) {
        throw new Error('Failed to fetch service categories');
      }
      return response;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Fetch service types based on selected category
  const { 
    data: serviceTypesData, 
    isLoading: loadingTypes, 
    error: typesError 
  } = useQuery({
    queryKey: serviceKeys.types(serviceForm.service_category_id),
    queryFn: async () => {
      if (!serviceForm.service_category_id) return [];
      const response = await serviceConfigApi.getTypes(serviceForm.service_category_id);
      if (!response) {
        throw new Error('Failed to fetch service types');
      }
      return response;
    },
    enabled: !!serviceForm.service_category_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
  });

  // Fetch service styles based on selected type
  const {
    data: serviceStylesData,
    isLoading: loadingStyles,
    error: stylesError
  } = useQuery({
    queryKey: serviceKeys.styles(serviceForm.service_type_id),
    queryFn: async () => {
      if (!serviceForm.service_type_id) return [];
      const response = await serviceConfigApi.getStyles(serviceForm.service_type_id);
      if (!response) {
        throw new Error('Failed to fetch service styles');
      }
      return response;
    },
    enabled: !!serviceForm.service_type_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
  });

  const filteredServiceStyles = useMemo(
    () =>
      (serviceStylesData || []).filter(
        (style) =>
          String(style.service_category_id) === String(serviceForm.service_category_id)
      ),
    [serviceStylesData, serviceForm.service_category_id]
  );

  const selectedCategory = useMemo(
    () =>
      serviceCategoriesData?.find(
        (c) => c.id === Number(serviceForm.service_category_id)
      ),
    [serviceCategoriesData, serviceForm.service_category_id]
  );

  const isOthersCategory =
    (selectedCategory?.slug || '').toLowerCase() === 'others' ||
    (serviceForm.service_category_slug || '').toLowerCase() === 'others' ||
    String(serviceForm.service_category_id) === '2';
  const serviceTypeSelected = isOthersCategory
    ? Boolean(serviceForm.service_type_title)
    : Boolean(serviceForm.service_type_id);

  // Fetch pricing option types
  const { 
    data: pricingOptionTypesData, 
    isLoading: loadingPricingTypes, 
    error: pricingTypesError 
  } = useQuery({
    queryKey: serviceKeys.pricingOptionTypes(),
    queryFn: async () => {
      const response = await serviceConfigApi.getPricingOptionTypes();
      if (!response) {
        throw new Error('Failed to fetch pricing option types');
      }
      return response;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });


  // Add handler for service elements changes
  const handleServiceElementsChange = (field, value) => {
    setServiceElements(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Add handler for service type change
  const handleServiceTypeChange = async (typeId) => {
    handleServiceFieldChange('service_type_id', typeId);
    setTouched(prev => ({ ...prev, service_type_id: true }));
  };

  // Add handler for service category change
  const handleServiceCategoryChange = (categoryId) => {
    handleServiceFieldChange('service_category_id', categoryId);
    const category = serviceCategoriesData?.find(c => c.id === Number(categoryId));
    handleServiceFieldChange(
      'service_category_slug',
      category?.slug ? category.slug.toLowerCase() : ''
    );
    // reset dependent fields when category changes
    handleServiceFieldChange('service_type_id', null);
    handleServiceFieldChange('service_style', []);
    setServiceElements({});
    setTouched(prev => ({ ...prev, service_category_id: true }));
  };

  const handleServiceStyleToggle = (styleId) => {
    const currentStyles = serviceForm.service_style || [];
    const isSelected = currentStyles.some(style => style.service_style_id === styleId);

    let updatedStyles;
    if (isSelected) {
      updatedStyles = currentStyles.filter(style => style.service_style_id !== styleId);
    } else {
      updatedStyles = [...currentStyles, {
        service_style_id: styleId,
        is_active: true,
        price:
          filteredServiceStyles.find((s) => s.id === styleId)?.preset_price || 0
      }];
    }

    handleServiceFieldChange('service_style', updatedStyles);
    setTouched(prev => ({ ...prev, service_style: true }));
  };

  const handleServiceStyleActiveToggle = async (
    serviceId,
    styleId,
    currentActive
  ) => {
    const key = `${serviceId}-${styleId}`;
    setTogglingStyles(prev => ({ ...prev, [key]: true }));
    // Optimistically update UI
    setOptimisticToggles(prev => ({ ...prev, [key]: !currentActive }));
    try {
      const service = userServicesData?.find(s => s.id === serviceId);
      if (!service) return;
      const updatedStyles = (service.service_style || service.service_styles).map(s => {
        const id = s.service_style_id ?? s.id;
        return {
          service_style_id: id,
          price: s.price,
          is_active: id === styleId ? !currentActive : (s.is_active ?? true)
        };
      });
      const payload = {
        id: service.id,
        service_category_id: service.service_category_id,
        service_type_id: service.service_type_id,
        pricing_option_id: service.pricing_option_id,
        service_style: updatedStyles,
        service_type_title: service.service_type_title,
        service_type_description: service.service_type_description,
        price: service.price,
      };
      await userServiceApi.updateService(payload);
      await refetchUserServices();
    } catch (error) {
      console.error('Error toggling style active state:', error);
    } finally {
      setTogglingStyles(prev => ({ ...prev, [key]: false }));
      // Clear optimistic toggle after refetch
      setTimeout(() => setOptimisticToggles(prev => {
        const copy = { ...prev };
        delete copy[key];
        return copy;
      }), 500);
    }
  };

  // Enhanced submit function that refetches data after success
  const handleSubmitNewService = async () => {
    try {
      setIsSubmittingService(true);
      await submitNewService(serviceElements);
      // Refetch user services after successful creation
      await refetchUserServices();
    } catch (error) {
      console.error('Error submitting service:', error);
    } finally {
      setIsSubmittingService(false);
    }
  };

  // Enhanced delete function that refetches data after success
  const handleDeleteServiceInternal = async (serviceId) => {
    try {
      await handleDeleteService(serviceId);
      // Refetch user services after successful deletion
      await refetchUserServices();
    } catch (error) {
      console.error('Error deleting service:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Service Form Section */}
      {showServiceForm && (
        <ServiceFormPortal isOpen={showServiceForm} onClose={handleCloseServiceForm}>
          <div className="bg-gradient-to-br from-white/95 via-blue-50/90 to-indigo-100/90 dark:from-gray-900 dark:via-gray-900 dark:to-gray-950 rounded-2xl shadow-2xl border border-blue-100 dark:border-gray-800 w-full max-w-lg sm:max-w-xl md:max-w-2xl overflow-hidden max-h-[90vh] flex flex-col">
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-blue-600 text-white sticky top-0 z-10 flex items-center justify-between px-6 py-4">
              <h3 className="text-xl font-semibold flex items-center gap-2 dark:text-white dark:bg-gradient-to-r dark:from-indigo-300 dark:to-purple-400 dark:bg-clip-text dark:text-transparent">
                <span className="inline-block text-2xl">💼</span>
                {serviceForm.id ? 'Edit Service' : 'Add New Service'}
              </h3>
            <button
              type="button"
              onClick={handleCloseServiceForm}
                className="p-3 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-200/50 transition-all duration-200"
                aria-label="Close service form"
            >
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
            {/* Content */}
            <div className="flex-1 px-6 py-6 overflow-y-auto dark:bg-gray-900">
              {/* Section headers and field groupings */}
          <div className="space-y-6">
            {/* Service Category Selection */}
                <div>
                  <h4 className="flex items-center gap-2 text-base font-semibold text-indigo-800 dark:text-indigo-300 mb-4">
                    <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:from-indigo-400 dark:to-purple-400 rounded-full mr-2"></span>
                    Service Category
                  </h4>
                  <div className="bg-white/80 dark:bg-gray-800 rounded-xl shadow border border-blue-100 dark:border-gray-700 p-6 mb-6">
              {loadingCategories ? (
                <div className="flex justify-center py-4">
                        <SectionLoader message="Loading service categories..." />
                </div>
              ) : categoriesError ? (
                <div className="text-center py-4 text-red-400">
                  Error loading service categories
                </div>
              ) : (
              <div className="grid grid-cols-2 gap-4">
                  {serviceCategoriesData?.map((category) => (
                  <button
                    type="button"
                    key={category.id}
                    onClick={() => handleServiceCategoryChange(category.id)}
                    className={`p-4 rounded-xl border-2 transition-all
                      ${serviceForm.service_category_id === category.id
                        ? 'border-indigo-500 bg-indigo-500/10 dark:bg-indigo-900/20'
                        : 'border-white/10 dark:border-gray-700 hover:border-white/20 dark:hover:border-indigo-400 bg-transparent dark:bg-gray-900'
                      }`}
                  >
                    <div className="flex items-center gap-3">
                      {category.icon_path ? (
                        <img
                          src={
                            category.icon_path.startsWith('http')
                              ? category.icon_path
                              : `${process.env.REACT_APP_CDN_URL || ''}/${category.icon_path}`
                          }
                          alt={category.name}
                          className="w-8 h-8 object-cover rounded"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      ) : (
                        <svg
                          className="w-6 h-6 text-indigo-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                          />
                        </svg>
                      )}
                      <div className="text-left">
                        <h5 className={`font-medium ${serviceForm.service_category_id === category.id ? 'text-indigo-800 dark:text-indigo-300' : 'text-gray-800 dark:text-gray-100'}`}>{category.name}</h5>
                        <p className="text-sm text-gray-800 dark:text-gray-300">{category.description}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
              )}
              {touched.service_category_id && errors.service_category_id && (
                <p className="text-sm text-red-400">{errors.service_category_id}</p>
              )}
            </div>
                </div>
            {/* Service Type Selection */}
            {serviceForm.service_category_id && (
              <div>
                <h4 className="flex items-center gap-2 text-base font-semibold text-indigo-800 dark:text-indigo-300 mb-4">
                  <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:from-indigo-400 dark:to-purple-400 rounded-full mr-2"></span>
                  Service Type
                </h4>
                <div className="bg-white/80 dark:bg-gray-800 rounded-xl shadow border border-blue-100 dark:border-gray-700 p-6 mb-6">
                  {isOthersCategory ? (
                    <div className="space-y-4">
                      <FloatingLabelInput
                        label="Service Type"
                        type="text"
                        value={serviceForm.service_type_title || ''}
                        onChange={(e) =>
                          handleServiceFieldChange('service_type_title', e.target.value)
                        }
                        placeholder="Enter service type"
                      />
                      <FloatingLabelInput
                        label="Service Description"
                        type="textarea"
                        value={serviceForm.service_type_description || ''}
                        onChange={(e) =>
                          handleServiceFieldChange('service_type_description', e.target.value)
                        }
                        placeholder="Enter service description"
                      />
                    </div>
                  ) : loadingTypes ? (
                    <div className="flex justify-center py-4">
                      <SectionLoader message="Loading service types..." />
                    </div>
                  ) : typesError ? (
                    <div className="text-center py-4 text-red-400">Error loading service types</div>
                  ) : (
                    <div className="grid grid-cols-2 gap-4">
                      {serviceTypesData
                        ?.filter(
                          (type) =>
                            type.service_category_id ===
                            Number(serviceForm.service_category_id)
                        )
                        .map((type) => (
                          <button
                            type="button"
                            key={type.id}
                            onClick={() => handleServiceTypeChange(type.id)}
                            className={`p-4 rounded-xl border-2 transition-all ${serviceForm.service_type_id === type.id ? 'border-indigo-500 bg-indigo-500/10 dark:bg-indigo-900/20' : 'border-white/10 dark:border-gray-700 hover:border-white/20 dark:hover:border-indigo-400 bg-transparent dark:bg-gray-900'}`}
                          >
                            <div className="flex items-center gap-3">
                              {type.icon_path ? (
                                <img
                                  src={
                                    type.icon_path.startsWith('http')
                                      ? type.icon_path
                                      : `${process.env.REACT_APP_CDN_URL || ''}/${type.icon_path}`
                                  }
                                  alt={type.name}
                                  className="w-8 h-8 object-cover rounded"
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                  }}
                                />
                              ) : (
                                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                              )}
                              <div className="text-left">
                                <h5 className={`font-medium ${serviceForm.service_type_id === type.id ? 'text-indigo-800 dark:text-indigo-300' : 'text-gray-800 dark:text-gray-100'}`}>{type.name}</h5>
                                <p className="text-sm text-gray-800 dark:text-gray-300">{type.description}</p>
                              </div>
                            </div>
                          </button>
                        ))}
                    </div>
                  )}
                  {!isOthersCategory && touched.service_type_id && errors.service_type_id && (
                    <p className="text-sm text-red-800">{errors.service_type_id}</p>
                  )}
                </div>
              </div>
            )}
            {/* Pricing Option Selection */}
            {serviceTypeSelected && (
                  <div>
                    <h4 className="flex items-center gap-2 text-base font-semibold text-indigo-800 dark:text-indigo-300 mb-4">
                      <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:from-indigo-400 dark:to-purple-400 rounded-full mr-2"></span>
                      Pricing Option
                    </h4>
                    <div className="bg-white/80 dark:bg-gray-800 rounded-xl shadow border border-blue-100 dark:border-gray-700 p-6 mb-6">
                  <div>
                      <h5 className="text-sm font-medium text-indigo-800 dark:text-indigo-300 mb-2">Select Pricing Type</h5>
                    {loadingPricingTypes ? (
                      <div className="flex justify-center py-4">
                            <SectionLoader message="Loading pricing option types..." />
                      </div>
                    ) : pricingTypesError ? (
                      <div className="text-center py-4 text-red-400">
                        Error loading pricing option types
                      </div>
                    ) : (
                    <div className="grid grid-cols-2 gap-4">
                        {pricingOptionTypesData?.map((type) => (
                        <button
                          type="button"
                          key={type.id}
                          onClick={() => handleServiceFieldChange('pricing_option_id', type.id)}
                          className={`p-4 rounded-xl border-2 transition-all ${serviceForm.pricing_option_id === type.id ? 'border-indigo-500 bg-indigo-500/10 dark:bg-indigo-900/20' : 'border-white/10 dark:border-gray-700 hover:border-white/20 dark:hover:border-indigo-400 bg-transparent dark:bg-gray-900'}`}
                        >
                          <div className="text-left">
                            <h5 className={`font-medium ${serviceForm.pricing_option_id === type.id ? 'text-indigo-800 dark:text-indigo-300' : 'text-gray-800 dark:text-gray-100'}`}>{type.name}</h5>
                                  <p className="text-sm text-gray-800 dark:text-gray-300">{type.description}</p>
                          </div>
                        </button>
                      ))}
                    </div>
                    )}
                    {touched.pricing_option_id && errors.pricing_option_id && (
                      <p className="text-sm text-red-400 mt-2">{errors.pricing_option_id}</p>
                    )}
                  </div>
                </div>
              </div>
            )}
            {isOthersCategory && serviceTypeSelected && (
              <div>
                <h4 className="flex items-center gap-2 text-base font-semibold text-indigo-800 dark:text-indigo-300 mb-4">
                  <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:from-indigo-400 dark:to-purple-400 rounded-full mr-2"></span>
                  Pricing
                </h4>
                <div className="bg-white/80 dark:bg-gray-800 rounded-xl shadow border border-blue-100 dark:border-gray-700 p-6 mb-6">
                  <FloatingLabelInput
                    label="Price (Credits)"
                    type="number"
                    value={serviceForm.price || ''}
                    onChange={(e) => handleServiceFieldChange('price', e.target.value)}
                    min="0"
                    step="0.01"
                    placeholder="Enter amount"
                  />
                </div>
              </div>
            )}
                {/* Service Styles */}
            {serviceTypeSelected && !isOthersCategory && (
                  <div>
                    <h4 className="flex items-center gap-2 text-base font-semibold text-indigo-800 dark:text-indigo-300 mb-4">
                      <span className="w-1 h-6 bg-gradient-to-b from-indigo-500 to-blue-500 dark:from-indigo-400 dark:to-purple-400 rounded-full mr-2"></span>
                      Service Styles
                    </h4>
                    <div className="bg-white/80 dark:bg-gray-800 rounded-xl shadow border border-blue-100 dark:border-gray-700 p-6 mb-6">
                      <p className="text-sm text-gray-800 dark:text-gray-300 mb-4">Select one or more styles for your service</p>
                {loadingStyles ? (
                  <div className="flex justify-center py-4">
                          <SectionLoader message="Loading service styles..." />
                  </div>
                ) : stylesError ? (
                  <div className="text-center py-4 text-red-400">
                    Error loading service styles
                  </div>
                ) : (
                <div className="grid grid-cols-2 gap-4">
                      {filteredServiceStyles.map((style, idx) => {
                        const styleName = style.name || `Style #${idx+1}`;
                        return (
                    <StyleCard
                      key={style.id}
                      style={style}
                      selected={serviceForm.service_style?.some(s => s.service_style_id === style.id)}
                      onSelect={() => handleServiceStyleToggle(style.id)}
                      onPriceChange={(price) => handleServiceStylePriceChange(style.id, price)}
                      price={serviceStylePrices[style.id]}
                      pricingSelected={!!serviceForm.pricing_option_id}
                            styleName={styleName}
                    />
                        );
                      })}
                </div>
                )}
                {touched.service_style && errors.service_style && (
                  <p className="text-sm text-red-400">{errors.service_style}</p>
                )}
                    </div>
              </div>
            )}
            </div>
            </div>
            {/* Footer */}
            <div className="sticky bottom-0 bg-gradient-to-r from-blue-700 via-indigo-800 to-blue-900 dark:from-gray-900 dark:via-gray-950 dark:to-black text-white px-6 py-4 flex gap-2 rounded-b-2xl shadow-lg z-20">
              <button
                type="button"
                onClick={handleCloseServiceForm}
                className="flex-1 px-6 py-3 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-200/50 border border-white/20"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSubmitNewService}
                disabled={isSubmittingService}
                className="flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium shadow-lg shadow-blue-900/20 hover:shadow-xl hover:shadow-indigo-900/30 focus:outline-none focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-lg disabled:hover:shadow-blue-900/25 border border-white/20"
              >
                {isSubmittingService ? 'Saving...' : (serviceForm.id ? 'Save Changes' : 'Create Service')}
              </button>
            </div>
          </div>
        </ServiceFormPortal>
      )}

      {/* Services List Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
        <h3 className="text-xl font-semibold text-indigo-900 dark:text-indigo-300">Your Services</h3>
        <button
          type="button"
          onClick={handleServiceFormToggle}
          className="w-full sm:w-auto px-5 py-2.5 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold shadow hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-200/50 transition-all duration-200"
        >
          + Add Service
        </button>
        </div>

      {/* 2. Service List: Card-based, mobile-friendly */}
        {loadingUserServices ? (
          <SectionLoader message="Loading your services..." />
        ) : userServicesError ? (
          <div className="text-center py-8 text-red-400">
            Error loading services. Please try again.
          </div>
        ) : userServicesData?.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
          No services added yet. Click "+ Add Service" to get started.
          </div>
        ) : (
          <div className="grid gap-4">
            {userServicesData?.map((service, idx) => {
              const isOthersService =
                (service.service_category?.slug || '').toLowerCase() === 'others' ||
                String(service.service_category_id) === '2';
              return (
              <motion.div
                key={service.id}
                style={{ boxShadow: '0 4px 24px 0 rgba(80,80,180,0.08)' }}
                initial={{ opacity: 0, y: 24, boxShadow: '0 4px 24px 0 rgba(80,80,180,0.08)' }}
                animate={{ opacity: 1, y: 0, boxShadow: '0 4px 24px 0 rgba(80,80,180,0.08)' }}
                transition={{ delay: idx * 0.05, type: 'spring', stiffness: 120, damping: 18 }}
                whileHover={{ scale: 1.025, boxShadow: '0 8px 32px 0 rgba(80,80,180,0.10)' }}
                whileFocus={{ scale: 1.025, boxShadow: '0 8px 32px 0 rgba(80,80,180,0.12)' }}
                className={`relative bg-gradient-to-br from-white/80 via-blue-50/70 to-indigo-100/80 dark:from-gray-900 dark:via-gray-900 dark:to-gray-950 backdrop-blur-md rounded-2xl shadow-2xl border border-blue-100 dark:border-gray-800 p-6 mb-2 transition-all duration-200 group overflow-hidden`}
                tabIndex={0}
              >
                {/* Colored left bar for status */}
                <div className={`absolute left-0 top-0 h-full w-2 rounded-l-2xl
                  ${service.status === 'approved' ? 'bg-green-400' : ''}
                  ${service.status === 'pending' ? 'bg-yellow-400' : ''}
                  ${service.status === 'rejected' ? 'bg-red-400' : ''}
                  ${service.status === 'disabled' ? 'bg-gray-400' : ''}
                `} />
                {/* Card Content Layout */}
                <div className="flex flex-col sm:flex-row items-start w-full gap-7">
                  {/* Left: Service Type Icon */}
              <div className="flex-shrink-0 flex items-center justify-center w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-indigo-100 to-blue-100 dark:from-gray-800 dark:to-gray-900 border border-indigo-200 dark:border-gray-700">
                {isOthersService
                  ? service.service_category?.icon_path
                    ? (
                        <img
                          src={
                            service.service_category.icon_path.startsWith('http')
                              ? service.service_category.icon_path
                              : `${process.env.REACT_APP_CDN_URL || ''}/${service.service_category.icon_path}`
                          }
                          alt={service.service_category.name}
                          className="w-10 h-10 object-cover"
                          onError={(e) => {
                            e.target.style.display = 'none';
                          }}
                        />
                      )
                    : (
                        <svg className="w-7 h-7 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      )
                  : service.service_type?.icon_path
                  ? (
                      <img
                        src={
                          service.service_type.icon_path.startsWith('http')
                            ? service.service_type.icon_path
                            : `${process.env.REACT_APP_CDN_URL || ''}/${service.service_type.icon_path}`
                        }
                        alt={service.service_type.name}
                        className="w-10 h-10 object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    )
                  : (
                      <svg className="w-7 h-7 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    )}
              </div>
                  {/* Center: Title, Description, Status, Styles/Pricing */}
                  <div className="flex-1 min-w-0 flex flex-col gap-2 text-left">
                <div className="flex items-center gap-3 mb-1">
                  <h4 className="text-lg font-semibold text-indigo-900 dark:text-indigo-200 truncate">
                          {service.title || (isOthersService ? service.service_type_title : service.service_type?.name)}
                        </h4>
                  {/* Status badge: pill with color */}
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold shadow bg-gradient-to-r
                        ${service.status === 'approved' ? 'from-green-500 to-green-600' : ''}
                        ${service.status === 'pending' ? 'from-yellow-400 to-yellow-500' : ''}
                        ${service.status === 'rejected' ? 'from-red-500 to-red-500' : ''}
                        ${service.status === 'disabled' ? 'from-gray-400 to-gray-400' : ''}
                  `}>
                    {service.status === 'approved' && 'Approved'}
                    {service.status === 'pending' && 'Pending'}
                    {service.status === 'rejected' && 'Rejected'}
                    {service.status === 'disabled' && 'Disabled'}
                  </span>
                </div>
                    <p className="text-sm text-gray-800 dark:text-gray-300 mb-2 truncate text-left">
                          {service.description || (isOthersService ? service.service_type_description : service.service_type?.description)}
                        </p>
                {/* Service Styles & Pricing */}
                <div className="space-y-2">
                  {isOthersService ? (
                    <div className="flex items-center gap-1">
                      <span className="text-base font-semibold text-gray-900 dark:text-gray-100">{service.price || 0}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">Credits</span>
                    </div>
                  ) : ( (service.service_style || service.service_styles)?.length > 0 ? (
                      (service.service_style || service.service_styles).map((style, idx) => {
                        const styleId = style.service_style_id || style.id;
                        const styleName =
                          style.name ||
                          serviceTypeStyles[service.service_type_id]?.find((s) => s.id === styleId)?.name ||
                          filteredServiceStyles.find((s) => s.id === styleId)?.name ||
                          `Style #${idx + 1}`;
                        return (
                          <div key={styleId || idx} className="flex items-center justify-between bg-white/70 dark:bg-gray-800 rounded-full shadow px-3">
                            <div className="flex items-center gap-3 text-left mt-3 ml-3">
                              <span className="font-medium text-indigo-800 dark:text-indigo-300">{styleName}</span>
                            </div>
                            <div className="flex items-center gap-2 mt-3">
                              <span className="text-base font-semibold text-gray-900 dark:text-gray-100">{style.price || 0}</span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">Credits</span>
                              <Switch
                                checked={
                                  optimisticToggles[`${service.id}-${styleId}`] ??
                                  Boolean(style.is_active)
                                }
                                disabled={togglingStyles[`${service.id}-${styleId}`]}
                                onChange={() =>
                                  handleServiceStyleActiveToggle(
                                    service.id,
                                    styleId,
                                    Boolean(style.is_active)
                                  )
                                }
                                color="primary"
                                inputProps={{ 'aria-label': 'Activate style' }}
                                className="ml-2 mt-2"
                              />
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <span className="text-sm text-gray-400 dark:text-gray-500">No styles added</span>
                    ) )}
                </div>
                {/* Pricing Option */}
                {service.pricing_option && (
                  <div className="mt-2 text-xs text-indigo-700 dark:text-indigo-300 font-medium">
                    Pricing Option: {service.pricing_option.name}
                      </div>
                    )}
                      </div>
                  {/* Right: Action icon: delete */}
                  <div className="flex flex-col gap-2 items-end justify-center min-h-[56px]">
                    <motion.button
                  type="button"
                      whileHover={{ scale: 1.15 }}
                      whileTap={{ scale: 0.92 }}
                  onClick={() => handleDeleteServiceInternal(service.id)}
                  className="p-2 rounded-full hover:bg-red-100/40 dark:hover:bg-red-900/40 bg-red-50 dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-red-400/30 text-red-500 dark:text-red-400"
                  aria-label="Delete Service"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                    </motion.button>
                </div>
              </div>
              </motion.div>
              );
            })}
          </div>
        )}

      {/* Revisions Modal */}
      {showRevisions && selectedServiceId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />
          <div className="relative bg-gray-900 dark:bg-black rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-200 dark:text-white">Revision History</h3>
              <button
                type="button"
                onClick={() => setShowRevisions(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-4">
              {revisions.map((revision, index) => (
                <RevisionTimelineItem
                  key={revision.id}
                  revision={revision}
                  isLatest={index === 0}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// ServiceFormPortal: renders children in a portal to document.body
export const ServiceFormPortal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  return ReactDOM.createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
        aria-label="Close service form modal"
      />
      {/* Modal Content */}
      <div className="relative z-10 w-full max-w-lg sm:max-w-xl md:max-w-2xl mx-auto">
        {children}
      </div>
    </div>,
    document.body
  );
};

export default ServiceManagement;
