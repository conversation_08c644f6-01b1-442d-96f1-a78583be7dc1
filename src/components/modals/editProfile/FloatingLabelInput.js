import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const FloatingLabelInput = ({
    id,
    label,
    type = 'text',
    value,
    onChange,
    error,
    maxLength,
    placeholder,
    required = false,
    disabled = false,
    className = '',
    multiline = false,
    ...props
}) => {
    const [isFocused, setIsFocused] = useState(false);
    const [isFilled, setIsFilled] = useState(!!value);

    useEffect(() => {
        setIsFilled(!!value);
    }, [value]);

    const inputClasses = `w-full px-4 py-3 bg-white/90 border-2 rounded-xl shadow-sm
        focus:outline-none focus:ring-2 focus:ring-indigo-400/40 focus:border-indigo-500
        transition-all duration-200 text-base
        ${error ? 'border-red-400 focus:ring-red-300 focus:border-red-500' : 'border-blue-200'}
        ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-100' : ''}
        text-gray-900 placeholder-transparent
        dark:bg-black dark:text-white dark:placeholder-gray-400 dark:border-gray-700`;

    const labelClasses = `absolute left-4 transition-all duration-200 pointer-events-none z-10
        px-2 py-0.5 rounded-full shadow-sm
        bg-white/90 dark:bg-black
        ${isFocused || isFilled
            ? '-top-4 text-xs text-indigo-600 scale-95 font-semibold shadow-md dark:text-indigo-300'
            : 'top-3 text-gray-500 font-medium dark:text-gray-300'}
        ${error && (isFocused || isFilled) ? 'text-red-500 dark:text-red-400' : ''}
        `;

    return (
        <div className={`relative ${className}`}>
            {multiline ? (
                <textarea
                    id={id}
                    value={value}
                    onChange={onChange}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    maxLength={maxLength}
                    placeholder={placeholder}
                    required={required}
                    disabled={disabled}
                    className={`${inputClasses} min-h-[100px] resize-y`}
                    {...props}
                />
            ) : (
                <input
                    id={id}
                    type={type}
                    value={value}
                    onChange={onChange}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    maxLength={maxLength}
                    placeholder={placeholder}
                    required={required}
                    disabled={disabled}
                    className={inputClasses}
                    {...props}
                />
            )}
            <label
                htmlFor={id}
                className={labelClasses}
            >
                {label}
            </label>
            {error && (
                <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-1 text-sm text-red-400 dark:text-red-400"
                >
                    {error}
                </motion.p>
            )}
            {maxLength && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-300 text-right">
                    {value.length}/{maxLength}
                </p>
            )}
        </div>
    );
};

export default FloatingLabelInput;
