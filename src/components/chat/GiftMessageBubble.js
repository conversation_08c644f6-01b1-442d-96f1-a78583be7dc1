import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
import { getCdnUrl } from '../../utils/cdnUtils';

// NOTE: For best results, add a playful font like 'Baloo 2' or 'Comic Neue' to your Tailwind config or import via Google Fonts in your app's index.html.
// Example for Google Fonts: <link href="https://fonts.googleapis.com/css2?family=Baloo+2:wght@600;800&display=swap" rel="stylesheet" />
// Then add 'font-baloo' to your Tailwind config:
// theme: { extend: { fontFamily: { baloo: ['Baloo 2', 'Comic Sans MS', 'cursive', 'sans-serif'] } } }

// Animation variants
const bubbleVariants = {
  initial: { opacity: 0, scale: 0.7, y: 40 },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 22,
      bounce: 0.5,
      delayChildren: 0.1,
      staggerChildren: 0.08
    }
  }
};

// Use 'tween' for multi-step pop effect
const giftIconVariants = {
  initial: { scale: 0.5, rotate: -15 },
  animate: {
    scale: [0.5, 1.2, 1],
    rotate: [0, 10, -10, 0],
    transition: {
      type: 'tween',
      duration: 0.7
    }
  }
};

// Use 'tween' for multi-step badge pop
const badgeVariants = {
  initial: { scale: 0, y: -10 },
  animate: {
    scale: [0, 1.3, 1],
    y: [0, -5, 0],
    transition: {
      type: 'tween',
      duration: 0.5,
      delay: 0.2
    }
  }
};

// Simple SVG confetti burst (animated with framer-motion)
const ConfettiBurst = () => (
  <motion.svg
    width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg"
    className="pointer-events-none"
    initial={{ opacity: 0, scale: 0.7 }}
    animate={{ opacity: 1, scale: [0.7, 1.1, 1], rotate: [0, 10, -10, 0] }}
    exit={{ opacity: 0, scale: 0.7 }}
    transition={{ duration: 1.2, type: 'tween', ease: 'easeInOut' }}
  >
    <circle cx="20" cy="20" r="4" fill="#FBBF24" />
    <circle cx="60" cy="10" r="5" fill="#F472B6" />
    <circle cx="100" cy="18" r="3" fill="#60A5FA" />
    <circle cx="40" cy="35" r="3" fill="#A78BFA" />
    <circle cx="80" cy="30" r="4" fill="#34D399" />
    <rect x="30" y="10" width="3" height="10" rx="1.5" fill="#F87171" />
    <rect x="90" y="25" width="2.5" height="8" rx="1.2" fill="#FBBF24" />
    <rect x="70" y="5" width="2" height="7" rx="1" fill="#60A5FA" />
    <rect x="55" y="30" width="2.5" height="8" rx="1.2" fill="#A78BFA" />
    <rect x="110" y="10" width="2" height="7" rx="1" fill="#F472B6" />
  </motion.svg>
);

// Props: msg, isCurrentUser, partnerInfo
const GiftMessageBubble = ({ msg, isCurrentUser, partnerInfo }) => {
  // Debug: log the message and giftData to inspect image fields
  // console.log('[GiftMessageBubble] msg:', msg);
  const isReceived = !isCurrentUser;
  const giftData = msg.gift_data || msg.gift || {};
  // console.log('[GiftMessageBubble] giftData:', giftData);
  const quantity = giftData.quantity || msg.quantity || 1;
  const giftName = giftData.gift_name || giftData.name || 'Gift';
  const giftImage = getCdnUrl(
    giftData.icon_path ||
    giftData.gift_image_url ||
    giftData.image_url ||
    '/gift.png'
  );
  const senderName = isCurrentUser ? 'You' : (msg.sender?.nickname || partnerInfo?.nickname || partnerInfo?.name || 'Someone');
  const recipientName = isCurrentUser ? (partnerInfo?.nickname || partnerInfo?.name || 'Someone') : 'You';
  const message = msg.content || giftData.message || '';

  // Playful gradient backgrounds
  const bubbleGradient = isReceived
    ? 'bg-gradient-to-br from-pink-200 via-yellow-100 to-pink-100 dark:from-pink-700 dark:via-yellow-800 dark:to-pink-900'
    : 'bg-gradient-to-br from-indigo-200 via-purple-100 to-indigo-100 dark:from-indigo-900 dark:via-purple-900 dark:to-indigo-800';

  // Bubble tail
  const tailClass = isReceived
    ? 'after:absolute after:-left-2 after:bottom-2 after:w-4 after:h-4 after:bg-pink-100 dark:after:bg-pink-900 after:rounded-bl-2xl after:rotate-45'
    : 'after:absolute after:-right-2 after:bottom-2 after:w-4 after:h-4 after:bg-indigo-100 dark:after:bg-indigo-900 after:rounded-br-2xl after:rotate-45';

  // Confetti state (show for 1.5s on mount)
  const [showConfetti, setShowConfetti] = useState(isReceived);
  useEffect(() => {
    if (isReceived) {
      setShowConfetti(true);
      const timeout = setTimeout(() => setShowConfetti(false), 1500);
      return () => clearTimeout(timeout);
    }
  }, [isReceived]);

  return (
    <motion.div
      variants={bubbleVariants}
      initial="initial"
      animate="animate"
      className={cn(
        'relative max-w-xs sm:max-w-md mx-2 my-2 p-4 rounded-3xl shadow-xl flex flex-col items-center',
        bubbleGradient,
        tailClass,
        isReceived ? 'self-start' : 'self-end',
        'overflow-visible border-2 border-white/70 dark:border-pink-900/60',
        'transition-all duration-200 ease-in-out'
      )}
    >
      {/* Confetti overlay for received gifts */}
      {isReceived && showConfetti && (
        <div className="absolute -top-10 left-1/2 -translate-x-1/2 z-30 pointer-events-none">
          <ConfettiBurst />
        </div>
      )}
      {/* Gift image/icon */}
      <motion.div
        className="w-24 h-24 rounded-3xl bg-gradient-to-br from-blue-100 via-indigo-100 to-violet-100 dark:from-blue-900 dark:via-indigo-900 dark:to-violet-900 flex items-center justify-center mb-2 shadow-xl border-2 border-white/70 dark:border-indigo-900/60 backdrop-blur-md"
        variants={giftIconVariants}
      >
        <img
          src={giftImage}
          alt={giftName}
          className="w-20 h-20 object-contain rounded-2xl bg-white/80 dark:bg-gray-900/80 shadow-lg dark:shadow-yellow-900/10"
        />
      </motion.div>
      {/* Quantity badge */}
      {quantity > 1 && (
        <motion.div
          className="absolute -top-2 -right-2 bg-yellow-400 dark:bg-yellow-500 text-white dark:text-gray-900 text-xs font-extrabold px-2 py-1 rounded-full shadow-lg border-2 border-white dark:border-yellow-900 drop-shadow-md"
          variants={badgeVariants}
        >
          x{quantity}
        </motion.div>
      )}
      {/* Gift name */}
      <motion.div className="text-lg font-extrabold text-pink-600 dark:text-yellow-300 drop-shadow-lg mb-1 font-baloo tracking-wide" variants={giftIconVariants}>
        {isReceived ? '🎁 You received a' : '🎁 You sent a'} <span className="text-indigo-600 dark:text-yellow-400 font-extrabold">{giftName}</span>!
      </motion.div>
      {/* Sender/recipient info */}
      <div className="text-xs text-gray-500 dark:text-gray-300 mb-1 font-semibold">
        {isReceived
          ? <>From: <span className="font-bold text-indigo-500 dark:text-yellow-400">{senderName}</span></>
          : <>To: <span className="font-bold text-pink-500 dark:text-yellow-300">{recipientName}</span></>}
      </div>
      {/* Personal message */}
      {message && (
        <motion.div className="bg-white/90 dark:bg-gray-900/90 rounded-xl px-3 py-2 text-base text-pink-700 dark:text-yellow-200 shadow-inner mt-1 font-baloo font-semibold tracking-tight border border-pink-100 dark:border-yellow-900" variants={giftIconVariants}>
          “{message}”
        </motion.div>
      )}
    </motion.div>
  );
};

export default GiftMessageBubble; 