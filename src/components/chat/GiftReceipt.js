import React from 'react';
import { motion } from 'framer-motion';
import { DateTime } from 'luxon';
import { Button } from '../ui/button';
import { CheckCircledIcon, Cross2Icon } from '@radix-ui/react-icons';

/**
 * GiftReceipt component displays a transaction receipt after sending a gift
 */
const GiftReceipt = ({
  isVisible,
  transaction,
  gift,
  recipient,
  wasFromInventory,
  inventoryBefore,
  inventoryAfter,
  walletBefore,
  walletAfter,
  onClose,
  status = 'success' // 'success', 'pending', 'error'
}) => {
  // Early return if not visible
  if (!isVisible || !gift) return null;

  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount / 100); // Assuming price is in cents
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Just now';
    return DateTime.fromISO(timestamp).toFormat('LLL dd, yyyy HH:mm:ss');
  };

  // Calculate price (free if from inventory)
  const price = wasFromInventory ? 0 : gift.price;
  
  // Get transaction ID or generate a placeholder
  const transactionId = transaction?.id || `temp-${Date.now()}`;

  return (
    <motion.div
      className="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-xl shadow-xl w-full max-w-md overflow-hidden"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
      >
        {/* Header */}
        <div className="relative px-6 py-5 border-b border-gray-200 flex items-center justify-center">
          <button 
            onClick={onClose}
            className="absolute right-4 top-4 p-1 rounded-full hover:bg-gray-100 transition-colors"
          >
            <Cross2Icon className="w-4 h-4 text-gray-500" />
          </button>
          
          <div className="text-center">
            {status === 'success' && (
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <CheckCircledIcon className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Gift Sent Successfully!</h3>
              </div>
            )}
            
            {status === 'pending' && (
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-2">
                  <div className="w-6 h-6 border-2 border-amber-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Processing Gift...</h3>
              </div>
            )}
            
            {status === 'error' && (
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2">
                  <Cross2Icon className="w-6 h-6 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Gift Sending Failed</h3>
              </div>
            )}
          </div>
        </div>
        
        {/* Receipt Content */}
        <div className="p-6 space-y-4">
          {/* Transaction Details */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg p-2 mr-3 flex items-center justify-center">
                  <img 
                    src={gift.image_url} 
                    alt={gift.name} 
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{gift.name}</h4>
                  <p className="text-sm text-gray-500">
                    {formatTimestamp(transaction?.created_at)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-indigo-600 flex items-center">
                  {price > 0 ? (
                    <>
                      <span className="mr-1">⭐</span>
                      {price.toLocaleString()}
                    </>
                  ) : (
                    <span className="text-sm bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                      From Inventory
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-3 mt-3">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-500">Recipient:</span>
                <span className="text-gray-900 font-medium">{recipient}</span>
              </div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-500">Transaction ID:</span>
                <span className="text-gray-700 font-mono text-xs">{transactionId}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Status:</span>
                <span className={`font-medium ${
                  status === 'success' ? 'text-green-600' :
                  status === 'pending' ? 'text-amber-600' : 'text-red-600'
                }`}>
                  {status === 'success' ? 'Completed' : 
                   status === 'pending' ? 'Processing' : 'Failed'}
                </span>
              </div>
            </div>
          </div>
          
          {/* Inventory Update */}
          {wasFromInventory && status === 'success' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Inventory Update</h4>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-500">Previous Quantity:</span>
                <span className="text-gray-900">{inventoryBefore}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Updated Quantity:</span>
                <span className="text-gray-900">{inventoryAfter}</span>
              </div>
            </div>
          )}
          
          {/* Wallet Update (Only if purchased and sent) */}
          {!wasFromInventory && status === 'success' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Wallet Update</h4>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-500">Previous Balance:</span>
                <span className="text-gray-900">⭐ {walletBefore.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Updated Balance:</span>
                <span className="text-gray-900">⭐ {walletAfter.toLocaleString()}</span>
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="flex justify-end pt-2">
            <Button
              onClick={onClose}
              variant="secondary"
              className="mr-2"
            >
              Close
            </Button>
            
            {status === 'success' && (
              <Button 
                className="bg-gradient-to-r from-indigo-500 to-purple-600"
              >
                Send Another Gift
              </Button>
            )}
            
            {status === 'error' && (
              <Button 
                className="bg-gradient-to-r from-indigo-500 to-purple-600"
              >
                Try Again
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default GiftReceipt; 