import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Cross1Icon, PlusIcon } from '@radix-ui/react-icons';
import { giftApi } from '../../services/giftApi';
import { chatApi } from '../../services/chatApi';
import giftService from '../../services/giftService';
import walletAPI from '../../services/walletService';
import { transactionService } from '../../services/transactionService';
import GiftItem from './GiftItem';
import { getCdnUrl } from '../../utils/cdnUtils';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { Progress } from '../ui/progress';
import { Textarea } from '../ui/textarea';
import { Spinner } from '../ui/spinner';
import { useToast } from '../common/ToastProvider';
import { useAnimation, AnimatePresence as FMAnimatePresence } from 'framer-motion';

const GiftModal = ({
    isOpen,
    onClose,
    onSendGift,
    conversationId,
    conversationPartnerName,
    recipientUserId
}) => {
    const [gifts, setGifts] = useState([]);
    const [popularGifts, setPopularGifts] = useState([]);
    const [categories, setCategories] = useState(['All', 'Popular', 'Animals', 'Food', 'Emotions', 'Special']);
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [selectedGift, setSelectedGift] = useState(null);
    const [message, setMessage] = useState('');
    const [walletBalance, setWalletBalance] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [isSending, setIsSending] = useState(false);
    const sendInProgressRef = useRef(false);
    const [isPurchasing, setIsPurchasing] = useState(false);
    const [error, setError] = useState(null);
    const [filterMode, setFilterMode] = useState('all'); // 'all', 'owned', 'shop'
    const [quantity, setQuantity] = useState(1);
    const [userGifts, setUserGifts] = useState([]);
    const [quantityError, setQuantityError] = useState('');
    const { success: showSuccessToast } = useToast();
    const detailsPanelRef = useRef(null);
    const [showConfetti, setShowConfetti] = useState(false);
    const prevWalletBalance = useRef(walletBalance);
    const [animatedBalance, setAnimatedBalance] = useState(walletBalance);

    useEffect(() => {
        if (isOpen) {
            loadWalletBalance();
            loadCategories();
            // loadUserGifts is invoked within loadGifts to avoid duplicate API calls
        }
    }, [isOpen]);

    useEffect(() => {
        if (!isOpen) return;

        if (selectedCategory === 'All') {
            loadGifts();
        } else if (selectedCategory) {
            loadGiftsByCategory(selectedCategory);
        }
    }, [isOpen, selectedCategory]);

    // Load gift categories from the backoffice API
    const loadCategories = async () => {
        try {
            const response = await giftService.getGiftCategories();
            if (response.data && response.data.categories) {
                const categoryNames = ['All', ...response.data.categories.map(cat => cat.name)];
                setCategories(categoryNames);
            }
        } catch (error) {
            console.error('Error loading gift categories:', error);
        }
    };

    // Load gifts with inventory information
    const loadGifts = async () => {
        try {
            setIsLoading(true);
            let allGifts = [];
            let inventory = [];
            const [catalogResp, inventoryResp] = await Promise.all([
                giftService.getGiftItems(),
                giftService.getUserGifts()
            ]);
            allGifts = catalogResp.data?.gifts || catalogResp.gifts || [];
            inventory = inventoryResp.data?.user_gifts || inventoryResp.user_gifts || [];
            const inventoryMap = new Map(
                inventory.map(g => [g.gift_item_id, g])
            );
            setGifts(
                allGifts.map(g => {
                    const inv = inventoryMap.get(g.id);
                    const giftableQty = inv?.giftable_quantity;
                    const qty = inv?.quantity;
                    const isOwned = !!inv && (typeof giftableQty === 'number' ? giftableQty > 0 : (typeof qty === 'number' ? qty > 0 : false));
                    return {
                        ...g,
                        image_url: g.image_url ? getCdnUrl(g.image_url) : getCdnUrl(g.icon_path),
                        owned: isOwned,
                        giftable_quantity: giftableQty,
                        user_gift_id: inv?.id,
                        quantity: qty
                    };
                })
            );
            setUserGifts(
                inventory.map(g => ({
                    ...g,
                    image_url: g.icon_path
                        ? getCdnUrl(g.icon_path)
                        : getCdnUrl(g.image_url),
                    owned: true,
                    user_gift_id: g.id,
                    id: g.gift_item_id
                }))
            );
            const popular = allGifts.filter(gift => gift.is_popular);
            setPopularGifts(popular);
        } catch (error) {
            console.error('Error loading gifts:', error);
            setError('Failed to load gifts. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // Load gifts by category
    const loadGiftsByCategory = async (category) => {
        try {
            setIsLoading(true);
            setError(null);
            
            // Convert category name to slug for API call
            const categorySlug = category === 'All' 
                ? 'all' 
                : category.toLowerCase().replace(/\s+/g, '-');
            
            const response = await giftService.getEnhancedGiftCatalog(categorySlug);
            setGifts(response.data.gifts || []);
        } catch (error) {
            console.error(`Error loading gifts for category ${category}:`, error);
            setError('Failed to load gifts for this category.');
        } finally {
            setIsLoading(false);
        }
    };

    const loadWalletBalance = async () => {
        try {
            const response = await walletAPI.getBalance();
            setWalletBalance(response.data.credits_balance || 0);
        } catch (error) {
            console.error('Error loading wallet balance:', error);
        }
    };

    const loadUserGifts = async () => {
        try {
            const response = await giftService.getUserGifts();
            const inventory = response.data?.user_gifts || [];
            setUserGifts(
                inventory.map(g => ({
                    ...g,
                    image_url: g.icon_path
                        ? getCdnUrl(g.icon_path)
                        : getCdnUrl(g.image_url),
                    owned: true,
                    user_gift_id: g.id,
                    id: g.gift_item_id
                }))
            );
        } catch (error) {
            console.error('Error loading user gifts:', error);
        }
    };

    const handleSelectGift = (gift) => {
        setSelectedGift(gift);
        setQuantity(1); // Reset quantity on new selection
        // Auto-populate a message based on the gift
        if (!message) {
            setMessage(`Here's a ${gift.name} for you!`);
        }
        // Clear any previous success messages
        // Scroll to details panel after state update
        setTimeout(() => {
            if (detailsPanelRef.current) {
                detailsPanelRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }, 100);
    };

    const handlePurchaseGift = async (gift) => {
        setIsPurchasing(true);
        setError(null);
        

        try {
            // Execute transaction to purchase the gift
            const result = await transactionService.executeTransaction({
                amount: gift.price,
                transactionFn: async () => {
                    return await giftService.purchaseGift(gift.id, 1);
                },
                onValidationFailed: (validationResult) => {
                    setError(`Insufficient balance to purchase this gift. Required: ${gift.price}, Available: ${validationResult.currentBalance}`);
                },
                onSuccess: async (result, balanceBefore) => {
                    // Update wallet balance
                    const newBalance = balanceBefore - gift.price;
                    setWalletBalance(newBalance);
                    
                    // Show global toast
                    showSuccessToast({ title: 'Gift Purchased', description: `Successfully purchased ${gift.name}!` }, 4000);
                    
                    // Refresh gifts to update inventory
                    await loadGifts();
                    setShowConfetti(true);
                    setTimeout(() => setShowConfetti(false), 1500);
                },
                onError: (error) => {
                    console.error('Error purchasing gift:', error);
                    setError('Failed to purchase gift. Please try again.');
                    // toast.error('Failed to purchase gift. Please try again.'); // This line is removed
                }
            });
        } finally {
            setIsPurchasing(false);
        }
    };

    const handleSendFromInventory = async () => {
        if (sendInProgressRef.current) return;
        if (!selectedGift || !selectedGift.owned) return;
        if (quantity < 1 || quantity > selectedGift.giftable_quantity) {
            setError(`You can only send between 1 and ${selectedGift.giftable_quantity} of this gift.`);
            // toast.error(`You can only send between 1 and ${selectedGift.giftable_quantity} of this gift.`); // This line is removed
            return;
        }
        sendInProgressRef.current = true;
        setIsSending(true);
        setError(null);
        try {
            const inventoryBefore = selectedGift.quantity;
            // Send the gift to the recipient
            const response = await giftService.giftToUser(
                selectedGift.user_gift_id,
                recipientUserId,
                Number(quantity)
            );
            // Show toast with backend message
            if (response.data && response.data.message) {
                showSuccessToast({ title: 'Gift Sent', description: response.data.message }, 4000);
            }
            // Send a chat message about the gift
            const chatResponse = await chatApi.sendGiftMessage({
                conversation_id: conversationId,
                gift_item_id: selectedGift.id,
                message,
                quantity
            });
            // Skip showing a receipt when sending a gift from inventory
            await loadUserGifts();
            // toast.success(`You sent ${selectedGift.name} x${quantity} to ${conversationPartnerName}`); // This line is removed
            setShowConfetti(true);
            setTimeout(() => setShowConfetti(false), 1500);
            onSendGift(chatResponse.data);
            setIsSending(false);
            sendInProgressRef.current = false;
            onClose();
        } catch (error) {
            console.error('Error sending gift from inventory:', error);
            setError('Failed to send gift from inventory. Please try again.');
            // toast.error('Failed to send gift. Please try again.'); // This line is removed
            setIsSending(false);
            sendInProgressRef.current = false;
        }
    };

    const handlePurchaseAndSend = async () => {
        if (sendInProgressRef.current) return;
        if (!selectedGift || selectedGift.owned) return;
        if (quantity < 1) {
            setError('You must send at least 1 gift.');
            // toast.error('You must send at least 1 gift.'); // This line is removed
            return;
        }
        sendInProgressRef.current = true;
        setIsSending(true);
        setError(null);
        const result = await transactionService.executeGiftTransaction({
            amount: selectedGift.price * quantity,
            sendGiftFn: async () => {
                // Use the new giftService.sendGiftMessage endpoint
                return await giftService.sendGiftMessage(conversationId, selectedGift.id, message, quantity);
            },
            onInsufficientBalance: (validationResult) => {
                setError(`Insufficient balance to send this gift. Required: ${selectedGift.price * quantity}, Available: ${validationResult.currentBalance}`);
                setIsSending(false);
                sendInProgressRef.current = false;
            },
            onSuccess: (result, balanceBefore) => {
                const newBalance = balanceBefore - selectedGift.price * quantity;
                setWalletBalance(newBalance);
                // Refresh inventory and notify user
                loadUserGifts();
                // toast.success(`You sent ${selectedGift.name} x${quantity} to ${conversationPartnerName}`); // This line is removed
                onSendGift(result.data);
                setIsSending(false);
                sendInProgressRef.current = false;
                onClose();
                setShowConfetti(true);
                setTimeout(() => setShowConfetti(false), 1500);
            },
            onError: (error) => {
                console.error('Error in gift transaction:', error);
                if (error.response && error.response.status === 422) {
                    setError(error.response.data.message || 'Failed to send gift due to validation error.');
                } else {
                    setError('Failed to send gift. Please try again.');
                }
                // toast.error('Failed to send gift. Please try again.'); // This line is removed
                loadWalletBalance();
                setIsSending(false);
                sendInProgressRef.current = false;
            }
        });
        if (!result.success) {
            setIsSending(false);
            sendInProgressRef.current = false;
        }
    };

    const handleSendGift = () => {
        if (!selectedGift) return;
        
        // Determine which flow to use based on gift ownership
        if (selectedGift.owned) {
            handleSendFromInventory();
        } else {
            handlePurchaseAndSend();
        }
    };


    // Filter gifts based on selected mode (all, owned, shop)
    const filteredGiftsByMode = () => {
        if (filterMode === 'shop') {
            // Show only gifts that can be purchased (exclude owned ones)
            return gifts.filter(g => !g.owned);
        } else if (filterMode === 'owned') {
            // Show only gifts the user owns from inventory, and only if they have a positive quantity
            return userGifts.filter(g => (typeof g.giftable_quantity === 'number' ? g.giftable_quantity > 0 : (typeof g.quantity === 'number' ? g.quantity > 0 : false)));
        }
        // All gifts
        return gifts;
    };

    // Format currency for display
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount / 100); // Assuming price is in cents
    };

    // Validate quantity on change
    const handleQuantityChange = (e) => {
        const val = Number(e.target.value);
        let max = selectedGift.owned ? selectedGift.giftable_quantity : 10;
        if (val < 1) {
            setQuantity(1);
            setQuantityError('Quantity must be at least 1.');
        } else if (val > max) {
            setQuantity(max);
            setQuantityError(selectedGift.owned ? `You only own ${max} of this gift.` : `Max allowed is ${max}.`);
        } else {
            setQuantity(val);
            setQuantityError('');
        }
    };

    // Animate wallet balance number
    useEffect(() => {
        if (walletBalance !== prevWalletBalance.current) {
            const start = prevWalletBalance.current;
            const end = walletBalance;
            const duration = 600;
            const startTime = performance.now();
            function animate(now) {
                const elapsed = now - startTime;
                const progress = Math.min(elapsed / duration, 1);
                setAnimatedBalance(Math.floor(start + (end - start) * progress));
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    setAnimatedBalance(end);
                }
            }
            requestAnimationFrame(animate);
            prevWalletBalance.current = walletBalance;
        }
    }, [walletBalance]);

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    key="modal"
                    className="fixed inset-0 bg-black/70 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-violet-100 dark:bg-gradient-to-br dark:from-blue-950 dark:via-indigo-950 dark:to-violet-950 backdrop-blur-2xl border border-indigo-100 dark:border-indigo-700 rounded-3xl shadow-2xl dark:shadow-indigo-900/40 w-full max-w-full sm:max-w-lg md:max-w-2xl lg:max-w-3xl max-h-[90vh] overflow-hidden overflow-y-auto custom-scrollbar px-2 sm:px-0"
                        initial={{ scale: 0.9, y: 20, opacity: 0 }}
                        animate={{ scale: 1, y: 0, opacity: 1 }}
                        exit={{ scale: 0.9, y: 20, opacity: 0 }}
                        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-200/40 to-indigo-200/40 rounded-full blur-2xl animate-pulse z-0" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-violet-200/40 to-blue-200/40 dark:from-violet-700/40 dark:to-blue-700/40 rounded-full blur-xl animate-pulse delay-1000 z-0" />
                        {/* Modal Header */}
                        <div className="relative z-10 p-6 border-b border-indigo-100/40 dark:border-indigo-700/60 bg-white dark:bg-blue-950/90">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-blue-400 to-indigo-400 dark:from-blue-500 dark:to-indigo-600 rounded-2xl shadow-lg dark:shadow-indigo-900/30">
                                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7V6a2 2 0 00-2-2h-1.28a2 2 0 00-1.92-1.37c-.88 0-1.64.58-1.9 1.37H9.1A2 2 0 007.18 2.63 2 2 0 005.28 4H4a2 2 0 00-2 2v1a2 2 0 001.37 1.92c0 .88.58 1.64 1.37 1.9V15a2 2 0 002 2h10a2 2 0 002-2V8.82c.79-.26 1.37-1.02 1.37-1.9A2 2 0 0020 7zM7 7a1 1 0 110-2 1 1 0 010 2zm10 0a1 1 0 110-2 1 1 0 010 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-700 via-indigo-700 to-violet-700 dark:from-blue-400 dark:via-indigo-400 dark:to-yellow-400 text-left bg-clip-text text-transparent">
                                            Send a Virtual Gift
                                        </h2>
                                    </div>
                                </div>
                            <button 
                                onClick={onClose}
                                    className="p-2 hover:bg-white/20 dark:hover:bg-indigo-900/40 bg-transparent shadow-lg rounded-xl transition-colors"
                            >
                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                            </button>
                            </div>
                        </div>

                        {/* Confetti/Fireworks Animation */}
                        <FMAnimatePresence>
                            {showConfetti && (
                                <motion.div
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    transition={{ duration: 0.5 }}
                                    className="pointer-events-none fixed inset-0 flex items-center justify-center z-50"
                                >
                                    <span className="text-6xl select-none animate-confetti-burst">🎉✨🎊</span>
                                </motion.div>
                            )}
                        </FMAnimatePresence>

                        {/* Wallet Balance */}
                        <div className="my-6 flex items-center justify-between">
                            <div className="flex-1">
                                <div className="rounded-2xl border-2 border-transparent bg-white/80 dark:bg-blue-950/80 backdrop-blur-md shadow-lg dark:shadow-indigo-900/30 px-6 py-4 flex items-center justify-between max-w-xs w-full" style={{ borderImage: 'linear-gradient(90deg, #3b82f6 0%, #6366f1 100%) 1' }}>
                                    <div className="w-12 h-12 flex items-center justify-center rounded-full">
                                        <img src="/In-AppAssets/xcoin.png" alt="Currency Icon" className="w-12 h-12" />
                                    </div>
                                    <div className="flex-1 flex flex-col items-end justify-center">
                                        <p className="text-sm text-blue-700 dark:text-yellow-300 font-semibold">Your Balance</p>
                                        <p className="text-2xl font-extrabold text-blue-600 dark:text-yellow-400 flex items-center">
                                            {animatedBalance.toLocaleString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="ml-2 flex-shrink-0">
                                <button
                                    className="px-5 py-3 rounded-xl font-bold bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-yellow-500 dark:to-yellow-400 text-white dark:text-gray-900 shadow-lg dark:shadow-yellow-900/20 hover:from-blue-600 hover:to-indigo-600 dark:hover:from-yellow-400 dark:hover:to-yellow-300 transition-colors flex items-center gap-2"
                                    onClick={() => window.location.href = '/wallet'}
                                >
                                    <PlusIcon className="w-5 h-5" />
                                    Go to Wallet to Top Up
                                </button>
                            </div>
                                </div>

                        {/* Modal Content: Make tabs, grid, and details panel scroll together */}
                        <FMAnimatePresence mode="wait">
                            <motion.div
                                key={filterMode}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.25 }}
                            >
                                {/* Tabs/Navigation */}
                                <div className="mb-6">
                                    <div className="flex space-x-2 border-b border-indigo-100/40 dark:border-indigo-700/60 relative">
                                        {['All', 'Owned', 'Shop'].map((tab, idx) => {
                                            const isActive = filterMode === tab.toLowerCase();
                                            return (
                                                <button
                                                    key={tab}
                                                    onClick={() => setFilterMode(tab.toLowerCase())}
                                                    className={
                                                        `relative px-5 py-2 text-sm font-bold rounded-t-xl transition-all duration-200 flex items-center gap-2 focus:outline-none ` +
                                                        (isActive
                                                            ? 'bg-gradient-to-r from-blue-400 via-indigo-400 to-violet-400 dark:from-yellow-500 dark:via-yellow-400 dark:to-yellow-300 text-white dark:text-gray-900 shadow-lg dark:shadow-yellow-900/20'
                                                            : 'text-gray-600 dark:text-gray-200 bg-gray-100/50 dark:bg-blue-950/60 hover:text-indigo-600 dark:hover:text-yellow-400 hover:bg-gray-200/50 dark:hover:bg-indigo-900/40')
                                                    }
                                                    style={{ zIndex: isActive ? 2 : 1 }}
                                                >
                                                    {/* Icon for each tab */}
                                                    {tab === 'All' && (
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" strokeWidth="2" /><path d="M8 12h8M12 8v8" strokeWidth="2" /></svg>
                                                    )}
                                                    {tab === 'Owned' && (
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M5 13l4 4L19 7" strokeWidth="2" /></svg>
                                                    )}
                                                    {tab === 'Shop' && (
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="4" y="4" width="16" height="16" rx="4" strokeWidth="2" /><path d="M9 9h6v6H9z" strokeWidth="2" /></svg>
                                                    )}
                                                    {tab}
                                                    {/* Animated underline */}
                                                    {isActive && (
                                                        <motion.div
                                                            layoutId="giftTabUnderline"
                                                            className="absolute left-0 right-0 -bottom-0.5 h-1 rounded-b-xl bg-gradient-to-r from-blue-400 via-indigo-400 to-violet-400 dark:from-yellow-500 dark:via-yellow-400 dark:to-yellow-300"
                                                            style={{ zIndex: 3 }}
                                                            transition={{ type: 'spring', duration: 0.5 }}
                                                        />
                                                    )}
                                                </button>
                                            );
                                        })}
                                    </div>
                                </div>

                                {/* Gift Grid */}
                                <div className="mb-6">
                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                        {filteredGiftsByMode().map((gift, idx) => {
                                            const isOwned = gift.owned;
                                            return (
                                                <div
                                                    key={gift.id || idx}
                                                    className={`relative group bg-white/70 dark:bg-blue-950/80 backdrop-blur-md rounded-2xl border border-indigo-100 dark:border-indigo-700 shadow-lg dark:shadow-indigo-900/30 p-3 flex flex-col items-center transition-all duration-300 hover:shadow-xl dark:hover:shadow-yellow-900/20 ${selectedGift?.id === gift.id ? 'ring-2 ring-blue-400 dark:ring-yellow-400' : ''}`}
                                                    onClick={() => handleSelectGift(gift)}
                                                    style={{ cursor: 'pointer' }}
                                                >
                                                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-100 via-indigo-100 to-violet-100 dark:from-yellow-900/40 dark:via-yellow-800/40 dark:to-yellow-700/40 flex items-center justify-center mb-2 overflow-hidden">
                                                        {gift.image_url ? (
                                                            <img src={gift.image_url} alt={gift.name} className="w-18 h-18 object-contain" />
                                                        ) : (
                                                            <svg className="w-16 h-16 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7V6a2 2 0 00-2-2h-1.28a2 2 0 00-1.92-1.37c-.88 0-1.64.58-1.9 1.37H9.1A2 2 0 007.18 2.63 2 2 0 005.28 4H4a2 2 0 00-2 2v1a2 2 0 001.37 1.92c0 .88.58 1.64 1.37 1.9V15a2 2 0 002 2h10a2 2 0 002-2V8.82c.79-.26 1.37-1.02 1.37-1.9A2 2 0 0020 7zM7 7a1 1 0 110-2 1 1 0 010 2zm10 0a1 1 0 110-2 1 1 0 010 2z" /></svg>
                                                        )}
                                                    </div>
                                                    <div className="text-center w-full">
                                                        <h5 className="font-bold text-gray-800 dark:text-yellow-200 mb-1 truncate">{gift.name}</h5>
                                                        {!isOwned && (
                                                            <div className="inline-flex items-center px-2 py-0.5 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-yellow-900/40 dark:to-yellow-700/40 text-indigo-700 dark:text-yellow-300 rounded-full text-xs font-semibold mb-1">
                                                                <img src="/In-AppAssets/xcoin.png" alt="pts" className="w-4 h-4 mr-1 inline-block" />
                                                                {gift.price?.toLocaleString() || gift.redeem_point || 0} pts
                                                            </div>
                                                        )}
                                                        {isOwned && (
                                                            <span className="ml-2 px-2 py-0.5 rounded-full bg-indigo-100 dark:bg-yellow-900/40 text-indigo-700 dark:text-yellow-300 text-xs font-bold">Owned</span>
                                                        )}
                                                    </div>
                                                    <button
                                                        className={`mt-2 w-full px-3 py-1.5 rounded-lg font-bold transition-all duration-200 flex items-center justify-center gap-2
                                                            ${isOwned
                                                                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-yellow-500 dark:to-yellow-400 text-white dark:text-gray-900 hover:from-blue-600 hover:to-indigo-600 dark:hover:from-yellow-400 dark:hover:to-yellow-300'
                                                                : 'bg-gradient-to-r from-indigo-500 to-violet-500 dark:from-yellow-700 dark:to-yellow-500 text-white dark:text-gray-900 hover:from-indigo-600 hover:to-violet-600 dark:hover:from-yellow-400 dark:hover:to-yellow-300'}
                                                            ${gift.price > walletBalance && !isOwned ? 'opacity-60 cursor-not-allowed' : ''}`}
                                                        disabled={gift.price > walletBalance && !isOwned}
                                                        onClick={e => {
                                                            e.stopPropagation();
                                                            handleSelectGift(gift);
                                                            // Do not immediately send or buy; let user confirm in details panel
                                                        }}
                                                    >
                                                        {isOwned ? 'Send' : 'Buy'}
                                                    </button>
                                                </div>
                                            );
                                        })}
                                        {filteredGiftsByMode().length === 0 && (
                                            <div className="col-span-full flex flex-col items-center justify-center py-12">
                                                <svg className="w-12 h-12 text-blue-300 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7V6a2 2 0 00-2-2h-1.28a2 2 0 00-1.92-1.37c-.88 0-1.64.58-1.9 1.37H9.1A2 2 0 007.18 2.63 2 2 0 005.28 4H4a2 2 0 00-2 2v1a2 2 0 001.37 1.92c0 .88.58 1.64 1.37 1.9V15a2 2 0 002 2h10a2 2 0 002-2V8.82c.79-.26 1.37-1.02 1.37-1.9A2 2 0 0020 7zM7 7a1 1 0 110-2 1 1 0 010 2zm10 0a1 1 0 110-2 1 1 0 010 2z" /></svg>
                                                <div className="text-gray-500 text-lg font-semibold">{filterMode === 'owned' ? "You don't own any gifts in this category yet" : "No gifts available in this category"}</div>
                                            </div>
                                        )}
                                    </div>
                                    {isLoading && (
                                        <div className="flex items-center justify-center py-8">
                                            <Spinner size="lg" />
                                        </div>
                                    )}
                                        </div>
                                {/* Selected Gift Details Panel */}
                                {selectedGift && (
                                    <div className="mt-8" ref={detailsPanelRef}>
                                        <div className="bg-white/90 dark:bg-blue-950/90 backdrop-blur-md rounded-2xl shadow-lg dark:shadow-indigo-900/30 border border-indigo-100 dark:border-indigo-700 p-4 flex flex-col lg:flex-row items-center gap-6">
                                            <div className="w-28 h-28 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-yellow-900/40 dark:to-yellow-700/40 flex items-center justify-center overflow-hidden">
                                                {selectedGift.image_url ? (
                                                    <img src={selectedGift.image_url} alt={selectedGift.name} className="w-24 h-24 object-contain" />
                                                ) : (
                                                    <svg className="w-16 h-16 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7V6a2 2 0 00-2-2h-1.28a2 2 0 00-1.92-1.37c-.88 0-1.64.58-1.9 1.37H9.1A2 2 0 007.18 2.63 2 2 0 005.28 4H4a2 2 0 00-2 2v1a2 2 0 001.37 1.92c0 .88.58 1.64 1.37 1.9V15a2 2 0 002 2h10a2 2 0 002-2V8.82c.79-.26 1.37-1.02 1.37-1.9A2 2 0 0020 7zM7 7a1 1 0 110-2 1 1 0 010 2zm10 0a1 1 0 110-2 1 1 0 010 2z" /></svg>
                                                )}
                                            </div>
                                            <div className="flex-1 w-full">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                                                    <h4 className="font-bold text-lg text-gray-800 dark:text-yellow-200">{selectedGift.name}</h4>
                                                    {!selectedGift.owned && (
                                                        <div className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-yellow-900/40 dark:to-yellow-700/40 text-indigo-700 dark:text-yellow-300 rounded-full text-sm font-semibold ml-0 sm:ml-2">
                                                            <img src="/In-AppAssets/xcoin.png" alt="pts" className="w-4 h-4 mr-1 inline-block" />
                                                            {selectedGift.price?.toLocaleString() || selectedGift.redeem_point || 0} pts
                                                        </div>
                                                    )}
                                                    {selectedGift.owned && (
                                                        <span className="ml-2 px-2 py-0.5 rounded-full bg-indigo-100 dark:bg-yellow-900/40 text-indigo-700 dark:text-yellow-300 text-xs font-bold">Owned</span>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-3 mb-3">
                                                    <label className="text-sm text-gray-700 dark:text-yellow-200 font-medium">Quantity:</label>
                                                <input
                                                    type="number"
                                                    min={1}
                                                    max={selectedGift.owned ? selectedGift.giftable_quantity : 10}
                                                    value={quantity}
                                                    onChange={handleQuantityChange}
                                                        className="w-20 border border-indigo-200 dark:border-yellow-700 rounded-lg px-2 py-1 text-sm bg-white/70 dark:bg-blue-950/70 focus:ring-2 focus:ring-blue-400 dark:focus:ring-yellow-400 focus:border-blue-400 dark:focus:border-yellow-400 text-gray-900 dark:text-yellow-200"
                                                    disabled={isSending}
                                                />
                                                {selectedGift.owned && (
                                                    <span className="ml-2 text-xs text-gray-500 dark:text-yellow-300">(Owned: {selectedGift.giftable_quantity})</span>
                                                )}
                                            </div>
                                            {quantityError && (
                                                <div className="mb-2 text-xs text-red-600 dark:text-red-400">{quantityError}</div>
                                            )}
                                                <div className="mb-3">
                                                    <label className="block text-sm text-gray-500 dark:text-yellow-200 mb-1">Add a message (optional)</label>
                                                    <textarea
                                                        placeholder={`Send a message to ${conversationPartnerName} with your gift...`}
                                                        value={message}
                                                        onChange={(e) => setMessage(e.target.value)}
                                                        className="w-full rounded-xl bg-white/60 dark:bg-blue-950/60 border border-indigo-100 dark:border-yellow-700 p-3 text-sm resize-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-yellow-400 focus:border-blue-400 dark:focus:border-yellow-400 text-gray-900 dark:text-yellow-200"
                                                        rows={2}
                                                        disabled={isSending}
                                                    />
                                                </div>
                                            <div className="flex flex-col sm:flex-row gap-2 justify-between items-center mt-2">
                                                    {selectedGift.owned && (
                                                        <motion.button
                                                            className="flex-1 px-4 py-2 rounded-lg font-bold bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-yellow-500 dark:to-yellow-400 text-white dark:text-gray-900 shadow-lg dark:shadow-yellow-900/20 hover:from-blue-600 hover:to-indigo-600 dark:hover:from-yellow-400 dark:hover:to-yellow-300 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-60"
                                                            onClick={handleSendFromInventory}
                                                            disabled={isSending || quantity < 1 || (selectedGift.giftable_quantity !== undefined && quantity > selectedGift.giftable_quantity) || !!quantityError}
                                                            whileTap={{ scale: 0.95 }}
                                                        >
                                                            {isSending ? <Spinner size="sm" className="mr-2" /> : null}
                                                            Send from Inventory
                                                        </motion.button>
                                                    )}
                                                    {!selectedGift.owned && (
                                                        <motion.button
                                                            className="flex-1 px-4 py-2 rounded-lg font-bold bg-gradient-to-r from-indigo-500 to-violet-500 dark:from-yellow-700 dark:to-yellow-500 text-white dark:text-gray-900 shadow-lg dark:shadow-yellow-900/20 hover:from-indigo-600 hover:to-violet-600 dark:hover:from-yellow-400 dark:hover:to-yellow-300 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-60"
                                                            onClick={() => handlePurchaseGift(selectedGift)}
                                                            disabled={isSending || isPurchasing || selectedGift.price > walletBalance}
                                                            whileTap={{ scale: 0.95 }}
                                                        >
                                                            {isPurchasing ? <Spinner size="sm" className="mr-2" /> : null}
                                                            Buy
                                                        </motion.button>
                                                    )}
                                                    <motion.button
                                                        className="flex-1 px-4 py-2 rounded-lg font-bold bg-gradient-to-r from-indigo-700 to-violet-700 dark:from-yellow-400 dark:to-yellow-300 text-white dark:text-gray-900 shadow-lg dark:shadow-yellow-900/20 hover:from-indigo-800 hover:to-violet-800 dark:hover:from-yellow-300 dark:hover:to-yellow-200 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-60"
                                                        onClick={handlePurchaseAndSend}
                                                        disabled={isSending || (!selectedGift.owned && selectedGift.price * quantity > walletBalance) || quantity < 1 || !!quantityError}
                                                        whileTap={{ scale: 0.95 }}
                                                    >
                                                        {isSending ? <Spinner size="sm" className="mr-2" /> : null}
                                                        Purchase & Send{quantity > 1 ? ` x${quantity}` : ''}
                                                        {!selectedGift.owned && selectedGift.price * quantity > walletBalance && (
                                                            <span className="ml-2 text-xs bg-white/20 dark:bg-yellow-900/20 px-2 py-1 rounded-full">Insufficient balance</span>
                                                        )}
                                                    </motion.button>
                                                </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                            </motion.div>
                        </FMAnimatePresence>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default GiftModal; 