import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useServiceCategories, useServiceStyles, useRaces, useLanguages } from '../hooks/talent/useTalents';
import { useQuery } from '@tanstack/react-query';
import { referenceDataApi } from '../services/referenceDataApi';
import talentService from '../services/talentService';
import { countActiveFilters } from '../utils/filterUtils';

// Skeleton loader component for filter options
const FilterOptionsSkeleton = ({ count = 3 }) => (
  <div className="w-full py-2 flex flex-wrap gap-2">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="h-10 rounded-full bg-gray-200 animate-pulse" style={{ width: `${Math.floor(Math.random() * 40) + 60}px` }} />
    ))}
  </div>
);

// Empty state component
const EmptyState = ({ message = "No options available." }) => (
  <div className="w-full py-3 flex flex-col items-center justify-center text-center">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <p className="text-sm text-gray-500">{message}</p>
  </div>
);

const FilterModal = ({ isOpen, onClose, initialFilters = {}, onApplyFilters }) => {
  const [selectedRating, setSelectedRating] = useState(initialFilters.rating || 0);
  const [priceRange, setPriceRange] = useState(initialFilters.priceRange || [0, 300]);
  const [experienceLevel, setExperienceLevel] = useState(initialFilters.experienceLevel || [1, 99]);
  const [selectedStyles, setSelectedStyles] = useState(initialFilters.styles || []);
  const [selectedType, setSelectedType] = useState(initialFilters.type || '');
  const [selectedGender, setSelectedGender] = useState(initialFilters.gender || '');
  const [selectedLanguages, setSelectedLanguages] = useState(initialFilters.languages || []);
  const [selectedRaceId, setSelectedRaceId] = useState(initialFilters.raceId || null);

  // Expanded sections state
  const [expandedSections, setExpandedSections] = useState({
    serviceCategory: true,
    ratings: true,
    priceRange: true,
    experienceLevel: true,
    talentStyle: true,
    talentType: true,
    gender: true,
    language: true,
    race: true
  });

  const [selectedCategoryId, setSelectedCategoryId] = useState(initialFilters.serviceCategoryId || null);

  // Use React Query hooks for data fetching
  const {
    data: serviceCategories = [],
    isLoading: categoriesLoading,
    error: categoriesError
  } = useServiceCategories({
    enabled: isOpen
  });

  const {
    data: allServiceTypes = [],
    isLoading: typesLoading,
    error: typesError,
  } = useQuery({
    queryKey: ['service-configuration', 'types'],
    queryFn: async () => {
      const res = await referenceDataApi.getServiceTypes();
      return res.data || [];
    },
    enabled: isOpen,
    staleTime: 1000 * 60 * 30,
    cacheTime: 1000 * 60 * 60,
  });

  const serviceTypes = selectedCategoryId
    ? allServiceTypes.filter((t) => t.service_category_id === selectedCategoryId)
    : [];

  const {
    data: serviceStyles = [],
    isLoading: stylesLoading,
    error: stylesError
  } = useServiceStyles(selectedCategoryId, {
    enabled: isOpen && !!selectedCategoryId
  });

  const {
    data: races = [],
    isLoading: racesLoading,
    error: racesError
  } = useRaces({
    enabled: isOpen
  });

  const {
    data: languages = [],
    isLoading: languagesLoading,
    error: languagesError
  } = useLanguages({
    enabled: isOpen
  });

  // Static options (hardcoded as requested)
  const ratings = [1.0, 2.0, 3.0, 4.0, 5.0];
  const genders = ['Male', 'Female'];

  // Handle service type selection with actual ID
  const handleServiceTypeSelect = (typeId) => {
    // If the same type is selected, deselect it
    if (selectedType === typeId) {
      setSelectedType('');
    } else {
      setSelectedType(typeId);
    }
  };

  // Handle service style selection with actual ID
  const toggleServiceStyle = (styleId) => {
    if (selectedStyles.includes(styleId)) {
      setSelectedStyles(selectedStyles.filter(id => id !== styleId));
    } else {
      setSelectedStyles([...selectedStyles, styleId]);
    }
  };

  // Toggle expanded sections
  const toggleSection = (section) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Handle rating selection (float values)
  const handleRatingSelect = (rating) => {
    setSelectedRating(rating === selectedRating ? 0 : rating);
  };

  // Handle price range change
  const handlePriceChange = (value, index) => {
    const newPriceRange = [...priceRange];
    newPriceRange[index] = value;
    setPriceRange(newPriceRange);
  };

  // Handle experience level change
  const handleExperienceChange = (value, index) => {
    const newExperienceLevel = [...experienceLevel];
    newExperienceLevel[index] = value;
    setExperienceLevel(newExperienceLevel);
  };

  // Handle talent style selection
  const toggleStyle = (style) => {
    if (selectedStyles.includes(style)) {
      setSelectedStyles(selectedStyles.filter(s => s !== style));
    } else {
      setSelectedStyles([...selectedStyles, style]);
    }
  };

  // Handle talent type selection
  const handleTypeSelect = (type) => {
    setSelectedType(type === selectedType ? '' : type);
  };

  // Handle gender selection
  const handleGenderSelect = (gender) => {
    setSelectedGender(gender === selectedGender ? '' : gender);
  };

  // Handle language selection
  const toggleLanguage = (language) => {
    if (selectedLanguages.includes(language)) {
      setSelectedLanguages(selectedLanguages.filter(l => l !== language));
    } else {
      setSelectedLanguages([...selectedLanguages, language]);
    }
  };

  // Handle category selection
  const handleCategorySelect = (categoryId) => {
    // If the same category is selected, deselect it
    if (selectedCategoryId === categoryId) {
      setSelectedCategoryId(null);
      // Clear the selected service type when category is deselected
      setSelectedType('');
    } else {
      setSelectedCategoryId(categoryId);
      // Clear the selected service type when category changes
      setSelectedType('');
    }
  };

  // Reset all filters
  const handleReset = () => {
    setSelectedRating(0);
    setPriceRange([0, 300]);
    setExperienceLevel([1, 99]);
    setSelectedStyles([]);
    setSelectedType('');
    setSelectedGender('');
    setSelectedLanguages([]);
    setSelectedRaceId(null);
    setSelectedCategoryId(null);
  };

  // Apply filters
  const handleApply = async () => {
    const filters = {};

    if (selectedRating > 0) filters.rating = parseFloat(selectedRating);
    if (priceRange[0] > 0 || priceRange[1] < 300) filters.priceRange = priceRange;
    if (experienceLevel[0] > 1 || experienceLevel[1] < 99) {
      filters.experienceLevel = experienceLevel;
    }
    if (selectedStyles.length > 0) filters.serviceStyleId = selectedStyles[0];
    if (selectedType) filters.serviceTypeId = selectedType;
    if (selectedGender) filters.gender = selectedGender;
    if (selectedLanguages.length > 0) filters.languages = selectedLanguages;
    if (selectedRaceId) filters.raceId = selectedRaceId;
    if (selectedCategoryId) filters.serviceCategoryId = selectedCategoryId;

    // Send a single GET request to /api/talents with the selected filters
    try {
      await talentService.filterTalents(filters, 1, 15, true);
    } catch (error) {
      console.error('Failed to fetch talents with filters', error);
    }

    onApplyFilters(filters);
    onClose();
  };

  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { y: "100%", opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { type: "spring", damping: 25, stiffness: 300 } }
  };

  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
          <motion.div
        className="fixed inset-0 z-50 overflow-hidden"
            initial="hidden"
            animate="visible"
            exit="hidden"
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            variants={backdropVariants}
            onClick={onClose}
          />

        {/* Modal */}
          <motion.div
          className="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-900 rounded-t-3xl overflow-hidden max-h-[90vh] border-t border-gray-100 dark:border-gray-800 shadow-2xl dark:shadow-indigo-900/30"
            variants={modalVariants}
          >
            {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-100 dark:border-gray-800 bg-white dark:bg-gray-900">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-violet-600 flex items-center justify-center shadow-lg shadow-indigo-500/20">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-violet-500 to-indigo-600 bg-clip-text text-transparent dark:from-indigo-400 dark:via-purple-400 dark:to-indigo-400">
                  Filter Talents
                </h2>
                <p className="text-base text-left text-gray-500 dark:text-indigo-300 mt-0.5">Refine your search</p>
              </div>
            </div>
              <button
                onClick={onClose}
              className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
              <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

          {/* Content */}
            <div className="overflow-y-auto h-[calc(90vh-130px)] pb-24 bg-white dark:bg-gray-900">
              <motion.div
                className="pb-4"
                initial="hidden"
                animate="visible"
                variants={childVariants}
              >
                {/* Service Category Section */}
                <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                  <div
                    className="px-5 py-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleSection('serviceCategory')}
                  >
                    <div className="flex items-center">
                      <span className="font-medium text-gray-800 dark:text-gray-100">Service Category</span>
                      {selectedCategoryId && (
                        <span className="ml-2 px-1.5 py-0.5 bg-green-50 dark:bg-green-900 text-green-600 dark:text-indigo-300 rounded-md text-xs font-bold">
                          {serviceCategories.find(cat => cat.id === selectedCategoryId)?.name || 'Selected'}
                        </span>
                      )}
                    </div>
                    <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections.serviceCategory ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedSections.serviceCategory && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-5 py-3">
                        {categoriesLoading ? (
                            <FilterOptionsSkeleton count={5} />
                        ) : categoriesError ? (
                          <EmptyState message="Failed to load service categories. Please try again." />
                          ) : serviceCategories.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {serviceCategories.map(category => (
                                <button
                                  key={category.id}
                                  onClick={() => handleCategorySelect(category.id)}
                                  className={`px-4 py-2.5 rounded-full text-sm font-medium transition-all ${
                                    selectedCategoryId === category.id
                                      ? 'bg-green-600 text-white shadow-sm ring-2 ring-green-200 dark:bg-green-500 dark:ring-green-300'
                                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                  }`}
                                >
                                  {category.name}
                                </button>
                              ))}
                            </div>
                          ) : (
                            <EmptyState message="No service categories available. Please try again later." />
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Race Section */}
                <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                  <div
                    className="px-5 py-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleSection('race')}
                  >
                    <div className="flex items-center">
                      <span className="font-medium text-gray-800 dark:text-gray-100">Race</span>
                      {selectedRaceId && (
                        <span className="ml-2 px-1.5 py-0.5 bg-red-50 dark:bg-red-900 text-red-600 dark:text-indigo-300 rounded-md text-xs font-bold">
                          {races.find(race => race.id === selectedRaceId)?.name || 'Selected'}
                        </span>
                      )}
                    </div>
                    <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections.race ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedSections.race && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-5 py-3">
                        {racesLoading ? (
                            <FilterOptionsSkeleton count={4} />
                        ) : racesError ? (
                          <EmptyState message="Failed to load race options. Please try again." />
                          ) : races.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {races.map(race => (
                                <button
                                  key={race.id}
                                  onClick={() => setSelectedRaceId(selectedRaceId === race.id ? null : race.id)}
                                  className={`px-4 py-2.5 rounded-full text-sm font-medium transition-all ${
                                    selectedRaceId === race.id
                                      ? 'bg-red-600 text-white shadow-sm ring-2 ring-red-200 dark:bg-red-500 dark:ring-red-300'
                                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                  }`}
                                >
                                  {race.name}
                                </button>
                              ))}
                            </div>
                          ) : (
                            <EmptyState message="No race options available. Please try again later." />
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Ratings Section */}
                <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                  <div
                    className="px-5 py-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleSection('ratings')}
                  >
                    <div className="flex items-center">
                    <span className="font-medium text-gray-800 dark:text-gray-100">Minimum Rating</span>
                      {selectedRating > 0 && (
                      <span className="ml-2 px-1.5 py-0.5 bg-indigo-50 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 rounded-md text-xs font-bold">
                        {selectedRating.toFixed(1)}
                        </span>
                      )}
                    </div>
                    <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections.ratings ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedSections.ratings && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                      <div className="px-5 py-3">
                        <div className="flex flex-wrap gap-2">
                            {ratings.map(rating => (
                              <button
                                key={rating}
                                onClick={() => handleRatingSelect(rating)}
                              className={`px-4 py-2.5 rounded-full text-sm font-medium transition-all ${
                                  selectedRating === rating
                                  ? 'bg-indigo-600 text-white shadow-sm ring-2 ring-indigo-200 dark:bg-indigo-500 dark:ring-indigo-300'
                                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                }`}
                              >
                              {rating.toFixed(1)}
                              </button>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Price Range Section */}
                <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                  <div
                    className="px-5 py-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleSection('priceRange')}
                  >
                    <div className="flex items-center">
                      <span className="font-medium text-gray-800 dark:text-gray-100">Price Range (Credits)</span>
                      {(priceRange[0] > 0 || priceRange[1] < 300) && (
                      <span className="ml-2 px-1.5 py-0.5 bg-yellow-50 dark:bg-indigo-900 text-yellow-600 dark:text-indigo-300 rounded-md text-xs font-bold flex items-center">
                        <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-3 h-3 mr-1" />
                        {priceRange[0]}-{priceRange[1]}
                        </span>
                      )}
                    </div>
                    <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections.priceRange ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedSections.priceRange && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                      <div className="px-5 py-4">
                        {/* Current Range Display */}
                        <div className="mb-6">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-5 h-5" />
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Current Range</span>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-indigo-600 dark:text-indigo-300 flex items-center justify-end">
                                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1" />
                                {priceRange[0]} - {priceRange[1]}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-indigo-300">
                                {priceRange[1] - priceRange[0]} Credits range
                              </div>
                            </div>
                          </div>
                          
                          {/* Range Bar */}
                          <div className="relative h-2 bg-gray-200 dark:bg-gray-800 rounded-full overflow-hidden">
                            <div 
                              className="absolute h-full bg-gradient-to-r from-yellow-400 to-yellow-600 dark:from-yellow-600 dark:to-yellow-400 rounded-full transition-all duration-300"
                              style={{ 
                                left: `${(priceRange[0] / 300) * 100}%`, 
                                right: `${100 - (priceRange[1] / 300) * 100}%` 
                              }}
                            ></div>
                          </div>
                        </div>

                        {/* Min and Max Inputs */}
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          {/* Min Price */}
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700 dark:text-indigo-300 flex items-center">
                              <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1" />
                              Minimum
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                min="0"
                                max={priceRange[1]}
                                value={priceRange[0]}
                                onChange={(e) => {
                                  const value = Math.min(parseInt(e.target.value) || 0, priceRange[1]);
                                  handlePriceChange(value, 0);
                                }}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white dark:bg-gray-900 dark:text-gray-100"
                                placeholder="0"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 opacity-50" />
                              </div>
                            </div>
                          </div>

                          {/* Max Price */}
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700 dark:text-indigo-300 flex items-center">
                              <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1" />
                              Maximum
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                min={priceRange[0]}
                                max="300"
                                value={priceRange[1]}
                                onChange={(e) => {
                                  const value = Math.max(parseInt(e.target.value) || 300, priceRange[0]);
                                  handlePriceChange(value, 1);
                                }}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white dark:bg-gray-900 dark:text-gray-100"
                                placeholder="300"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 opacity-50" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Quick Preset Buttons */}
                        <div className="space-y-3">
                          <label className="text-sm font-medium text-gray-700 dark:text-indigo-300">Quick Presets</label>
                          <div className="grid grid-cols-2 gap-2">
                            {[
                              { label: 'Budget', range: [0, 50], color: 'green' },
                              { label: 'Mid-Range', range: [50, 150], color: 'yellow' },
                              { label: 'Premium', range: [150, 250], color: 'orange' },
                              { label: 'Luxury', range: [250, 300], color: 'red' }
                            ].map((preset) => (
                              <button
                                key={preset.label}
                                onClick={() => {
                                  setPriceRange(preset.range);
                                }}
                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                  priceRange[0] === preset.range[0] && priceRange[1] === preset.range[1]
                                    ? `bg-${preset.color}-600 text-white shadow-lg dark:bg-indigo-700`
                                    : `bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700`
                                }`}
                              >
                                <div className="flex items-center justify-center space-x-1">
                                  <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-3 h-3" />
                                  <span>{preset.label}</span>
                                </div>
                                <div className="text-xs opacity-75">
                                  {preset.range[0]}-{preset.range[1]}
                                </div>
                              </button>
                            ))}
                          </div>
                        </div>

                        {/* Reset Button */}
                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <button
                            onClick={() => setPriceRange([0, 300])}
                            className="w-full px-4 py-2 text-sm text-gray-600 dark:text-indigo-300 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Reset to Full Range</span>
                          </button>
                        </div>
                      </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Experience Level Section */}
                <div className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                  <div
                    className="px-5 py-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleSection('experienceLevel')}
                  >
                    <div className="flex items-center">
                      <span className="font-medium text-gray-800 dark:text-gray-100">Experience Level</span>
                      {(experienceLevel[0] > 1 || experienceLevel[1] < 99) && (
                      <span className="ml-2 px-1.5 py-0.5 bg-green-50 dark:bg-green-900 text-green-600 dark:text-indigo-300 rounded-md text-xs font-bold flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                        </svg>
                        LV {experienceLevel[0]}-{experienceLevel[1]}
                        </span>
                      )}
                    </div>
                    <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections.experienceLevel ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedSections.experienceLevel && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                      <div className="px-5 py-4">
                        {/* Current Level Range Display */}
                        <div className="mb-6">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-green-600 dark:text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                              </svg>
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Current Level Range</span>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-green-600 dark:text-indigo-300 flex items-center justify-end">
                                <span className="mr-1">LV</span>
                                {experienceLevel[0]} - {experienceLevel[1]}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-indigo-300">
                                {experienceLevel[1] - experienceLevel[0] + 1} levels available
                              </div>
                            </div>
                          </div>
                          
                          {/* Level Range Bar */}
                          <div className="relative h-3 bg-gray-200 dark:bg-gray-800 rounded-full overflow-hidden">
                            <div 
                              className="absolute h-full bg-gradient-to-r from-green-400 to-green-600 dark:from-green-600 dark:to-green-400 rounded-full transition-all duration-300"
                              style={{ 
                                left: `${((experienceLevel[0] - 1) / 98) * 100}%`, 
                                right: `${100 - (experienceLevel[1] / 99) * 100}%` 
                              }}
                            ></div>
                            {/* Level markers */}
                            <div className="absolute inset-0 flex justify-between items-center px-2">
                              <span className="text-xs text-gray-500 dark:text-indigo-300 font-medium">1</span>
                              <span className="text-xs text-gray-500 dark:text-indigo-300 font-medium">25</span>
                              <span className="text-xs text-gray-500 dark:text-indigo-300 font-medium">50</span>
                              <span className="text-xs text-gray-500 dark:text-indigo-300 font-medium">75</span>
                              <span className="text-xs text-gray-500 dark:text-indigo-300 font-medium">99</span>
                            </div>
                          </div>
                        </div>

                        {/* Min and Max Level Inputs */}
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          {/* Min Level */}
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700 dark:text-indigo-300 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                              </svg>
                              Minimum Level
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                min="1"
                                max={experienceLevel[1]}
                                value={experienceLevel[0]}
                                onChange={(e) => {
                                  const value = Math.min(Math.max(parseInt(e.target.value) || 1, 1), experienceLevel[1]);
                                  handleExperienceChange(value, 0);
                                }}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white dark:bg-gray-900 dark:text-gray-100"
                                placeholder="1"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <span className="text-sm text-gray-400 font-medium">LV</span>
                              </div>
                            </div>
                          </div>

                          {/* Max Level */}
                          <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700 dark:text-indigo-300 flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                              </svg>
                              Maximum Level
                            </label>
                            <div className="relative">
                              <input
                                type="number"
                                min={experienceLevel[0]}
                                max="99"
                                value={experienceLevel[1]}
                                onChange={(e) => {
                                  const value = Math.max(Math.min(parseInt(e.target.value) || 99, 99), experienceLevel[0]);
                                  handleExperienceChange(value, 1);
                                }}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors bg-white dark:bg-gray-900 dark:text-gray-100"
                                placeholder="99"
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <span className="text-sm text-gray-400 font-medium">LV</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Quick Level Preset Buttons */}
                        <div className="space-y-3">
                          <label className="text-sm font-medium text-gray-700 dark:text-indigo-300">Quick Level Presets</label>
                          <div className="grid grid-cols-2 gap-2">
                            {[
                              { label: 'Beginner', range: [1, 25], color: 'blue', icon: '🌱' },
                              { label: 'Intermediate', range: [25, 50], color: 'yellow', icon: '⚡' },
                              { label: 'Advanced', range: [50, 75], color: 'orange', icon: '🔥' },
                              { label: 'Expert', range: [75, 99], color: 'red', icon: '👑' }
                            ].map((preset) => (
                              <button
                                key={preset.label}
                                onClick={() => {
                                  setExperienceLevel(preset.range);
                                }}
                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                  experienceLevel[0] === preset.range[0] && experienceLevel[1] === preset.range[1]
                                    ? `bg-${preset.color}-600 text-white shadow-lg dark:bg-indigo-700`
                                    : `bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700`
                                }`}
                              >
                                <div className="flex items-center justify-center space-x-1">
                                  <span className="text-sm">{preset.icon}</span>
                                  <span>{preset.label}</span>
                                </div>
                                <div className="text-xs opacity-75">
                                  LV {preset.range[0]}-{preset.range[1]}
                                </div>
                              </button>
                            ))}
                          </div>
                        </div>

                        {/* Level Categories Info */}
                        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-xs text-gray-600 dark:text-indigo-300 space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="flex items-center">
                                <span className="mr-1">🌱</span>
                                <span>Beginner (LV 1-25):</span>
                              </span>
                              <span className="font-medium">New players, learning phase</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="flex items-center">
                                <span className="mr-1">⚡</span>
                                <span>Intermediate (LV 25-50):</span>
                              </span>
                              <span className="font-medium">Skilled players, good experience</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="flex items-center">
                                <span className="mr-1">🔥</span>
                                <span>Advanced (LV 50-75):</span>
                              </span>
                              <span className="font-medium">Veteran players, high skill</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="flex items-center">
                                <span className="mr-1">👑</span>
                                <span>Expert (LV 75-99):</span>
                              </span>
                              <span className="font-medium">Elite players, master level</span>
                            </div>
                          </div>
                        </div>

                        {/* Reset Button */}
                        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <button
                            onClick={() => setExperienceLevel([1, 99])}
                            className="w-full px-4 py-2 text-sm text-gray-600 dark:text-indigo-300 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Reset to Full Range</span>
                          </button>
                        </div>
                      </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

              {/* Dynamic Filter Sections */}
                {[
                  {
                    key: 'talentType',
                    title: 'Service Type',
                    options: serviceTypes,
                    selected: selectedType ? [selectedType] : [],
                    toggleFn: handleServiceTypeSelect,
                    color: 'indigo',
                    singleSelect: true,
                    showCount: !!selectedType,
                  loading: typesLoading,
                  error: typesError,
                    displayFn: (option) => option.name,
                    valueFn: (option) => option.id
                  },
                  {
                    key: 'talentStyle',
                    title: 'Service Style',
                    options: serviceStyles,
                    selected: selectedStyles,
                    toggleFn: toggleServiceStyle,
                    color: 'purple',
                    showCount: selectedStyles.length > 0,
                  loading: stylesLoading,
                  error: stylesError,
                    displayFn: (option) => option.name,
                    valueFn: (option) => option.id,
                    disabled: !selectedCategoryId
                  },
                  {
                    key: 'gender',
                    title: 'Gender',
                    options: genders,
                    selected: selectedGender ? [selectedGender] : [],
                    toggleFn: handleGenderSelect,
                    color: 'pink',
                    singleSelect: true,
                    showCount: !!selectedGender
                  },
                  {
                    key: 'language',
                    title: 'Language',
                    options: languages.map(lang => lang.name || lang),
                    selected: selectedLanguages,
                    toggleFn: toggleLanguage,
                    color: 'cyan',
                    showCount: selectedLanguages.length > 0,
                  loading: languagesLoading,
                  error: languagesError
                  }
                ].map(section => (
                  <div key={section.key} className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                    <div
                      className={`px-5 py-3 flex justify-between items-center ${section.disabled ? 'opacity-50' : 'cursor-pointer'}`}
                      onClick={() => !section.disabled && toggleSection(section.key)}
                    >
                      <div className="flex items-center">
                        <span className="font-medium text-gray-800 dark:text-gray-100">{section.title}</span>
                        {section.showCount && (
                          <span className={`ml-2 px-1.5 py-0.5 bg-${section.color}-50 dark:bg-indigo-900 text-${section.color}-600 dark:text-indigo-300 rounded-md text-xs font-bold`}>
                            {section.selected.length}
                          </span>
                        )}
                        {section.disabled && (
                          <span className="ml-2 text-xs text-gray-500 dark:text-indigo-300 italic">
                            (Select a service category first)
                          </span>
                        )}
                      </div>
                      <div className={`w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 transition-transform duration-300 ${expandedSections[section.key] ? 'rotate-180' : ''}`}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>

                    <AnimatePresence>
                      {expandedSections[section.key] && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                        <div className="px-5 py-3">
                            {section.loading ? (
                            <FilterOptionsSkeleton count={4} />
                          ) : section.error ? (
                            <EmptyState message={`Failed to load ${section.title.toLowerCase()} options. Please try again.`} />
                            ) : section.options.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {section.options.map(option => {
                                const value = section.valueFn ? section.valueFn(option) : option;
                                const display = section.displayFn ? section.displayFn(option) : option;
                                const isSelected = section.singleSelect 
                                  ? section.selected[0] === value
                                  : section.selected.includes(value);

                                return (
                                  <button
                                    key={value}
                                    onClick={() => section.toggleFn(value)}
                                    className={`px-4 py-2.5 rounded-full text-sm font-medium transition-all ${
                                      isSelected
                                        ? `bg-${section.color}-600 text-white shadow-sm ring-2 ring-indigo-700 dark:bg-indigo-700 dark:ring-indigo-400`
                                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700'
                                    }`}
                                  >
                                    {display}
                                  </button>
                                );
                              })}
                            </div>
                            ) : (
                            <EmptyState message={`No ${section.title.toLowerCase()} options available. Please try again later.`} />
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
                
                {/* Bottom Spacing to prevent footer overlap */}
                <div className="h-24 bg-white dark:bg-gray-900"></div>
              </motion.div>
            </div>

          {/* Footer */}
          <div className="absolute bottom-0 left-0 right-0 pb-6 bg-white dark:bg-gray-900 border-t border-gray-100 dark:border-gray-800 p-4">
              <div className="flex items-center justify-between px-2">
                {(() => {
                  const currentFilters = {
                    serviceCategoryId: selectedCategoryId,
                    serviceTypeId: selectedType,
                    serviceStyleId: selectedStyles.length > 0 ? selectedStyles[0] : undefined,
                    experienceLevel,
                    priceRange,
                    gender: selectedGender,
                    languages: selectedLanguages,
                    raceId: selectedRaceId,
                    rating: selectedRating
                  };
                  const count = countActiveFilters(currentFilters);
                  return (
                    <div className="text-sm text-gray-500 dark:text-indigo-300">
                      {count} filter{count !== 1 ? 's' : ''} applied
                    </div>
                  );
                })()}
                <button
                  onClick={handleReset}
                  className="text-sm text-blue-600 dark:text-indigo-400 bg-transparent hover:bg-transparent font-medium transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reset All
                </button>
              </div>
            
            <div className="flex space-x-3 mt-4">
                <button
                  onClick={onClose}
                className="flex-1 px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-indigo-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApply}
                className="flex-1 px-6 py-3 bg-gradient-to-r from-indigo-600 to-violet-600 dark:from-indigo-700 dark:to-purple-700 text-white dark:text-gray-100 rounded-xl font-medium hover:from-indigo-700 hover:to-violet-700 dark:hover:from-indigo-500 dark:hover:to-purple-500 transition-all shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-100"
                >
                  Apply Filters
                </button>
            </div>
              </div>
            </motion.div>
          </motion.div>
    </AnimatePresence>
  );
};

export default FilterModal;