import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const VerificationStatus = ({ onVerificationStatusChange = null }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState({
    email_verified: false,
    kyc_verified: false
  });

  // Fetch verification status on component mount
  useEffect(() => {
    fetchVerificationStatus();
  }, []);

  // Mock function to fetch verification status - replace with actual API call
  const fetchVerificationStatus = async () => {
    setIsLoading(true);
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // In development environment, mock the data
      // In production, this would be an API call to get user verification status
      const mockStatus = {
        email_verified: localStorage.getItem('email_verified') === 'true' || Math.random() > 0.5,
        kyc_verified: localStorage.getItem('kyc_verified') === 'true' || Math.random() > 0.5
      };
      
      setVerificationStatus(mockStatus);
      
      // Store in localStorage for persistence in development
      localStorage.setItem('email_verified', mockStatus.email_verified);
      localStorage.setItem('kyc_verified', mockStatus.kyc_verified);
      
      // Notify parent component if callback is provided
      if (onVerificationStatusChange) {
        onVerificationStatusChange(mockStatus);
      }
    } catch (error) {
      console.error('Error fetching verification status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate if all requirements are met
  const allRequirementsMet = verificationStatus.email_verified && verificationStatus.kyc_verified;

  // Skeleton loader for loading state
  if (isLoading) {
    return (
      <div className="animate-pulse bg-white rounded-xl p-6 shadow-sm">
        <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-xl p-6 shadow-sm"
    >
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Verification Status</h2>
      
      <div className="space-y-4">
        {/* Email Verification Status */}
        <div className="flex items-center justify-center">
          <div className={`flex-shrink-0 h-5 w-5 rounded-full flex items-center justify-center ${verificationStatus.email_verified ? 'bg-green-100' : 'bg-amber-100'}`}>
            {verificationStatus.email_verified ? (
              <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="h-4 w-4 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-900">Email Verification</h3>
            {verificationStatus.email_verified ? (
              <p className="mt-1 text-sm text-gray-500">Your email has been verified.</p>
            ) : (
              <div className="mt-1 text-sm text-gray-500">
                <p className="mb-2">Your email is not verified. Please verify your email to enable withdrawals.</p>
                <Link to="/profile" className="text-indigo-600 hover:text-indigo-800 font-medium">
                  Verify your email
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* KYC Verification Status */}
        <div className="flex items-center justify-center">
          <div className={`flex-shrink-0 h-5 w-5 rounded-full flex items-center justify-center ${verificationStatus.kyc_verified ? 'bg-green-100' : 'bg-amber-100'}`}>
            {verificationStatus.kyc_verified ? (
              <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="h-4 w-4 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-900">KYC Verification</h3>
            {verificationStatus.kyc_verified ? (
              <p className="mt-1 text-sm text-gray-500">Your identity has been verified.</p>
            ) : (
              <div className="mt-1 text-sm text-gray-500">
                <p className="mb-2">Identity verification is required for withdrawals. Please complete KYC verification.</p>
                <Link to="/settings" className="text-indigo-600 hover:text-indigo-800 font-medium">
                  Complete KYC verification
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Bank Account Requirement */}
        <div className="flex items-center justify-center">
          <div className="flex-shrink-0 h-5 w-5 rounded-full flex items-center justify-center bg-blue-100">
            <svg className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-900">Bank Account Required</h3>
            <div className="mt-1 text-sm text-gray-500">
              <p className="mb-2">At least one bank account must be registered to withdraw funds.</p>
              <Link to="/bank-accounts" className="text-indigo-600 hover:text-indigo-800 font-medium">
                Manage bank accounts
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      {/* Verification Status Summary */}
      <div className={`mt-6 p-4 rounded-lg ${allRequirementsMet ? 'bg-green-50 border border-green-100' : 'bg-amber-50 border border-amber-100'}`}>
        <div className="flex items-center justify-center">
          <div className={`flex-shrink-0 ${allRequirementsMet ? 'text-green-600' : 'text-amber-600'}`}>
            {allRequirementsMet ? (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            ) : (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <h3 className={`text-sm font-medium ${allRequirementsMet ? 'text-green-800' : 'text-amber-800'}`}>
              {allRequirementsMet 
                ? 'All verification requirements are met.' 
                : 'Please complete all verification requirements to enable withdrawals.'}
            </h3>
            <div className="mt-2 text-sm">
              <p className={allRequirementsMet ? 'text-green-700' : 'text-amber-700'}>
                {allRequirementsMet 
                  ? 'You can now withdraw funds to your registered bank accounts.' 
                  : 'Complete the verification steps above to unlock withdrawal functionality.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default VerificationStatus; 