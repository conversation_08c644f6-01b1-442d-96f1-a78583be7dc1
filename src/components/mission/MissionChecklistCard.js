import React from 'react';
import { motion } from 'framer-motion';

const MissionChecklistCard = ({ checklist, onToggleItem }) => {
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Mission Checklist</h3>
        
        <div className="space-y-3">
          {checklist.map(item => (
            <div key={item.id} className="flex items-center bg-gray-50 p-3 rounded-lg">
              <input
                type="checkbox"
                id={`checklist-${item.id}`}
                checked={item.completed}
                onChange={() => onToggleItem(item.id)}
                className="form-checkbox h-5 w-5 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
              />
              <label
                htmlFor={`checklist-${item.id}`}
                className={`ml-3 text-gray-800 ${item.completed ? 'line-through text-gray-500' : 'font-medium'}`}
              >
                {item.title}
              </label>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default MissionChecklistCard;
