import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { getCdnUrl } from '../../utils/cdnUtils';

const EnhancedParticipantsTab = ({
  mission,
  isCurrentUserHost,
  hasApplied,
  onShowParticipantsModal,
  onShowApplicationModal,
  onReviewParticipants = () => {},
  onDisputeParticipant = () => {},
  user
}) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  // Determine if the mission is joinable
  const canJoin = mission.status === 'open' && !isCurrentUserHost && !hasApplied;
  
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-800 flex items-center">
            Participants 
            <span className="ml-2 bg-indigo-100 text-indigo-800 text-sm px-2.5 py-0.5 rounded-full">
              {mission.participants?.length || 0}/{mission.slots_total || 0}
            </span>
          </h2>
          {mission.status === 'open' && (
            <p className="text-sm text-gray-500 mt-1">
              {mission.slots_total - (mission.participants?.length || 0)} slots available
            </p>
          )}
        </div>
        <div className="flex gap-2 items-center">
          <motion.button
            className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center bg-white px-3 py-1.5 rounded-lg shadow-sm border border-indigo-100"
            onClick={onShowParticipantsModal}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            View All
          </motion.button>
          {isCurrentUserHost && (
            <motion.button
              className="text-white text-sm font-medium flex items-center bg-gradient-to-r from-yellow-500 to-orange-600 px-3 py-1.5 rounded-lg shadow-sm disabled:opacity-50"
              onClick={onReviewParticipants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled={mission.status !== 'completed'}
            >
              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
              Review
            </motion.button>
          )}
        </div>
      </div>

      {mission.participants && mission.participants.length > 0 ? (
        <div className="w-full">
          <ul className="w-full space-y-8">
            {mission.participants.slice(0, 4).map((participant, index) => {
              const profilePic = participant.profile_picture ? getCdnUrl(participant.profile_picture) : '/images/default-avatar.jpg';
              return (
                <li key={index} className="w-full bg-white/95 rounded-3xl shadow-xl border border-gray-100 p-8 flex flex-col md:flex-row gap-8 items-center hover:shadow-2xl transition-shadow duration-200">
                  {/* Avatar */}
                  <div className="flex-shrink-0 flex flex-col items-center">
                    <img
                      src={profilePic}
                      alt={participant.nickname || participant.name || 'Participant'}
                      className="w-32 h-32 rounded-full object-cover border-4 border-indigo-200 shadow-lg bg-gray-100"
                      onError={e => { e.target.onerror = null; e.target.src = '/images/default-avatar.jpg'; }}
                    />
                  </div>
                  {/* Info */}
                  <div className="flex-1 flex flex-col gap-3 min-w-0">
                    <div className="flex flex-wrap items-center gap-3 mb-1">
                      <span className="text-2xl font-bold text-gray-800 truncate">{participant.nickname || participant.name || 'Unnamed'}</span>
                      {participant.uid && (
                        <span className="bg-indigo-100 text-indigo-700 text-sm font-mono px-3 py-1 rounded-full border border-indigo-200">{participant.uid}</span>
                      )}
                      <span className={`px-4 py-1 rounded-full text-sm font-semibold border shadow-sm ${participant.status === 'approved' ? 'bg-green-100 text-green-700 border-green-200' : participant.status === 'pending' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' : 'bg-gray-100 text-gray-600 border-gray-200'}`}>{participant.status ? participant.status.charAt(0).toUpperCase() + participant.status.slice(1) : 'Participant'}</span>
                    </div>
                    {participant.biography && (
                      <div className="text-gray-600 text-left text-base mt-1 whitespace-pre-line line-clamp-4">{participant.biography}</div>
                    )}
                  </div>
                  {/* Dispute Button for Host */}
                  {isCurrentUserHost && (
                    <div className="flex flex-col items-center gap-2 mt-6 md:mt-0 md:ml-8">
                      <button
                        className="px-6 py-3 rounded-full bg-gradient-to-r from-red-400 to-pink-500 text-white font-semibold shadow-lg hover:from-red-500 hover:to-pink-600 transition-colors text-lg"
                        onClick={() => onDisputeParticipant(participant)}
                        title="Dispute Participant"
                      >
                        <svg className="w-6 h-6 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                        Dispute
                      </button>
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
          {/* Show more button if there are more than 4 participants */}
          {mission.participants.length > 4 && (
            <div className="mt-4 text-center">
              <motion.button
                className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center mx-auto"
                onClick={onShowParticipantsModal}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
                Show {mission.participants.length - 4} more participants
              </motion.button>
            </div>
          )}
          {/* Join button if mission is open and user is not host or participant */}
          {canJoin && isAuthenticated && (
            <div className="mt-4 text-center">
              <motion.button
                className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all"
                onClick={onShowApplicationModal}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 0 18 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                Join This Mission
              </motion.button>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-gray-50 p-8 rounded-lg text-center">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-gray-600 mb-6 text-lg">No participants have joined this mission yet</p>
          {canJoin && isAuthenticated && (
            <motion.button
              className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all"
              onClick={onShowApplicationModal}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
              Be the first to join
            </motion.button>
          )}
        </div>
      )}
      
      {/* Mission chat preview */}
      {mission.status === 'in_progress' && (mission.participants?.length > 0) && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Mission Chat</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-gray-800">Mission Communication</h4>
                <span className="text-xs text-gray-500">Last active 5m ago</span>
              </div>
              
              <div className="space-y-3 mb-4">
                {/* Sample chat messages */}
                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 mr-2 flex-shrink-0">
                    <img src={mission.host?.avatar || '/images/default-avatar.jpg'} alt="Host" className="w-full h-full object-cover" />
                  </div>
                  <div className="bg-gray-100 rounded-lg p-2 max-w-[80%]">
                    <p className="text-xs text-gray-500 mb-1">{mission.host?.name || 'Host'} (Host)</p>
                    <p className="text-sm">Welcome everyone! Let's get started with our mission.</p>
                  </div>
                </div>
                
                <div className="flex items-start justify-end">
                  <div className="bg-indigo-100 rounded-lg p-2 max-w-[80%]">
                    <p className="text-xs text-indigo-600 mb-1">You</p>
                    <p className="text-sm">I'm ready to go! What's the plan?</p>
                  </div>
                  <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 ml-2 flex-shrink-0">
                    <img src={user?.avatar || '/images/default-avatar.jpg'} alt="You" className="w-full h-full object-cover" />
                  </div>
                </div>
              </div>
              
              <motion.button
                className="w-full py-2 bg-indigo-50 text-indigo-600 rounded-lg font-medium hover:bg-indigo-100 transition-colors flex items-center justify-center"
                onClick={() => navigate('/chat')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Open Mission Chat
              </motion.button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedParticipantsTab;
