import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const ResultsHeader = ({
  resultsCount,
  sortOption,
  onSortChange
}) => {
  const [showSortOptions, setShowSortOptions] = useState(false);

  // Sort options
  const sortOptions = [
    { value: 'newest', label: 'Newest First', icon: 'clock' },
    { value: 'oldest', label: 'Oldest First', icon: 'clock-reverse' },
    { value: 'highest_bounty', label: 'Highest Bounty', icon: 'trending-up' },
    { value: 'lowest_bounty', label: 'Lowest Bounty', icon: 'trending-down' }
  ];

  // Get icon for sort option
  const getSortIcon = (icon) => {
    switch (icon) {
      case 'clock':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'clock-reverse':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l-3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'trending-up':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'trending-down':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Get current sort option
  const currentSort = sortOptions.find(option => option.value === sortOption) || sortOptions[0];

  return (
    <motion.div
      className="relative mb-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Enhanced Animated Gaming Header Background */}
      <div className="absolute inset-0 z-0 rounded-xl overflow-hidden pointer-events-none">
        <div className="absolute -top-10 -left-10 w-72 h-32 bg-gradient-to-r from-indigo-500/40 via-blue-500/30 to-purple-500/0 rounded-full blur-2xl animate-pulse-slow" />
        <div className="absolute bottom-0 right-0 w-40 h-20 bg-gradient-to-tr from-pink-400/30 via-yellow-300/20 to-indigo-400/0 rounded-full blur-2xl animate-pulse-slower" />
        {/* Gaming particles */}
        <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-yellow-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '0.5s' }} />
        <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-pink-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '1.2s' }} />
        <div className="absolute bottom-1/4 left-1/2 w-2 h-2 bg-blue-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '2s' }} />
      </div>
      {/* Main Header Content Grid */}
      <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between bg-white/90 dark:bg-gray-900/90 rounded-xl shadow-lg p-4 border border-indigo-50 dark:border-indigo-900/30 backdrop-blur-md">
        <div className="text-gray-700 dark:text-indigo-100 mb-3 sm:mb-0 flex items-center">
          <svg className="w-5 h-5 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
          <span className="font-bold text-gray-900 dark:text-white text-lg">{resultsCount}</span> <span className="ml-1 text-gray-600 dark:text-indigo-100 text-base">{resultsCount === 1 ? 'mission' : 'missions'} found</span>
        </div>

        <div className="flex items-center">
          <span className="text-gray-600 dark:text-indigo-100 mr-3 flex items-center">
            <svg className="w-4 h-4 mr-1 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
            </svg>
            Sort by:
          </span>
          <div className="relative">
            <motion.button
              className="appearance-none bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-indigo-900 text-gray-700 dark:text-indigo-100 py-2 pl-3 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 flex items-center min-w-[160px] shadow-md"
              onClick={() => setShowSortOptions(!showSortOptions)}
              whileHover={{ backgroundColor: '#F5F7FF' }}
            >
              <span className="flex items-center">
                <span className="w-6 h-6 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-300 mr-2">
                  {getSortIcon(currentSort.icon)}
                </span>
                <span>{currentSort.label}</span>
              </span>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-indigo-100">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </motion.button>

            <AnimatePresence>
              {showSortOptions && (
                <motion.div
                  className="absolute right-0 mt-2 w-56 rounded-lg shadow-xl bg-white dark:bg-gray-900 ring-1 ring-black ring-opacity-5 z-10 border border-indigo-100 dark:border-indigo-900/30"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="py-1">
                    {sortOptions.map((option) => (
                      <motion.button
                        key={option.value}
                        className={`flex items-center w-full text-left px-4 py-2 text-sm rounded-lg transition-colors duration-150 ${
                          option.value === sortOption
                            ? 'bg-indigo-50 dark:bg-indigo-900/40 text-indigo-700 dark:text-indigo-200'
                            : 'text-gray-700 dark:text-indigo-100 dark:bg-indigo-900/30 hover:bg-gray-50 dark:hover:bg-indigo-900/30'
                        }`}
                        onClick={() => {
                          onSortChange(option.value);
                          setShowSortOptions(false);
                        }}
                        whileHover={{ x: 5 }}
                      >
                        <span className="w-6 h-6 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-300 mr-2">
                          {getSortIcon(option.icon)}
                        </span>
                        {option.label}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ResultsHeader;
