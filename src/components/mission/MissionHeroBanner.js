import React from 'react';
import { motion } from 'framer-motion';
import { FaTrophy, FaRocket, FaUsers } from 'react-icons/fa';
import useTranslation from '../../hooks/useTranslation';

// Props: stats: { active, completed, hosted }, featuredMissions: []
const MissionHeroBanner = ({ stats = { active: 0, completed: 0, hosted: 0 }, onCreate, onMyMissions, featuredMissions = [] }) => {
  const { t } = useTranslation('mission');

  return (
    <motion.section
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.7 }}
      className="relative w-full mb-10"
    >
      {/* Animated Gaming Background */}
      <div className="absolute inset-0 z-0 overflow-hidden rounded-3xl">
        <div className="absolute -top-20 -left-20 w-96 h-96 bg-gradient-to-br from-indigo-600 via-blue-700 to-purple-700 opacity-40 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute -bottom-20 -right-20 w-96 h-96 bg-gradient-to-tr from-pink-500 via-yellow-400 to-indigo-400 opacity-30 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }} />
        {/* Gaming particles */}
        <div className="absolute top-1/4 left-1/3 w-4 h-4 bg-yellow-400/70 rounded-full animate-float-slow" style={{ animationDelay: '0.5s' }} />
        <div className="absolute top-2/3 right-1/4 w-3 h-3 bg-pink-400/70 rounded-full animate-float-slow" style={{ animationDelay: '1.2s' }} />
        <div className="absolute bottom-1/3 left-1/2 w-2 h-2 bg-blue-400/70 rounded-full animate-float-slow" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 right-1/2 w-5 h-5 bg-fuchsia-400/60 rounded-full animate-float-slow" style={{ animationDelay: '2.5s' }} />
        {/* Mascot or logo */}
        <img src="/MissionXMascot.png" alt="Mascot" className="absolute bottom-0 right-8 w-40 h-40 opacity-80 pointer-events-none select-none hidden md:block" />
      </div>

      {/* Main Grid Content */}
      <div className="relative z-10 grid grid-cols-1 md:grid-cols-3 gap-8 items-center bg-gradient-to-r from-indigo-700/90 via-blue-800/80 to-purple-900/90 rounded-3xl shadow-2xl p-8 md:p-12">
        {/* Left: Title & CTA */}
        <div className="flex flex-col gap-4 md:col-span-1">
          <h1 className="text-4xl md:text-5xl font-extrabold text-white text-left drop-shadow mb-2">
            {t('landing.discoverTitle')}
          </h1>
          <p className="text-indigo-100 text-lg md:text-xl text-left font-medium mb-4">
            {t('landing.discoverSubtitle')}
          </p>
          <div className="flex gap-4 mt-2">
            <motion.button
              onClick={onCreate}
              className="px-6 py-3 bg-yellow-400 text-indigo-900 font-bold rounded-xl shadow-lg hover:bg-yellow-300 transition-colors text-lg flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.97 }}
            >
              <FaRocket className="w-5 h-5" />
              {t('landing.actions.create')}
            </motion.button>
            <motion.button
              onClick={onMyMissions}
              className="px-6 py-3 bg-white/20 text-white font-bold rounded-xl shadow-lg hover:bg-white/30 transition-colors text-lg flex items-center gap-2 border border-white/20"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.97 }}
            >
              <FaTrophy className="w-5 h-5" />
              {t('landing.actions.myMissions')}
            </motion.button>
          </div>
        </div>

        {/* Center: Stats Grid */}
        <div className="grid grid-cols-3 gap-4 md:col-span-1 justify-center">
          <div className="flex flex-col items-center bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl p-6 shadow-lg">
            <span className="text-3xl font-extrabold text-yellow-300 flex items-center gap-2">
              <FaRocket className="w-6 h-6" /> {stats.active}
            </span>
            <span className="text-indigo-100 text-sm mt-2">{t('landing.stats.active')}</span>
          </div>
          <div className="flex flex-col items-center bg-gradient-to-br from-pink-500 to-yellow-400 rounded-2xl p-6 shadow-lg">
            <span className="text-3xl font-extrabold text-white flex items-center gap-2">
              <FaTrophy className="w-6 h-6" /> {stats.completed}
            </span>
            <span className="text-white text-sm mt-2">{t('landing.stats.completed')}</span>
          </div>
          <div className="flex flex-col items-center bg-gradient-to-br from-fuchsia-600 to-indigo-400 rounded-2xl p-6 shadow-lg">
            <span className="text-3xl font-extrabold text-white flex items-center gap-2">
              <FaUsers className="w-6 h-6" /> {stats.hosted}
            </span>
            <span className="text-white text-sm mt-2">{t('landing.stats.hosted')}</span>
          </div>
        </div>

        {/* Right: Featured Missions Carousel Placeholder */}
        <div className="md:col-span-1 flex flex-col items-center justify-center">
          {/* Placeholder for a future carousel of featured missions */}
          <div className="w-full h-73 flex items-center justify-center bg-gradient-to-br from-indigo-900/60 to-blue-900/60 rounded-2xl shadow-inner border-2 border-indigo-700/30">
            <img src="/missionAwards.svg" alt="Featured Mission" className="w-full h-full object-cover rounded-2xl" />
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default MissionHeroBanner; 