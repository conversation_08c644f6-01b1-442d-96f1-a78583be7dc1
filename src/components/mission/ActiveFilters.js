import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import FilterChip from './FilterChip';

const ActiveFilters = ({
  searchQuery,
  activeFilters,
  onClearSearch,
  onToggleCategory,
  onToggleStyle,
  onClearPriceRange,
  onClearLevelRequirement,
  onResetFilters,
  getActiveFilterCount
}) => {
  // Skip rendering if no active filters
  if (getActiveFilterCount() === 0 && !searchQuery) {
    return null;
  }

  // Get category icon
  const getCategoryIcon = (category) => {
    return (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
      </svg>
    );
  };

  // Get style icon
  const getStyleIcon = (style) => {
    return (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
      </svg>
    );
  };

  // Get price icon
  const getPriceIcon = () => {
    return (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
      </svg>
    );
  };

  // Get level icon
  const getLevelIcon = () => {
    return (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
      </svg>
    );
  };

  // Get search icon
  const getSearchIcon = () => {
    return (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <motion.div 
      className="flex flex-wrap items-center gap-2 mb-6"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <AnimatePresence>
        {/* Search query filter */}
        {searchQuery && (
          <FilterChip
            label={`"${searchQuery}"`}
            onRemove={onClearSearch}
            color="purple"
            icon={getSearchIcon()}
          />
        )}

        {/* Category filters */}
        {activeFilters.serviceCategories.map((category, index) => (
          <FilterChip
            key={`filter-category-${index}`}
            label={category}
            onRemove={() => onToggleCategory(category)}
            color="blue"
            icon={getCategoryIcon(category)}
          />
        ))}

        {/* Style filters */}
        {activeFilters.missionStyles.map((style, index) => (
          <FilterChip
            key={`filter-style-${index}`}
            label={style}
            onRemove={() => onToggleStyle(style)}
            color="indigo"
            icon={getStyleIcon(style)}
          />
        ))}

        {/* Price range filter */}
        {(activeFilters.priceRange.min > 0 || activeFilters.priceRange.max < 500) && (
          <FilterChip
            label={`${activeFilters.priceRange.min} - ${activeFilters.priceRange.max} credits`}
            onRemove={onClearPriceRange}
            color="green"
            icon={getPriceIcon()}
          />
        )}

        {/* Level requirement filter */}
        {(activeFilters.levelRequirement.min > 1 || activeFilters.levelRequirement.max < 99) && (
          <FilterChip
            label={`LV.${activeFilters.levelRequirement.min} - LV.${activeFilters.levelRequirement.max}`}
            onRemove={onClearLevelRequirement}
            color="gray"
            icon={getLevelIcon()}
          />
        )}

        {/* Clear all button */}
        {(getActiveFilterCount() > 0 || searchQuery) && (
          <motion.button
            className="text-sm text-indigo-600 hover:text-indigo-800 font-medium underline flex items-center"
            onClick={onResetFilters}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Clear All
          </motion.button>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ActiveFilters;
