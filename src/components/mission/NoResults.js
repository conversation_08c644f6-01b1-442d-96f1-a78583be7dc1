import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const NoResults = ({
  hasActiveFilters,
  hasSearchResults,
  searchQuery,
  onResetFilters,
  onClearSearch
}) => {
  const navigate = useNavigate();

  // Determine which message to show
  const getMessage = () => {
    if (hasActiveFilters || (hasSearchResults && !hasActiveFilters)) {
      return {
        title: 'No missions match your filters',
        description: 'Try adjusting your filter criteria to see more results.',
        action: {
          label: 'Clear All Filters',
          onClick: onResetFilters
        },
        illustration: 'filter'
      };
    } else if (searchQuery) {
      return {
        title: `No missions match "${searchQuery}"`,
        description: 'Try a different search term or browse all available missions.',
        action: {
          label: 'Clear Search',
          onClick: onClearSearch
        },
        illustration: 'search'
      };
    } else {
      return {
        title: 'No missions available',
        description: 'There are no missions available at the moment. Check back later or create your own mission!',
        action: {
          label: 'Create a Mission',
          onClick: () => navigate('/missions/create')
        },
        illustration: 'empty'
      };
    }
  };

  const message = getMessage();

  // Get illustration based on message type
  const getIllustration = (type) => {
    switch (type) {
      case 'filter':
        return (
          <svg className="w-24 h-24 text-indigo-200 mx-auto mb-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
          </svg>
        );
      case 'search':
        return (
          <svg className="w-24 h-24 text-indigo-200 mx-auto mb-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
          </svg>
        );
      case 'empty':
      default:
        return (
          <svg className="w-24 h-24 text-indigo-200 mx-auto mb-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-8 text-center border border-indigo-50"
    >
      <div className="max-w-md mx-auto">
        {getIllustration(message.illustration)}
        
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{message.title}</h3>
        
        <p className="text-gray-600 mb-6">
          {message.description}
        </p>
        
        <motion.button
          className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all"
          onClick={message.action.onClick}
          whileHover={{ scale: 1.05, boxShadow: "0 10px 15px -3px rgba(79, 70, 229, 0.2)" }}
          whileTap={{ scale: 0.95 }}
        >
          {message.action.label}
        </motion.button>
      </div>
    </motion.div>
  );
};

export default NoResults;
