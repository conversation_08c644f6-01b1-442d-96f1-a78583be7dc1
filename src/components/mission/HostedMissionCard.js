import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { missionApi } from '../../services/missionApi';
import { useToast } from '../common/ToastProvider';

const HostedMissionCard = ({ mission }) => {
  const navigate = useNavigate();
  const toast = useToast();
  const [showMenu, setShowMenu] = useState(false);
  const images = mission.images && mission.images.length > 0 ? mission.images : [mission.image || '/images/mission-default.jpg'];
  const [currentImage, setCurrentImage] = useState(0);
  const hasMultipleImages = images.length > 1;

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Calculate slots filled/remaining text
  const getSlotsText = () => {
    const filled = mission.slots_filled || 0;
    const total = mission.slots_total || 5;
    const remaining = total - filled;
    if (remaining === 0) {
      return 'All slots filled';
    } else if (remaining === 1) {
      return '1 slot remaining';
    } else {
      return `${remaining} slots remaining`;
    }
  };

  const handlePrev = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };
  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  // Handle menu toggle
  const toggleMenu = (e) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  // Handle mission edit
  const handleEdit = (e) => {
    e.stopPropagation();
    setShowMenu(false);
    // Navigate to edit page
    navigate(`/missions/${mission.id}/edit`);
  };

  // Handle mission deletion
  const handleDelete = (e) => {
    e.stopPropagation();
    setShowMenu(false);
    // Navigate to mission details page with delete intent
    navigate(`/missions/${mission.id}`, { state: { showDeleteModal: true } });
  };

  const handleCloseMission = async (e) => {
    e.stopPropagation();
    setShowMenu(false);
    try {
      await missionApi.closeMission(mission.id);
      toast.success('Mission closed');
    } catch (error) {
      toast.error('Failed to close mission');
      console.error('Error closing mission:', error);
    }
  };

  const handleStartMission = async (e) => {
    e.stopPropagation();
    setShowMenu(false);
    try {
      await missionApi.startMission(mission.id);
      toast.success('Mission started');
    } catch (error) {
      toast.error('Failed to start mission');
      console.error('Error starting mission:', error);
    }
  };

  return (
    <motion.div
      className="bg-white/95 dark:bg-gray-900/90 rounded-3xl border-2 border-indigo-200 dark:border-indigo-800 shadow-xl dark:shadow-indigo-900/20 hover:shadow-2xl transition-all duration-300 overflow-hidden h-full flex flex-col group relative scale-100 hover:scale-[1.025]"
      initial={{ opacity: 0, y: 20, scale: 1 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      whileHover={{
        y: -10,
        scale: 1.025,
        boxShadow: '0 12px 32px 0 rgba(60, 80, 180, 0.18)',
        transition: { duration: 0.25, ease: 'easeOut' }
      }}
      onClick={() => navigate(`/missions/host/${mission.id}`)}
    >
      {/* Decorative elements */}
      <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-full blur-xl"></div>
      <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-blue-500/10 to-indigo-500/10 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full blur-xl"></div>

      {/* Mission Image Carousel with Overlay */}
      <div className="relative h-52 overflow-hidden rounded-t-3xl">
        <motion.img
          key={images[currentImage]}
          src={images[currentImage]}
            alt={mission.title}
          className="w-full h-full object-cover rounded-t-3xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        />
        {/* Carousel Arrows */}
        {hasMultipleImages && (
          <>
            <button
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/70 dark:bg-gray-800/70 hover:bg-white dark:hover:bg-gray-900 text-indigo-700 dark:text-indigo-200 rounded-full p-2 shadow-md z-10"
              onClick={handlePrev}
              aria-label="Previous image"
            >
              <FaChevronLeft />
            </button>
            <button
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/70 dark:bg-gray-800/70 hover:bg-white dark:hover:bg-gray-900 text-indigo-700 dark:text-indigo-200 rounded-full p-2 shadow-md z-10"
              onClick={handleNext}
              aria-label="Next image"
            >
              <FaChevronRight />
            </button>
          </>
        )}
        {/* Bottom gradient overlay for text readability */}
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/50 dark:from-black/70 to-transparent pointer-events-none rounded-b-none rounded-t-3xl" />
        {/* Dot indicators */}
        {hasMultipleImages && (
          <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-2 z-10">
            {images.map((_, idx) => (
              <span
                key={idx}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentImage ? 'bg-white dark:bg-indigo-200 shadow-lg scale-110' : 'bg-white/40 dark:bg-indigo-900/40'}`}
              />
            ))}
          </div>
        )}
        {/* Image overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 dark:from-black/80 to-transparent"></div>

        {/* Status indicator */}
        {mission.status && (
          <div className={`absolute top-3 left-3 px-3 py-1 rounded-full ${
            mission.status.toLowerCase() === 'open'
              ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200/50 dark:border-green-800/50'
              : mission.status.toLowerCase() === 'in progress'
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200/50 dark:border-blue-800/50'
                : mission.status.toLowerCase() === 'completed'
                  ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 border border-purple-200/50 dark:border-purple-800/50'
                  : mission.status.toLowerCase() === 'cancelled'
                    ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border border-red-200/50 dark:border-red-800/50'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 border border-gray-200/50 dark:border-gray-700/50'
          } text-xs font-medium flex items-center shadow-sm`}>
            <span className={`w-2 h-2 rounded-full ${
              mission.status.toLowerCase() === 'open'
                ? 'bg-green-500 animate-pulse'
                : mission.status.toLowerCase() === 'in progress'
                  ? 'bg-blue-500 animate-pulse'
                  : mission.status.toLowerCase() === 'completed'
                    ? 'bg-purple-500'
                    : mission.status.toLowerCase() === 'cancelled'
                      ? 'bg-red-500'
                      : 'bg-gray-500 dark:bg-gray-400'
            } mr-1.5`}></span>
            {mission.status}
        </div>
        )}

        {/* Tags overlay */}
        <div className="absolute bottom-3 left-3 right-3 flex flex-wrap gap-2">
          {mission.level_requirement && (
            <motion.span
              className="bg-indigo-900/80 dark:bg-indigo-800/80 text-indigo-100 dark:text-indigo-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-indigo-700/50 dark:border-indigo-600/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              LV{mission.level_requirement.min || 1}-{mission.level_requirement.max || 99}
            </motion.span>
          )}

          {mission.style && (
            <motion.span
              className="bg-purple-900/80 dark:bg-purple-800/80 text-purple-100 dark:text-purple-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-purple-700/50 dark:border-purple-600/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              {mission.style}
            </motion.span>
          )}

          {mission.category && (
            <motion.span
              className="bg-blue-900/80 dark:bg-blue-800/80 text-blue-100 dark:text-blue-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-blue-700/50 dark:border-blue-600/50 shadow-sm flex items-center"
              whileHover={{ scale: 1.05 }}
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
              </svg>
              {mission.category}
            </motion.span>
          )}
        </div>

        {/* Host-specific action menu (edit/delete) */}
        <div className="absolute top-3 right-3 flex space-x-2 z-20">
          <button
            className="w-8 h-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-600 dark:text-gray-200 hover:text-indigo-600 dark:hover:text-indigo-300 transition-colors"
            onClick={toggleMenu}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden z-30 border border-indigo-100 dark:border-gray-800">
              <button
                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center"
                onClick={handleEdit}
              >
                <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Mission
              </button>
              {mission.status && mission.status.toLowerCase() === 'open' && (
                <button
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  onClick={handleCloseMission}
                >
                  <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Close Mission
                </button>
              )}
              {mission.status && mission.status.toLowerCase() === 'closed' && (
                <button
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                  onClick={handleStartMission}
                >
                  <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7-7 7M5 5v14" />
                  </svg>
                  Start Mission
                </button>
              )}
              <button
                className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 flex items-center"
                onClick={handleDelete}
              >
                <svg className="w-4 h-4 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete Mission
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Mission Content */}
      <div className="p-5 flex-grow flex flex-col relative">
        {/* Title - hidden on small screens as it's shown in the image overlay */}
        <h3 className="hidden md:block font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-300 dark:to-blue-400 text-xl mb-3">{mission.title}</h3>

        {/* Enhanced Bounty, Date, and Countdown Grid Layout */}
        <div className="mb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Credits Card */}
            <motion.div
              className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-indigo-100 dark:border-indigo-800 rounded-xl shadow-sm py-3 px-4 space-x-2 min-h-[56px]"
              whileHover={{ scale: 1.04 }}
            >
              <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-semibold text-indigo-700 dark:text-indigo-200 text-base">{mission.bounty} <span className="font-normal text-sm">credits</span></span>
            </motion.div>
            {/* Start Date/Time Card */}
            <motion.div
              className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 rounded-xl shadow-sm py-3 px-4 min-h-[56px]"
              whileHover={{ scale: 1.04 }}
            >
              <svg className="w-5 h-5 text-gray-400 dark:text-gray-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
             </svg>
              <span className="text-gray-700 dark:text-gray-200 text-base font-medium">{mission.date ? formatDate(mission.date) : 'Date TBD'}</span>
            </motion.div>
          </div>
        </div>

        {/* Slots information */}
        <div className="mb-5">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-300 font-medium flex items-center">
              <svg className="w-4 h-4 mr-1.5 text-gray-400 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
             </svg>
              {getSlotsText()}
            </span>
            <span className="text-sm font-bold text-indigo-700 dark:text-indigo-200 bg-indigo-50 dark:bg-indigo-900 px-2 py-0.5 rounded-md">
              {mission.slots_filled || 0}/{mission.slots_total || 5}
            </span>
          </div>
          <div className="w-full bg-gray-100 dark:bg-gray-800 rounded-full h-2.5 overflow-hidden shadow-inner">
            <motion.div
              className="bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-400 dark:to-blue-500 h-2.5 rounded-full relative"
              initial={{ width: 0 }}
              animate={{ width: `${((mission.slots_filled || 0) / (mission.slots_total || 5)) * 100}%` }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              {/* Animated shine effect */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/30 dark:via-indigo-200/20 to-transparent animate-shimmer transform -translate-x-full"></div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Host information */}
        <div className="mt-auto pt-4 border-t border-indigo-50 dark:border-indigo-900 flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative">
              <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gradient-to-br from-indigo-100 to-blue-100 dark:from-indigo-900 dark:to-blue-900 p-0.5">
             <img
               src={mission.host?.avatar || '/images/default-avatar.jpg'}
               alt={mission.host?.name || 'Host'}
                   className="w-full h-full object-cover rounded-full"
             />
               </div>
               {/* Online status indicator */}
               {mission.host?.online && (
                <span className="absolute bottom-0 right-2 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></span>
               )}
          </div>
          <div>
            <p className="text-sm font-medium text-gray-800 dark:text-gray-100">
              {mission.host?.name || 'Anonymous Host'}
            </p>
              <p className="text-xs text-gray-500 dark:text-gray-300 flex items-center">
                <svg className="w-3 h-3 mr-1 text-yellow-500 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                 </svg>
                 LV{mission.host?.level || '??'}
               </p>
             </div>
           </div>
         </div>
       </div>
     </motion.div>
   );
 };

 export default HostedMissionCard;
