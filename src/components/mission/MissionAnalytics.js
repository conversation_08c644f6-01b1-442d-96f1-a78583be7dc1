import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { missionApi } from '../../services/missionApi';

const MissionAnalytics = ({ missions = [] }) => {
  const [analytics, setAnalytics] = useState({
    totalMissions: 0,
    activeMissions: 0,
    completedMissions: 0,
    totalEarnings: 0,
    averageRating: 0,
    totalParticipants: 0,
    successRate: 0,
    popularTimes: [],
    topCategories: []
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d'); // '7d', '30d', '90d', 'all'
  
  useEffect(() => {
    calculateAnalytics();
  }, [missions, timeRange]);
  
  const calculateAnalytics = () => {
    setIsLoading(true);
    
    try {
      // Filter missions based on time range
      const now = new Date();
      const filteredMissions = missions.filter(mission => {
        if (timeRange === 'all') return true;
        
        const missionDate = new Date(mission.created_at || mission.date);
        const daysAgo = parseInt(timeRange.replace('d', ''));
        const cutoffDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000));
        
        return missionDate >= cutoffDate;
      });
      
      // Calculate basic stats
      const totalMissions = filteredMissions.length;
      const activeMissions = filteredMissions.filter(m => m.status === 'open' || m.status === 'in_progress').length;
      const completedMissions = filteredMissions.filter(m => m.status === 'completed').length;
      
      // Calculate earnings (for completed missions)
      const totalEarnings = filteredMissions
        .filter(m => m.status === 'completed')
        .reduce((sum, m) => sum + (m.bounty * m.slots_filled || 0), 0);
      
      // Calculate total participants
      const totalParticipants = filteredMissions.reduce((sum, m) => sum + (m.slots_filled || 0), 0);
      
      // Calculate success rate (completed vs total)
      const successRate = totalMissions > 0 ? Math.round((completedMissions / totalMissions) * 100) : 0;
      
      // Calculate average rating (mock data for now)
      const averageRating = 4.2; // This would come from actual ratings
      
      // Calculate popular times (hours of day when missions are created)
      const hourCounts = new Array(24).fill(0);
      filteredMissions.forEach(mission => {
        const hour = new Date(mission.date).getHours();
        hourCounts[hour]++;
      });
      
      const popularTimes = hourCounts
        .map((count, hour) => ({ hour, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 3);
      
      // Calculate top categories
      const categoryCount = {};
      filteredMissions.forEach(mission => {
        const category = mission.theme || mission.category || 'Other';
        categoryCount[category] = (categoryCount[category] || 0) + 1;
      });
      
      const topCategories = Object.entries(categoryCount)
        .map(([category, count]) => ({ category, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      setAnalytics({
        totalMissions,
        activeMissions,
        completedMissions,
        totalEarnings,
        averageRating,
        totalParticipants,
        successRate,
        popularTimes,
        topCategories
      });
    } catch (error) {
      console.error('Error calculating analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount).replace('$', '₵');
  };
  
  const formatTime = (hour) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };
  
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Mission Analytics</h3>
          
          {/* Time Range Selector */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { value: '7d', label: '7 Days' },
              { value: '30d', label: '30 Days' },
              { value: '90d', label: '90 Days' },
              { value: 'all', label: 'All Time' }
            ].map((option) => (
              <button
                key={option.value}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  timeRange === option.value
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                onClick={() => setTimeRange(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Main Stats */}
      <div className="p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <motion.div
            className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100"
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{analytics.totalMissions}</p>
                <p className="text-xs text-gray-500">Total Missions</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100"
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{analytics.completedMissions}</p>
                <p className="text-xs text-gray-500">Completed</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-100"
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{formatCurrency(analytics.totalEarnings)}</p>
                <p className="text-xs text-gray-500">Total Earnings</p>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-100"
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mr-3">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">{analytics.averageRating.toFixed(1)}</p>
                <p className="text-xs text-gray-500">Avg Rating</p>
              </div>
            </div>
          </motion.div>
        </div>
        
        {/* Additional Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Success Rate */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Success Rate</h4>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-green-500 h-3 rounded-full transition-all duration-300" 
                    style={{ width: `${analytics.successRate}%` }}
                  ></div>
                </div>
              </div>
              <span className="ml-3 text-lg font-semibold text-gray-800">{analytics.successRate}%</span>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {analytics.completedMissions} of {analytics.totalMissions} missions completed
            </p>
          </div>
          
          {/* Popular Times */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Popular Times</h4>
            <div className="space-y-2">
              {analytics.popularTimes.map((time, index) => (
                <div key={time.hour} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{formatTime(time.hour)}</span>
                  <div className="flex items-center">
                    <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="bg-indigo-500 h-2 rounded-full" 
                        style={{ 
                          width: `${analytics.popularTimes.length > 0 ? (time.count / analytics.popularTimes[0].count) * 100 : 0}%` 
                        }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">{time.count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Top Categories */}
        {analytics.topCategories.length > 0 && (
          <div className="mt-6 bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Top Categories</h4>
            <div className="flex flex-wrap gap-2">
              {analytics.topCategories.map((category, index) => (
                <span 
                  key={category.category}
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    index === 0 ? 'bg-indigo-100 text-indigo-800' :
                    index === 1 ? 'bg-blue-100 text-blue-800' :
                    index === 2 ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}
                >
                  {category.category} ({category.count})
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MissionAnalytics;
