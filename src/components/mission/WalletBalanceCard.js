import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import useGlobalBalance from '../../hooks/useGlobalBalance';

/**
 * WalletBalanceCard Component
 *
 * Displays the user's wallet balance and provides options to add credits
 * Used in mission creation and other payment-related flows
 *
 * @param {Object} props - Component props
 * @param {number} props.requiredAmount - The amount required for the current action
 * @param {boolean} props.showAddCredits - Whether to show the add credits button
 * @param {string} props.actionType - The type of action (e.g., 'create', 'join')
 */
const WalletBalanceCard = ({
  requiredAmount = 0,
  showAddCredits = true,
  actionType = 'create'
}) => {
  // Use global balance hook for synchronized balance
  const {
    balance,
    loading: isLoading,
    error,
    formatBalance
  } = useGlobalBalance({
    autoLoad: true,
    enablePolling: false
  });

  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);

  // Update sufficient balance check when balance or required amount changes
  useEffect(() => {
    if (balance !== null) {
      setHasSufficientBalance(balance >= requiredAmount);
    }
  }, [balance, requiredAmount]);

  // Format currency
  const formatCurrency = (amount) => {
    return amount?.toLocaleString() || '0';
  };

  return (
    <motion.div
      className={`rounded-3xl shadow-2xl border border-gray-100 bg-white/90 backdrop-blur-lg overflow-hidden`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6">
        <h3 className="text-lg font-bold mb-5 text-gray-800 mb-2 flex items-center">
          <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-6 h-6 mr-2 object-contain" /> X Wallet Balance
        </h3>
        <div className="flex items-center justify-between mb-6">
          <div className="text-gray-700 text-base font-medium">Current Balance</div>
          {isLoading ? (
            <div className="w-5 h-5 rounded-full border-2 border-indigo-500 border-t-transparent animate-spin"></div>
          ) : error ? (
            <div className="text-red-500 text-sm">Error loading balance</div>
          ) : (
            <div className="flex items-center font-bold text-lg text-indigo-700">
              <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-5 h-5 mr-1 object-contain" />
              {formatBalance(balance)}
            </div>
          )}
        </div>

        {!isLoading && !error && (
          <div className="space-y-4">
            {/* Required Amount */}
            <div className="flex justify-between items-center">
              <div className="text-gray-700 text-left">Required for this mission</div>
              <div className="font-medium text-gray-900 flex items-center">
                <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1 object-contain" />
                {formatCurrency(requiredAmount)}
              </div>
            </div>
            {/* Divider */}
            <div className="border-t border-gray-200 my-2"></div>
            {/* Sufficient/Insufficient State */}
            {hasSufficientBalance ? (
              <div className="flex items-center text-green-700 bg-green-50 rounded-lg px-4 py-3">
                <svg className="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">You have sufficient balance to {actionType} this mission.</span>
              </div>
            ) : (
              <div className="flex flex-col gap-2 bg-red-100 rounded-lg px-4 py-3">
                <div className="flex items-center text-red-700">
                  <svg className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium">Insufficient balance to {actionType} this mission.</span>
                </div>
                <div className="flex justify-between items-center text-xs text-red-700">
                  <span>Required: <span className="font-medium flex items-center"><img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1 object-contain" />{formatCurrency(requiredAmount)}</span></span>
                  <span>Missing: <span className="font-medium flex items-center"><img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1 object-contain" />{formatCurrency(requiredAmount - balance)}</span></span>
                </div>
              </div>
            )}
            {/* Action Buttons */}
            {showAddCredits && (
              <div className="flex justify-between items-center mt-4">
                <Link
                  to="/wallet"
                  className="text-indigo-600 text-sm hover:text-indigo-800 transition-colors"
                >
                  View Wallet
                </Link>
                <Link
                  to="/wallet/deposit"
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Credits
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default WalletBalanceCard;
