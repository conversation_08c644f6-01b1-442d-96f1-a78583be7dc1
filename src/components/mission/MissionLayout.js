import React from 'react';
import MainNavigation from '../../components/navigation/MainNavigation';
import MobileNavigation from '../../components/navigation/MobileNavigation';
import MissionNavigation from './MissionNavigation';
import MissionSubNavigation from './MissionSubNavigation';
import MissionMobileNavigation from './MissionMobileNavigation';
import MissionFooter from './MissionFooter';
import MissionBackground from './MissionBackground';

const MissionLayout = ({
  children,
  title,
  showBackButton = true,
  backPath = '/missions',
  actions = null,
  showFooter = true,
  useMainNav = true,
  useMissionMobileNav = false
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-blue-950 to-purple-950 dark:from-gray-950 dark:via-blue-950 dark:to-gray-950 relative overflow-x-hidden">
      {/* Skip to main content for accessibility */}
      <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-indigo-600 focus:text-white focus:rounded-md focus:shadow-lg focus:outline-none">
        Skip to main content
      </a>
      {/* Enhanced Animated Gaming Background */}
      <MissionBackground />

      {useMainNav ? (
        // Use the main navigation from Home.js
        <div className="sticky top-0 z-30 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md shadow-md dark:shadow-indigo-900/30">
          <MainNavigation activeItem="/missions" />
        </div>
      ) : (
        // Use the original mission navigation
        <MissionNavigation
          title={title}
          showBackButton={showBackButton}
          backPath={backPath}
          actions={actions}
        />
      )}

      {/* Mission-specific sub-navigation */}
      {useMainNav && (
        <div className="sticky top-16 z-20 bg-white/70 dark:bg-gray-900/70 backdrop-blur-md shadow-sm dark:shadow-indigo-900/20">
          <MissionSubNavigation
            title={title}
            showBackButton={showBackButton}
            backPath={backPath}
            actions={actions}
          />
        </div>
      )}

      {/* Main content grid system */}
      <main id="main-content" role="main" className="relative z-10 flex justify-center items-start min-h-[60vh] px-2 sm:px-6 py-8 mb-24 md:mb-0">
        <div className="w-full max-w-7xl xl:max-w-7xl bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg rounded-3xl shadow-2xl dark:shadow-indigo-900/30 border border-gray-100 dark:border-gray-800 p-4 sm:p-8 md:p-12 mx-auto grid grid-cols-1">
          {children}
        </div>
      </main>

      {showFooter && (
        useMissionMobileNav ? (
          <MissionMobileNavigation />
        ) : useMainNav ? (
          <div className="fixed bottom-0 left-0 w-full z-30">
            <div className="bg-gradient-to-t from-white/90 to-white/60 dark:from-gray-900/90 dark:to-gray-900/60 border-t border-gray-200 dark:border-gray-800 shadow-lg dark:shadow-indigo-900/30">
              <MobileNavigation activeItem="/missions" />
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-t from-white/90 to-white/60 dark:from-gray-900/90 dark:to-gray-900/60 border-t border-gray-200 dark:border-gray-800 shadow-lg dark:shadow-indigo-900/30">
            <MissionFooter />
          </div>
        )
      )}
    </div>
  );
};

export default MissionLayout;
