import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

const MissionNavigation = ({ title, showBackButton = true, backPath = '/missions', actions = null }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine if we're on a specific mission page
  const isOnMissionPage = location.pathname.includes('/missions/') && !location.pathname.includes('/create');
  const isOnCreatePage = location.pathname.includes('/missions/create');
  const isOnHostDashboard = location.pathname.includes('/host/dashboard');
  const isOnMyMissions = location.pathname.includes('/missions/my-missions');
  
  return (
    <nav className="bg-white/90 dark:bg-gray-900/90 shadow-lg dark:shadow-indigo-900/30 sticky top-0 z-50 backdrop-filter backdrop-blur-md transition-all duration-300">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            {showBackButton && (
              <button 
                className="mr-4 text-gray-600 dark:text-yellow-400 hover:text-indigo-600 dark:hover:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400 rounded transition-colors"
                onClick={() => navigate(backPath)}
                aria-label="Go back"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
              </button>
            )}
            <h1 className="text-2xl font-bold text-gray-800 dark:text-yellow-400">
              {title || 'Mission'}<span className="text-indigo-600 dark:text-yellow-400">X</span>
            </h1>
          </div>
          <div className="flex items-center space-x-3">
            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-1">
              <button
                onClick={() => navigate('/missions')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400 ${
                  location.pathname === '/missions' 
                    ? 'bg-indigo-50 dark:bg-gray-800 text-indigo-700 dark:text-yellow-400' 
                    : 'text-gray-600 dark:text-yellow-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-indigo-700 dark:hover:text-yellow-400'
                }`}
              >
                Browse
              </button>
              <button
                onClick={() => navigate('/missions/my-missions')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400 ${
                  isOnMyMissions
                    ? 'bg-indigo-50 dark:bg-gray-800 text-indigo-700 dark:text-yellow-400' 
                    : 'text-gray-600 dark:text-yellow-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-indigo-700 dark:hover:text-yellow-400'
                }`}
              >
                My Missions
              </button>
              <button
                onClick={() => navigate('/host/dashboard')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400 ${
                  isOnHostDashboard
                    ? 'bg-indigo-50 dark:bg-gray-800 text-indigo-700 dark:text-yellow-400' 
                    : 'text-gray-600 dark:text-yellow-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-indigo-700 dark:hover:text-yellow-400'
                }`}
              >
                Host Dashboard
              </button>
            </div>
            {/* Action Buttons */}
            {actions ? (
              actions
            ) : (
              <button
                onClick={() => navigate('/missions/create')}
                className={`px-4 py-2 rounded-lg font-medium flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400 transition-colors ${
                  isOnCreatePage
                    ? 'bg-indigo-700 dark:bg-yellow-400 text-white dark:text-gray-900'
                    : 'bg-indigo-600 dark:bg-yellow-400 text-white dark:text-gray-900 hover:bg-indigo-700 dark:hover:bg-yellow-500'
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Mission
              </button>
            )}
          </div>
        </div>
      </div>
      {/* Mobile Navigation Menu (only shown on smaller screens) */}
      <motion.div 
        className="md:hidden border-t border-gray-100 dark:border-gray-700 bg-white/90 dark:bg-gray-900/90"
        initial={false}
        animate={{ height: isOnMissionPage ? 'auto' : 0, opacity: isOnMissionPage ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        style={{ overflow: 'hidden' }}
      >
        <div className="flex justify-around py-2">
          <button
            onClick={() => navigate('/missions')}
            className="flex flex-col items-center px-3 py-2 text-xs font-medium text-gray-600 dark:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400"
          >
            <svg className="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            Browse
          </button>
          <button
            onClick={() => navigate('/missions/my-missions')}
            className="flex flex-col items-center px-3 py-2 text-xs font-medium text-gray-600 dark:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400"
          >
            <svg className="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            My Missions
          </button>
          <button
            onClick={() => navigate('/host/dashboard')}
            className="flex flex-col items-center px-3 py-2 text-xs font-medium text-gray-600 dark:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400"
          >
            <svg className="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Host
          </button>
          <button
            onClick={() => navigate('/missions/create')}
            className="flex flex-col items-center px-3 py-2 text-xs font-medium text-indigo-600 dark:text-yellow-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-yellow-400"
          >
            <svg className="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create
          </button>
        </div>
      </motion.div>
    </nav>
  );
};

export default MissionNavigation;
