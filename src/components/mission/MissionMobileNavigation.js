import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const MissionMobileNavigation = () => {
  const location = useLocation();

  // Determine active tab
  const isOnMissions = location.pathname === '/missions';
  const isOnMyMissions = location.pathname.includes('/missions/my-missions');
  const isOnCreatePage = location.pathname.includes('/missions/create');

  // Haptic feedback function
  const triggerHapticFeedback = () => {
    if (navigator.vibrate) {
      navigator.vibrate(5);
    }
  };

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/95 shadow-[0_-4px_20px_rgba(0,0,0,0.08)] z-50 backdrop-filter backdrop-blur-lg border-t border-indigo-100/50">
      <div className="flex justify-around items-center py-3 px-2">
        {/* Browse Missions */}
        <Link
          to="/missions"
          className="flex flex-col items-center relative group"
          onClick={triggerHapticFeedback}
        >
          {/* Active indicator pill */}
          {isOnMissions && (
            <div className="absolute -top-3 w-10 h-1 bg-indigo-600 rounded-full shadow-[0_0_8px_rgba(79,70,229,0.5)]"></div>
          )}
          {/* Active background glow */}
          {isOnMissions && (
            <div className="absolute inset-0 bg-indigo-50 rounded-xl -z-10"></div>
          )}

          <div className="w-12 h-12 flex items-center justify-center">
            <svg
              className={`w-6 h-6 ${isOnMissions ? 'text-indigo-600' : 'text-gray-500'} transition-transform group-active:scale-90 duration-150`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </div>
          <span className={`text-xs font-medium ${isOnMissions ? 'text-indigo-600' : 'text-gray-500'} mt-0.5`}>Browse</span>

          {/* Hover effect */}
          {!isOnMissions && (
            <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
          )}
        </Link>

        {/* My Missions */}
        <Link
          to="/missions/my-missions"
          className="flex flex-col items-center relative group"
          onClick={triggerHapticFeedback}
        >
          {/* Active indicator pill */}
          {isOnMyMissions && (
            <div className="absolute -top-3 w-10 h-1 bg-indigo-600 rounded-full shadow-[0_0_8px_rgba(79,70,229,0.5)]"></div>
          )}
          {/* Active background glow */}
          {isOnMyMissions && (
            <div className="absolute inset-0 bg-indigo-50 rounded-xl -z-10"></div>
          )}

          <div className="w-12 h-12 flex items-center justify-center">
            <svg
              className={`w-6 h-6 ${isOnMyMissions ? 'text-indigo-600' : 'text-gray-500'} transition-transform group-active:scale-90 duration-150`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
          </div>
          <span className={`text-xs font-medium ${isOnMyMissions ? 'text-indigo-600' : 'text-gray-500'} mt-0.5`}>My Missions</span>

          {/* Hover effect */}
          {!isOnMyMissions && (
            <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
          )}
        </Link>

        {/* Create Mission */}
        <Link
          to="/missions/create"
          className="flex flex-col items-center relative group"
          onClick={triggerHapticFeedback}
        >
          {/* Active indicator pill */}
          {isOnCreatePage && (
            <div className="absolute -top-3 w-10 h-1 bg-indigo-600 rounded-full shadow-[0_0_8px_rgba(79,70,229,0.5)]"></div>
          )}
          {/* Active background glow */}
          {isOnCreatePage && (
            <div className="absolute inset-0 bg-indigo-50 rounded-xl -z-10"></div>
          )}

          <div className="w-12 h-12 flex items-center justify-center">
            <div className={`rounded-full p-2 ${isOnCreatePage ? 'bg-indigo-100' : 'bg-gray-100'}`}>
              <svg
                className={`w-6 h-6 ${isOnCreatePage ? 'text-indigo-600' : 'text-gray-500'} transition-transform group-active:scale-90 duration-150`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
          <span className={`text-xs font-medium ${isOnCreatePage ? 'text-indigo-600' : 'text-gray-500'} mt-0.5`}>Create</span>

          {/* Hover effect */}
          {!isOnCreatePage && (
            <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
          )}
        </Link>

        {/* Create Mission */}
        <Link
          to="/missions/create"
          className="flex flex-col items-center relative group"
          onClick={triggerHapticFeedback}
        >
          {/* Active indicator pill */}
          {isOnCreatePage && (
            <div className="absolute -top-3 w-10 h-1 bg-indigo-600 rounded-full shadow-[0_0_8px_rgba(79,70,229,0.5)]"></div>
          )}
          {/* Active background glow */}
          {isOnCreatePage && (
            <div className="absolute inset-0 bg-indigo-50 rounded-xl -z-10"></div>
          )}

          <div className="w-12 h-12 flex items-center justify-center">
            <svg
              className={`w-6 h-6 ${isOnCreatePage ? 'text-indigo-600' : 'text-gray-500'} transition-transform group-active:scale-90 duration-150`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <span className={`text-xs font-medium ${isOnCreatePage ? 'text-indigo-600' : 'text-gray-500'} mt-0.5`}>Create</span>

          {/* Hover effect */}
          {!isOnCreatePage && (
            <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
          )}
        </Link>
      </div>

      {/* Bottom safe area for newer iPhones */}
      <div className="h-safe-bottom bg-white/95 backdrop-filter backdrop-blur-lg"></div>
    </div>
  );
};

export default MissionMobileNavigation;
