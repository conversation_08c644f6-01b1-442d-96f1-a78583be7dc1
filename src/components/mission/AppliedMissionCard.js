import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const AppliedMissionCard = ({ mission }) => {
  const navigate = useNavigate();
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  // Format relative time for application date
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return formatDate(dateString);
    }
  };
  
  // Get status badge color and text
  const getStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          text: 'Pending'
        };
      case 'accepted':
        return {
          color: 'bg-green-100 text-green-800',
          text: 'Accepted'
        };
      case 'rejected':
        return {
          color: 'bg-red-100 text-red-800',
          text: 'Rejected'
        };
      case 'cancelled':
        return {
          color: 'bg-gray-100 text-gray-800',
          text: 'Cancelled'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          text: status.charAt(0).toUpperCase() + status.slice(1)
        };
    }
  };
  
  // Handle withdraw application
  const handleWithdraw = (e) => {
    e.stopPropagation();
    // In a real app, this would be an API call
    if (window.confirm('Are you sure you want to withdraw your application? This action cannot be undone.')) {
      console.log('Application withdrawn:', mission.id);
    }
  };

  const statusInfo = getStatusInfo(mission.status);

  return (
    <motion.div 
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-100"
      whileHover={{ y: -5 }}
      onClick={() => navigate(`/missions/${mission.id}`)}
    >
      <div className="relative">
        {/* Mission Image */}
        <div className="h-40 overflow-hidden">
          <img 
            src={mission.image || '/images/mission-default.jpg'} 
            alt={mission.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
        </div>
        
        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color} flex items-center`}>
            <span className="w-1.5 h-1.5 rounded-full bg-current mr-1"></span>
            {statusInfo.text}
          </span>
        </div>
        
        {/* Withdraw Button (only for pending applications) */}
        {mission.status === 'pending' && (
          <div className="absolute top-3 right-3">
            <button 
              className="px-2 py-1 bg-white/80 backdrop-blur-sm rounded-lg text-xs font-medium text-red-600 hover:bg-white hover:text-red-700 transition-colors"
              onClick={handleWithdraw}
            >
              Withdraw
            </button>
          </div>
        )}
        
        {/* Mission Title */}
        <div className="absolute bottom-3 left-3 right-3">
          <h3 className="text-white font-bold text-lg line-clamp-2 drop-shadow-sm">{mission.title}</h3>
        </div>
      </div>
      
      {/* Mission Details */}
      <div className="p-4">
        {/* Date and Bounty */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center text-gray-500 text-sm">
            <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {formatDate(mission.date)}
          </div>
          <div className="flex items-center text-indigo-600 font-medium">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {mission.bounty}
          </div>
        </div>
        
        {/* Host Information */}
        <div className="flex items-center mb-3">
          <div className="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200">
            <img 
              src={mission.host?.avatar || '/images/default-avatar.jpg'} 
              alt={mission.host?.name || 'Host'} 
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-800">
              {mission.host?.name || 'Anonymous Host'}
            </p>
          </div>
        </div>
        
        {/* Application Info */}
        <div className="flex justify-between items-center text-sm">
          <div className="text-gray-500">
            Applied {formatRelativeTime(mission.applied_at)}
          </div>
          
          {mission.status === 'accepted' && (
            <button 
              className="px-3 py-1 bg-indigo-50 text-indigo-600 rounded-lg font-medium hover:bg-indigo-100 transition-colors flex items-center text-sm"
              onClick={(e) => {
                e.stopPropagation();
                navigate('/chat');
              }}
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Chat
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default AppliedMissionCard;
