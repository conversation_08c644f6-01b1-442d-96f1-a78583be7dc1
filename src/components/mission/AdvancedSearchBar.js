import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../common/ToastProvider';
import { debounce } from 'lodash';

const AdvancedSearchBar = ({
  searchQuery,
  setSearchQuery,
  onSearch,
  recentSearches = [],
  popularSearches = [],
  onClearSearch,
  advancedOptions = {},
  onAdvancedOptionsChange,
  isAdvancedMode = false,
  setIsAdvancedMode,
  isLoading = false
}) => {
  const [expandedSearch, setExpandedSearch] = useState(false);
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(isAdvancedMode);
  const [localIsLoading, setLocalIsLoading] = useState(false);
  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const toast = useToast();

  // Default advanced options
  const defaultAdvancedOptions = {
    searchIn: {
      title: true,
      description: true,
      host: true,
      tags: true
    },
    exactMatch: false,
    includeCompleted: false,
    autoSearch: true
  };

  // Merge with provided options
  const options = { ...defaultAdvancedOptions, ...advancedOptions };

  // Close search dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setExpandedSearch(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update search input when searchQuery changes
  useEffect(() => {
    setSearchInput(searchQuery);
  }, [searchQuery]);

  // Create debounced search function
  const debouncedSearch = useRef(
    debounce((query, opts) => {
      if (query.trim()) {
        onSearch(query.trim(), opts);
        setLocalIsLoading(false);
      }
    }, 500)
  ).current;

  // Auto-search when typing
  useEffect(() => {
    if (options.autoSearch && searchInput.trim() !== searchQuery.trim()) {
      setLocalIsLoading(true);
      debouncedSearch(searchInput, options);
    }

    return () => {
      debouncedSearch.cancel();
    };
  }, [searchInput, options.autoSearch, debouncedSearch, options, searchQuery]);

  // Generate suggestions based on input
  useEffect(() => {
    if (searchInput.trim() === '') {
      setSuggestions([]);
      return;
    }

    // Combine recent and popular searches
    const allSearches = [...new Set([...recentSearches, ...popularSearches])];

    // Filter searches that match the input
    const matchedSuggestions = allSearches.filter(search =>
      search.toLowerCase().includes(searchInput.toLowerCase())
    ).slice(0, 5); // Limit to 5 suggestions

    setSuggestions(matchedSuggestions);
  }, [searchInput, recentSearches, popularSearches]);

  // Handle search submission
  const handleSearch = () => {
    if (searchInput.trim()) {
      onSearch(searchInput.trim(), options);
      setExpandedSearch(false);
      setIsTyping(false);
    }
  };

  // Handle search suggestion click
  const handleSearchSuggestion = (suggestion) => {
    setSearchInput(suggestion);
    onSearch(suggestion, options);
    setExpandedSearch(false);
    setIsTyping(false);
  };

  // Clear search
  const clearSearch = () => {
    setSearchInput('');
    onClearSearch();
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Toggle advanced options
  const toggleAdvancedOptions = () => {
    const newState = !showAdvancedOptions;
    setShowAdvancedOptions(newState);
    setIsAdvancedMode(newState);
  };

  // Handle advanced option change
  const handleOptionChange = (category, option, value) => {
    const updatedOptions = { ...options };

    if (category) {
      updatedOptions[category] = { ...updatedOptions[category], [option]: value };
    } else {
      updatedOptions[option] = value;
    }

    onAdvancedOptionsChange(updatedOptions);
  };

  return (
    <div className="relative w-full" ref={searchRef}>
      {/* Enhanced Animated Gaming SearchBar Background */}
      <div className="absolute inset-0 z-0 rounded-2xl overflow-hidden pointer-events-none">
        <div className="absolute -top-8 -left-8 w-60 h-20 bg-gradient-to-r from-indigo-500/40 via-blue-500/30 to-purple-500/0 rounded-full blur-2xl animate-pulse-slow" />
        <div className="absolute bottom-0 right-0 w-32 h-12 bg-gradient-to-tr from-pink-400/30 via-yellow-300/20 to-indigo-400/0 rounded-full blur-2xl animate-pulse-slower" />
        {/* Gaming particles */}
        <div className="absolute top-1/2 left-1/4 w-2 h-2 bg-yellow-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '0.5s' }} />
        <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-pink-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '1.2s' }} />
        <div className="absolute bottom-1/4 left-1/2 w-2 h-2 bg-blue-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '2s' }} />
      </div>
      <div className="relative">
        <motion.div
          className={`flex items-center bg-white/90 dark:bg-gray-900/90 border-2 ${expandedSearch ? 'border-indigo-400 ring-4 ring-indigo-100' : 'border-gray-200 dark:border-indigo-900/30'} rounded-2xl overflow-hidden transition-all duration-300 shadow-lg hover:shadow-xl ${expandedSearch ? 'shadow-xl' : ''} backdrop-blur-md`}
          animate={{
            width: expandedSearch ? '100%' : '100%'
          }}
        >
          <div className="flex-grow flex items-center">
            <div className={`pl-5 ${(isTyping || isLoading || localIsLoading) ? 'text-indigo-500 animate-pulse' : 'text-gray-400'}`}>
              {(isLoading || localIsLoading) ? (
                <svg className="w-6 h-6 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              )}
            </div>
            <input
              ref={inputRef}
              type="text"
              placeholder={isLoading || localIsLoading ? "Searching..." : "Search missions"}
              className="w-full py-4 px-4 text-gray-700 dark:text-indigo-100 focus:outline-none bg-transparent placeholder-gray-400 dark:placeholder-indigo-300 text-base"
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                setIsTyping(true);
              }}
              onFocus={() => {
                setExpandedSearch(true);
                setIsTyping(true);
              }}
              onBlur={() => setIsTyping(false)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                } else if (e.key === 'Escape') {
                  setExpandedSearch(false);
                  setIsTyping(false);
                }
              }}
              disabled={isLoading}
            />
          </div>

          <AnimatePresence>
            {searchInput && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-full transition-all duration-200"
                onClick={clearSearch}
                aria-label="Clear search"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>

          <button
            className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white px-6 py-3 rounded-r-2xl hover:from-indigo-700 hover:to-blue-700 transition-all duration-300 flex items-center font-medium shadow-md hover:shadow-lg transform hover:scale-105"
            onClick={handleSearch}
            disabled={isLoading || localIsLoading}
          >
            {(isLoading || localIsLoading) ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Searching...
              </>
            ) : (
              <>Search</>
            )}
          </button>
        </motion.div>

        {/* Search suggestions */}
        <AnimatePresence>
          {expandedSearch && (suggestions.length > 0 || recentSearches.length > 0 || popularSearches.length > 0) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute z-10 mt-2 w-full bg-white/90 dark:bg-gray-900/90 rounded-xl shadow-xl border border-gray-200 dark:border-indigo-900/30 overflow-hidden backdrop-blur-md"
            >
              {suggestions.length > 0 && (
                <div className="p-3">
                  <h3 className="text-xs font-semibold text-gray-500 dark:text-indigo-200 uppercase tracking-wider mb-2">Suggestions</h3>
                  <div className="space-y-1">
                    {suggestions.map((suggestion, index) => (
                      <div
                        key={`suggestion-${index}`}
                        className="px-3 py-2 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 rounded-lg cursor-pointer flex items-center text-gray-700 dark:text-indigo-100"
                        onClick={() => handleSearchSuggestion(suggestion)}
                      >
                        <svg className="w-4 h-4 text-gray-400 dark:text-indigo-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {suggestions.length === 0 && (
                <>
                  {recentSearches.length > 0 && (
                    <div className="p-3">
                      <h3 className="text-xs font-semibold text-gray-500 dark:text-indigo-200 uppercase tracking-wider mb-2">Recent Searches</h3>
                      <div className="space-y-1">
                        {recentSearches.map((search, index) => (
                          <div
                            key={`recent-${index}`}
                            className="px-3 py-2 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 rounded-lg cursor-pointer flex items-center text-gray-700 dark:text-indigo-100"
                            onClick={() => handleSearchSuggestion(search)}
                          >
                            <svg className="w-4 h-4 text-gray-400 dark:text-indigo-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {search}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {popularSearches.length > 0 && (
                    <div className="p-3 border-t border-gray-100 dark:border-indigo-900/30">
                      <h3 className="text-xs font-semibold text-gray-500 dark:text-indigo-200 uppercase tracking-wider mb-2">Popular Searches</h3>
                      <div className="space-y-1">
                        {popularSearches.map((search, index) => (
                          <div
                            key={`popular-${index}`}
                            className="px-3 py-2 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 rounded-lg cursor-pointer flex items-center text-gray-700 dark:text-indigo-100"
                            onClick={() => handleSearchSuggestion(search)}
                          >
                            <svg className="w-4 h-4 text-gray-400 dark:text-indigo-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            {search}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Advanced Search Options */}
      <AnimatePresence>
        {showAdvancedOptions && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 bg-white/90 dark:bg-gray-900/90 rounded-xl shadow-lg border border-gray-200 dark:border-indigo-900/30 p-4 overflow-hidden backdrop-blur-md"
          >
            <h3 className="text-sm font-semibold text-gray-700 dark:text-indigo-100 mb-3">Advanced Search Options</h3>

            <div className="space-y-4">
              {/* Search In Options */}
              <div>
                <h4 className="text-xs font-medium text-gray-500 dark:text-indigo-200 uppercase tracking-wider mb-2">Search In</h4>
                <div className="grid grid-cols-2 gap-2">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.searchIn.title}
                      onChange={(e) => handleOptionChange('searchIn', 'title', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Title</span>
                  </label>
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.searchIn.description}
                      onChange={(e) => handleOptionChange('searchIn', 'description', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Description</span>
                  </label>
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.searchIn.host}
                      onChange={(e) => handleOptionChange('searchIn', 'host', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Host</span>
                  </label>
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.searchIn.tags}
                      onChange={(e) => handleOptionChange('searchIn', 'tags', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Tags</span>
                  </label>
                </div>
              </div>

              {/* Additional Options */}
              <div>
                <h4 className="text-xs font-medium text-gray-500 dark:text-indigo-200 uppercase tracking-wider mb-2">Additional Options</h4>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.exactMatch}
                      onChange={(e) => handleOptionChange(null, 'exactMatch', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Exact Match</span>
                  </label>
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.includeCompleted}
                      onChange={(e) => handleOptionChange(null, 'includeCompleted', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Include Completed Missions</span>
                  </label>

                  <label className="flex items-center space-x-2 cursor-pointer mt-2">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-indigo-600 rounded"
                      checked={options.autoSearch}
                      onChange={(e) => handleOptionChange(null, 'autoSearch', e.target.checked)}
                    />
                    <span className="text-sm text-gray-700 dark:text-indigo-100">Auto-Search While Typing</span>
                  </label>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedSearchBar;
