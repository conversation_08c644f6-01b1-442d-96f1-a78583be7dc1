import React from 'react';
import { motion } from 'framer-motion';

const MissionPlatformFeeSummary = ({
  bounty = 0,
  paxRequired = 1,
  platformFeePercentage = 10,
  platformFeeAmount = 0,
  totalAmount = 0
}) => {
  const missionCost = bounty * paxRequired;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-sm p-4 border border-gray-200"
    >
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Fee Breakdown</h3>
      
      <div className="space-y-3">
        {/* Mission Cost */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Mission Cost</span>
          <span className="font-medium flex items-center gap-1">
            <img src="/In-AppAssets/xcoin.png" alt="Credits" className="inline w-4 h-4 object-contain mr-1" />
            {missionCost}
          </span>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-2"></div>

        {/* Total Amount */}
        <div className="flex justify-between items-center">
          <span className="text-gray-900 font-semibold">Total Amount</span>
          <span className="text-gray-900 font-semibold flex items-center gap-1">
            <img src="/In-AppAssets/xcoin.png" alt="Credits" className="inline w-4 h-4 object-contain mr-1" />
            {totalAmount}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default MissionPlatformFeeSummary; 