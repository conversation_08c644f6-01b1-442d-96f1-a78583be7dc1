import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { formatDate } from '../../utils/dateUtils';
import { getCdnUrl } from '../../utils/cdnUtils';
import useTranslation from '../../hooks/useTranslation';

const UpcomingMissionCard = ({ mission }) => {
  const navigate = useNavigate();
  const { t } = useTranslation('mission');

  const handleClick = () => {
    navigate(`/missions/${mission.id}`);
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
      whileHover={{ y: -4 }}
      onClick={handleClick}
    >
      {/* Mission Image */}
      <div className="relative h-48">
        <img
          src={mission.image || '/images/mission-default.jpg'}
          alt={mission.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute bottom-4 left-4 right-4">
          <h3 className="text-white text-lg font-semibold truncate">
            {mission.title || t('mission.untitled')}
          </h3>
          <p className="text-white/80 text-sm">
            {formatDate(mission.date)}
          </p>
        </div>
      </div>

      {/* Mission Details */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <img
              src={getCdnUrl(mission.host?.avatar || '/images/default-avatar.jpg')}
              alt={mission.host?.name}
              className="w-8 h-8 rounded-full mr-2"
            />
            <span className="text-gray-700 text-sm">
              {mission.host?.name || t('mission.anonymousHost')}
            </span>
          </div>
          <span className="text-indigo-600 font-semibold">
            ${mission.bounty}
          </span>
        </div>

        {/* Mission Status */}
        <div className="flex items-center justify-between">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {t('mission.status.upcoming')}
          </span>
          <span className="text-gray-500 text-sm">
            {mission.slots_filled}/{mission.slots_total} {t('mission.slots')}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default UpcomingMissionCard; 