import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../common/ToastProvider';

const SearchBar = ({ 
  searchQuery, 
  setSearchQuery, 
  onSearch, 
  recentSearches = [], 
  popularSearches = [],
  onClearSearch
}) => {
  const [expandedSearch, setExpandedSearch] = useState(false);
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const searchRef = useRef(null);
  const inputRef = useRef(null);
  const toast = useToast();

  // Handle click outside to close expanded search
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setExpandedSearch(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update search input when searchQuery changes
  useEffect(() => {
    setSearchInput(searchQuery);
  }, [searchQuery]);

  // Generate suggestions based on input
  useEffect(() => {
    if (searchInput.trim() === '') {
      setSuggestions([]);
      return;
    }

    // Combine recent and popular searches
    const allSearches = [...new Set([...recentSearches, ...popularSearches])];
    
    // Filter searches that match the input
    const matchedSuggestions = allSearches.filter(search => 
      search.toLowerCase().includes(searchInput.toLowerCase())
    ).slice(0, 5); // Limit to 5 suggestions
    
    setSuggestions(matchedSuggestions);
  }, [searchInput, recentSearches, popularSearches]);

  // Handle search submission
  const handleSearch = () => {
    if (searchInput.trim()) {
      onSearch(searchInput.trim());
      setExpandedSearch(false);
      setIsTyping(false);
    }
  };

  // Handle search suggestion click
  const handleSearchSuggestion = (suggestion) => {
    setSearchInput(suggestion);
    onSearch(suggestion);
    setExpandedSearch(false);
    setIsTyping(false);
  };

  // Clear search
  const clearSearch = () => {
    setSearchInput('');
    onClearSearch();
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle voice search
  const handleVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      toast.error('Voice search is not supported in your browser');
      return;
    }

    try {
      const SpeechRecognition = window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.lang = 'en-US';
      recognition.continuous = false;
      recognition.interimResults = false;
      
      recognition.onstart = () => {
        toast.info('Listening... Speak now');
        setIsTyping(true);
      };
      
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setSearchInput(transcript);
        onSearch(transcript);
        toast.success('Voice search completed');
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error', event.error);
        toast.error('Voice search failed. Please try again');
        setIsTyping(false);
      };
      
      recognition.onend = () => {
        setIsTyping(false);
      };
      
      recognition.start();
    } catch (error) {
      console.error('Speech recognition error:', error);
      toast.error('Voice search failed. Please try again');
    }
  };

  return (
    <div className="relative w-full max-w-md" ref={searchRef}>
      <div className="relative">
        <motion.div 
          className={`flex items-center bg-white border ${expandedSearch ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-300'} rounded-full overflow-hidden transition-all duration-200 ${expandedSearch ? 'shadow-md' : ''}`}
          animate={{ 
            width: expandedSearch ? '100%' : '100%'
          }}
        >
          <div className="flex-grow flex items-center">
            <div className={`pl-4 ${isTyping ? 'text-indigo-500 animate-pulse' : 'text-gray-400'}`}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              ref={inputRef}
              type="text"
              placeholder="Search missions..."
              className="w-full py-3 px-3 text-gray-700 focus:outline-none bg-transparent"
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                setIsTyping(true);
              }}
              onFocus={() => {
                setExpandedSearch(true);
                setIsTyping(true);
              }}
              onBlur={() => setIsTyping(false)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                } else if (e.key === 'Escape') {
                  setExpandedSearch(false);
                  setIsTyping(false);
                }
              }}
            />
          </div>

          <AnimatePresence>
            {searchInput && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="p-2 text-gray-400 hover:text-gray-600"
                onClick={clearSearch}
                aria-label="Clear search"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </motion.button>
            )}
          </AnimatePresence>

          <div className="h-full border-l border-gray-200"></div>

          <button
            className="p-3 text-indigo-500 bg-transparent hover:text-red-400 hover:bg-transparent transition-colors"
            onClick={handleVoiceSearch}
            aria-label="Voice search"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
          </button>

          <button
            className="p-3 bg-indigo-600 text-white hover:bg-indigo-700 transition-colors rounded-r-full"
            onClick={handleSearch}
            aria-label="Search"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </motion.div>
      </div>

      {/* Expanded Search Panel */}
      <AnimatePresence>
        {expandedSearch && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-200 z-20 overflow-hidden"
          >
            {/* Live Suggestions */}
            {isTyping && searchInput && suggestions.length > 0 && (
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Suggestions</h3>
                <div className="space-y-2">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={`suggestion-${index}`}
                      className="flex items-center w-full text-left px-2 py-1.5 rounded-lg hover:bg-indigo-50 text-gray-700"
                      onClick={() => handleSearchSuggestion(suggestion)}
                    >
                      <svg className="w-4 h-4 text-indigo-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <span dangerouslySetInnerHTML={{ 
                        __html: suggestion.replace(
                          new RegExp(searchInput, 'gi'), 
                          match => `<span class="font-medium text-indigo-600">${match}</span>`
                        ) 
                      }} />
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div className="p-4 border-b border-gray-100">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Recent Searches</h3>
                <div className="space-y-2">
                  {recentSearches.map((search, index) => (
                    <button
                      key={`recent-${index}`}
                      className="flex items-center w-full text-left px-2 py-1.5 rounded-lg hover:bg-indigo-50 text-gray-700 group"
                      onClick={() => handleSearchSuggestion(search)}
                    >
                      <svg className="w-4 h-4 text-gray-400 mr-2 group-hover:text-indigo-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Popular Searches */}
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Popular Searches</h3>
              <div className="flex flex-wrap gap-2">
                {popularSearches.map((search, index) => (
                  <button
                    key={`popular-${index}`}
                    className="px-3 py-1.5 bg-gray-100 hover:bg-indigo-100 hover:text-indigo-700 rounded-full text-sm text-gray-700 transition-colors"
                    onClick={() => handleSearchSuggestion(search)}
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;
