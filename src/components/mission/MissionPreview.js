import React from 'react';
import { motion } from 'framer-motion';
import { getCdnUrl } from '../../utils/cdnUtils';

const MissionPreview = ({ mission }) => {
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const options = {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Calculate duration
  const calculateDuration = (startDate, endDate) => {
    if (!startDate || !endDate) return '';

    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationMs = end - start;
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours === 0) {
      return `${minutes} minutes`;
    } else if (minutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  };

  return (
    <motion.div
      className="bg-white max-w-5xl rounded-xl shadow-md overflow-hidden border border-gray-100"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Mission Header */}
      <div className="relative h-48 bg-gradient-to-r from-indigo-500 to-purple-600 overflow-hidden">
        {/* Mission Image (if available) */}
        {mission.images && mission.images.length > 0 && (
          <img
            src={typeof mission.images[0] === 'string' 
              ? getCdnUrl(mission.images[0]) 
              : URL.createObjectURL(mission.images[0])}
            alt={mission.title || 'Mission cover'}
            className="absolute inset-0 w-full h-full object-cover"
          />
        )}

        <div className="absolute inset-0 bg-black/30"></div>

        {/* Mission Title */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <h3 className="text-white font-bold text-2xl drop-shadow-sm">
            {mission.title || 'Your Mission Title'}
          </h3>

          <div className="flex flex-wrap gap-2 mt-2">
            {mission.style && (
              <span className="bg-white/20 backdrop-blur-sm text-white text-xs px-2.5 py-1 rounded-full">
                {mission.style}
              </span>
            )}

            {mission.theme && (
              <span className="bg-white/20 backdrop-blur-sm text-white text-xs px-2.5 py-1 rounded-full">
                {mission.theme}
              </span>
            )}

            {mission.platform && (
              <span className="bg-white/20 backdrop-blur-sm text-white text-xs px-2.5 py-1 rounded-full">
                {mission.platform}
              </span>
            )}

            {mission.language && (
              <span className="bg-white/20 backdrop-blur-sm text-white text-xs px-2.5 py-1 rounded-full">
                {mission.language}
              </span>
            )}
          </div>
        </div>

        {/* Status Badge */}
        <div className="absolute top-4 left-4">
          <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
            <span className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></span>
            Preview
          </span>
        </div>

        {/* Level Requirement */}
        {mission.levelRequirement && (
          <div className="absolute top-4 right-4">
            <span className="bg-white/80 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-lg text-sm font-medium">
              LV{mission.levelRequirement.min || 1}-LV{mission.levelRequirement.max || 99}
            </span>
          </div>
        )}
      </div>

      {/* Mission Details */}
      <div className="p-6">
        {/* Bounty and Slots */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex items-center text-indigo-600 text-xl font-semibold mb-4 md:mb-0">
            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {mission.bounty || 0} credits
          </div>
          <div className="bg-indigo-50 px-4 py-2 rounded-lg text-indigo-800 font-medium">
            {mission.slotsTotal || 0} slots available
          </div>
        </div>

        {/* Date and Time */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Date & Time</h2>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center text-gray-600 mb-2">
              <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="font-medium">Start:</span> <span className="ml-2">{formatDate(mission.date)}</span>
            </div>
            <div className="flex items-center text-gray-600">
              <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Duration:</span> <span className="ml-2">{calculateDuration(mission.date, mission.endDate)}</span>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Description</h2>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-gray-600 whitespace-pre-line">
              {mission.description || 'No description provided.'}
            </p>
          </div>
        </div>

        {/* Requirements */}
        {mission.requirements && mission.requirements.filter(r => r).length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-2">Requirements</h2>
            <ul className="bg-gray-50 p-4 rounded-lg space-y-2">
              {mission.requirements.filter(r => r).map((requirement, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-5 h-5 text-indigo-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-gray-600">{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Images Gallery */}
        {mission.images && mission.images.length > 1 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-2">Images</h2>
            <div className="grid grid-cols-3 gap-2">
              {mission.images.slice(1).map((image, index) => (
                <div key={index} className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={typeof image === 'string' 
                      ? getCdnUrl(image) 
                      : URL.createObjectURL(image)}
                    alt={`Mission ${index + 2}`}
                    className="object-cover w-full h-full"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {mission.tags && mission.tags.filter(t => t).length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-2">Tags</h2>
            <div className="flex flex-wrap gap-2">
              {mission.tags.filter(t => t).map((tag, index) => (
                <span key={index} className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Mission Footer */}
      <div className="bg-gray-50 p-6 border-t border-gray-100">
        <div className="flex justify-center">
          <span className="text-gray-500 text-sm">
            This is a preview of how your mission will appear to others
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default MissionPreview;
