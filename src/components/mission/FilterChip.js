import React from 'react';
import { motion } from 'framer-motion';

const FilterChip = ({
  label,
  onRemove,
  color = 'indigo',
  icon = null,
  removable = true
}) => {
  // Define color variants
  const colorVariants = {
    indigo: {
      bg: 'bg-indigo-50 dark:bg-indigo-900/70',
      text: 'text-indigo-800 dark:text-indigo-100',
      border: 'border-indigo-100 dark:border-indigo-800',
      hover: 'hover:bg-indigo-100 dark:hover:bg-indigo-800/80',
      iconBg: 'bg-indigo-100 dark:bg-indigo-800',
      iconText: 'text-indigo-600 dark:text-indigo-300'
    },
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/70',
      text: 'text-blue-800 dark:text-blue-100',
      border: 'border-blue-100 dark:border-blue-800',
      hover: 'hover:bg-blue-100 dark:hover:bg-blue-800/80',
      iconBg: 'bg-blue-100 dark:bg-blue-800',
      iconText: 'text-blue-600 dark:text-blue-300'
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/70',
      text: 'text-green-800 dark:text-green-100',
      border: 'border-green-100 dark:border-green-800',
      hover: 'hover:bg-green-100 dark:hover:bg-green-800/80',
      iconBg: 'bg-green-100 dark:bg-green-800',
      iconText: 'text-green-600 dark:text-green-300'
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/70',
      text: 'text-purple-800 dark:text-purple-100',
      border: 'border-purple-100 dark:border-purple-800',
      hover: 'hover:bg-purple-100 dark:hover:bg-purple-800/80',
      iconBg: 'bg-purple-100 dark:bg-purple-800',
      iconText: 'text-purple-600 dark:text-purple-300'
    },
    gray: {
      bg: 'bg-gray-50 dark:bg-gray-800/70',
      text: 'text-gray-800 dark:text-gray-100',
      border: 'border-gray-100 dark:border-gray-700',
      hover: 'hover:bg-gray-100 dark:hover:bg-gray-800/80',
      iconBg: 'bg-gray-100 dark:bg-gray-800',
      iconText: 'text-gray-600 dark:text-gray-300'
    },
    teal: {
      bg: 'bg-teal-50 dark:bg-teal-900/70',
      text: 'text-teal-800 dark:text-teal-100',
      border: 'border-teal-100 dark:border-teal-800',
      hover: 'hover:bg-teal-100 dark:hover:bg-teal-800/80',
      iconBg: 'bg-teal-100 dark:bg-teal-800',
      iconText: 'text-teal-600 dark:text-teal-300'
    },
    amber: {
      bg: 'bg-amber-50 dark:bg-amber-900/70',
      text: 'text-amber-800 dark:text-amber-100',
      border: 'border-amber-100 dark:border-amber-800',
      hover: 'hover:bg-amber-100 dark:hover:bg-amber-800/80',
      iconBg: 'bg-amber-100 dark:bg-amber-800',
      iconText: 'text-amber-600 dark:text-amber-300'
    },
    rose: {
      bg: 'bg-rose-50 dark:bg-rose-900/70',
      text: 'text-rose-800 dark:text-rose-100',
      border: 'border-rose-100 dark:border-rose-800',
      hover: 'hover:bg-rose-100 dark:hover:bg-rose-800/80',
      iconBg: 'bg-rose-100 dark:bg-rose-800',
      iconText: 'text-rose-600 dark:text-rose-300'
    },
    cyan: {
      bg: 'bg-cyan-50 dark:bg-cyan-900/70',
      text: 'text-cyan-800 dark:text-cyan-100',
      border: 'border-cyan-100 dark:border-cyan-800',
      hover: 'hover:bg-cyan-100 dark:hover:bg-cyan-800/80',
      iconBg: 'bg-cyan-100 dark:bg-cyan-800',
      iconText: 'text-cyan-600 dark:text-cyan-300'
    },
    orange: {
      bg: 'bg-orange-50 dark:bg-orange-900/70',
      text: 'text-orange-800 dark:text-orange-100',
      border: 'border-orange-100 dark:border-orange-800',
      hover: 'hover:bg-orange-100 dark:hover:bg-orange-800/80',
      iconBg: 'bg-orange-100 dark:bg-orange-800',
      iconText: 'text-orange-600 dark:text-orange-300'
    }
  };

  // Get color classes
  const colors = colorVariants[color] || colorVariants.indigo;

  return (
    <motion.div
      className={`flex items-center ${colors.bg} ${colors.text} px-3 py-1.5 rounded-full text-sm border ${colors.border} shadow-sm group`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.2 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {icon && (
        <span className={`w-5 h-5 rounded-full ${colors.iconBg} ${colors.iconText} flex items-center justify-center mr-1.5`}>
          {icon}
        </span>
      )}
      <span>{label}</span>
      {removable && onRemove && (
        <button
          className={`ml-2 ${colors.iconText} ${colors.hover} rounded-full w-5 h-5 flex items-center justify-center transition-colors`}
          onClick={onRemove}
          aria-label={`Remove ${label} filter`}
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </motion.div>
  );
};

export default FilterChip;
