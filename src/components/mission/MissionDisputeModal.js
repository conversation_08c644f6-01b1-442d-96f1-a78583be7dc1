/**
 * Mission Dispute Modal Component
 * 
 * A modal for creating and managing mission-specific disputes with:
 * - Dispute creation form
 * - Evidence upload support
 * - Status tracking
 * - Glassmorphism styling
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import missionDisputeService from '../../services/missionDisputeService';
import disputeService from '../../services/disputeService';
import { InlineLoader } from '../ui/LoadingIndicator';
import useTranslation from '../../hooks/useTranslation';
import { useToast } from '../common/ToastProvider';

const MissionDisputeModal = ({
  isOpen = false,
  onClose,
  mission,
  participant = null,
  onDisputeCreated
}) => {
  const { t } = useTranslation('mission');
  const toast = useToast();
  const [formData, setFormData] = useState({
    reason: '',
    description: '',
    media: []
  });
  const [disputeTypes, setDisputeTypes] = useState([]);
  const [loadingTypes, setLoadingTypes] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (!isOpen) return;
    const fetchTypes = async () => {
      setLoadingTypes(true);
      try {
        const response = await disputeService.getDisputeTypes();
        if (response.success) {
          const types = Array.isArray(response.data)
            ? response.data
            : response.data?.data || [];
          setDisputeTypes(types);
        } else {
          setDisputeTypes([]);
        }
      } catch (err) {
        setDisputeTypes([]);
      } finally {
        setLoadingTypes(false);
      }
    };
    fetchTypes();
  }, [isOpen]);

  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95, y: 20 }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData(prev => ({
      ...prev,
      media: [...prev.media, ...files]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const payload = { ...formData };
      const response = await missionDisputeService.createDispute(mission.id, payload);

      if (response.success) {
        setSuccess(true);
        toast.success(
          response.data?.message ||
            t('mission:modals.dispute.successMessage')
        );
        if (onDisputeCreated) {
          onDisputeCreated(response.data);
        }
        // Close modal after 2 seconds
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError(response.error);
        toast.error(response.error || 'Failed to submit dispute');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-0 sm:p-4"
          style={{
            background: 'linear-gradient(120deg, rgba(255, 228, 230, 0.7) 0%, rgba(254, 215, 170, 0.7) 100%)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)'
          }}
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="relative rounded-3xl shadow-2xl border border-white/30 bg-white/80 backdrop-blur-2xl w-full max-w-full sm:max-w-3xl max-h-screen sm:max-h-[90vh] overflow-hidden"
            style={{
              background: 'linear-gradient(120deg, rgba(255,255,255,0.95) 0%, rgba(255,245,235,0.92) 100%)',
              boxShadow: '0 8px 40px 0 rgba(255, 87, 34, 0.10), 0 1.5px 8px 0 rgba(255, 87, 34, 0.08)',
              border: '1.5px solid rgba(255,255,255,0.25)',
              backdropFilter: 'blur(16px)',
              WebkitBackdropFilter: 'blur(16px)'
            }}
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Animated background decorations */}
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-red-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000" />

            {/* Header */}
            <div className="relative z-10 px-6 py-6 border-b border-white/20 bg-gradient-to-r from-red-50 to-orange-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-red-500 to-orange-600 flex items-center justify-center shadow-lg">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-3xl font-extrabold bg-gradient-to-r from-red-600 via-orange-500 to-orange-600 bg-clip-text text-transparent">
                      {t('mission:modals.dispute.title')}
                    </h2>
                    <p className="text-gray-500 text-base mt-1">
                      {t('mission:modals.dispute.subtitle', { missionTitle: mission.title })}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 p-3 bg-white/70 hover:bg-red-100 text-red-500 rounded-full shadow transition-colors duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-400"
                  aria-label="Close dispute modal"
                  tabIndex={0}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
              {success ? (
                <motion.div
                  className="text-center py-10"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
                >
                  <motion.div
                    className="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ duration: 0.7, type: 'spring', stiffness: 200, delay: 0.1 }}
                  >
                    <motion.svg
                      className="w-10 h-10 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 0.8, delay: 0.2 }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                    </motion.svg>
                  </motion.div>
                  <motion.div
                    className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mx-auto max-w-md shadow"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <h3 className="text-2xl font-extrabold text-green-700 mb-2">{t('mission:modals.dispute.success')}</h3>
                    <p className="text-green-700 text-lg font-medium">{t('mission:modals.dispute.successMessage')}</p>
                  </motion.div>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-8 max-w-xl">
                  {/* Reason Selection */}
                  <motion.div
                    className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-orange-50/60 p-6 border-l-4 border-orange-300"
                    initial={{ opacity: 0, y: 30, scale: 0.97 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 30, scale: 0.97 }}
                    transition={{ duration: 0.4, type: 'spring', delay: 0.05 }}
                  >
                    <div className="flex items-center mb-4">
                      <span className="w-1.5 h-6 bg-gradient-to-b from-orange-400 to-red-400 rounded-full mr-3"></span>
                      <label className="text-lg font-semibold text-gray-900">
                        {t('mission:modals.dispute.reason')}
                      </label>
                    </div>
                    {loadingTypes ? (
                      <div className="flex items-center space-x-2 p-2">
                        <InlineLoader size="small" color="gray" />
                        <span className="text-gray-600">{t('common:loading')}</span>
                      </div>
                    ) : (
                      <select
                        name="reason"
                        value={formData.reason}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-xl shadow focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-orange-50/60 border border-orange-200 text-gray-900 text-base transition-colors"
                        required
                      >
                        <option value="">{t('mission:modals.dispute.selectReason')}</option>
                        {Array.isArray(disputeTypes) &&
                          disputeTypes.map((type) => (
                            <option key={type.id} value={type.id}>
                              {type.name}
                            </option>
                          ))}
                      </select>
                    )}
                  </motion.div>

                  {/* Description */}
                  <motion.div
                    className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-orange-50/60 p-6 border-l-4 border-orange-300"
                    initial={{ opacity: 0, y: 30, scale: 0.97 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 30, scale: 0.97 }}
                    transition={{ duration: 0.4, type: 'spring', delay: 0.10 }}
                  >
                    <div className="flex items-center mb-4">
                      <span className="w-1.5 h-6 bg-gradient-to-b from-orange-400 to-red-400 rounded-full mr-3"></span>
                      <label className="text-lg font-semibold text-gray-900">
                        {t('mission:modals.dispute.description')}
                      </label>
                    </div>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows="4"
                      className="w-full px-4 py-3 rounded-xl shadow focus:ring-2 focus:ring-orange-400 focus:border-orange-400 bg-orange-50/60 border border-orange-200 text-gray-900 text-base transition-colors"
                      placeholder={t('mission:modals.dispute.descriptionPlaceholder')}
                      required
                    />
                  </motion.div>

                  {/* Evidence Upload */}
                  <motion.div
                    className="rounded-2xl shadow-lg bg-gradient-to-br from-white/90 to-orange-50/60 p-6 border-l-4 border-orange-300"
                    initial={{ opacity: 0, y: 30, scale: 0.97 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 30, scale: 0.97 }}
                    transition={{ duration: 0.4, type: 'spring', delay: 0.15 }}
                  >
                    <div className="flex items-center mb-4">
                      <span className="w-1.5 h-6 bg-gradient-to-b from-orange-400 to-red-400 rounded-full mr-3"></span>
                      <label className="text-lg font-semibold text-gray-900">
                        {t('mission:modals.dispute.evidence')}
                      </label>
                    </div>
                    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-orange-200 border-dashed rounded-xl bg-orange-50/40 hover:border-orange-400 transition-colors duration-200 group focus-within:border-orange-400">
                      <div className="space-y-1 text-center">
                        <svg className="mx-auto h-12 w-12 text-orange-400 group-hover:scale-110 transition-transform duration-200" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                        <div className="flex text-sm text-gray-600 items-center justify-center">
                          <label className="relative cursor-pointer bg-white/80 rounded-md font-medium text-orange-600 hover:text-orange-700 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-400 px-2 py-1 transition-colors duration-200">
                            <span>{t('mission:modals.dispute.uploadEvidence')}</span>
                            <input
                              type="file"
                              className="sr-only"
                              multiple
                              onChange={handleFileChange}
                              accept="image/*,.pdf,.doc,.docx"
                            />
                          </label>
                          <p className="pl-2 text-orange-500">{t('mission:modals.dispute.orDragAndDrop')}</p>
                        </div>
                        <p className="text-xs text-gray-500">
                          {t('mission:modals.dispute.supportedFormats')}
                        </p>
                      </div>
                    </div>
                    {formData.media.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">{t('mission:modals.dispute.uploadedFiles')}</h4>
                        <ul className="space-y-2">
                          {formData.media.map((file, index) => (
                            <li key={index} className="flex items-center justify-between text-sm text-gray-600">
                              <span>{file.name}</span>
                              <button
                                type="button"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    media: prev.media.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="text-orange-500 hover:text-red-600 font-semibold"
                              >
                                {t('mission:modals.dispute.remove')}
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </motion.div>

                  {/* Error Message */}
                  {error && (
                    <motion.div
                      className="p-4 bg-gradient-to-br from-red-100 to-orange-100 border border-red-200 rounded-xl flex items-center space-x-3 mb-4"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <svg className="w-6 h-6 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="text-base font-semibold text-red-700">{typeof error === 'string' ? error : error.message}</p>
                      </div>
                    </motion.div>
                  )}

                  {/* Loading State */}
                  {loading && (
                    <motion.div
                      className="flex justify-center items-center py-8 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl shadow-inner mt-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                        className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mr-4"
                      />
                      <span className="text-blue-700 text-lg font-semibold">{t('mission:modals.dispute.submitting')}</span>
                    </motion.div>
                  )}

                  {/* Submit Button */}
                  <motion.div
                    className="sticky bottom-0 left-0 right-0 z-20 bg-gradient-to-r from-white/90 to-orange-50/80 border-t border-orange-100 px-0 sm:px-6 py-4 flex justify-center mt-8"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 30 }}
                    transition={{ duration: 0.4, type: 'spring', delay: 0.2 }}
                  >
                    <motion.button
                      type="submit"
                      className="w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-red-500 to-orange-500 text-white font-extrabold text-lg rounded-2xl shadow-xl hover:from-red-600 hover:to-orange-600 transition-all duration-300 flex items-center justify-center space-x-3 focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-400 disabled:opacity-50 disabled:cursor-not-allowed"
                      whileHover={{ scale: 1.04 }}
                      whileTap={{ scale: 0.97 }}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <InlineLoader size="small" color="white" />
                          <span>{t('mission:modals.dispute.submitting')}</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          <span>{t('mission:modals.dispute.submit')}</span>
                        </>
                      )}
                    </motion.button>
                  </motion.div>
                </form>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MissionDisputeModal;
