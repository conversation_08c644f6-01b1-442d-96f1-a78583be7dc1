import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const ParticipantsModal = ({ isOpen, onClose, mission }) => {
  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } }
  };
  
  // Calculate remaining slots
  const remainingSlots = mission ? mission.slots_total - mission.slots_filled : 0;
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={backdropVariants}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4 text-white">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">Mission Participants</h3>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Content */}
            <div className="p-6">
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-gray-800">Current Participants</h4>
                  <span className="text-sm text-gray-500">
                    {mission?.participants?.length || 0}/{mission?.slots_total || 0} slots filled
                  </span>
                </div>
                
                {/* Progress bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-indigo-600 h-2 rounded-full" 
                    style={{ width: `${((mission?.participants?.length || 0) / (mission?.slots_total || 1)) * 100}%` }}
                  ></div>
                </div>
              </div>
              
              {/* Host */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-800 mb-2">Host</h4>
                <div className="flex items-center p-3 bg-indigo-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full overflow-hidden mr-3 bg-gray-200">
                    <img 
                      src={mission?.host?.avatar || '/images/default-avatar.jpg'} 
                      alt={mission?.host?.name || 'Host'} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">
                      {mission?.host?.name || 'Anonymous Host'}
                    </p>
                    <div className="flex items-center text-sm text-gray-500">
                      <span className="mr-2">LV{mission?.host?.level || '??'}</span>
                      {mission?.host?.rating && (
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="ml-1">{mission?.host?.rating}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Participants List */}
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Participants</h4>
                {mission?.participants && mission.participants.length > 0 ? (
                  <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
                    {mission.participants.map((participant, index) => (
                      <div key={participant.id || index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                        <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-200">
                          <img 
                            src={participant.avatar || '/images/default-avatar.jpg'} 
                            alt={participant.name || 'Participant'} 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-800">
                            {participant.name || 'Anonymous Participant'}
                          </p>
                          <p className="text-sm text-gray-500">
                            LV{participant.level || '??'}
                          </p>
                        </div>
                        {participant.role && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            {participant.role}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 bg-gray-50 rounded-lg">
                    <svg className="w-12 h-12 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <p className="text-gray-500">No participants yet</p>
                  </div>
                )}
                
                {/* Remaining Slots */}
                {remainingSlots > 0 && (
                  <div className="mt-4 p-3 border border-dashed border-gray-300 rounded-lg text-center">
                    <p className="text-gray-500">
                      {remainingSlots} slot{remainingSlots !== 1 ? 's' : ''} remaining
                    </p>
                  </div>
                )}
              </div>
              
              <div className="mt-6 flex justify-end">
                <button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
                  onClick={onClose}
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ParticipantsModal;
