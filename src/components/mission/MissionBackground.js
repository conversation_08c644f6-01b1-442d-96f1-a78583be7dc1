import React from 'react';

const MissionBackground = () => (
  <div aria-hidden="true" className="pointer-events-none fixed inset-0 z-0">
    {/* Main gradient overlays */}
    <div className="absolute top-0 left-0 w-full h-96 bg-gradient-to-tr from-indigo-700/60 via-blue-800/40 to-purple-900/0 dark:from-yellow-400/10 dark:via-pink-400/10 dark:to-indigo-400/0 blur-2xl animate-pulse-slow" style={{ willChange: 'transform' }} />
    <div className="absolute bottom-0 right-0 w-1/2 h-80 bg-gradient-to-br from-blue-900/40 via-indigo-900/20 to-purple-900/0 dark:from-blue-900/20 dark:via-indigo-900/10 dark:to-gray-950/0 blur-2xl animate-pulse-slower" style={{ willChange: 'transform' }} />
    {/* Gaming particles and floating elements */}
    <div className="absolute top-1/4 left-1/3 w-6 h-6 bg-yellow-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '0.5s', willChange: 'transform' }} />
    <div className="absolute top-2/3 right-1/4 w-4 h-4 bg-pink-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '1.2s', willChange: 'transform' }} />
    <div className="absolute bottom-1/3 left-1/2 w-3 h-3 bg-blue-400/60 rounded-full blur-md animate-float-slow" style={{ animationDelay: '2s', willChange: 'transform' }} />
    <div className="absolute top-1/2 right-1/2 w-8 h-8 bg-fuchsia-400/40 rounded-full blur-lg animate-float-slow" style={{ animationDelay: '2.5s', willChange: 'transform' }} />
    {/* Mascot or logo for extra gaming flavor */}
    <img src="/MissionXMascot.png" alt="MissionX Mascot" loading="lazy" className="absolute bottom-0 right-8 w-32 h-32 opacity-70 pointer-events-none select-none hidden md:block" />
  </div>
);

export default MissionBackground; 