import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../../common/ToastProvider';

const MissionRewards = ({ 
  mission = {}, 
  currentUser = {},
  isHost = false,
  onClaimReward,
  onDistributeRewards
}) => {
  const toast = useToast();
  const [showDistributeModal, setShowDistributeModal] = useState(false);
  const [customDistribution, setCustomDistribution] = useState([]);
  
  // Initialize custom distribution when modal opens
  const initializeDistribution = () => {
    if (!mission.participants || mission.participants.length === 0) {
      return;
    }
    
    const totalReward = mission.bounty || 0;
    const equalShare = Math.floor(totalReward / mission.participants.length);
    const remainder = totalReward - (equalShare * mission.participants.length);
    
    const distribution = mission.participants.map((participant, index) => ({
      id: participant.id,
      name: participant.name,
      avatar: participant.avatar,
      amount: equalShare + (index === 0 ? remainder : 0)
    }));
    
    setCustomDistribution(distribution);
  };
  
  // Handle reward claim
  const handleClaimReward = () => {
    if (mission.status?.toLowerCase() !== 'completed') {
      toast.info('Rewards can only be claimed when the mission is completed');
      return;
    }
    
    onClaimReward();
    toast.success('Reward claimed successfully!');
  };
  
  // Handle reward distribution
  const handleDistributeRewards = () => {
    if (mission.status?.toLowerCase() !== 'completed') {
      toast.info('Rewards can only be distributed when the mission is completed');
      return;
    }
    
    onDistributeRewards(customDistribution);
    setShowDistributeModal(false);
    toast.success('Rewards distributed successfully!');
  };
  
  // Update distribution amount
  const updateDistributionAmount = (id, amount) => {
    const newAmount = parseInt(amount) || 0;
    
    setCustomDistribution(prev => 
      prev.map(item => 
        item.id === id ? { ...item, amount: newAmount } : item
      )
    );
  };
  
  // Calculate total distributed amount
  const getTotalDistributed = () => {
    return customDistribution.reduce((sum, item) => sum + (item.amount || 0), 0);
  };
  
  // Calculate remaining amount
  const getRemainingAmount = () => {
    const totalReward = mission.bounty || 0;
    const totalDistributed = getTotalDistributed();
    return totalReward - totalDistributed;
  };
  
  // Reset distribution to equal shares
  const resetToEqualShares = () => {
    initializeDistribution();
  };
  
  // Check if user has claimed reward
  const hasClaimedReward = () => {
    return mission.rewardsClaimed?.includes(currentUser.id);
  };
  
  // Check if all rewards have been claimed
  const allRewardsClaimed = () => {
    return mission.rewardsClaimed?.length === mission.participants?.length;
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-800 flex items-center">
            <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Mission Rewards
          </h2>
        </div>
        
        {/* Main Reward Card */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl p-6 text-white mb-6 relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -mr-20 -mt-20"></div>
          <div className="absolute bottom-0 left-0 w-20 h-20 bg-white/10 rounded-full -ml-10 -mb-10"></div>
          
          <div className="relative">
            <h3 className="text-lg font-medium mb-2">Mission Bounty</h3>
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-3xl font-bold">{mission.bounty || 0} Credits</p>
                <p className="text-white/70">
                  {mission.status?.toLowerCase() === 'completed' 
                    ? allRewardsClaimed() 
                      ? 'All rewards claimed' 
                      : 'Available for claim' 
                    : mission.status?.toLowerCase() === 'in progress' 
                      ? 'Pending mission completion' 
                      : 'Pending mission start'
                  }
                </p>
              </div>
            </div>
            
            {mission.status?.toLowerCase() === 'completed' && (
              <div>
                {isHost ? (
                  <button 
                    className="px-4 py-2 bg-white text-indigo-700 rounded-lg font-medium hover:bg-indigo-50 transition-colors"
                    onClick={() => {
                      setShowDistributeModal(true);
                      initializeDistribution();
                    }}
                    disabled={allRewardsClaimed()}
                  >
                    {allRewardsClaimed() ? 'All Rewards Distributed' : 'Distribute Rewards'}
                  </button>
                ) : (
                  <button 
                    className="px-4 py-2 bg-white text-indigo-700 rounded-lg font-medium hover:bg-indigo-50 transition-colors"
                    onClick={handleClaimReward}
                    disabled={hasClaimedReward()}
                  >
                    {hasClaimedReward() ? 'Reward Claimed' : 'Claim Reward'}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* Reward Distribution */}
        {mission.participants && mission.participants.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Reward Distribution</h3>
            
            <div className="space-y-3">
              {mission.participants.map((participant) => (
                <div 
                  key={participant.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <img 
                        src={participant.avatar || '/images/default-avatar.jpg'} 
                        alt={participant.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{participant.name}</p>
                      <p className="text-sm text-gray-500">
                        {participant.role || 'Participant'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    {participant.rewardAmount ? (
                      <div className="flex items-center bg-green-100 px-3 py-1 rounded-full text-green-800">
                        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {participant.rewardAmount} Credits
                      </div>
                    ) : (
                      <div className="flex items-center bg-gray-100 px-3 py-1 rounded-full text-gray-600">
                        <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Pending
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Additional Rewards */}
        {mission.additionalRewards && mission.additionalRewards.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Additional Rewards</h3>
            
            <div className="space-y-3">
              {mission.additionalRewards.map((reward, index) => (
                <div 
                  key={index}
                  className="p-3 bg-gray-50 rounded-lg border border-gray-200"
                >
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-800">{reward.name}</h4>
                  </div>
                  
                  <p className="text-sm text-gray-600 ml-11">{reward.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Distribute Rewards Modal */}
      <AnimatePresence>
        {showDistributeModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] overflow-hidden"
            >
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-semibold text-gray-800">Distribute Rewards</h3>
                <p className="text-gray-600 mt-1">
                  Allocate the mission bounty of {mission.bounty || 0} credits among participants
                </p>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[60vh]">
                <div className="space-y-4">
                  {customDistribution.map((item) => (
                    <div key={item.id} className="flex items-center">
                      <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                        <img 
                          src={item.avatar || '/images/default-avatar.jpg'} 
                          alt={item.name} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-grow mr-3">
                        <p className="font-medium text-gray-800">{item.name}</p>
                      </div>
                      <div className="w-24">
                        <div className="relative">
                          <input
                            type="number"
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-right"
                            value={item.amount || 0}
                            onChange={(e) => updateDistributionAmount(item.id, e.target.value)}
                            min="0"
                            max={mission.bounty || 0}
                          />
                          <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                            <span className="text-gray-500">₵</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Total Bounty:</span>
                    <span className="font-medium text-gray-800">{mission.bounty || 0} Credits</span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Allocated:</span>
                    <span className={`font-medium ${getRemainingAmount() < 0 ? 'text-red-600' : 'text-gray-800'}`}>
                      {getTotalDistributed()} Credits
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Remaining:</span>
                    <span className={`font-medium ${getRemainingAmount() < 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {getRemainingAmount()} Credits
                    </span>
                  </div>
                </div>
                
                {getRemainingAmount() < 0 && (
                  <div className="mt-3 p-3 bg-red-50 rounded-lg text-red-700 text-sm">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      <p>
                        You've allocated more credits than available. Please adjust the distribution to match the total bounty.
                      </p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="p-6 border-t border-gray-200 flex justify-between">
                <div>
                  <button 
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                    onClick={resetToEqualShares}
                  >
                    Reset to Equal Shares
                  </button>
                </div>
                <div className="flex space-x-3">
                  <button 
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={() => setShowDistributeModal(false)}
                  >
                    Cancel
                  </button>
                  <button 
                    className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    onClick={handleDistributeRewards}
                    disabled={getRemainingAmount() !== 0}
                  >
                    Distribute Rewards
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MissionRewards;
