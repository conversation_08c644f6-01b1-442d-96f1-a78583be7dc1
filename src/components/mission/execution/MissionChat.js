import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../../common/ToastProvider';

const MissionChat = ({ 
  messages = [], 
  currentUser = {}, 
  onSendMessage,
  participants = [],
  isHost = false
}) => {
  const toast = useToast();
  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  // Handle message submission
  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!messageText.trim()) {
      return;
    }
    
    onSendMessage({
      text: messageText,
      type: 'text',
      timestamp: new Date().toISOString()
    });
    
    setMessageText('');
    setShowEmojiPicker(false);
    setShowAttachmentOptions(false);
  };
  
  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size exceeds 5MB limit');
      return;
    }
    
    // Create a preview URL
    const previewUrl = URL.createObjectURL(file);
    
    // Determine file type
    const fileType = file.type.split('/')[0]; // 'image', 'video', etc.
    
    onSendMessage({
      text: `Sent a ${fileType}`,
      type: fileType,
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
        url: previewUrl
      },
      timestamp: new Date().toISOString()
    });
    
    setShowAttachmentOptions(false);
    
    // Clean up the preview URL
    URL.revokeObjectURL(previewUrl);
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Format date for date separators
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' });
    }
  };
  
  // Check if message is from current user
  const isCurrentUser = (senderId) => {
    return senderId === currentUser.id;
  };
  
  // Get sender name
  const getSenderName = (senderId) => {
    if (senderId === currentUser.id) {
      return 'You';
    }
    
    const sender = participants.find(p => p.id === senderId);
    return sender ? sender.name : 'Unknown User';
  };
  
  // Get sender avatar
  const getSenderAvatar = (senderId) => {
    if (senderId === currentUser.id) {
      return currentUser.avatar || '/images/default-avatar.jpg';
    }
    
    const sender = participants.find(p => p.id === senderId);
    return sender ? sender.avatar || '/images/default-avatar.jpg' : '/images/default-avatar.jpg';
  };
  
  // Group messages by date
  const groupMessagesByDate = () => {
    const groups = [];
    let currentDate = null;
    let currentGroup = [];
    
    messages.forEach(message => {
      const messageDate = new Date(message.timestamp).toDateString();
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({
            date: currentDate,
            messages: currentGroup
          });
        }
        
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });
    
    if (currentGroup.length > 0) {
      groups.push({
        date: currentDate,
        messages: currentGroup
      });
    }
    
    return groups;
  };
  
  // Render message content based on type
  const renderMessageContent = (message) => {
    switch (message.type) {
      case 'image':
        return (
          <div className="mt-1 rounded-lg overflow-hidden">
            <img 
              src={message.file?.url || message.url} 
              alt="Shared image" 
              className="max-w-full h-auto max-h-60 object-contain"
            />
          </div>
        );
      case 'video':
        return (
          <div className="mt-1 rounded-lg overflow-hidden">
            <video 
              src={message.file?.url || message.url} 
              controls 
              className="max-w-full h-auto max-h-60"
            />
          </div>
        );
      case 'file':
        return (
          <div className="mt-1 bg-gray-100 rounded-lg p-3 flex items-center">
            <svg className="w-8 h-8 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-gray-800">{message.file?.name || 'File'}</p>
              <p className="text-xs text-gray-500">
                {message.file?.size ? `${Math.round(message.file.size / 1024)} KB` : ''}
              </p>
            </div>
          </div>
        );
      case 'system':
        return (
          <div className="py-1 px-3 bg-gray-100 rounded-lg text-sm text-gray-600 italic">
            {message.text}
          </div>
        );
      default:
        return <p className="whitespace-pre-wrap break-words">{message.text}</p>;
    }
  };
  
  const messageGroups = groupMessagesByDate();
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden flex flex-col h-[600px]">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <h2 className="text-lg font-semibold text-gray-800">Mission Chat</h2>
        </div>
        
        <div className="flex items-center">
          <div className="flex -space-x-2 mr-3">
            {participants.slice(0, 3).map((participant, index) => (
              <div key={participant.id || index} className="relative w-8 h-8 rounded-full overflow-hidden border-2 border-white">
                <img 
                  src={participant.avatar || '/images/default-avatar.jpg'} 
                  alt={participant.name} 
                  className="w-full h-full object-cover"
                />
                {participant.online && (
                  <span className="absolute bottom-0 right-0 w-2 h-2 bg-green-500 rounded-full border border-white"></span>
                )}
              </div>
            ))}
            {participants.length > 3 && (
              <div className="w-8 h-8 rounded-full bg-indigo-100 border-2 border-white flex items-center justify-center text-xs font-medium text-indigo-600">
                +{participants.length - 3}
              </div>
            )}
          </div>
          
          <button className="p-2 text-gray-500 hover:text-indigo-600 transition-colors">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Messages Container */}
      <div className="flex-grow overflow-y-auto p-4 space-y-6 custom-scrollbar">
        {messageGroups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">No Messages Yet</h3>
            <p className="text-gray-500 max-w-md">
              Start the conversation by sending a message to the mission participants.
            </p>
          </div>
        ) : (
          messageGroups.map((group, groupIndex) => (
            <div key={groupIndex} className="space-y-4">
              {/* Date Separator */}
              <div className="flex items-center justify-center">
                <div className="bg-gray-100 px-3 py-1 rounded-full text-xs font-medium text-gray-600">
                  {formatDate(group.messages[0].timestamp)}
                </div>
              </div>
              
              {/* Messages */}
              <div className="space-y-3">
                {group.messages.map((message, messageIndex) => (
                  <div 
                    key={message.id || messageIndex}
                    className={`flex ${isCurrentUser(message.senderId) ? 'justify-end' : 'justify-start'}`}
                  >
                    {message.type === 'system' ? (
                      <div className="flex justify-center w-full my-2">
                        {renderMessageContent(message)}
                      </div>
                    ) : (
                      <div className={`flex ${isCurrentUser(message.senderId) ? 'flex-row-reverse' : 'flex-row'} max-w-[80%]`}>
                        {/* Avatar */}
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 rounded-full overflow-hidden">
                            <img 
                              src={getSenderAvatar(message.senderId)} 
                              alt={getSenderName(message.senderId)} 
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </div>
                        
                        {/* Message Content */}
                        <div className={`mx-2 ${isCurrentUser(message.senderId) ? 'text-right' : 'text-left'}`}>
                          <div className="flex items-center mb-1">
                            <p className={`text-xs font-medium text-gray-600 ${isCurrentUser(message.senderId) ? 'order-2 ml-2' : 'order-1 mr-2'}`}>
                              {getSenderName(message.senderId)}
                            </p>
                            <p className={`text-xs text-gray-400 ${isCurrentUser(message.senderId) ? 'order-1' : 'order-2'}`}>
                              {formatTimestamp(message.timestamp)}
                            </p>
                          </div>
                          
                          <div 
                            className={`rounded-lg p-3 inline-block max-w-full ${
                              isCurrentUser(message.senderId) 
                                ? 'bg-indigo-600 text-white rounded-tr-none' 
                                : 'bg-gray-100 text-gray-800 rounded-tl-none'
                            }`}
                          >
                            {renderMessageContent(message)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSendMessage} className="flex items-end">
          <div className="relative flex-grow">
            <textarea
              className="w-full border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              placeholder="Type a message..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              rows={1}
              style={{ minHeight: '42px', maxHeight: '120px' }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage(e);
                }
              }}
            />
            
            <div className="absolute right-2 bottom-2 flex items-center space-x-1">
              {/* Emoji Button */}
              <button
                type="button"
                className="p-1 text-gray-500 hover:text-indigo-600 transition-colors"
                onClick={() => {
                  setShowEmojiPicker(!showEmojiPicker);
                  setShowAttachmentOptions(false);
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              
              {/* Attachment Button */}
              <button
                type="button"
                className="p-1 text-gray-500 hover:text-indigo-600 transition-colors"
                onClick={() => {
                  setShowAttachmentOptions(!showAttachmentOptions);
                  setShowEmojiPicker(false);
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              </button>
            </div>
            
            {/* Emoji Picker */}
            <AnimatePresence>
              {showEmojiPicker && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute bottom-full mb-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 p-2 w-64"
                >
                  <div className="grid grid-cols-8 gap-1">
                    {['😊', '😂', '❤️', '👍', '🎮', '🏆', '🔥', '👏', '🙏', '😎', '🤔', '😢', '😡', '🤯', '🥳', '👋'].map((emoji) => (
                      <button
                        key={emoji}
                        type="button"
                        className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded"
                        onClick={() => setMessageText(prev => prev + emoji)}
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Attachment Options */}
            <AnimatePresence>
              {showAttachmentOptions && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute bottom-full mb-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 p-2 w-48"
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*,video/*,application/pdf"
                    onChange={handleFileUpload}
                  />
                  
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded flex items-center"
                    onClick={() => {
                      fileInputRef.current.accept = 'image/*';
                      fileInputRef.current.click();
                    }}
                  >
                    <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Image
                  </button>
                  
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded flex items-center"
                    onClick={() => {
                      fileInputRef.current.accept = 'video/*';
                      fileInputRef.current.click();
                    }}
                  >
                    <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Video
                  </button>
                  
                  <button
                    type="button"
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded flex items-center"
                    onClick={() => {
                      fileInputRef.current.accept = 'application/pdf';
                      fileInputRef.current.click();
                    }}
                  >
                    <svg className="w-5 h-5 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Document
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          <button
            type="submit"
            className="ml-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex-shrink-0 flex items-center"
            disabled={!messageText.trim()}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  );
};

export default MissionChat;
