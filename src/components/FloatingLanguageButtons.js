import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { getLanguageFlag } from '../utils/i18nUtils';

/**
 * Floating Language Buttons Component
 * 
 * Displays three floating buttons for language selection (English, Malay, Chinese)
 * 
 * @param {Object} props - Component props
 * @param {string} props.position - Position of the buttons ('bottom-left', 'bottom-right', 'top-left', 'top-right')
 * @param {string} props.className - Additional CSS classes
 */
const FloatingLanguageButtons = ({
  position = 'bottom-left',
  className = '',
}) => {
  // Get language context
  const { currentLanguage, changeLanguage } = useLanguage();
  
  // Define supported languages
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ms', name: 'Bahasa Melayu', flag: '🇲🇾' },
    { code: 'cn', name: '中文', flag: '🇨🇳' }
  ];
  
  // Determine container classes based on position
  let containerClasses = 'fixed z-50 flex gap-2';
  
  switch (position) {
    case 'bottom-right':
      containerClasses += ' bottom-4 right-4';
      break;
    case 'top-left':
      containerClasses += ' top-4 left-4';
      break;
    case 'top-right':
      containerClasses += ' top-4 right-4';
      break;
    case 'bottom-left':
    default:
      containerClasses += ' bottom-4 left-4';
      break;
  }
  
  return (
    <div className={`${containerClasses} ${className}`}>
      {languages.map((lang) => (
        <button
          key={lang.code}
          onClick={() => changeLanguage(lang.code)}
          className={`flex items-center justify-center p-2 rounded-full shadow-md transition-all duration-200 ${
            currentLanguage === lang.code
              ? 'bg-blue-600 text-white scale-110 ring-2 ring-blue-300'
              : 'bg-white text-gray-700 hover:bg-gray-100 hover:-translate-y-1'
          }`}
          aria-label={`Switch to ${lang.name}`}
          title={lang.name}
        >
          <span className="text-lg" aria-hidden="true">
            {getLanguageFlag(lang.code)}
          </span>
        </button>
      ))}
    </div>
  );
};

export default FloatingLanguageButtons;
