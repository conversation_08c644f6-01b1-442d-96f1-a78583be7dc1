import React, { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useNotifications } from '../../context/NotificationContext';
import GiftNotificationCard from './GiftNotificationCard';

const GiftToastContainer = () => {
  const { notifications, markAsRead } = useNotifications();
  const [activeGifts, setActiveGifts] = useState([]);

  const isGiftNotification = (notification) => {
    const type =
      notification.data?.type || notification.data?.notification_type || '';
    const title = (notification.title || '').toLowerCase();
    const body = (notification.body || '').toLowerCase();
    const hasGiftInfo =
      type.toLowerCase().includes('gift') ||
      !!notification.data?.gift_name ||
      !!notification.data?.giftName ||
      !!notification.data?.gift_id ||
      !!notification.data?.gift_item_id ||
      !!notification.data?.gift;

    return (
      hasGiftInfo ||
      title.includes('gift') ||
      body.includes('gift')
    );
  };

  useEffect(() => {
    const giftNotifications = notifications.filter(
      (n) => isGiftNotification(n) && !n.read
    );

    setActiveGifts(prev => {
      const existingIds = prev.map(g => g.id);
      const newGifts = giftNotifications.filter(n => !existingIds.includes(n.id));
      return [...prev, ...newGifts];
    });
  }, [notifications]);

  const dismissGift = (id) => {
    setActiveGifts(prev => prev.filter(g => g.id !== id));
    if (markAsRead) {
      markAsRead(id);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-4 pointer-events-none">
      <AnimatePresence>
        {activeGifts.map(gift => (
          <motion.div
            key={gift.id}
            className="pointer-events-auto"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
          >{/*}
            <GiftNotificationCard
              sender={gift.data.senderName || gift.data.sender_name || 'Someone'}
              giftName={gift.data.giftName || gift.data.gift_name || 'Gift'}
              giftImage={gift.data.icon_path || '/gifts/default-gift.png'}
              quantity={gift.data.quantity || 1}
              onReceive={() => dismissGift(gift.id)}
              onReply={() => dismissGift(gift.id)}
            /> */}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default GiftToastContainer;
