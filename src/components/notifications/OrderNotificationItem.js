import React from 'react';
import { motion } from 'framer-motion';

const OrderNotificationItem = ({ notification, onMarkAsRead, onDismiss, onClick }) => {
    const { data, title, body, timestamp, read } = notification;
    
    // Extract order-specific data
    const orderId = data?.order_id || data?.orderId;
    const orderType = data?.type || data?.notification_type || 'order';
    const customerName = data?.customer_name || data?.customerName;
    const talentName = data?.talent_name || data?.talentName;
    const serviceName = data?.service_name || data?.serviceName;
    const status = data?.status;
    const amount = data?.amount;
    const isScheduled = data?.is_scheduled || data?.isScheduled;

    // Get relative time
    const getRelativeTime = (timestamp) => {
        const now = new Date();
        const notificationTime = new Date(timestamp);
        const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
    };

    // Get notification icon based on type and status
    const getNotificationIcon = () => {
        const iconClass = "w-5 h-5";
        
        switch (orderType) {
            case 'order_placed':
            case 'new_order':
            case 'prebooked_order_request':
                return (
                    <div className="p-2 bg-blue-100 rounded-lg">
                        <svg className={`${iconClass} text-blue-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                );
            case 'order_accepted':
                return (
                    <div className="p-2 bg-green-100 rounded-lg">
                        <svg className={`${iconClass} text-green-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                );
            case 'order_rejected':
                return (
                    <div className="p-2 bg-red-100 rounded-lg">
                        <svg className={`${iconClass} text-red-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                );
            case 'order_completed':
                return (
                    <div className="p-2 bg-emerald-100 rounded-lg">
                        <svg className={`${iconClass} text-emerald-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                );
            case 'order_cancelled':
                return (
                    <div className="p-2 bg-orange-100 rounded-lg">
                        <svg className={`${iconClass} text-orange-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                        </svg>
                    </div>
                );
            case 'order_timeout':
                return (
                    <div className="p-2 bg-yellow-100 rounded-lg">
                        <svg className={`${iconClass} text-yellow-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                );
            default:
                return (
                    <div className="p-2 bg-gray-100 rounded-lg">
                        <svg className={`${iconClass} text-gray-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                    </div>
                );
        }
    };

    // Get status badge
    const getStatusBadge = () => {
        if (!status) return null;
        
        const badgeClasses = {
            'accepted': 'bg-green-100 text-green-800',
            'rejected': 'bg-red-100 text-red-800',
            'completed': 'bg-emerald-100 text-emerald-800',
            'cancelled': 'bg-orange-100 text-orange-800',
            'timeout': 'bg-yellow-100 text-yellow-800',
            'pending': 'bg-blue-100 text-blue-800',
        };

        return (
            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    const handleClick = () => {
        if (!read) {
            onMarkAsRead(notification.id);
        }
        if (onClick) {
            onClick(notification);
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className={`relative p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer group ${!read ? 'bg-blue-50/30' : ''}`}
            onClick={handleClick}
        >
            {/* Unread indicator */}
            {!read && (
                <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
            )}

            <div className="flex items-start space-x-3 ml-4">
                {/* Notification Icon */}
                {getNotificationIcon()}

                {/* Content */}
                <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                                {title || `Order #${orderId}`}
                                {isScheduled && (
                                    <span className="ml-2 inline-flex px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                        Scheduled
                                    </span>
                                )}
                            </h4>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                                {body || `Order ${orderType?.replace('order_', '')} ${customerName || talentName ? `by ${customerName || talentName}` : ''}`}
                            </p>
                            
                            {/* Additional details */}
                            <div className="flex items-center space-x-3 mt-2">
                                {serviceName && (
                                    <span className="text-xs text-gray-500">
                                        Service: {serviceName}
                                    </span>
                                )}
                                {amount && (
                                    <span className="text-xs font-medium text-green-600">
                                        ${amount}
                                    </span>
                                )}
                                {getStatusBadge()}
                            </div>
                        </div>

                        {/* Timestamp and actions */}
                        <div className="flex flex-col items-end space-y-2 ml-4">
                            <span className="text-xs text-gray-500">
                                {getRelativeTime(timestamp)}
                            </span>
                            
                            {/* Dismiss button */}
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onDismiss(notification.id);
                                }}
                                className="opacity-0 group-hover:opacity-100 p-1 rounded-full hover:bg-gray-200 transition-all"
                                aria-label="Dismiss notification"
                            >
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

export default OrderNotificationItem;
