import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '../../context/NotificationContext';
import { useNavigate } from 'react-router-dom';
import OrderNotificationList from './OrderNotificationList';
import { InlineLoader } from '../ui/LoadingIndicator';
import { useAuth } from '../../contexts/AuthContext';
import { formatRelativeTime } from '../../utils/i18nUtils';
import GiftNotificationCard from './GiftNotificationCard';
import { FaCheckCircle, FaTrash } from 'react-icons/fa';

const NotificationDropdown = ({ isOpen, onClose, onToggle }) => {
    const { isAuthenticated } = useAuth();
    const {
        notifications,
        unreadCount,
        markAsRead,
        removeNotification,
        deleteNotification,
        getNotificationIcon,
        getNotificationRoute
    } = useNotifications();
    const navigate = useNavigate();

    const [activeTab, setActiveTab] = useState('all'); // all, orders, messages
    const [loading, setLoading] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Filter notifications by tab
    const getFilteredNotifications = () => {
        switch (activeTab) {
            case 'orders':
                return notifications.filter(n => {
                    const type = n.data?.type || n.data?.notification_type || '';
                    return type.includes('order') || n.title?.toLowerCase().includes('order');
                });
            case 'messages':
                return notifications.filter(n => {
                    const type = n.data?.type || n.data?.notification_type || n.category || '';
                    return type === 'chat_message' || n.category === 'chat';
                });
            default:
                return notifications;
        }
    };

    const filteredNotifications = getFilteredNotifications();
    const orderNotifications = notifications.filter(n => {
        const type = n.data?.type || n.data?.notification_type || '';
        return type.includes('order') || n.title?.toLowerCase().includes('order');
    });
    const chatNotifications = notifications.filter(n => {
        const type = n.data?.type || n.data?.notification_type || n.category || '';
        return type === 'chat_message' || n.category === 'chat';
    });

    const orderUnreadCount = orderNotifications.filter(n => !n.read).length;
    const chatUnreadCount = chatNotifications.filter(n => !n.read).length;

    const handleNotificationClick = (notification) => {
        if (!notification.read) {
            markAsRead(notification.id);
        }
        const route = getNotificationRoute(notification);
        if (route) {
            navigate(route);
        }
        onClose();
    };

    const handleMarkAllAsRead = () => {
        filteredNotifications.forEach(notification => {
            if (!notification.read) {
                markAsRead(notification.id);
            }
        });
    };

    const handleClearAll = () => {
        filteredNotifications.forEach(notification => {
            removeNotification(notification.id);
        });
    };

    if (!isAuthenticated) return null;
    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                ref={dropdownRef}
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full right-0 mt-2 w-96 bg-white/80 dark:bg-gray-900/90 backdrop-blur-xl rounded-3xl shadow-2xl shadow-indigo-50 dark:shadow-indigo-900 border border-indigo-100/40 dark:border-indigo-900/60 z-50 overflow-hidden"
                style={{ maxHeight: '80vh' }}
            >
                {/* Glassy/gradient animated overlay */}
                <div className="absolute inset-0 pointer-events-none z-0">
                  <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-200/20 via-blue-200/10 to-purple-200/10 dark:from-indigo-900/20 dark:via-blue-950/10 dark:to-purple-900/10"></div>
                </div>
                {/* Header */}
                <div className="relative z-10 bg-gradient-to-r from-indigo-500 to-blue-600 dark:from-indigo-900 dark:to-blue-950 px-6 py-4 border-b border-indigo-100/40 dark:border-indigo-900/60">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-bold text-white dark:text-indigo-100 flex items-center drop-shadow">
                            <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            Notifications
                            {unreadCount > 0 && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-pink-400 to-indigo-500 text-white animate-pulse shadow-lg shadow-pink-400/20 border border-white/20">
                                    {unreadCount}
                                </span>
                            )}
                        </h3>
                        
                        <button
                            onClick={onClose}
                            className="p-1 rounded-full bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 transition-colors shadow-md border border-red-200 dark:border-red-800"
                        >
                            <svg className="w-5 h-5 bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-500 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Tabs */}
                    <div className="flex space-x-1 mt-3 bg-white dark:bg-gray-800 rounded-lg p-1 shadow border border-indigo-100/40 dark:border-indigo-900/60">
                        <button
                            aria-label="Show all notifications"
                            onClick={() => setActiveTab('all')}
                            className={`flex-1 px-3 py-2 text-sm font-bold rounded-md transition-colors focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none ${
                                activeTab === 'all'
                                    ? 'bg-gradient-to-r from-indigo-100 dark:from-indigo-800 to-blue-100 dark:to-blue-800 hover:bg-indigo-200 dark:hover:bg-indigo-700 text-indigo-700 dark:text-indigo-100 shadow'
                                    : 'text-gray-600 dark:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                            }`}
                        >
                            All ({notifications.length})
                        </button>
                        <button
                            aria-label="Show order notifications"
                            onClick={() => setActiveTab('orders')}
                            className={`flex-1 px-3 py-2 text-sm font-bold rounded-md transition-colors relative focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none ${
                                activeTab === 'orders'
                                    ? 'bg-gradient-to-r from-indigo-100 dark:from-indigo-800 to-blue-100 dark:to-blue-800 hover:bg-indigo-200 dark:hover:bg-indigo-700 text-indigo-700 dark:text-indigo-100 shadow'
                                    : 'text-gray-600 dark:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                            }`}
                        >
                            Orders ({orderNotifications.length})
                            {orderUnreadCount > 0 && (
                                <span className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-indigo-500 text-white text-xs rounded-full flex items-center justify-center dark:bg-pink-700 animate-pulse shadow-lg shadow-pink-400/20 border border-white/20">
                                    {orderUnreadCount}
                                </span>
                            )}
                        </button>
                        <button
                            aria-label="Show message notifications"
                            onClick={() => setActiveTab('messages')}
                            className={`flex-1 px-3 py-2 text-sm font-bold rounded-md transition-colors relative focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none ${
                                activeTab === 'messages'
                                    ? 'bg-gradient-to-r from-indigo-100 dark:from-indigo-800 to-blue-100 dark:to-blue-800 hover:bg-indigo-200 dark:hover:bg-indigo-700 text-indigo-700 dark:text-indigo-100 shadow'
                                    : 'text-gray-600 dark:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                            }`}
                        >
                            Messages ({chatNotifications.length})
                            {chatUnreadCount > 0 && (
                                <span className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-indigo-500 text-white text-xs rounded-full flex items-center justify-center dark:bg-pink-700 animate-pulse shadow-lg shadow-pink-400/20 border border-white/20">
                                    {chatUnreadCount}
                                </span>
                            )}
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="max-h-96 overflow-hidden dark:bg-gray-900/80">
                    {activeTab === 'orders' ? (
                        <OrderNotificationList
                            notifications={orderNotifications}
                            loading={loading}
                            onMarkAsRead={markAsRead}
                            onDismiss={removeNotification}
                            onClearAll={handleClearAll}
                            onNotificationClick={handleNotificationClick}
                            maxHeight="384px"
                        />
                    ) : (
                        <div className="overflow-y-auto" style={{ maxHeight: '384px' }}>
                            {filteredNotifications.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-12 px-4">
                                    <div className="w-24 h-24 mb-4">
                                        {/* Friendly SVG illustration for empty state */}
                                        <svg viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="48" cy="48" r="48" fill="#EEF2FF" className="dark:fill-gray-800"/>
                                            <rect x="28" y="36" width="40" height="28" rx="6" fill="#C7D2FE" className="dark:fill-indigo-900"/>
                                            <rect x="36" y="44" width="24" height="12" rx="3" fill="#6366F1" className="dark:fill-indigo-700"/>
                                            <circle cx="48" cy="50" r="2" fill="#A5B4FC" className="dark:fill-indigo-400"/>
                                            <rect x="44" y="60" width="8" height="4" rx="2" fill="#A5B4FC" className="dark:fill-indigo-400"/>
                                        </svg>
                                    </div>
                                    <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 drop-shadow">No notifications</h4>
                                    <p className="text-gray-500 dark:text-gray-400 text-center">
                                        You're all caught up! No new notifications.
                                    </p>
                                </div>
                            ) : (
                                <div>
                                    {/* Action bar for non-order tabs */}
                                    <div className="sticky top-0 bg-white/90 dark:bg-gray-900/90 border-b border-indigo-100/40 dark:border-indigo-900/60 px-4 py-3 z-10 shadow-sm">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-indigo-700 dark:text-indigo-200 font-bold">
                                                {filteredNotifications.length} notifications
                                            </span>
                                            <div className="flex items-center space-x-3">
                                                {filteredNotifications.some(n => !n.read) && (
                                                    <button
                                                        aria-label="Mark all notifications as read"
                                                        onClick={handleMarkAllAsRead}
                                                        className="text-sm text-indigo-600 dark:text-indigo-300 bg-gradient-to-r from-indigo-100 to-blue-100 dark:from-indigo-800 dark:to-blue-800 hover:text-indigo-800 dark:hover:text-indigo-100 hover:bg-indigo-200 dark:hover:bg-indigo-700 font-bold px-3 py-1 rounded-lg shadow focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none transition-colors"
                                                    >
                                                        Mark all read
                                                    </button>
                                                )}
                                                <button
                                                    aria-label="Clear all notifications"
                                                    onClick={handleClearAll}
                                                    className="text-sm text-gray-500 dark:text-gray-300 bg-gradient-to-r from-white to-indigo-50 dark:from-gray-900 dark:to-indigo-900 hover:text-gray-700 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 font-bold px-3 py-1 rounded-lg shadow focus-visible:ring-2 focus-visible:ring-indigo-400 focus:outline-none transition-colors"
                                                >
                                                    Clear all
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Generic notification list */}
                                    <div className="divide-y divide-indigo-100 dark:divide-indigo-900" role="listbox" aria-label="Notification list">
                                        {filteredNotifications.map((notification) => {
                                            if (notification.data?.type === 'gift') {
                                                return (
                                                    <motion.div
                                                        key={notification.id}
                                                        initial={{ opacity: 0, y: 10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        exit={{ opacity: 0, y: 10 }}
                                                        transition={{ duration: 0.2 }}
                                                        className="flex justify-center py-4 bg-gradient-to-br from-pink-50 to-purple-50 dark:from-indigo-950 dark:to-purple-950 hover:scale-[1.01] hover:shadow-lg transition-transform"
                                                    >
                                                        {/*<GiftNotificationCard
                                                            sender={notification.data.senderName || notification.data.sender_name || 'Someone'}
                                                            giftName={notification.data.giftName || notification.data.gift_name || 'Gift'}
                                                            giftImage={notification.data.icon_path || '/gifts/default-gift.png'}
                                                            quantity={notification.data.quantity || 1}
                                                            onReceive={() => {}}
                                                            onReply={() => {}}
                                                        />} */}
                                                    </motion.div>
                                                );
                                            }
                                            return (
                                                <motion.div
                                                    key={notification.id}
                                                    initial={{ opacity: 0, y: 10 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    exit={{ opacity: 0, y: 10 }}
                                                    transition={{ duration: 0.2 }}
                                                    className={`p-4 hover:bg-indigo-50 dark:hover:bg-indigo-900 cursor-pointer transition-colors rounded-xl shadow-md border border-indigo-100/40 dark:border-indigo-900/60 mb-2 ${
                                                        !notification.read
                                                            ? 'bg-gradient-to-r from-blue-50/60 via-indigo-100/40 to-purple-50/30 dark:from-indigo-900/40 dark:via-blue-950/30 dark:to-purple-900/20'
                                                            : 'bg-white/80 dark:bg-gray-900/80'
                                                    }`}
                                                    onClick={() => handleNotificationClick(notification)}
                                                >
                                                    <div className="flex items-start space-x-3">
                                                        <div className="flex-shrink-0">
                                                            <div className="w-8 h-8 bg-gradient-to-br from-indigo-200 to-blue-200 dark:from-indigo-900 dark:to-blue-900 rounded-full flex items-center justify-center text-lg shadow">
                                                                {getNotificationIcon(notification.data?.type)}
                                                            </div>
                                                        </div>
                                                        <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-bold text-gray-900 dark:text-gray-100 truncate">
                                                                {notification.title}
                                                            </p>
                                                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                                                {notification.body}
                                                            </p>
                                                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                                {formatRelativeTime(notification.timestamp)}
                                                            </p>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <button
                                                                aria-label={notification.read ? 'Remove notification' : 'Mark notification as read'}
                                                                className="p-1 bg-transparent rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-400 transition-colors text-gray-400 dark:text-gray-500 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900 shadow"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    if (notification.read) {
                                                                        removeNotification(notification.id);
                                                                    } else {
                                                                        markAsRead(notification.id);
                                                                    }
                                                                }}
                                                            >
                                                                <FaCheckCircle size={18} className={notification.read ? 'text-gray-300 dark:text-gray-700' : 'text-green-500 dark:text-green-400'} />
                                                            </button>
                                                            <button
                                                                aria-label="Delete notification"
                                                                className="p-1 bg-transparent rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-red-400 transition-colors text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 ml-1 shadow"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    deleteNotification(notification.id);
                                                                }}
                                                            >
                                                                <FaTrash size={18} />
                                                            </button>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </motion.div>
        </AnimatePresence>
    );
};

export default NotificationDropdown;
