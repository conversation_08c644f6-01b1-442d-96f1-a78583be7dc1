import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import OrderToast from './OrderToast';
import { useNotifications } from '../../context/NotificationContext';

const ToastContainer = ({ onAcceptOrder, onRejectOrder }) => {
    const { notifications } = useNotifications();
    const [activeToasts, setActiveToasts] = useState([]);

    // Filter for toast-worthy notifications (urgent order notifications)
    useEffect(() => {
        const urgent = notifications.filter(notification => {
            const type = notification.data?.type || notification.data?.notification_type || '';
            // Show toasts for new orders and important status updates
            return (
                type === 'order_placed' ||
                type === 'new_order' ||
                type === 'prebooked_order_request' ||
                type === 'order_accepted' ||
                type === 'order_rejected' ||
                type === 'order_completed' ||
                type === 'order_cancelled'
            ) && !notification.read;
        });

        // Ensure only one toast per order ID
        const seen = new Set();
        const unique = [];
        urgent.forEach(n => {
            const raw = n.data?.order_id || n.data?.orderId;
            const oid = raw != null ? String(raw) : null;
            if (oid) {
                if (seen.has(oid)) return;
                seen.add(oid);
            }
            unique.push(n);
        });

        setActiveToasts(unique);
    }, [notifications]);

    const handleDismissToast = (toastId) => {
        setActiveToasts(prevToasts => 
            prevToasts.filter(toast => toast.id !== toastId)
        );
    };

    const handleAcceptOrder = async (orderId, orderType) => {
        if (onAcceptOrder) {
            await onAcceptOrder(orderId, orderType);
        }
        // Remove the toast after accepting
        setActiveToasts(prevToasts =>
            prevToasts.filter(toast => {
                const raw = toast.data?.order_id || toast.data?.orderId;
                const toastOrderId = raw != null ? String(raw) : null;
                return toastOrderId !== String(orderId);
            })
        );
    };

    const handleRejectOrder = async (orderId, orderType) => {
        if (onRejectOrder) {
            await onRejectOrder(orderId, orderType);
        }
        // Remove the toast after rejecting
        setActiveToasts(prevToasts =>
            prevToasts.filter(toast => {
                const raw = toast.data?.order_id || toast.data?.orderId;
                const toastOrderId = raw != null ? String(raw) : null;
                return toastOrderId !== String(orderId);
            })
        );
    };

    return (
        <div className="fixed top-4 right-4 z-[9999] space-y-4 pointer-events-none">
            <AnimatePresence>
                {activeToasts.map((notification, index) => {
                    const orderType = notification.data?.type || notification.data?.notification_type || '';
                    const isNewOrder =
                        orderType === 'order_placed' ||
                        orderType === 'new_order' ||
                        orderType === 'prebooked_order_request';
                    
                    const orderId = notification.data?.order_id || notification.data?.orderId;
                    return (
                        <div
                            key={notification.id}
                            className="pointer-events-auto"
                            style={{
                                zIndex: 1000 - index // Stack toasts properly
                            }}
                        >
                            <OrderToast
                                notification={notification}
                                onAccept={isNewOrder ? () => handleAcceptOrder(orderId, orderType) : undefined}
                                onReject={isNewOrder ? () => handleRejectOrder(orderId, orderType) : undefined}
                                onDismiss={() => handleDismissToast(notification.id)}
                                autoHide={!isNewOrder} // Don't auto-hide new orders
                                duration={isNewOrder ? 60000 : 5000} // New orders have 60s timer
                            />
                        </div>
                    );
                })}
            </AnimatePresence>
        </div>
    );
};

export default ToastContainer;
