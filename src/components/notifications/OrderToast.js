import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getCdnUrl } from '../../utils/cdnUtils';

const OrderToast = ({
    notification,
    onAccept,
    onReject,
    onDismiss,
    autoHide = true,
    duration = 30000 // 30 seconds for order notifications
}) => {
    const { data, title, body } = notification;
    const orderType = data?.type || data?.notification_type || 'order';
    const expiresAt = data?.expires_at;
    const isNewOrder =
        orderType === 'order_placed' ||
        orderType === 'new_order';
    const initialDuration = useMemo(() => {
        if (isNewOrder) {
            if (expiresAt) {
                const remaining = Math.max(
                    0,
                    Math.floor((new Date(expiresAt).getTime() - Date.now()) / 1000)
                );
                return remaining || 60;
            }
            return 60;
        }
        return Math.floor(duration / 1000);
    }, [isNewOrder, expiresAt, duration]);
    const [timeLeft, setTimeLeft] = useState(initialDuration);
    const [isVisible, setIsVisible] = useState(true);
    const [isResponding, setIsResponding] = useState(false);
    const orderId = data?.order_id || data?.orderId;
    // Robust customer name extraction
    const customerName = data?.customer_name || data?.customerName || data?.customer?.name || data?.customer?.profile?.name;
    const serviceName = data?.service_name || data?.serviceName || data?.user_service?.title;
    const amount = data?.amount || data?.pricing || data?.user_service?.pricing;
    const serviceStyle = data?.service_style || data?.user_service?.service_style;
    const pricingType = data?.pricing_option_type || data?.user_service?.pricing_option_type;
    const iconPath = data?.icon || data?.service_icon || data?.user_service?.icon;
    const isScheduled = data?.is_scheduled || data?.isScheduled;
    useEffect(() => {
        if (!expiresAt || !isNewOrder) return;
        const timer = setInterval(() => {
            const now = new Date().getTime();
            const expiry = new Date(expiresAt).getTime();
            const remaining = Math.max(0, Math.floor((expiry - now) / 1000));
            setTimeLeft(remaining);
            if (remaining <= 0) {
                setIsVisible(false);
                setTimeout(() => onDismiss(), 300);
            }
        }, 1000);
        return () => clearInterval(timer);
    }, [expiresAt, isNewOrder, onDismiss]);
    useEffect(() => {
        if (!isNewOrder || expiresAt) return;
        const timer = setInterval(() => {
            setTimeLeft(prev => {
                if (prev <= 1) {
                    clearInterval(timer);
                    setIsVisible(false);
                    setTimeout(() => onDismiss(), 300);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
        return () => clearInterval(timer);
    }, [isNewOrder, expiresAt, onDismiss]);
    useEffect(() => {
        if (
            autoHide &&
            !isNewOrder
        ) {
            const timer = setTimeout(() => {
                setIsVisible(false);
                setTimeout(() => onDismiss(), 300);
            }, duration);
            return () => clearTimeout(timer);
        }
    }, [autoHide, duration, isNewOrder, onDismiss]);
    const handleAccept = async () => {
        setIsResponding(true);
        try {
            await onAccept(orderId);
            setIsVisible(false);
            setTimeout(() => onDismiss(), 300);
        } catch (error) {
            setIsResponding(false);
        }
    };
    const handleReject = async () => {
        setIsResponding(true);
        try {
            await onReject(orderId);
            setIsVisible(false);
            setTimeout(() => onDismiss(), 300);
        } catch (error) {
            setIsResponding(false);
        }
    };
    const handleDismiss = () => {
        setIsVisible(false);
        setTimeout(() => onDismiss(), 300);
    };
    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    // Get customer profile picture (if available)
    const customerProfilePicture = data?.customer?.profile_picture || data?.profile_picture || data?.customer_profile_picture;
    if (!isVisible) return null;
    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0, y: -40, scale: 0.96 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -40, scale: 0.96 }}
                transition={{ type: 'spring', stiffness: 260, damping: 30 }}
                className="fixed top-6 right-6 z-50 max-w-md w-full"
            >
                <div className="relative rounded-3xl shadow-2xl border border-white/30 bg-white/90 backdrop-blur-2xl overflow-hidden" style={{background: 'linear-gradient(120deg, rgba(255,255,255,0.92) 0%, rgba(139,92,246,0.14) 100%)'}}>
                    {/* FIRST SECTION: Customer info and order title */}
                        <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ type: 'spring', stiffness: 180, damping: 18 }}
                        className="flex flex-row items-center gap-5 px-6 pt-6 pb-3 border-b border-white/20 bg-white/70/80 rounded-t-3xl shadow-sm relative"
                    >
                        {/* Customer profile picture with colored ring */}
                        <div className="flex-shrink-0 w-16 h-16 rounded-full bg-gradient-to-br from-indigo-300 via-purple-300 to-emerald-200 p-1 shadow-lg relative">
                            <div className="w-full h-full rounded-full bg-white/90 flex items-center justify-center overflow-hidden">
                                {customerProfilePicture ? (
                                    <img
                                        src={getCdnUrl(customerProfilePicture)}
                                        alt="Customer Profile"
                                        className="w-14 h-14 rounded-full object-cover"
                                    />
                                ) : (
                                    <svg className="w-10 h-10 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                )}
                    </div>
                            {/* New badge */}
                            {isNewOrder && (
                                <span className="absolute -top-2 -right-2 px-2 py-0.5 text-xs font-bold bg-gradient-to-r from-emerald-400 to-indigo-400 text-white rounded-full shadow">New</span>
                            )}
                        </div>
                        {/* Divider */}
                        <div className="h-12 w-px bg-gradient-to-b from-indigo-200 via-purple-200 to-emerald-100 opacity-60 mx-2 rounded-full" />
                        {/* Title and customer name */}
                        <div className="flex-1 min-w-0">
                            <h3 className="text-xl font-extrabold bg-gradient-to-r from-indigo-600 via-purple-500 to-emerald-500 bg-clip-text text-transparent mb-1 leading-tight">
                                {title || `Order #${orderId}`}
                            </h3>
                            <p className="text-base text-gray-700/90 font-medium mb-0.5">
                                Order from {customerName || 'a customer'}
                            </p>
                        </div>
                        <button
                            onClick={handleDismiss}
                            className="ml-2 p-2 bg-transparent rounded-full hover:bg-indigo-100/60 transition-colors self-start"
                        >
                            <svg className="w-5 h-5 bg-transparent hover:bg-indigo-100/60 rounded-full text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </motion.div>
                    {/* SECOND SECTION: Service icon and details */}
                    <div className="flex flex-row items-start gap-4 px-6 py-4">
                        {/* Service icon */}
                        <div className="flex-shrink-0 w-14 h-14 rounded-xl bg-gradient-to-br from-indigo-100 via-purple-100 to-emerald-50 p-1 shadow flex items-center justify-center">
                            <div className="w-full h-full rounded-xl bg-white/80 flex items-center justify-center overflow-hidden">
                                {iconPath ? (
                                    <img
                                        src={getCdnUrl(iconPath)}
                                        alt="Service Icon"
                                        className="w-10 h-10 rounded object-cover"
                                    />
                                ) : (
                                    <svg className="w-8 h-8 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                )}
                            </div>
                        </div>
                        {/* Service details */}
                        <div className="flex-1 min-w-0 space-y-1">
                            {serviceName && (
                                <div className="flex items-center gap-2 text-sm text-gray-700/80">
                                    <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 21l3-1.5L15 21l-.75-4M4 10V7a4 4 0 014-4h8a4 4 0 014 4v3M4 10v10a2 2 0 002 2h12a2 2 0 002-2V10M4 10h16" /></svg>
                                    <span>{serviceName}</span>
                                </div>
                                    )}
                                    {(serviceStyle || pricingType) && (
                                <div className="flex items-center gap-2 text-sm text-gray-700/80">
                                    <svg className="w-4 h-4 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 17l4 4 4-4m-4-5v9" /></svg>
                                    <span>{serviceStyle}{serviceStyle && pricingType ? ', ' : ''}{pricingType}</span>
                                </div>
                                    )}
                                    {amount && (
                                <div className="flex items-center gap-2 text-sm text-gray-700/80 font-semibold">
                                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13v2a1 1 0 01-2 0V5a1 1 0 112 0zm-1 4a1 1 0 00-1 1v2a1 1 0 002 0v-2a1 1 0 00-1-1zm-3 2a1 1 0 100 2 1 1 0 000-2zm8 0a1 1 0 100 2 1 1 0 000-2z" /></svg>
                                    <span className="flex items-center gap-1">
                                        <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 inline-block align-middle" />
                                        {amount}
                                    </span>
                                </div>
                                    )}
                                    {isNewOrder && timeLeft > 0 && (
                                <div className="flex items-center gap-2 mt-2 px-3 py-2 bg-gradient-to-r from-indigo-100/60 to-purple-100/40 rounded-xl">
                                    <svg className="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                    <span className="text-xs font-bold text-indigo-700">Time left: {formatTime(timeLeft)}</span>
                                </div>
                            )}
                        </div>
                    </div>
                    {/* Sticky footer for actions */}
                    {isNewOrder && onAccept && onReject && (
                        <div className="sticky bottom-0 left-0 w-full bg-gradient-to-r from-white/80 via-indigo-50/80 to-purple-50/80 px-6 py-4 flex gap-3 rounded-b-3xl border-t border-white/30 backdrop-blur-xl">
                            <button
                                onClick={handleAccept}
                                disabled={isResponding}
                                className="flex-1 py-3 rounded-xl font-bold text-white bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-600 shadow-lg hover:from-emerald-500 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-300 transition-all disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                            >
                                {isResponding ? (
                                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Accept
                                    </>
                                )}
                            </button>
                            <button
                                onClick={handleReject}
                                disabled={isResponding}
                                className="flex-1 py-3 rounded-xl font-bold text-white bg-gradient-to-r from-rose-400 via-rose-500 to-rose-600 shadow-lg hover:from-rose-500 hover:to-rose-700 focus:outline-none focus:ring-2 focus:ring-rose-300 transition-all disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                            >
                                {isResponding ? (
                                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                        Reject
                                    </>
                                )}
                            </button>
                        </div>
                    )}
                </div>
            </motion.div>
        </AnimatePresence>
    );
};

export default OrderToast;
