import React, { useState } from 'react';

/**
 * GiftNotificationCard
 * Props:
 * - sender: string (name of the user who sent the gift)
 * - giftName: string (name of the gift, e.g., 'Teddy Bear')
 * - giftImage: string (URL or path to the gift image)
 * - quantity: number (how many gifts)
 * - onReceive: function (handler for 'Receive Gift' button)
 * - onReply: function (handler for 'Reply with Thanks' button)
 * - onSendReply: function (handler when a suggested reply is selected)
 */
const GiftNotificationCard = ({
  sender,
  giftName,
  giftImage,
  quantity,
  onReceive,
  onReply,
  onSendReply,
}) => {
  const [showReplies, setShowReplies] = useState(false);
  const [replies, setReplies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleReplyClick = async () => {
    setShowReplies(true);
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/chat/suggested-replies');
      if (!res.ok) throw new Error('Failed to fetch replies');
      const data = await res.json();
      setReplies(data);
    } catch (err) {
      setError('Could not load replies');
    } finally {
      setLoading(false);
    }
  };

  const handleSendReply = (reply) => {
    setShowReplies(false);
    if (onSendReply) onSendReply(reply);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Fullscreen pastel background and particles */}
      <div className="fixed inset-0 -z-10 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 opacity-80"></div>
        {/* Floating confetti/particles */}
        <span className="absolute left-4 top-4 w-3 h-3 bg-pink-200 rounded-full opacity-70 animate-float-particle" style={{animationDuration:'6s'}}></span>
        <span className="absolute right-6 top-8 w-2 h-2 bg-purple-200 rounded-full opacity-60 animate-float-particle" style={{animationDuration:'8s'}}></span>
        <span className="absolute left-10 bottom-6 w-2.5 h-2.5 bg-yellow-200 rounded-full opacity-60 animate-float-particle" style={{animationDuration:'7s'}}></span>
        <span className="absolute right-2 bottom-4 w-3 h-3 bg-blue-200 rounded-full opacity-50 animate-float-particle" style={{animationDuration:'9s'}}></span>
      </div>
      {/* Card with pastel gradient border and soft shadow */}
      <div className="relative max-w-sm w-full animate-fade-in overflow-visible flex flex-col items-center justify-center pt-6 pb-6 px-2">
        {/* Extra top padding so ribbon is never clipped */}
        {/* Ribbon Banner */}
        <div className="absolute -top-8 left-0 w-full flex justify-center z-20">
          <div className="w-full py-4 rounded-t-3xl font-extrabold text-2xl text-white shadow-xl bg-gradient-to-r from-pink-200 via-purple-200 to-blue-200 border-b-2 border-pink-300 text-center"
             style={{letterSpacing: '0.02em', fontFamily: 'Poppins, sans-serif'}}>
            <span className="bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 bg-clip-text text-transparent drop-shadow-lg">New Gift</span>
          </div>
        </div>
        {/* Gift Image with glow */}
        <div className="relative flex justify-center items-center mt-12 mb-4">
          <div className="absolute inset-0 flex justify-center items-center">
            <div className="w-36 h-36 rounded-full bg-gradient-to-br from-pink-100 via-purple-100 to-blue-100 blur-2xl opacity-70"></div>
          </div>
          <img
            src={'gift.png'}
            alt={giftName}
            className="w-48 h-48 object-contain z-10 drop-shadow-2xl animate-gift-pop"
            style={{ filter: 'drop-shadow(0 0 24px #e0b3ff)' }}
          />
        </div>
        {/* Sender and Gift Info */}
        <div className="text-center mb-4">
          <div className="font-medium text-gray-700 text-base">
            <span className="font-bold text-indigo-700">{sender}</span> gifted you a
          </div>
          <div className="font-extrabold text-xl text-purple-700 mt-1" style={{fontFamily:'Poppins, sans-serif'}}>
            Gift 🎁 <span className="text-black font-bold">x {quantity}</span>
          </div>
        </div>
        {/* Action Buttons */}
        <button
          className="w-full bg-gradient-to-r from-pink-200 via-purple-200 to-blue-200 font-extrabold py-3 rounded-full mb-2 shadow-lg hover:from-pink-300 hover:via-purple-300 hover:to-blue-300 transition text-xl tracking-wide focus:outline-none focus:ring-2 focus:ring-pink-200"
          onClick={onReceive}
        >
          <div className="bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
            Receive Gift
          </div>
        </button>
      </div>
    </div>
  );
};

export default GiftNotificationCard; 