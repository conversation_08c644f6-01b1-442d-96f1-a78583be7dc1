import React from 'react';
import { motion } from 'framer-motion';

const GiftItem = ({ gift, onSell, onGift }) => {
  const {
    id,
    name,
    description,
    icon_path,
    acquired_at,
    can_sell,
    sell_back_price,
    quantity,
    is_giftable,
    gifted_by
  } = gift;

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <motion.div
      whileHover={{ y: -5 }}
      className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
    >
      <div className="p-1 bg-gradient-to-r from-indigo-500 to-purple-500" />
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-gray-900">{name}</h3>
          {quantity > 1 && (
            <span className="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded">
              x{quantity}
            </span>
          )}
        </div>
        
        <div className="flex items-center justify-center mb-4">
          <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
            {icon_path ? (
              <img 
                src={icon_path.startsWith('http') ? icon_path : `${process.env.REACT_APP_API_URL}/${icon_path}`} 
                alt={name} 
                className="w-full h-full object-contain" 
              />
            ) : (
              <svg className="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="1" 
                  d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" 
                />
              </svg>
            )}
          </div>
        </div>
        
        <p className="text-sm text-gray-500 mb-2">{description}</p>
        
        <div className="text-xs text-gray-500 mb-4">
          <p>Acquired: {formatDate(acquired_at)}</p>
          {gifted_by && (
            <p className="mt-1">
              From: {gifted_by.nickname || `User #${gifted_by.id}`}
            </p>
          )}
          {can_sell && (
            <p className="mt-1">Sell value: {sell_back_price} credits</p>
          )}
        </div>
        
        <div className="flex space-x-2">
          {can_sell && (
            <button
              onClick={onSell}
              className="flex-1 px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded hover:bg-red-100 transition-colors"
            >
              Sell
            </button>
          )}
          
          {is_giftable && (
            <button
              onClick={onGift}
              className="flex-1 px-3 py-1.5 text-sm bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 transition-colors"
            >
              Gift
            </button>
          )}
          
          {!can_sell && !is_giftable && (
            <button
              disabled
              className="flex-1 px-3 py-1.5 text-sm bg-gray-100 text-gray-400 rounded cursor-not-allowed"
            >
              Non-tradable
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default GiftItem; 