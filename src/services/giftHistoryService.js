import { createApi } from './api';
import giftAPI from './giftService';

const API_URL = process.env.REACT_APP_API_URL || '';

// Cache settings
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const historyCache = {
  giftHistory: null,
  giftAnalytics: null,
  popularGifts: null,
  timestamps: {
    giftHistory: 0,
    giftAnalytics: 0,
    popularGifts: 0
  }
};

// Create an axios instance with default config
const api = createApi();

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      console.warn('[giftHistoryService] Unauthorized - token may be invalid');
    }
    return Promise.reject(error);
  }
);

// Helper function to check if cache is valid
const isCacheValid = (cacheKey) => {
  const timestamp = historyCache.timestamps[cacheKey];
  if (!timestamp) return false;
  return Date.now() - timestamp < CACHE_DURATION;
};

// Generate mock gift history if needed for development
const generateMockGiftHistory = (count = 20) => {
  const mockUsers = [
    { id: 101, name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?u=a1' },
    { id: 102, name: 'Bob Smith', avatar: 'https://i.pravatar.cc/150?u=a2' },
    { id: 103, name: 'Carol White', avatar: 'https://i.pravatar.cc/150?u=a3' },
    { id: 104, name: 'David Brown', avatar: 'https://i.pravatar.cc/150?u=a4' },
    { id: 105, name: 'Eve Martinez', avatar: 'https://i.pravatar.cc/150?u=a5' }
  ];
  
  const mockGifts = [
    { id: 1, name: 'Rose', image_url: 'https://via.placeholder.com/150?text=Rose', price: 100 },
    { id: 2, name: 'Heart', image_url: 'https://via.placeholder.com/150?text=Heart', price: 250 },
    { id: 3, name: 'Star', image_url: 'https://via.placeholder.com/150?text=Star', price: 150 },
    { id: 4, name: 'Diamond', image_url: 'https://via.placeholder.com/150?text=Diamond', price: 500 },
    { id: 5, name: 'Cake', image_url: 'https://via.placeholder.com/150?text=Cake', price: 200 }
  ];
  
  const currentUserId = parseInt(localStorage.getItem('user_id')) || 100;
  const now = new Date();
  
  const history = [];
  for (let i = 0; i < count; i++) {
    const isSent = Math.random() > 0.5;
    const randomUser = mockUsers[Math.floor(Math.random() * mockUsers.length)];
    const randomGift = mockGifts[Math.floor(Math.random() * mockGifts.length)];
    const dateDaysAgo = new Date(now);
    dateDaysAgo.setDate(now.getDate() - Math.floor(Math.random() * 30));
    
    history.push({
      id: `gift_${Date.now()}_${i}`,
      gift_id: randomGift.id,
      gift_name: randomGift.name,
      gift_image_url: randomGift.image_url,
      sender_id: isSent ? currentUserId : randomUser.id,
      sender_name: isSent ? 'You' : randomUser.name,
      sender_avatar: isSent ? null : randomUser.avatar,
      recipient_id: isSent ? randomUser.id : currentUserId,
      recipient_name: isSent ? randomUser.name : 'You',
      recipient_avatar: isSent ? randomUser.avatar : null,
      sent_at: dateDaysAgo.toISOString(),
      message: Math.random() > 0.3 ? `Here's a ${randomGift.name} for you!` : '',
      is_sent: isSent,
      is_received: !isSent
    });
  }
  
  // Sort by date (newest first)
  history.sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));
  
  return history;
};

// Generate mock analytics data
const generateMockAnalytics = () => {
  const mockHistory = historyCache.giftHistory || generateMockGiftHistory(50);
  
  // Get all sent and received gifts
  const sentGifts = mockHistory.filter(item => item.is_sent);
  const receivedGifts = mockHistory.filter(item => item.is_received);
  
  // Count by gift type
  const sentByType = {};
  const receivedByType = {};
  
  sentGifts.forEach(gift => {
    sentByType[gift.gift_name] = (sentByType[gift.gift_name] || 0) + 1;
  });
  
  receivedGifts.forEach(gift => {
    receivedByType[gift.gift_name] = (receivedByType[gift.gift_name] || 0) + 1;
  });
  
  // Get top 3 users you've gifted to
  const giftedToUsers = {};
  sentGifts.forEach(gift => {
    giftedToUsers[gift.recipient_id] = {
      count: (giftedToUsers[gift.recipient_id]?.count || 0) + 1,
      name: gift.recipient_name,
      avatar: gift.recipient_avatar
    };
  });
  
  const topGiftedTo = Object.entries(giftedToUsers)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);
  
  // Get top 3 users who've gifted you
  const giftedByUsers = {};
  receivedGifts.forEach(gift => {
    giftedByUsers[gift.sender_id] = {
      count: (giftedByUsers[gift.sender_id]?.count || 0) + 1,
      name: gift.sender_name,
      avatar: gift.sender_avatar
    };
  });
  
  const topGiftedBy = Object.entries(giftedByUsers)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);
  
  // Monthly trends (last 6 months)
  const now = new Date();
  const monthlyTrends = [];
  
  for (let i = 0; i < 6; i++) {
    const monthDate = new Date(now);
    monthDate.setMonth(now.getMonth() - i);
    const monthName = monthDate.toLocaleString('default', { month: 'short' });
    const year = monthDate.getFullYear();
    const month = monthDate.getMonth();
    
    const sentInMonth = sentGifts.filter(gift => {
      const giftDate = new Date(gift.sent_at);
      return giftDate.getMonth() === month && giftDate.getFullYear() === year;
    }).length;
    
    const receivedInMonth = receivedGifts.filter(gift => {
      const giftDate = new Date(gift.sent_at);
      return giftDate.getMonth() === month && giftDate.getFullYear() === year;
    }).length;
    
    monthlyTrends.unshift({
      month: monthName,
      year,
      sent: sentInMonth,
      received: receivedInMonth
    });
  }
  
  return {
    total_sent: sentGifts.length,
    total_received: receivedGifts.length,
    sent_by_type: sentByType,
    received_by_type: receivedByType,
    top_gifted_to: topGiftedTo,
    top_gifted_by: topGiftedBy,
    monthly_trends: monthlyTrends,
    most_gifted: Object.entries(sentByType)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3),
    most_received: Object.entries(receivedByType)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
  };
};

// Calculate gift recommendations based on user history
const calculateGiftRecommendations = async (history, preferences) => {
  // Get all gift items first
  let allGifts = [];
  try {
    const response = await giftAPI.getGiftItems();
    allGifts = response.data.gifts || [];
  } catch (error) {
    console.error('Error fetching gifts for recommendations:', error);
  }
  
  if (!history || !allGifts || !allGifts.length) return [];
  
  // Default weights if no preferences
  const weights = {
    popularityWeight: 0.4,
    personalHistoryWeight: 0.3,
    priceWeight: 0.2,
    categoryWeight: 0.1,
    ...preferences
  };
  
  // Gather data from history
  const receivedGifts = history.filter(item => item.is_received);
  const receivedCounts = {};
  receivedGifts.forEach(gift => {
    receivedCounts[gift.gift_id] = (receivedCounts[gift.gift_id] || 0) + 1;
  });
  
  // Get popular categories from received gifts
  const categoryCount = {};
  receivedGifts.forEach(gift => {
    const targetGift = allGifts.find(g => g.id === gift.gift_id);
    if (targetGift && targetGift.category) {
      categoryCount[targetGift.category] = (categoryCount[targetGift.category] || 0) + 1;
    }
  });
  
  const totalReceived = receivedGifts.length || 1;
  const popularCategories = Object.entries(categoryCount)
    .map(([category, count]) => ({ category, score: count / totalReceived }))
    .sort((a, b) => b.score - a.score);
  
  // Score each gift
  const scoredGifts = allGifts.map(gift => {
    // Popularity score (0-1) - how often this gift appears in all history
    const popularityScore = (receivedCounts[gift.id] || 0) / (totalReceived || 1);
    
    // Category score (0-1) - how much this gift's category matches preferred categories
    const categoryScore = popularCategories.findIndex(c => c.category === gift.category) > -1 
      ? popularCategories.find(c => c.category === gift.category).score
      : 0;
    
    // Price score (0-1) - normalized price score, with mid-range prices scoring higher
    // Assume 1000 is the max price for normalization
    const normalizedPrice = Math.min(gift.price / 1000, 1);
    const priceScore = 1 - Math.abs(0.5 - normalizedPrice); // Mid-range prices get higher scores
    
    // Calculate weighted score
    const score = 
      weights.popularityWeight * popularityScore +
      weights.categoryWeight * categoryScore +
      weights.priceWeight * priceScore;
    
    return {
      ...gift,
      recommendation_score: score
    };
  });
  
  // Return top recommendations, sorted by score
  return scoredGifts
    .sort((a, b) => b.recommendation_score - a.recommendation_score)
    .slice(0, 15);
};

const giftHistoryService = {
  // Get gift history (sent and received)
  getGiftHistory: async (forceRefresh = false, limit = 50, offset = 0) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('giftHistory') && historyCache.giftHistory) {
      return { data: { history: historyCache.giftHistory } };
    }

    try {
      const response = await api.get(`/gifts/history?limit=${limit}&offset=${offset}`);
      
      // Update cache
      historyCache.giftHistory = response.data.history || [];
      historyCache.timestamps.giftHistory = Date.now();
      
      return response;
    } catch (error) {
      console.error('Error fetching gift history:', error);
      
      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        const mockHistory = generateMockGiftHistory(50);
        historyCache.giftHistory = mockHistory;
        historyCache.timestamps.giftHistory = Date.now();
        
        return { data: { history: mockHistory } };
      }
      
      throw error;
    }
  },
  
  // Get gift analytics
  getGiftAnalytics: async (forceRefresh = false) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('giftAnalytics') && historyCache.giftAnalytics) {
      return { data: { analytics: historyCache.giftAnalytics } };
    }

    try {
      const response = await api.get('/gifts/analytics');
      
      // Update cache
      historyCache.giftAnalytics = response.data.analytics || {};
      historyCache.timestamps.giftAnalytics = Date.now();
      
      return response;
    } catch (error) {
      console.error('Error fetching gift analytics:', error);
      
      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        // First ensure we have history data
        if (!historyCache.giftHistory) {
          await giftHistoryService.getGiftHistory();
        }
        
        const mockAnalytics = generateMockAnalytics();
        historyCache.giftAnalytics = mockAnalytics;
        historyCache.timestamps.giftAnalytics = Date.now();
        
        return { data: { analytics: mockAnalytics } };
      }
      
      throw error;
    }
  },
  
  // Get popular gifts recommendations
  getPopularGifts: async (forceRefresh = false, limit = 10) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('popularGifts') && historyCache.popularGifts) {
      return { data: { gifts: historyCache.popularGifts } };
    }

    try {
      const response = await api.get(`/gifts/popular?limit=${limit}`);
      
      // Update cache
      historyCache.popularGifts = response.data.gifts || [];
      historyCache.timestamps.popularGifts = Date.now();
      
      return response;
    } catch (error) {
      console.error('Error fetching popular gifts:', error);
      
      // Fallback to algorithm-based popular gifts
      if (process.env.NODE_ENV === 'development') {
        // First ensure we have history data
        if (!historyCache.giftHistory) {
          await giftHistoryService.getGiftHistory();
        }
        
        // Get user preferences (empty object if not found)
        let preferences = {};
        try {
          const preferencesResponse = await api.get('/user/gift-preferences');
          preferences = preferencesResponse.data.preferences || {};
        } catch (e) {
          console.error('Could not fetch preferences, using defaults:', e);
        }
        
        const recommendations = await calculateGiftRecommendations(
          historyCache.giftHistory,
          preferences
        );
        
        historyCache.popularGifts = recommendations.slice(0, limit);
        historyCache.timestamps.popularGifts = Date.now();
        
        return { data: { gifts: historyCache.popularGifts } };
      }
      
      throw error;
    }
  },
  
  // Get personalized gift recommendations
  getRecommendedGifts: async (preferences = {}, limit = 10) => {
    try {
      // First ensure we have history data
      if (!historyCache.giftHistory) {
        await giftHistoryService.getGiftHistory();
      }
      
      const recommendations = await calculateGiftRecommendations(
        historyCache.giftHistory,
        preferences
      );
      
      return { data: { gifts: recommendations.slice(0, limit) } };
    } catch (error) {
      console.error('Error generating gift recommendations:', error);
      throw error;
    }
  },
  
  // Clear history caches
  clearCache: (cacheKey = null) => {
    if (cacheKey) {
      historyCache.timestamps[cacheKey] = 0;
      historyCache[cacheKey] = null;
    } else {
      // Clear all caches
      Object.keys(historyCache.timestamps).forEach(key => {
        historyCache.timestamps[key] = 0;
        historyCache[key] = null;
      });
    }
  }
};

export default giftHistoryService; 