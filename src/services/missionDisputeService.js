import { getApiErrorMessage } from '../utils/errorHandlers';

class MissionDisputeService {
  async createDispute(missionId, data) {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const formData = new FormData();
      // API expects mission_id even though it is also in the path
      formData.append('mission_id', missionId);
      if (data.reason) formData.append('dispute_type_id', data.reason);
      if (data.description) formData.append('description', data.description);
      if (Array.isArray(data.media)) {
        data.media.forEach((file, index) => {
          if (file instanceof File) {
            formData.append(`media[${index}]`, file);
          }
        });
      }

      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/missions/${missionId}/disputes`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(getApiErrorMessage(error));
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error creating mission dispute:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new MissionDisputeService();
