import { firebaseMessaging } from './firebaseMessaging';

/**
 * Notification Handlers
 *
 * This module provides custom handlers for different types of notifications.
 * Each handler can process notifications differently based on their content type.
 */

/**
 * Play notification sound based on type
 * @param {string} type - Type of notification sound to play ('default', 'urgent', etc.)
 */
export const playNotificationSound = (type = 'default') => {
  try {
    let soundUrl;
    
    switch (type) {
      case 'urgent':
        soundUrl = '/sounds/urgent-notification.mp3';
        break;
      case 'success':
        soundUrl = '/sounds/success-notification.mp3';
        break;
      case 'error':
        soundUrl = '/sounds/error-notification.mp3';
        break;
      case 'default':
      default:
        soundUrl = '/sounds/notification.mp3';
        break;
    }
    
    const audio = new Audio(soundUrl);
    audio.volume = 0.5; // Set volume to 50%
    audio.play().catch(error => {
      // Autoplay might be blocked by browser policy
      console.warn('Could not play notification sound:', error);
    });
  } catch (error) {
    console.error('Error playing notification sound:', error);
  }
};

// Handler for chat message notifications
const handleChatMessageNotification = (payload) => {
  const { notification, data } = payload;

  try {
    // Extract and parse message data
    const messageData = data.message ? JSON.parse(data.message) : {};
    const conversationId = messageData.conversation_id || data.conversation_id;
    const senderId = messageData.sender_id || data.sender_id;
    const senderName = messageData.sender_name || data.sender_name || 'Someone';
    const content = messageData.content || '';

    // Create custom notification options
    const notificationOptions = {
      body: notification.body || `New message from ${senderName}`,
      icon: messageData.sender_avatar || notification.icon || '/logo192.png',
      badge: '/notification-badge.png',
      tag: `chat-${conversationId}`,
      data: {
        type: 'chat_message',
        conversationId,
        senderId,
        messageId: messageData.id || data.message_id,
        url: `/chat/${conversationId}`,
        timestamp: Date.now()
      },
      actions: [
        {
          action: 'reply',
          title: 'Reply'
        },
        {
          action: 'view',
          title: 'View Chat'
        }
      ]
    };

    // Create and show notification
    const notificationInstance = new Notification(
      notification.title || `${senderName} sent you a message`,
      notificationOptions
    );

    // Handle notification click
    notificationInstance.onclick = (event) => {
      event.preventDefault();
      if (window.location.pathname === `/chat/${conversationId}`) {
        window.focus();
      } else {
        window.open(`/chat/${conversationId}`, '_blank');
      }
      notificationInstance.close();
    };

    // Dispatch event to update NotificationContext
    window.dispatchEvent(new CustomEvent('external-chat-notification', {
      detail: {
        conversationId,
        senderId,
        senderName,
        content
      }
    }));
  } catch (error) {
    console.error('Error handling chat message notification:', error);
    // Fall back to basic notification
    new Notification(notification.title, {
      body: notification.body,
      icon: notification.icon || '/logo192.png'
    });
  }
};

// Handler for gift notifications
const handleGiftNotification = (payload) => {
  const { notification, data } = payload;

  try {
    // Extract and parse gift data
    const giftData = data.gift ? JSON.parse(data.gift) : {};
    const conversationId = giftData.conversation_id || data.conversation_id;
    const senderName = giftData.sender_name || data.sender_name || 'Someone';
    const giftName = giftData.gift?.name || data.gift_name || 'a gift';

    // Get the appropriate gift icon or fallback
    const giftIcon = giftData.gift?.image_url || data.gift_image || '/gift-icon.png';

    // Create custom notification options
    const notificationOptions = {
      body: notification.body || `${senderName} sent you ${giftName}!`,
      icon: giftIcon,
      badge: '/gift-badge.png',
      tag: `gift-${conversationId}`,
      data: {
        type: 'gift',
        conversationId,
        senderId: giftData.sender_id || data.sender_id,
        giftId: giftData.gift?.id || data.gift_id,
        messageId: giftData.message_id || data.message_id,
        url: `/chat/${conversationId}`,
        timestamp: Date.now()
      },
      image: giftIcon,
      vibrate: [200, 100, 200], // Special vibration pattern for gifts
      actions: [
        {
          action: 'view',
          title: 'View Gift'
        },
        {
          action: 'thanks',
          title: 'Thank You!'
        }
      ]
    };

    // Create and show notification
    const notificationInstance = new Notification(
      notification.title || `You received a gift!`,
      notificationOptions
    );

    // Handle notification click
    notificationInstance.onclick = (event) => {
      event.preventDefault();

      // Focus existing window or open a new one to the conversation
      if (window.location.pathname === `/chat/${conversationId}`) {
        window.focus();

        // Trigger event to highlight the gift message
        window.dispatchEvent(new CustomEvent('highlight-gift', {
          detail: { messageId: giftData.message_id || data.message_id }
        }));
      } else {
        window.open(`/chat/${conversationId}?highlight=${giftData.message_id || data.message_id}`, '_blank');
      }

      notificationInstance.close();
    };
  } catch (error) {
    console.error('Error handling gift notification:', error);
    // Fall back to basic notification
    new Notification(notification.title, {
      body: notification.body,
      icon: notification.icon || '/logo192.png'
    });
  }
};

// Handler for system notifications
const handleSystemNotification = (payload) => {
  const { notification, data } = payload;

  // Create system notification options
  const notificationOptions = {
    body: notification.body,
    icon: '/system-icon.png',
    tag: `system-${Date.now()}`,
    data: {
      type: 'system',
      url: data.url || '/',
      timestamp: Date.now()
    }
  };

  // Create and show notification
  const notificationInstance = new Notification(
    notification.title || 'System Notification',
    notificationOptions
  );

  // Handle notification click
  notificationInstance.onclick = (event) => {
    event.preventDefault();

    if (data.url) {
      window.open(data.url, '_blank');
    } else {
      window.focus();
    }

    notificationInstance.close();
  };
};

// Default fallback handler for unknown notification types
const handleDefaultNotification = (payload) => {
  const { notification, data } = payload;

  // Create basic notification
  const notificationInstance = new Notification(notification.title, {
    body: notification.body,
    icon: notification.icon || '/logo192.png',
    tag: data.id || `notification-${Date.now()}`,
    data
  });

  // Handle notification click
  notificationInstance.onclick = () => {
    if (data?.url) {
      window.open(data.url, '_blank');
    } else {
      window.focus();
    }

    notificationInstance.close();
  };
};

/**
 * Handle order-related notifications
 * @param {Object} payload - Firebase notification payload
 */
const handleOrderNotification = (payload) => {
  console.log('🔔 Order notification received:', payload);

  try {
    const { notification, data } = payload;

    // Extract order-specific data
    const orderId = data?.order_id || data?.orderId;
    const orderType = data?.type || 'order';
    const customerName = data?.customer_name || data?.customerName;
    const talentName = data?.talent_name || data?.talentName;
    const serviceName = data?.service_name || data?.serviceName;
    const amount = data?.amount;
    const status = data?.status;
    const isScheduled = data?.is_scheduled === 'true' || data?.isScheduled === 'true';

    // Create enhanced notification data
    const notificationData = {
      id: Date.now() + Math.random(),
      title: notification?.title || getOrderNotificationTitle(orderType, orderId),
      body: notification?.body || getOrderNotificationBody(orderType, customerName, talentName, serviceName),
      data: {
        ...data,
        type: orderType,
        order_id: orderId,
        customer_name: customerName,
        talent_name: talentName,
        service_name: serviceName,
        amount: amount,
        status: status,
        is_scheduled: isScheduled
      },
      read: false,
      timestamp: new Date().toISOString(),
      category: 'order'
    };

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notificationData.title, {
        body: notificationData.body,
        icon: '/logo192.png',
        badge: '/logo192.png',
        tag: `order-${orderId}`,
        requireInteraction: orderType === 'order_placed' || orderType === 'new_order',
        data: notificationData.data
      });

      // Auto-close after 10 seconds for non-urgent notifications
      if (orderType !== 'order_placed' && orderType !== 'new_order') {
        setTimeout(() => {
          browserNotification.close();
        }, 10000);
      }

      // Handle notification click
      browserNotification.onclick = () => {
        window.focus();
        // Navigate to order details or appropriate page
        if (orderId) {
          window.location.href = `/orders/${orderId}`;
        }
        browserNotification.close();
      };
    }

    // Play notification sound for urgent order notifications
    if (orderType === 'order_placed' || orderType === 'new_order') {
      playNotificationSound('urgent');
    } else {
      playNotificationSound('default');
    }

    // Store in notification context/state
    // This will be handled by the notification context
    return notificationData;

  } catch (error) {
    console.error('❌ Error handling order notification:', error);
    return null;
  }
};

/**
 * Generate notification title based on order type
 */
const getOrderNotificationTitle = (orderType, orderId) => {
  switch (orderType) {
    case 'order_placed':
    case 'new_order':
      return `New Order #${orderId}`;
    case 'order_accepted':
      return `Order #${orderId} Accepted`;
    case 'order_rejected':
      return `Order #${orderId} Rejected`;
    case 'order_completed':
      return `Order #${orderId} Completed`;
    case 'order_cancelled':
      return `Order #${orderId} Cancelled`;
    case 'order_timeout':
      return `Order #${orderId} Expired`;
    default:
      return `Order #${orderId} Update`;
  }
};

/**
 * Generate notification body based on order type and participants
 */
const getOrderNotificationBody = (orderType, customerName, talentName, serviceName) => {
  const service = serviceName ? ` for ${serviceName}` : '';

  switch (orderType) {
    case 'order_placed':
    case 'new_order':
      return `You have a new order${service}${customerName ? ` from ${customerName}` : ''}`;
    case 'order_accepted':
      return `Your order${service} has been accepted${talentName ? ` by ${talentName}` : ''}`;
    case 'order_rejected':
      return `Your order${service} has been rejected${talentName ? ` by ${talentName}` : ''}`;
    case 'order_completed':
      return `Order${service} has been completed successfully`;
    case 'order_cancelled':
      return `Order${service} has been cancelled`;
    case 'order_timeout':
      return `Order${service} has expired due to timeout`;
    default:
      return `Your order${service} has been updated`;
  }
};

// Initialize and register notification handlers
export const initializeNotificationHandlers = () => {
  // Register handlers for different notification types
  firebaseMessaging.registerHandler('chat_message', handleChatMessageNotification);
  firebaseMessaging.registerHandler('gift', handleGiftNotification);
  firebaseMessaging.registerHandler('system', handleSystemNotification);
  firebaseMessaging.registerHandler('order', handleOrderNotification);
  firebaseMessaging.registerHandler('order_placed', handleOrderNotification);
  firebaseMessaging.registerHandler('order_accepted', handleOrderNotification);
  firebaseMessaging.registerHandler('order_rejected', handleOrderNotification);
  firebaseMessaging.registerHandler('order_completed', handleOrderNotification);
  firebaseMessaging.registerHandler('order_cancelled', handleOrderNotification);
  firebaseMessaging.registerHandler('order_timeout', handleOrderNotification);
  firebaseMessaging.registerHandler('new_order', handleOrderNotification);
  firebaseMessaging.registerHandler('default', handleDefaultNotification);

  // Return registered handlers for testing/reference
  return {
    chat_message: handleChatMessageNotification,
    gift: handleGiftNotification,
    system: handleSystemNotification,
    order: handleOrderNotification,
    order_placed: handleOrderNotification,
    order_accepted: handleOrderNotification,
    order_rejected: handleOrderNotification,
    order_completed: handleOrderNotification,
    order_cancelled: handleOrderNotification,
    order_timeout: handleOrderNotification,
    new_order: handleOrderNotification,
    default: handleDefaultNotification
  };
};