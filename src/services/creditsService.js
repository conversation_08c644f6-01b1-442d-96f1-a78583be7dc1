// Use the shared API instance
import api from './api';

export const creditsAPI = {
  // Get total income for the last 30 days
  getIncome: async () => {
    try {
      const response = await api.get('/credits/transactions', {
        params: {
          fee_type: 'income'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching income data:', error);
      throw error;
    }
  },

  // Get total spent for the last 30 days
  getSpent: async () => {
    try {
      const response = await api.get('/credits/transactions', {
        params: {
          fee_type: 'spent'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching spent data:', error);
      throw error;
    }
  }
}; 