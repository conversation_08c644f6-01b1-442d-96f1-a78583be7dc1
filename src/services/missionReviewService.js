import { getApiErrorMessage } from '../utils/errorHandlers';

class MissionReviewService {
    async createReview(reviewData) {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${reviewData.mission_id}/reviews`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    rating: reviewData.rating,
                    review_text: reviewData.review_text,
                    is_anonymous: reviewData.is_anonymous
                })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(getApiErrorMessage(error));
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating mission review:', error);
            throw error;
        }
    }

    async createParticipantReviews(missionId, reviews) {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            const response = await fetch(`${process.env.REACT_APP_API_URL}/missions/${missionId}/reviews`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(reviews)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(getApiErrorMessage(error));
            }

            return await response.json();
        } catch (error) {
            console.error('Error creating participant reviews:', error);
            throw error;
        }
    }

    async getMissionReviews(missionId, filters = {}) {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            // Build query parameters
            const queryParams = new URLSearchParams();
            if (filters.rating) queryParams.append('rating', filters.rating);
            if (filters.fromDate) queryParams.append('from_date', filters.fromDate);
            if (filters.toDate) queryParams.append('to_date', filters.toDate);
            if (filters.isAnonymous !== null) queryParams.append('is_anonymous', filters.isAnonymous);

            const queryString = queryParams.toString();
            const url = `${process.env.REACT_APP_API_URL}/missions/${missionId}/reviews${queryString ? `?${queryString}` : ''}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(getApiErrorMessage(error));
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching mission reviews:', error);
            throw error;
        }
    }
}

export default new MissionReviewService();
