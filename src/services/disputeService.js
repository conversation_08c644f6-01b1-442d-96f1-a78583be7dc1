/**
 * Dispute Service
 * 
 * Handles all dispute-related API interactions including:
 * - Fetching dispute types
 * - Creating disputes with media uploads
 * - Fetching user disputes
 * - Getting dispute details
 */

import { createApi } from './api';

const api = createApi();

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        console.error('Dispute API Response Error:', error);
        
        // Handle specific error cases
        if (error.response?.status === 401) {
            console.warn('[disputeService] Unauthorized response');
        }
        
        return Promise.reject(error);
    }
);

const disputeService = {
    /**
     * Get all available dispute types
     * @returns {Promise} API response with dispute types
     */
    getDisputeTypes: async () => {
        try {
            console.log('Fetching dispute types...');
            const response = await api.get('/disputes/types');
            console.log('Dispute types response:', response.data);

            const types = Array.isArray(response.data)
                ? response.data
                : response.data?.data || [];

            return {
                success: true,
                data: types
            };
        } catch (error) {
            console.error('Error fetching dispute types:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Failed to fetch dispute types'
            };
        }
    },

    /**
     * Create a new dispute for an order
     * @param {number} orderId - The order ID
     * @param {Object} disputeData - Dispute form data
     * @param {number} disputeData.dispute_type_id - Selected dispute type ID
     * @param {string} disputeData.description - Dispute description
     * @param {File[]} disputeData.images - Array of image files (max 3)
     * @param {File} disputeData.video - Video file (optional)
     * @param {string} orderType - 'immediate' or 'scheduled'
     * @returns {Promise} API response
     */
    createDispute: async (orderId, disputeData, orderType = 'immediate') => {
        try {
            console.log('Creating dispute for order:', orderId, disputeData);
            
            // Create FormData for multipart upload
            const formData = new FormData();
            formData.append('dispute_type_id', disputeData.dispute_type_id);
            formData.append('description', disputeData.description);
            
            // Add images if provided
            if (disputeData.images && disputeData.images.length > 0) {
                disputeData.images.forEach((image, index) => {
                    formData.append('images[]', image);
                });
            }
            
            // Add video if provided
            if (disputeData.video) {
                formData.append('video', disputeData.video);
            }
            
            // Determine the correct endpoint based on order type
            const endpoint = orderType === 'scheduled' 
                ? `/scheduled-orders/${orderId}/disputes`
                : `/orders/${orderId}/disputes`;
            
            const response = await api.post(endpoint, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            
            console.log('Dispute created successfully:', response.data);
            
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Error creating dispute:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Failed to create dispute'
            };
        }
    },

    /**
     * Get all disputes for the current user
     * @param {Object} filters - Optional filters
     * @param {string} filters.status - Filter by status
     * @param {number} filters.page - Page number for pagination
     * @param {number} filters.limit - Items per page
     * @returns {Promise} API response with user disputes
     */
    getUserDisputes: async (filters = {}) => {
        try {
            console.log('Fetching user disputes with filters:', filters);
            
            // Build query parameters
            const params = new URLSearchParams();
            if (filters.status) params.append('status', filters.status);
            if (filters.page) params.append('page', filters.page);
            if (filters.limit) params.append('limit', filters.limit);
            
            const queryString = params.toString();
            const url = queryString ? `/disputes?${queryString}` : '/disputes';
            
            const response = await api.get(url);
            console.log('User disputes response:', response.data);

            const disputes = Array.isArray(response.data)
                ? response.data
                : response.data?.data?.data || response.data?.data || [];

            return {
                success: true,
                data: disputes,
                meta: response.data?.data || null
            };
        } catch (error) {
            console.error('Error fetching user disputes:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Failed to fetch disputes'
            };
        }
    },

    /**
     * Get details of a specific dispute
     * @param {number} disputeId - The dispute ID
     * @returns {Promise} API response with dispute details
     */
    getDisputeDetails: async (disputeId) => {
        try {
            console.log('Fetching dispute details for ID:', disputeId);
            const response = await api.get(`/disputes/${disputeId}`);
            console.log('Dispute details response:', response.data);
            
            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            console.error('Error fetching dispute details:', error);
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Failed to fetch dispute details'
            };
        }
    },

    /**
     * Validate file for dispute upload
     * @param {File} file - File to validate
     * @param {string} type - 'image' or 'video'
     * @returns {Object} Validation result
     */
    validateFile: (file, type) => {
        const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif'];
        const validVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/flv'];
        
        const maxImageSize = 10 * 1024 * 1024; // 10MB
        const maxVideoSize = 20 * 1024 * 1024; // 20MB
        
        if (type === 'image') {
            if (!validImageTypes.includes(file.type)) {
                return {
                    valid: false,
                    error: 'Invalid image format. Please use JPEG, PNG, JPG, HEIC, or HEIF.'
                };
            }
            
            if (file.size > maxImageSize) {
                return {
                    valid: false,
                    error: 'Image size exceeds 10MB limit.'
                };
            }
        } else if (type === 'video') {
            if (!validVideoTypes.includes(file.type)) {
                return {
                    valid: false,
                    error: 'Invalid video format. Please use MP4, MOV, AVI, or FLV.'
                };
            }
            
            if (file.size > maxVideoSize) {
                return {
                    valid: false,
                    error: 'Video size exceeds 20MB limit.'
                };
            }
        }
        
        return { valid: true };
    },

    /**
     * Get dispute status badge configuration
     * @param {string} status - Dispute status
     * @returns {Object} Badge configuration
     */
    getStatusBadge: (status) => {
        const statusConfig = {
            submitted: {
                label: 'Submitted',
                className: 'bg-blue-100 text-blue-700 border-blue-200',
                icon: '📝'
            },
            in_review: {
                label: 'In Review',
                className: 'bg-yellow-100 text-yellow-700 border-yellow-200',
                icon: '🔍'
            },
            resolved: {
                label: 'Resolved',
                className: 'bg-green-100 text-green-700 border-green-200',
                icon: '✅'
            },
            rejected: {
                label: 'Rejected',
                className: 'bg-red-100 text-red-700 border-red-200',
                icon: '❌'
            }
        };
        
        return statusConfig[status] || {
            label: status || 'Unknown',
            className: 'bg-gray-100 text-gray-700 border-gray-200',
            icon: '❓'
        };
    }
};

export default disputeService;
