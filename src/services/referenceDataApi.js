import api from './api';

// Create base API instance
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

const apiInstance = api.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
apiInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Simple in-memory cache
const cache = {
  serviceTypes: null,
  serviceStyles: null,
  platforms: null,
  languages: null,
  levels: null,
  // Cache expiration time in milliseconds (30 minutes)
  expirationTime: 30 * 60 * 1000,
  timestamps: {
    serviceTypes: 0,
    serviceStyles: 0,
    platforms: 0,
    languages: 0,
    levels: 0
  }
};

// In-flight request deduplication
const inflight = {
  serviceTypes: null,
  serviceStyles: null,
  platforms: null,
  languages: null,
  levels: null
};

// Check if cache is valid
const isCacheValid = (key) => {
  const timestamp = cache.timestamps[key];
  return timestamp && (Date.now() - timestamp < cache.expirationTime);
};

export const referenceDataApi = {
  /**
   * Get all service types
   * @returns {Promise<Object>} Response with service types
   */
  getServiceTypes: async () => {
    try {
      if (isCacheValid('serviceTypes') && cache.serviceTypes) {
        return { data: cache.serviceTypes };
      }
      if (inflight.serviceTypes) {
        return inflight.serviceTypes;
      }
      inflight.serviceTypes = apiInstance.get('/service-configuration/types')
        .then(response => {
          cache.serviceTypes = response.data;
          cache.timestamps.serviceTypes = Date.now();
          inflight.serviceTypes = null;
          return { data: cache.serviceTypes };
        })
        .catch(error => {
          inflight.serviceTypes = null;
          throw error;
        });
      return inflight.serviceTypes;
    } catch (error) {
      console.error('Error fetching service types:', error);
      throw error;
    }
  },

  /**
   * Get all service styles
   * @returns {Promise<Object>} Response with service styles
   */
  getServiceStyles: async () => {
    try {
      if (isCacheValid('serviceStyles') && cache.serviceStyles) {
        return { data: cache.serviceStyles };
      }
      if (inflight.serviceStyles) {
        return inflight.serviceStyles;
      }
      inflight.serviceStyles = apiInstance.get('/service-configuration/styles')
        .then(response => {
          cache.serviceStyles = response.data;
          cache.timestamps.serviceStyles = Date.now();
          inflight.serviceStyles = null;
          return { data: cache.serviceStyles };
        })
        .catch(error => {
          inflight.serviceStyles = null;
          throw error;
        });
      return inflight.serviceStyles;
    } catch (error) {
      console.error('Error fetching service styles:', error);
      throw error;
    }
  },

  /**
   * Get all levels
   * @returns {Promise<Object>} Response with levels
   */
  getLevels: async () => {
    try {
      if (isCacheValid('levels') && cache.levels) {
        return { data: cache.levels };
      }
      if (inflight.levels) {
        return inflight.levels;
      }
      inflight.levels = apiInstance.get('/levels')
        .then(response => {
          cache.levels = response.data;
          cache.timestamps.levels = Date.now();
          inflight.levels = null;
          return { data: cache.levels };
        })
        .catch(error => {
          inflight.levels = null;
          throw error;
        });
      return inflight.levels;
    } catch (error) {
      console.error('Error fetching levels:', error);
      throw error;
    }
  },

  /**
   * Get all platforms
   * @returns {Promise<Object>} Response with platforms
   */
  getPlatforms: async () => {
    try {
      if (isCacheValid('platforms') && cache.platforms) {
        return { data: cache.platforms };
      }
      if (inflight.platforms) {
        return inflight.platforms;
      }
      inflight.platforms = apiInstance.get('/service-configuration/categories')
        .then(response => {
          cache.platforms = response.data;
          cache.timestamps.platforms = Date.now();
          inflight.platforms = null;
          return { data: cache.platforms };
        })
        .catch(error => {
          inflight.platforms = null;
          throw error;
        });
      return inflight.platforms;
    } catch (error) {
      console.error('Error fetching platforms:', error);
      throw error;
    }
  },

  /**
   * Get all languages
   * @returns {Promise<Object>} Response with languages
   */
  getLanguages: async () => {
    try {
      if (isCacheValid('languages') && cache.languages) {
        return { data: cache.languages };
      }
      if (inflight.languages) {
        return inflight.languages;
      }
      inflight.languages = apiInstance.get('/languages')
        .then(response => {
          cache.languages = response.data;
          cache.timestamps.languages = Date.now();
          inflight.languages = null;
          return { data: cache.languages };
        })
        .catch(error => {
          inflight.languages = null;
          // Return fallback data if API fails
          return {
            data: [
              { id: 1, name: 'English', code: 'en' },
              { id: 2, name: 'Chinese', code: 'zh' },
              { id: 3, name: 'Japanese', code: 'ja' },
              { id: 4, name: 'Korean', code: 'ko' }
            ]
          };
        });
      return inflight.languages;
    } catch (error) {
      console.error('Error fetching languages:', error);
      // Return fallback data if API fails
      return {
        data: [
          { id: 1, name: 'English', code: 'en' },
          { id: 2, name: 'Chinese', code: 'zh' },
          { id: 3, name: 'Japanese', code: 'ja' },
          { id: 4, name: 'Korean', code: 'ko' }
        ]
      };
    }
  },

  /**
   * Clear all cached data
   */
  clearCache: () => {
    cache.serviceTypes = null;
    cache.serviceStyles = null;
    cache.platforms = null;
    cache.languages = null;
    cache.levels = null;
    cache.timestamps.serviceTypes = 0;
    cache.timestamps.serviceStyles = 0;
    cache.timestamps.platforms = 0;
    cache.timestamps.languages = 0;
    cache.timestamps.levels = 0;
  },

  /**
   * Fetch reference data
   * @returns {Promise<Object>} Response with reference data
   */
  fetchReferenceData: async () => {
    try {
      const [serviceTypes, serviceStyles, platforms] = await Promise.all([
        referenceDataApi.getServiceTypes(),
        referenceDataApi.getServiceStyles(),
        referenceDataApi.getPlatforms()
      ]);

      return {
        serviceTypes: serviceTypes.data,
        serviceStyles: serviceStyles.data,
        platforms: platforms.data
      };
    } catch (error) {
      console.error('Error fetching reference data:', error);
      throw error;
    }
  }
};

export default referenceDataApi;
