/**
 * Phone Validation Service
 * Implements exact Malaysian phone validation matching backend MalaysianPhone rule
 * Backend regex: /^(\+?6?01)(?:1[0-9]{8}|[0-46-9][0-9]{7})(?:-[0-9]+)*$/
 */

/**
 * Malaysian Phone Validation Service
 * Matches backend App\Rules\MalaysianPhone exactly
 */
export const phoneValidationService = {
  /**
   * Validate Malaysian phone number using exact backend regex
   * @param {string} phoneNumber - Phone number to validate
   * @returns {boolean} True if valid Malaysian phone number
   */
  isValidMalaysianPhone: (phoneNumber) => {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return false;
    }

    // Backend MalaysianPhone rule regex
    // Matches Malaysian phone numbers:
    // +601x-xxxxxxx (where x is 0,1,2,3,4,6,7,8,9)
    // +6011-xxxxxxxx
    // 601x-xxxxxxx
    // 6011-xxxxxxxx
    // 01x-xxxxxxx
    // 011-xxxxxxxx
    const malaysianPhoneRegex = /^(\+?6?01)(?:1[0-9]{8}|[0-46-9][0-9]{7})(?:-[0-9]+)*$/;
    
    return malaysianPhoneRegex.test(phoneNumber.trim());
  },

  /**
   * Format phone number to Malaysian standard
   * @param {string} phoneNumber - Raw phone number input
   * @returns {string} Formatted Malaysian phone number
   */
  formatMalaysianPhone: (phoneNumber) => {
    if (!phoneNumber) return '';

    // Remove all non-digit characters except +
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');

    // Handle different input formats
    if (cleaned.startsWith('+60')) {
      // Already in international format
      return cleaned;
    } else if (cleaned.startsWith('60')) {
      // Add + prefix
      return `+${cleaned}`;
    } else if (cleaned.startsWith('01')) {
      // Local format, add country code
      return `+6${cleaned}`;
    } else if (cleaned.startsWith('1') && cleaned.length >= 9) {
      // Missing leading 0, add it
      return `+60${cleaned}`;
    }

    return phoneNumber; // Return original if can't format
  },

  /**
   * Normalize phone number for backend submission
   * Backend expects Malaysian format without + prefix for SMS provider
   * @param {string} phoneNumber - Phone number to normalize
   * @returns {string} Normalized phone number (60XXXXXXXXX format)
   */
  normalizeForBackend: (phoneNumber) => {
    if (!phoneNumber) return '';

    let cleaned = phoneNumber.replace(/[^\d]/g, '');

    // Convert to 60XXXXXXXXX format (without + prefix)
    if (cleaned.startsWith('60')) {
      return cleaned;
    } else if (cleaned.startsWith('01')) {
      return `6${cleaned}`;
    } else if (cleaned.startsWith('1') && cleaned.length >= 9) {
      return `60${cleaned}`;
    }

    return cleaned;
  },

  /**
   * Get display format for phone number
   * @param {string} phoneNumber - Phone number to format for display
   * @returns {string} User-friendly display format
   */
  getDisplayFormat: (phoneNumber) => {
    if (!phoneNumber) return '';

    const normalized = phoneValidationService.normalizeForBackend(phoneNumber);
    
    if (normalized.startsWith('60')) {
      // Format as +60 XX-XXXX XXXX or +60 XX-XXX XXXX
      const countryCode = '+60';
      const number = normalized.substring(2);
      
      if (number.startsWith('11')) {
        // 11-series format: +60 11-XXXX XXXX
        return `${countryCode} ${number.substring(0, 2)}-${number.substring(2, 6)} ${number.substring(6)}`;
      } else {
        // Standard format: +60 1X-XXX XXXX
        return `${countryCode} ${number.substring(0, 2)}-${number.substring(2, 5)} ${number.substring(5)}`;
      }
    }

    return phoneNumber;
  },

  /**
   * Validate and get error message
   * @param {string} phoneNumber - Phone number to validate
   * @returns {Object} Validation result with valid status and message
   */
  validateWithMessage: (phoneNumber) => {
    if (!phoneNumber || phoneNumber.trim() === '') {
      return {
        valid: false,
        message: 'Phone number is required'
      };
    }

    if (!phoneValidationService.isValidMalaysianPhone(phoneNumber)) {
      return {
        valid: false,
        message: 'Please enter a valid Malaysian phone number'
      };
    }

    return {
      valid: true,
      message: 'Valid Malaysian phone number'
    };
  },

  /**
   * Get supported phone number formats for user guidance
   * @returns {Array} Array of example formats
   */
  getSupportedFormats: () => {
    return [
      '+60123456789',
      '+************',
      '60123456789',
      '************',
      '0123456789',
      '01122220000'
    ];
  },

  /**
   * Check if phone number is 11-series (different formatting)
   * @param {string} phoneNumber - Phone number to check
   * @returns {boolean} True if 11-series number
   */
  is11Series: (phoneNumber) => {
    const normalized = phoneValidationService.normalizeForBackend(phoneNumber);
    return normalized.startsWith('6011');
  },

  /**
   * Auto-format phone number as user types
   * @param {string} input - Current input value
   * @param {string} previousInput - Previous input value
   * @returns {string} Formatted input
   */
  autoFormat: (input, previousInput = '') => {
    if (!input) return '';

    // Remove all non-digit characters except +
    let cleaned = input.replace(/[^\d+]/g, '');

    // Handle backspace - don't auto-format if user is deleting
    if (previousInput && input.length < previousInput.length) {
      return input;
    }

    // Auto-add + for international format
    if (cleaned.startsWith('60') && !cleaned.startsWith('+60')) {
      cleaned = `+${cleaned}`;
    }

    // Auto-add +6 for local format
    if (cleaned.startsWith('01') && !cleaned.startsWith('+6')) {
      cleaned = `+6${cleaned}`;
    }

    return cleaned;
  }
};

export default phoneValidationService;
