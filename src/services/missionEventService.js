/**
 * Mission Event Service
 *
 * This service handles mission events and integrates them with the chat system.
 * It provides methods to send event notifications to mission chats when mission
 * status changes, participants join/leave, tasks are completed, etc.
 */

import { missionApi } from './missionApi';
import { sendMissionEventMessage, findOrCreateMissionChat } from '../utils/missionChatUtils';

/**
 * Mission Event Service
 */
export const missionEventService = {
  /**
   * Handle mission started event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {Object} missionData - The mission data
   * @param {Object} userData - The user data of the person who started the mission
   * @returns {Promise<Object>} - The result of the API call
   */
  handleMissionStarted: async (missionId, missionData, userData) => {
    try {
      console.log(`Mission ${missionId} started by ${userData?.name || 'host'}`);

      // Call the API to start the mission
      const result = await missionApi.startMission(missionId);

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'mission_started', {
        startedBy: userData?.id,
        startedByName: userData?.name || 'Host',
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error('Error handling mission started event:', error);
      throw error;
    }
  },

  /**
   * Handle mission completed event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {Object} completionData - The completion data
   * @param {Object} userData - The user data of the person who completed the mission
   * @returns {Promise<Object>} - The result of the API call
   */
  handleMissionCompleted: async (missionId, completionData, userData) => {
    try {
      console.log(`Mission ${missionId} completed by ${userData?.name || 'host'}`);

      // Call the API to complete the mission
      const result = await missionApi.completeMission(missionId, completionData);

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'mission_completed', {
        completedBy: userData?.id,
        completedByName: userData?.name || 'Host',
        timestamp: new Date().toISOString(),
        ratings: completionData.ratings
      });

      // If there are rewards, send a separate message about reward distribution
      if (completionData.rewards) {
        await sendMissionEventMessage(missionId, 'rewards_distributed', {
          totalRewards: completionData.rewards.total,
          distributedBy: userData?.id,
          distributedByName: userData?.name || 'Host',
          timestamp: new Date().toISOString()
        });
      }

      return result;
    } catch (error) {
      console.error('Error handling mission completed event:', error);
      throw error;
    }
  },

  /**
   * Handle mission cancelled event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {Object} cancellationData - The cancellation data
   * @param {Object} userData - The user data of the person who cancelled the mission
   * @returns {Promise<Object>} - The result of the API call
   */
  handleMissionCancelled: async (missionId, cancellationData, userData) => {
    try {
      console.log(`Mission ${missionId} cancelled by ${userData?.name || 'host'}`);

      // Call the API to cancel the mission
      const result = await missionApi.cancelMission(missionId, cancellationData);

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'mission_cancelled', {
        cancelledBy: userData?.id,
        cancelledByName: userData?.name || 'Host',
        reason: cancellationData?.reason,
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error('Error handling mission cancelled event:', error);
      throw error;
    }
  },

  /**
   * Handle participant joined event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {Object} participantData - The participant data
   * @returns {Promise<Object>} - The result of the API call
   */
  handleParticipantJoined: async (missionId, participantData) => {
    try {
      console.log(`Participant ${participantData.name} (${participantData.id}) joined mission ${missionId}`);

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'participant_joined', {
        participantId: participantData.id,
        participantName: participantData.name,
        timestamp: new Date().toISOString()
      });

      // Ensure the participant is added to the mission chat
      const missionDetails = await missionApi.getMissionById(missionId);
      const missionTitle = missionDetails.data?.title || `Mission ${missionId}`;

      // Get all participants including the host
      const participants = [
        ...(missionDetails.data?.participants || []).map(p => p.id),
        missionDetails.data?.host?.id
      ].filter(Boolean); // Remove any undefined/null values

      // Find or create the mission chat and ensure the new participant is included
      await findOrCreateMissionChat(missionId, missionTitle, participants);

      return { success: true };
    } catch (error) {
      console.error('Error handling participant joined event:', error);
      throw error;
    }
  },

  /**
   * Handle participant left event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {Object} participantData - The participant data
   * @returns {Promise<Object>} - The result of the API call
   */
  handleParticipantLeft: async (missionId, participantData) => {
    try {
      console.log(`Participant ${participantData.name} (${participantData.id}) left mission ${missionId}`);

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'participant_left', {
        participantId: participantData.id,
        participantName: participantData.name,
        timestamp: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error handling participant left event:', error);
      throw error;
    }
  },

  /**
   * Handle task completed event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {string|number} taskId - The ID of the task
   * @param {Object} taskData - The task data
   * @param {Object} userData - The user data of the person who completed the task
   * @returns {Promise<Object>} - The result of the API call
   */
  handleTaskCompleted: async (missionId, taskId, taskData, userData) => {
    try {
      console.log(`Task ${taskId} completed by ${userData?.name || 'user'} in mission ${missionId}`);

      // Call the API to update the task
      const result = await missionApi.updateMissionTask(missionId, taskId, {
        ...taskData,
        completed: true,
        completed_by: userData?.id,
        completed_at: new Date().toISOString()
      });

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'task_completed', {
        taskId,
        taskName: taskData.title,
        completedBy: userData?.id,
        completedByName: userData?.name || 'User',
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error('Error handling task completed event:', error);
      throw error;
    }
  },

  /**
   * Handle task uncompleted event
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {string|number} taskId - The ID of the task
   * @param {Object} taskData - The task data
   * @param {Object} userData - The user data of the person who uncompleted the task
   * @returns {Promise<Object>} - The result of the API call
   */
  handleTaskUncompleted: async (missionId, taskId, taskData, userData) => {
    try {
      console.log(`Task ${taskId} marked as incomplete by ${userData?.name || 'user'} in mission ${missionId}`);

      // Call the API to update the task
      const result = await missionApi.updateMissionTask(missionId, taskId, {
        ...taskData,
        completed: false,
        completed_by: null,
        completed_at: null
      });

      // Send event message to mission chat
      await sendMissionEventMessage(missionId, 'task_uncompleted', {
        taskId,
        taskName: taskData.title,
        uncompletedBy: userData?.id,
        uncompletedByName: userData?.name || 'User',
        timestamp: new Date().toISOString()
      });

      return result;
    } catch (error) {
      console.error('Error handling task uncompleted event:', error);
      throw error;
    }
  }
};
