/**
 * OTP Service
 * Handles all OTP (One-Time Password) related operations
 */

import api from './api';
import { handleApiError } from './errorHandler';
import { authService } from './authService';

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Helper function to simulate API delay in development
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * OTP Service
 * Provides methods for requesting, verifying, and managing OTPs
 */
const otpService = {
  /**
   * Request an OTP for registration
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @returns {Promise<Object>} Response with success status
   */
  requestRegistrationOtp: async (mobileNumber, countryCode) => {
    try {
      // Log OTP request in development mode
      if (isDevelopment) {
        console.log(`[DEV] OTP requested for mobile number ${mobileNumber} with country code ${countryCode}`);
        console.log(`[DEV] Check backoffice for the actual OTP code`);
      }

      // Always use the real backend API to request OTP
      const response = await authService.requestOtp(mobileNumber, countryCode, 'registration');

      if (isDevelopment && response.success) {
        console.log(`[DEV] OTP request successful. Check backoffice for the OTP code.`);
      }

      return response;
    } catch (error) {
      return handleApiError(error, 'Failed to send verification code');
    }
  },

  /**
   * NOTE: For registration, we don't need to verify the OTP separately.
   * The OTP is collected from the user and sent directly with the registration data.
   * The backend will validate the OTP during registration.
   *
   * This method is kept for backward compatibility but doesn't make an API call.
   *
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @param {string} otp - The OTP code to verify
   * @returns {Promise<Object>} Response with success status
   */
  verifyRegistrationOtp: async (mobileNumber, countryCode, otp) => {
    try {
      // Log in development mode
      if (isDevelopment) {
        console.log(`[DEV] Registration flow: Collecting OTP: ${otp} for registration`);
        console.log(`[DEV] No separate OTP verification needed for registration`);
      }

      // Return a success response without making an API call
      return {
        success: true,
        data: {
          message: 'OTP collected for registration',
          verified: true,
          otp: otp // Include the OTP in the response
        }
      };
    } catch (error) {
      return handleApiError(error, 'Failed to process OTP');
    }
  },

  /**
   * Request an OTP for password reset
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @returns {Promise<Object>} Response with success status
   */
  requestPasswordResetOtp: async (mobileNumber, countryCode) => {
    try {
      // Log the request in development mode
      if (isDevelopment) {
        console.log(`[DEV] Password reset OTP requested for mobile number ${mobileNumber} with country code ${countryCode}`);
        console.log(`[DEV] Check backoffice for the actual OTP code`);
      }

      // Always use the real backend API to request OTP
      const response = await authService.requestOtp(mobileNumber, countryCode, 'reset');

      if (isDevelopment && response.success) {
        console.log(`[DEV] Password reset OTP request successful. Check backoffice for the OTP code.`);
      }

      return response;
    } catch (error) {
      return handleApiError(error, 'Failed to send password reset code');
    }
  },

  /**
   * Verify an OTP for password reset
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @param {string} otp - The OTP code to verify
   * @returns {Promise<Object>} Response with success status
   */
  verifyPasswordResetOtp: async (mobileNumber, countryCode, otp) => {
    try {
      // Log the verification in development mode
      if (isDevelopment) {
        console.log(`[DEV] Verifying password reset OTP ${otp} for mobile number ${mobileNumber} with country code ${countryCode}`);
      }

      // Make the actual API call to verify OTP for reset
      return await authService.verifyOtpForReset(mobileNumber, countryCode, otp);
    } catch (error) {
      return handleApiError(error, 'Failed to verify password reset code');
    }
  },

  /**
   * Request an OTP for login
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @returns {Promise<Object>} Response with success status
   */
  requestLoginOtp: async (mobileNumber, countryCode) => {
    try {
      // Log the request in development mode
      if (isDevelopment) {
        console.log(`[DEV] Login OTP requested for mobile number ${mobileNumber} with country code ${countryCode}`);
        console.log(`[DEV] Check backoffice for the actual OTP code`);
      }

      // Always use the real backend API to request OTP
      const response = await authService.requestOtp(mobileNumber, countryCode, 'login');

      if (isDevelopment && response.success) {
        console.log(`[DEV] Login OTP request successful. Check backoffice for the OTP code.`);
      }

      return response;
    } catch (error) {
      return handleApiError(error, 'Failed to send login code');
    }
  },

  /**
   * Verify an OTP for login
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code in ISO format (e.g., 'MY' for Malaysia)
   * @param {string} otp - The OTP code to verify
   * @returns {Promise<Object>} Response with success status
   */
  verifyLoginOtp: async (mobileNumber, countryCode, otp) => {
    try {
      // Log the verification in development mode
      if (isDevelopment) {
        console.log(`[DEV] Verifying login OTP ${otp} for mobile number ${mobileNumber} with country code ${countryCode}`);
      }

      // Make the actual API call to verify OTP for login
      const response = await api.post('/auth/verify-otp', {
        mobile_number: mobileNumber,
        country_code: countryCode, // Using ISO country code format (MY)
        verification_code: otp,
        purpose: 'login'
      });

      return { success: true, data: response.data };
    } catch (error) {
      return handleApiError(error, 'Failed to verify login code');
    }
  }
};

export default otpService;
