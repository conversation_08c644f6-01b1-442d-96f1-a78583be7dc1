/**
 * Error Handler Service
 * Provides utilities for handling API errors consistently across the application
 */

/**
 * Handle API errors consistently
 * @param {Error} error - The error object from axios
 * @param {string} defaultMessage - Default error message
 * @returns {Object} Formatted error response
 */
export const handleApiError = (error, defaultMessage) => {
  let errorMessage = defaultMessage;
  let errorCode = null;
  let validationErrors = null;

  if (error.response) {
    // Server responded with an error
    if (error.response.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response.data?.error) {
      errorMessage = error.response.data.error;
    }

    // Handle validation errors
    if (error.response.data?.errors) {
      validationErrors = error.response.data.errors;
    }

    errorCode = error.response.status;

    // Log detailed error information in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[API ERROR] ${errorCode}: ${errorMessage}`, error.response.data);
    }
  } else if (error.request) {
    // Request made but no response received
    errorMessage = 'Unable to connect to the server. Please check your internet connection.';

    if (process.env.NODE_ENV === 'development') {
      console.error('[API ERROR] Network error - no response received', error.request);
    }
  } else {
    // Something else happened while setting up the request
    if (process.env.NODE_ENV === 'development') {
      console.error('[API ERROR] Request setup error', error.message);
    }
  }

  return {
    success: false,
    error: errorMessage,
    code: errorCode,
    validationErrors: validationErrors,
    originalError: error
  };
};

/**
 * Format validation errors from API
 * @param {Object} validationErrors - Validation errors from API
 * @returns {Object} Formatted validation errors
 */
export const formatValidationErrors = (validationErrors) => {
  if (!validationErrors) return {};

  const formattedErrors = {};

  // Handle different validation error formats
  if (Array.isArray(validationErrors)) {
    // Format: [{ field: 'fieldName', message: 'error message' }, ...]
    validationErrors.forEach(error => {
      formattedErrors[error.field] = error.message;
    });
  } else if (typeof validationErrors === 'object') {
    // Format: { fieldName: 'error message', ... }
    Object.keys(validationErrors).forEach(field => {
      formattedErrors[field] = validationErrors[field];
    });
  }

  return formattedErrors;
};

export default {
  handleApiError,
  formatValidationErrors
};
