import api from './api';
import { getApiErrorMessage } from '../utils/errorUtils';

/**
 * Mission Payment Service
 * 
 * This service handles all payment-related operations for missions,
 * including processing payments, checking balances, and handling refunds.
 */
class MissionPaymentService {
  /**
   * Check if user has sufficient balance to create a mission
   * @param {number} totalAmount - The total amount required for the mission
   * @returns {Promise<boolean>} - True if user has sufficient balance, false otherwise
   */
  async checkSufficientBalance(totalAmount) {
    try {
      const response = await api.get('/credits/balance');
      const balance = response.data.balance ?? response.data.credits_balance;
      return balance >= totalAmount;
    } catch (error) {
      console.error('Error checking balance:', error);
      throw error;
    }
  }
  
  /**
   * Process payment for creating a mission
   * @param {number} missionId - The ID of the mission
   * @param {number} totalAmount - The total amount required for the mission
   * @returns {Promise<Object>} - The payment transaction details
   */
  async processMissionCreationPayment(missionId, totalAmount) {
    try {
      const response = await api.post('/missions/payment', {
        mission_id: missionId,
        amount: totalAmount,
        payment_type: 'mission_creation'
      });
      return response.data;
    } catch (error) {
      console.error('Error processing mission payment:', error);
      throw error;
    }
  }
  
  /**
   * Process payment for mission completion
   * @param {number} missionId - The ID of the mission
   * @param {Object} completionData - The mission completion data including ratings
   * @returns {Promise<Object>} - The payment transaction details
   */
  async processMissionCompletionPayment(missionId, completionData) {
    try {
      // Process the payment through the mission API
      const response = await api.post('/missions/payment', {
        mission_id: missionId,
        payment_type: 'mission_completion',
        ratings: completionData.ratings,
        feedback: completionData.feedback
      });
      
      return response.data;
    } catch (error) {
      console.error('Error processing mission completion payment:', error);
      throw error;
    }
  }
  
  /**
   * Process refund for a cancelled mission
   * @param {number} missionId - The ID of the mission
   * @returns {Promise<Object>} - The refund transaction details
   */
  async refundMissionPayment(missionId) {
    try {
      const response = await api.post(`/missions/${missionId}/refund`);
      return response.data;
    } catch (error) {
      console.error('Error refunding mission payment:', error);
      throw error;
    }
  }
  
  /**
   * Get wallet balance
   * @returns {Promise<number>} - The current wallet balance
   */
  async getWalletBalance() {
    try {
      const response = await api.get('/credits/balance');
      return response.data.balance ?? response.data.credits_balance;
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      throw error;
    }
  }
  
  /**
   * Get mission payment history
   * @param {number} missionId - The ID of the mission
   * @returns {Promise<Array>} - The mission payment history
   */
  async getMissionPaymentHistory(missionId) {
    try {
      const response = await api.get(`/missions/${missionId}/payment-history`);
      return response.data;
    } catch (error) {
      console.error('Error getting mission payment history:', error);
      throw error;
    }
  }

  async getMissionPaymentDetails(missionId) {
    try {
      const response = await api.get(`/missions/${missionId}/payment-details`);
      return response.data;
    } catch (error) {
      console.error('Error fetching mission payment details:', error);
      throw error;
    }
  }
}

export default new MissionPaymentService();
