import api from './api';

/**
 * Order Service
 *
 * This service handles all order-related API calls based on the Orders API documentation.
 */
export const orderAPI = {
  /**
   * Get all orders for the authenticated user
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Orders data
   */
  getOrders: async (params = {}) => {
    try {
      const response = await api.get('/orders', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  },

  /**
   * Get order details
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Object>} - Order details
   */
  getOrderDetails: async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order details for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new order
   * @param {Object} orderData - The order data
   * @returns {Promise<Object>} - Created order data
   */
  createOrder: async (orderData) => {
    try {
      const response = await api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  /**
   * Place an immediate order.  This now uses the unified
   * `/orders` endpoint and relies on the backend to determine
   * the order type based on the payload (no `scheduled_start_time`).
   *
   * @param {Object} orderData - The order payload
   * @returns {Promise<Object>} Created order data
   */
  createOrderNow: async (orderData) => {
    try {
      const response = await api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error placing immediate order:', error);
      throw error;
    }
  },

  /**
   * Schedule an order for a future time.  This also posts to the
   * unified `/orders` endpoint and expects `scheduled_start_time`
   * to be provided in the payload.
   *
   * @param {Object} orderData - The order payload
   * @returns {Promise<Object>} Created order data
   */
  scheduleOrder: async (orderData) => {
    try {
      const response = await api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error scheduling order:', error);
      throw error;
    }
  },

  /**
   * Respond to an order (accept or reject)
   * @param {number} orderId - The ID of the order
   * @param {Object} responseData - The response data (action: 'accept' or 'reject')
   * @returns {Promise<Object>} - Updated order data
   */
  respondToOrder: async (orderId, responseData) => {
    try {
      const response = await api.post(`/orders/${orderId}/respond`, responseData);
      return response.data;
    } catch (error) {
      console.error(`Error responding to order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Accept an order
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Object>} - Updated order data
   */
  acceptOrder: async (orderId) => {
    try {
      console.log(`🟢 Accepting order ${orderId}...`);
      const response = await api.post(`/orders/${orderId}/respond`, { action: 'accept' });
      console.log(`✅ Order ${orderId} accepted successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error accepting order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Reject an order
   * @param {number} orderId - The ID of the order
   * @param {string} reason - Optional rejection reason
   * @returns {Promise<Object>} - Updated order data
   */
  rejectOrder: async (orderId, reason = null) => {
    try {
      console.log(`🔴 Rejecting order ${orderId}...`);
      const payload = { action: 'reject' };
      if (reason) {
        payload.reason = reason;
      }
      const response = await api.post(`/orders/${orderId}/respond`, payload);
      console.log(`✅ Order ${orderId} rejected successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error rejecting order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Complete an order
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Object>} - Completed order data
   */
  completeOrder: async (orderId) => {
    try {
      const response = await api.post(`/orders/${orderId}/complete`);
      return response.data;
    } catch (error) {
      console.error(`Error completing order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Create a review for an order
   * @param {number} orderId - The ID of the order
   * @param {Object} reviewData - The review data
   * @returns {Promise<Object>} - Created review data
   */
  createReview: async (orderId, reviewData) => {
    try {
      const response = await api.post(`/orders/${orderId}/reviews`, reviewData);
      return response.data;
    } catch (error) {
      console.error(`Error creating review for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get reviews for an order
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Object>} - Reviews data
   */
  getReviews: async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}/reviews`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching reviews for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Check if accepting an order would cause overtime for a talent
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Object>} - Overtime check data
   */
  checkOrderOvertime: async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}/check-overtime`);
      return response.data;
    } catch (error) {
      console.error(`Error checking overtime for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get estimated wait time for a talent
   * @param {number} talentId - The ID of the talent
   * @returns {Promise<Object>} - Wait time data
   */
  getEstimatedWaitTime: async (talentId) => {
    try {
      const response = await api.get(`/orders/talents/${talentId}/wait-time`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching wait time for talent ${talentId}:`, error);
      throw error;
    }
  },

  /**
   * Process order payment
   * @param {number} orderId - The ID of the order
   * @param {Object} paymentData - The payment data
   * @returns {Promise<Object>} - Payment transaction details
   */
  processOrderPayment: async (orderId, paymentData) => {
    try {
      const response = await api.post(`/orders/${orderId}/payment`, paymentData);
      return response.data;
    } catch (error) {
      console.error(`Error processing payment for order ${orderId}:`, error);
      throw error;
    }
  },

  /**
   * Get order payment history
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Array>} - The order payment history
   */
  getOrderPaymentHistory: async (orderId) => {
    try {
      const response = await api.get(`/orders/${orderId}/payment-history`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching payment history for order ${orderId}:`, error);
      throw error;
    }
  }
};

export default orderAPI;
