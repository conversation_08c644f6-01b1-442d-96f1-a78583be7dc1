import { useEffect } from 'react';
import { io } from 'socket.io-client';

// Base URL for Socket.IO server
const SOCKET_URL = process.env.REACT_APP_SOCKET_URL_OUT;

let socket = null;

/**
 * Connect to the Socket.IO server and join a specific room (e.g., talent:{id}).
 * @param {string} talentId - The talent/user ID to join the room for.
 * @param {object} [options] - Optional Socket.IO connection options.
 */
export function connectSocket(talentId, options = {}) {
  if (socket) {
    socket.disconnect();
  }
  socket = io(SOCKET_URL, {
    transports: ['websocket'],
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 5,
    ...options,
  });
  socket.on('connect', () => {
    console.log('Socket.IO: Connected', socket.id);
    socket.emit('join', `mx_talent:${talentId}`);
  });
}

/**
 * Listen for a specific event from the socket server.
 * @param {string} event - The event name (e.g., 'order.placed').
 * @param {function} callback - The callback to handle the event data.
 */
export function onSocketEvent(event, callback) {
  if (!socket) return;
  socket.on(event, callback);
}

/**
 * Remove a specific event listener.
 * @param {string} event - The event name.
 * @param {function} callback - The callback to remove.
 */
export function offSocketEvent(event, callback) {
  if (!socket) return;
  socket.off(event, callback);
}

/**
 * Disconnect from the socket server.
 */
export function disconnectSocket() {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
}

/**
 * Get the current socket instance (for advanced usage).
 */
export function getSocket() {
  return socket;
}

/**
 * React hook to connect to the Socket.IO server and listen for new orders.
 * @param {string} talentId - The talent/user ID to join the room for.
 * @param {(data: any) => void} onOrderPlaced - Callback when an order is placed.
 */
export function useSocket(talentId, onOrderPlaced) {
  useEffect(() => {
    if (!talentId) return undefined;

    const s = io(SOCKET_URL, {
      transports: ['websocket'],
      autoConnect: true,
    });

    s.on('connect', () => {
      console.log('Socket.IO: Connected', s.id);
      s.emit('join', `mx_talent:${talentId}`);
    });

    s.on('order.placed', (data) => {
      console.log('Socket.IO: order.placed received', data);
      if (onOrderPlaced) onOrderPlaced(data);
    });

    s.on('disconnect', () => {
      console.log('Socket.IO: Disconnected');
    });

    return () => {
      s.disconnect();
    };
  }, [talentId, onOrderPlaced]);
}

export default useSocket;
