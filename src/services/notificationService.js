// Simple notification service for handling FCM foreground messages
import { onMessage } from 'firebase/messaging';
import { messaging } from '../firebase/config';
import { toast } from 'react-hot-toast';

/**
 * Listen for FCM messages while the app is in the foreground.
 * @param {Function} callback - Called with parsed notification data.
 */
export const setupForegroundMessageListener = (callback) => {
  if (!messaging) return;
  onMessage(messaging, (payload) => {
    try {
      const notification = parseNotificationPayload(payload);
      if (notification) {
        if (callback) callback(notification);
        showInAppNotification(notification);
      }
    } catch (err) {
      console.error('FCM foreground message handling failed:', err);
    }
  });
};

/**
 * Parse notification payload from backend into internal format.
 * @param {Object} payload - Firebase payload
 * @returns {Object|null}
 */
export const parseNotificationPayload = (payload) => {
  try {
    const { notification = {}, data = {} } = payload || {};
    // Normalize data type for consistent handling across the app
    if (!data.type && data.notification_type) {
      data.type = data.notification_type;
    }

    // Helper to parse sender from title/body
    function parseSender(title, body) {
      if (title && title.includes('from ')) {
        return title.split('from ')[1];
      }
      if (body && body.match(/from ([^ ]+)/)) {
        return body.match(/from ([^ ]+)/)[1];
      }
      if (body && body.match(/([A-Za-z0-9_]+) sent you/)) {
        return body.match(/([A-Za-z0-9_]+) sent you/)[1];
      }
      return data.sender || data.senderName || data.sender_name || 'Someone';
    }
    // Helper to parse gift name from body
    function parseGiftName(body) {
      if (body && body.match(/sent you a ([A-Za-z0-9 _-]+)/i)) {
        return body.match(/sent you a ([A-Za-z0-9 _-]+)/i)[1];
      }
      if (body && body.toLowerCase().includes('gift')) {
        return 'Gift';
      }
      return data.giftName || data.gift_name || 'Gift';
    }

    const sender = parseSender(notification.title || data.title, notification.body || data.body);
    const giftName = parseGiftName(notification.body || data.body);

    return {
      id: data.message_id || data.notification_id || Date.now(),
      title: notification.title || data.title || 'Notification',
      body: notification.body || data.body || '',
      data: {
        ...data,
        sender,
        giftName,
      },
      sender,
      giftName,
      read: false,
      timestamp: Date.now(),
      type: data.type || 'generic'
    };
  } catch (err) {
    console.error('Failed to parse notification payload', err);
    return null;
  }
};

/**
 * Handle click on a displayed notification.
 * Currently just logs the action; actual navigation should be handled by caller.
 */
export const handleNotificationClick = (notificationData) => {
  console.log('Notification clicked:', notificationData);
};

/**
 * Display a basic in-app toast for a foreground notification.
 * Uses react-hot-toast if available.
 */
export const showInAppNotification = (notification) => {
  try {
    if (notification?.title) {
      toast(notification.title, {
        description: notification.body
      });
    }
  } catch (err) {
    console.error('Failed to display in-app notification:', err);
  }
};
