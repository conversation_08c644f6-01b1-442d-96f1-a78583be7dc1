import { createApi } from './api';

// Create base API instance using shared configuration
const api = createApi({
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for detailed error logging
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
    });
    return Promise.reject(error);
  }
);

// In-flight request deduplication
const inflight = {
  getMissions: {}, // key: filter string
  getMissionById: {}, // key: mission id
};

// Mission API service
export const missionApi = {
  // Core mission endpoints
  getMissions: async (filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          queryParams.append(key, value);
        }
      });
      const key = queryParams.toString();
      if (inflight.getMissions[key]) {
        return inflight.getMissions[key];
      }
      inflight.getMissions[key] = api.get(`/missions?${key}`)
        .then(response => {
          inflight.getMissions[key] = null;
          return response;
        })
        .catch(error => {
          inflight.getMissions[key] = null;
          throw error;
        });
      return inflight.getMissions[key];
    } catch (error) {
      console.error('Error fetching missions:', error);
      throw error;
    }
  },

  getMissionById: async (id) => {
    try {
      if (inflight.getMissionById[id]) {
        return inflight.getMissionById[id];
      }
      inflight.getMissionById[id] = api.get(`/missions/${id}`)
        .then(response => {
          inflight.getMissionById[id] = null;
          return response;
        })
        .catch(error => {
          inflight.getMissionById[id] = null;
          throw error;
        });
      return inflight.getMissionById[id];
    } catch (error) {
      console.error(`Error fetching mission ${id}:`, error);
      throw error;
    }
  },

  createMission: async (missionData) => {
    try {
      const isFormData = missionData instanceof FormData;
      const containsFiles =
        isFormData ||
        (Array.isArray(missionData.images) &&
          missionData.images.some((img) => img instanceof File));

      if (containsFiles) {
        const formData = isFormData ? missionData : new FormData();

        if (!isFormData) {
          Object.entries(missionData).forEach(([key, value]) => {
            if (key === 'images') {
              value.forEach((file) => {
                if (file instanceof File) {
                  formData.append('images[]', file);
                }
              });
            } else if (value !== undefined && value !== null) {
              if (typeof value === 'boolean') {
                formData.append(key, value ? '1' : '0');
              } else {
                formData.append(key, value);
              }
            }
          });
        }

        return await api.post('/missions', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      return await api.post('/missions', missionData);
    } catch (error) {
      console.error('Error creating mission:', error);
      throw error;
    }
  },

  // Additional mission management endpoints
  updateMission: async (id, missionData) => {
    try {
      return await api.put(`/missions/${id}`, missionData);
    } catch (error) {
      console.error(`Error updating mission ${id}:`, error);
      throw error;
    }
  },

  deleteMission: async (id) => {
    try {
      return await api.delete(`/missions/${id}`);
    } catch (error) {
      console.error(`Error deleting mission ${id}:`, error);
      throw error;
    }
  },

  // Mission image management
  uploadMissionImage: async (missionId, imageFile) => {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      return await api.post(`/missions/${missionId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    } catch (error) {
      console.error(`Error uploading image for mission ${missionId}:`, error);
      throw error;
    }
  },

  /**
   * Deletes a specific image from a mission
   *
   * @param {string|number} missionId - The ID of the mission
   * @param {string|number} imageId - The ID of the image to delete
   * @returns {Promise<Object>} - The result of the API call
   * @throws {Error} - If the API call fails
   *
   * @example
   * // Delete an image from a mission
   * try {
   *   const result = await missionApi.deleteMissionImage(123, 456);
   *   console.log('Image deleted successfully');
   * } catch (error) {
   *   console.error('Failed to delete image:', error);
   * }
   */
  deleteMissionImage: async (missionId, imageId) => {
    try {
      return await api.delete(`/missions/${missionId}/images/${imageId}`);
    } catch (error) {
      console.error(`Error deleting image ${imageId} from mission ${missionId}:`, error);
      throw error;
    }
  },

  // Applicant management endpoints
  applyForMission: async (missionId, applicationData) => {
    try {
      return await api.post(`/missions/${missionId}/apply`, applicationData);
    } catch (error) {
      console.error(`Error applying for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Simplified apply function for quick applications
  applyToMission: async (missionId) => {
    try {
      // Default application with minimal data
      const defaultApplication = {
        message: 'I would like to join this mission!',
        availability: true
      };

      const response = await api.post(`/missions/${missionId}/apply`, defaultApplication);
      return response.data;
    } catch (error) {
      console.error(`Error applying to mission ${missionId}:`, error);
      throw error;
    }
  },

  // Get mission applicants
  getMissionApplicants: async (missionId) => {
    try {
      const response = await api.get(`/missions/${missionId}/applicants`);
      return response;
    } catch (error) {
      console.error(`Error fetching applicants for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Approve applicant
  approveApplicant: async (missionId, applicantId) => {
    try {
      const response = await api.post(`/missions/${missionId}/applicants/${applicantId}/approve`);
      return response.data;
    } catch (error) {
      console.error(`Error approving applicant ${applicantId} for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Reject applicant
  rejectApplicant: async (missionId, applicantId) => {
    try {
      const response = await api.post(`/missions/${missionId}/applicants/${applicantId}/reject`);
      return response.data;
    } catch (error) {
      console.error(`Error rejecting applicant ${applicantId} for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Withdraw application for the current user
  withdrawApplication: async (missionId, childId) => {
    try {
      const response = await api.post(
        `/missions/${missionId}/applicants/${childId}/withdraw`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error withdrawing application for child ${childId} on mission ${missionId}:`,
        error
      );
      throw error;
    }
  },

  // Leave mission (for approved participants)
  leaveMission: async (missionId) => {
    try {
      const response = await api.post(`/missions/${missionId}/leave`);
      return response.data;
    } catch (error) {
      console.error(`Error leaving mission ${missionId}:`, error);
      throw error;
    }
  },

  // Get user's application status for a mission
  getUserApplication: async (missionId) => {
    try {
      const response = await api.get(`/missions/${missionId}/my-application`);
      return response.data;
    } catch (error) {
      // If no application found, return null instead of throwing error
      if (error.response?.status === 404) {
        return null;
      }
      console.error(`Error getting user application for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Check if user can join mission
  canJoinMission: async (missionId) => {
    try {
      const response = await api.get(`/missions/${missionId}/can-join`);
      return response.data;
    } catch (error) {
      console.error(`Error checking if user can join mission ${missionId}:`, error);
      throw error;
    }
  },

  /**
   * Get user's mission applications
   * @returns {Promise<AxiosResponse>}
   */
  getUserApplications: async () => {
    try {
      // Get all missions first
      const missionsResponse = await api.get('/missions');
      const allMissions = missionsResponse.data.data;

      // Get all applications for the current user
      const applications = [];
      for (const mission of allMissions) {
        try {
          const response = await api.get(`/missions/${mission.id}/applicants`);
          const userApplication = response.data.find(app => app.user_id === parseInt(localStorage.getItem('user_id')));
          if (userApplication) {
            applications.push({
              ...userApplication,
              mission: mission
            });
          }
        } catch (error) {
          console.error(`Error fetching applicants for mission ${mission.id}:`, error);
          // Continue with next mission even if this one fails
          continue;
        }
      }

      return { data: applications };
    } catch (error) {
      console.error('Error fetching user applications:', error);
      throw error;
    }
  },

  // Mission execution endpoints
  startMission: async (missionId) => {
    try {
      return await api.post(`/missions/${missionId}/start`);
    } catch (error) {
      console.error(`Error starting mission ${missionId}:`, error);
      throw error;
    }
  },

  closeMission: async (missionId) => {
    try {
      return await api.post(`/missions/${missionId}/close`);
    } catch (error) {
      console.error(`Error closing mission ${missionId}:`, error);
      throw error;
    }
  },

  completeMission: async (missionId, completionData) => {
    try {
      return await api.post(`/missions/${missionId}/complete`, completionData);
    } catch (error) {
      console.error(`Error completing mission ${missionId}:`, error);
      throw error;
    }
  },

  // Get mission execution details
  getMissionExecutionDetails: async (missionId) => {
    try {
      return await api.get(`/missions/${missionId}/execution`);
    } catch (error) {
      console.error(`Error fetching mission execution details for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Update mission task
  updateMissionTask: async (missionId, taskId, taskData) => {
    try {
      return await api.put(`/missions/${missionId}/tasks/${taskId}`, taskData);
    } catch (error) {
      console.error(`Error updating task ${taskId} for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Send mission message
  sendMissionMessage: async (missionId, messageData) => {
    try {
      return await api.post(`/missions/${missionId}/messages`, messageData);
    } catch (error) {
      console.error(`Error sending message for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Get mission messages
  getMissionMessages: async (missionId) => {
    try {
      return await api.get(`/missions/${missionId}/messages`);
    } catch (error) {
      console.error(`Error fetching messages for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Payment endpoints
  processMissionPayment: async (missionId, paymentData) => {
    try {
      return await api.post(`/missions/${missionId}/payments`, paymentData);
    } catch (error) {
      console.error(`Error processing payment for mission ${missionId}:`, error);
      throw error;
    }
  },

  getMissionPaymentHistory: async (missionId) => {
    try {
      return await api.get(`/missions/${missionId}/payments`);
    } catch (error) {
      console.error(`Error fetching payment history for mission ${missionId}:`, error);
      throw error;
    }
  },

  // Add user statistics endpoint
  getUserStatistics: async () => {
    try {
      const response = await api.get('/missions/user/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching user mission statistics:', error);
      throw error;
    }
  },

  // Add user missions endpoint
  // Accepts an optional query string to filter by role and status
  // Example: getMyMissions('role=host&status=open')
  getMyMissions: async (query = '') => {
    try {
      const url = query
        ? `/missions/my-missions?${query}`
        : '/missions/my-missions';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching my missions:', error);
      throw error;
    }
  },

  // Bookmark a mission
  bookmarkMission: async (missionId) => {
    try {
      return await api.post(`/missions/${missionId}/bookmark`);
    } catch (error) {
      console.error(`Error bookmarking mission ${missionId}:`, error);
      throw error;
    }
  }
};

export default missionApi;
