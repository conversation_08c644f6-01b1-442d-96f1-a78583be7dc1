import api from './api';
import { cacheManager } from '../utils/cacheManager';

export const serviceConfigApi = {
  getCategories: async () => {
    const cacheKey = 'service-config-categories';
    const cached = cacheManager.get(cacheKey);
    if (cached) return cached;
    try {
      const res = await api.get('/service-configuration/categories');
      return cacheManager.set(cacheKey, res.data);
    } catch (error) {
      console.error('Error fetching service categories:', error);
      return [];
    }
  },
  getTypes: async (categoryId = null) => {
    const params = {};
    if (categoryId) params.category_id = categoryId;
    const cacheKey = 'service-config-types';
    const cached = cacheManager.get(cacheKey, params);
    if (cached) return cached;
    try {
      const res = await api.get('/service-configuration/types', { params });
      return cacheManager.set(cacheKey, res.data, params);
    } catch (error) {
      console.error('Error fetching service types:', error);
      return [];
    }
  },
  getStyles: async (typeId) => {
    try {
      const response = await api.get(`/service-configuration/styles?service_type_id=${typeId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching service styles:', error);
      throw error;
    }
  },
  getPricingOptionTypes: async () => {
    try {
      const response = await api.get('/service-configuration/pricing-option-types');
      return response.data;
    } catch (error) {
      console.error('Error fetching pricing option types:', error);
      throw error;
    }
  }
};

export default serviceConfigApi;
