import api from './api';

const emergencyContactService = {
  getContacts: async () => {
    try {
      const response = await api.get('/emergency-contacts');
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error fetching emergency contacts:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch emergency contacts'
      };
    }
  },
  getRelationships: async () => {
    try {
      const response = await api.get('/emergency-contact-relationships');
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error fetching relationships:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch relationships'
      };
    }
  },
  createContact: async (data) => {
    try {
      const response = await api.post('/emergency-contacts', data);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error creating contact:', error);
      let message = error.response?.data?.message || 'Failed to create contact';
      if (error.response?.status === 403) {
        message = 'You are not authorized to create contacts';
      }
      return { success: false, error: message };
    }
  },
  updateContact: async (id, data) => {
    try {
      const response = await api.put(`/emergency-contacts/${id}`, data);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error updating contact:', error);
      let message = error.response?.data?.message || 'Failed to update contact';
      if (error.response?.status === 404) {
        message = 'Contact not found';
      } else if (error.response?.status === 403) {
        message = 'You are not authorized to modify this contact';
      }
      return { success: false, error: message };
    }
  },
  deleteContact: async (id) => {
    try {
      const response = await api.delete(`/emergency-contacts/${id}`);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error deleting contact:', error);
      let message = error.response?.data?.message || 'Failed to delete contact';
      if (error.response?.status === 404) {
        message = 'Contact not found';
      } else if (error.response?.status === 403) {
        message = 'You are not authorized to delete this contact';
      }
      return { success: false, error: message };
    }
  },
  setDefaultContact: async (contact) => {
    try {
      const data = {
        relationship_id: contact.relationship?.id,
        contact_person_name: contact.contact_person_name,
        phone_number: contact.phone_number,
        is_default: true
      };
      const response = await api.put(`/emergency-contacts/${contact.id}`, data);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error setting default contact:', error);
      let message = error.response?.data?.message || 'Failed to set default contact';
      if (error.response?.status === 404) {
        message = 'Contact not found';
      } else if (error.response?.status === 403) {
        message = 'You are not authorized to modify this contact';
      }
      return { success: false, error: message };
    }
  }
};

export default emergencyContactService;
