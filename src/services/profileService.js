/**
 * Profile Service
 *
 * This service handles all API calls related to user profiles.
 */

import api from './api';

// Environment detection
const NODE_ENV = process.env.NODE_ENV;

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const profileCache = new Map();

/**
 * Profile Service
 */
const profileService = {
  /**
   * Get user profile
   *
   * @returns {Promise<Object>} Profile data
   */
  getProfile: async () => {
    try {
      const response = await api.get('/user/all-profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch profile'
      };
    }
  },

  /**
   * Get complete user profile with all related data
   * This endpoint returns all user data in a single request
   * 
   * @returns {Promise<Object>} Complete profile data
   */
  getCompleteProfile: async () => {
    try {
      // Check cache first
      const cachedData = profileCache.get('completeProfile');
      if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {
        console.log('Returning cached profile data');
        return cachedData.data;
      }

      const response = await api.get('/user/all-profile');
      const result = {
        success: true,
        data: response.data
      };

      // Cache the result
      profileCache.set('completeProfile', {
        data: result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error('Error fetching complete profile:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch complete profile'
      };
    }
  },

  /**
   * Get user biography
   *
   * @returns {Promise<Object>} Biography data
   */
  getBiography: async () => {

    try {
      const response = await api.get('/user/biography');
      return response.data;
    } catch (error) {
      console.error('Error fetching biography:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch biography'
      };
    }
  },

  /**
   * Get user statistics
   *
   * @returns {Promise<Object>} Statistics data
   */
  getStatistics: async () => {
    try {
      const response = await api.get('/user/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch statistics'
      };
    }
  },

  /**
   * Get user services
   *
   * @returns {Promise<Object>} Services data
   */
  getServices: async () => {
    try {
      const response = await api.get('/user/services');
      return response.data;
    } catch (error) {
      console.error('Error fetching services:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch services'
      };
    }
  },

  /**
   * Get user availability
   *
   * @returns {Promise<Object>} Availability data
   */
  getAvailability: async () => {
    try {
      const response = await api.get('/user/availability');
      return response.data;
    } catch (error) {
      console.error('Error fetching availability:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch availability'
      };
    }
  },

  /**
   * Get user media gallery
   *
   * @returns {Promise<Object>} Media gallery data
   */
  getMediaGallery: async () => {
    try {
      const response = await api.get('/user/media');
      const resp = response.data;

      if (resp && typeof resp === 'object') {
        if (Object.prototype.hasOwnProperty.call(resp, 'success')) {
          return resp;
        }

        return { success: true, data: resp };
      }

      return { success: true, data: resp };
    } catch (error) {
      console.error('Error fetching media gallery:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch media gallery'
      };
    }
  },

  /**
   * Update user profile
   *
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Update result
   */
  updateProfile: async (profileData, onUploadProgress = null) => {
    try {
      // Check if we have files to upload
      const hasFiles = profileData.profile_picture instanceof File || profileData.voice_note instanceof File;

      if (hasFiles) {
        // Use multipart/form-data with PUT when files are present
        const formData = new FormData();

        // Add all profile fields
        Object.keys(profileData).forEach(key => {
          const value = profileData[key];
          if (value !== null && value !== undefined) {
            if (key === 'height' || key === 'weight') {
              // Handle numeric fields - send if valid number or empty string to clear
              const stringValue = value.toString().trim();
              if (stringValue === '' || (!isNaN(stringValue) && parseFloat(stringValue) >= 0)) {
                formData.append(key, stringValue);
              }
            } else if (key === 'biography') {
              // Always send biography, even if empty
              formData.append(key, value.toString());
            } else if (value instanceof File) {
              // Handle file uploads
              formData.append(key, value);
            } else if (typeof value === 'boolean') {
              // Handle boolean values
              formData.append(key, value ? '1' : '0');
            } else if (value !== '') {
              // Handle other non-empty values
              formData.append(key, value.toString());
            }
          }
        });

        for (let [key, value] of formData.entries()) {
          console.log(`${key}:`, value);
        }
        // Laravel does not handle file uploads properly via PUT, so
        // we use POST with a _method override. This ensures files like
        // profile pictures are processed correctly by the backend.
        const response = await api.post('/user/profile', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          params: { _method: 'PUT' },
          onUploadProgress: onUploadProgress
            ? (evt) => {
                const percent = Math.round((evt.loaded * 100) / evt.total);
                onUploadProgress(percent);
              }
            : undefined
        });

        return {
          success: true,
          data: response.data
        };
      } else {

        const response = await api.put('/user/profile', profileData, {
          onUploadProgress: onUploadProgress
            ? (evt) => {
                const percent = Math.round((evt.loaded * 100) / evt.total);
                onUploadProgress(percent);
              }
            : undefined
        });
        return {
          success: true,
          data: response.data
        };
      }
    } catch (error) {
      console.error('Error updating profile:', error);

      // Handle validation errors
      if (error.response?.status === 422 && error.response?.data?.errors) {
        return {
          success: false,
          validationErrors: error.response.data.errors,
          error: error.response.data.message || 'Validation failed'
        };
      }

      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update profile'
      };
    }
  },

  /**
   * Update user biography
   *
   * @param {Object} biographyData - Updated biography data
   * @returns {Promise<Object>} Update result
   */
  updateBiography: async (biographyData) => {
    try {
      const response = await api.put('/user/biography', biographyData);
      // Always return a success property for frontend compatibility
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating biography:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update biography'
      };
    }
  },

  /**
   * Validate media file before upload
   *
   * @param {File} file - File to validate
   * @param {string} type - Media type (image, video)
   * @returns {Object} Validation result
   */
  validateMediaFile: (file, type = 'image') => {
    const errors = [];

    if (!file) {
      errors.push('No file selected');
      return { isValid: false, errors };
    }

    if (type === 'video') {
      // Video validation - be more specific about MP4 requirements
      const validVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/flv', 'video/wmv', 'video/3gp', 'video/mkv'];
      const maxVideoSize = 50 * 1024 * 1024; // 50MB

      if (!validVideoTypes.includes(file.type)) {
        errors.push('Invalid video format. Supported formats: MP4, MOV, AVI, FLV, WMV, 3GP, MKV');
      }

      if (file.size > maxVideoSize) {
        errors.push('Video file size must be less than 50MB');
      }

      // Additional check for MP4 files - recommend MP4 for best compatibility
      if (file.type !== 'video/mp4') {
        console.warn('Non-MP4 video detected. MP4 format is recommended for best compatibility with server processing.');
      }
    } else {
      // Image validation
      const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif', 'image/webp'];
      const maxImageSize = 20 * 1024 * 1024; // 20MB

      if (!validImageTypes.includes(file.type)) {
        errors.push('Invalid image format. Supported formats: JPEG, JPG, PNG, HEIC, HEIF, WebP');
      }

      if (file.size > maxImageSize) {
        errors.push('Image file size must be less than 20MB');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Validate video duration and properties with FFmpeg compatibility checks
   *
   * @param {File} file - Video file to validate
   * @returns {Promise<Object>} Validation result
   */
  validateVideoDuration: (file) => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);

        const errors = [];
        const warnings = [];

        // Check duration (max 10 seconds)
        if (video.duration > 10) {
          errors.push('Video duration must be 10 seconds or less');
        }

        // Check if video has valid dimensions
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          errors.push('Video file appears to be corrupted or has no video track');
        }

        // Critical: Check for FFmpeg-problematic resolutions
        // Backend FFmpeg command uses 'scale=1280:-2' which can fail with odd dimensions
        if (video.videoWidth > 1280) {
          errors.push(`Video width ${video.videoWidth}px is too high. Maximum supported width is 1280px.`);
        }

        if (video.videoHeight > 720) {
          errors.push(`Video height ${video.videoHeight}px is too high. Maximum supported height is 720px.`);
        }

        // Check for odd dimensions that cause FFmpeg scale issues
        if (video.videoWidth % 2 !== 0) {
          warnings.push(`Video width ${video.videoWidth}px is odd. Even dimensions work better with server processing.`);
        }

        if (video.videoHeight % 2 !== 0) {
          warnings.push(`Video height ${video.videoHeight}px is odd. Even dimensions work better with server processing.`);
        }

        // Check for very high resolution that might cause memory issues
        const totalPixels = video.videoWidth * video.videoHeight;
        if (totalPixels > 1280 * 720) {
          errors.push(`Video resolution ${video.videoWidth}x${video.videoHeight} is too high. Use 1280x720 or lower.`);
        }

        // Check for very short videos (less than 0.5 seconds)
        if (video.duration < 0.5) {
          errors.push('Video is too short. Minimum duration is 0.5 seconds.');
        }

        // Check for very long videos that might timeout
        if (video.duration > 8) {
          warnings.push('Video is close to the 10-second limit. Consider shortening to 8 seconds for reliable processing.');
        }

        console.log('Video metadata analysis:', {
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight,
          totalPixels: totalPixels,
          aspectRatio: (video.videoWidth / video.videoHeight).toFixed(2),
          readyState: video.readyState,
          warnings: warnings
        });

        resolve({
          isValid: errors.length === 0,
          errors,
          warnings,
          metadata: {
            duration: video.duration,
            width: video.videoWidth,
            height: video.videoHeight,
            totalPixels: totalPixels,
            aspectRatio: video.videoWidth / video.videoHeight
          }
        });
      };

      video.onerror = (e) => {
        window.URL.revokeObjectURL(video.src);
        console.error('Video validation error:', e);
        resolve({
          isValid: false,
          errors: ['Video file cannot be read by browser. This usually indicates codec incompatibility or file corruption. Try converting with: ffmpeg -i input.mp4 -c:v libx264 -c:a aac -vf "scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2" output.mp4']
        });
      };

      // Set timeout to prevent hanging
      setTimeout(() => {
        if (video.readyState === 0) {
          window.URL.revokeObjectURL(video.src);
          resolve({
            isValid: false,
            errors: ['Video file could not be loaded within 10 seconds. File may be corrupted or in an unsupported format.']
          });
        }
      }, 10000); // 10 second timeout

      video.src = URL.createObjectURL(file);
    });
  },

  /**
   * Get video optimization recommendations based on FFmpeg compatibility
   *
   * @param {File} file - Video file to analyze
   * @returns {Promise<Object>} Optimization recommendations
   */
  getVideoOptimizationTips: async (file) => {
    try {
      const metadata = await profileService.validateVideoDuration(file);
      const recommendations = [];
      const criticalIssues = [];

      if (metadata.metadata) {
        const { duration, width, height, totalPixels } = metadata.metadata;

        // Critical FFmpeg compatibility issues
        if (width > 1280) {
          criticalIssues.push(`❌ Width ${width}px exceeds server limit of 1280px`);
        }

        if (height > 720) {
          criticalIssues.push(`❌ Height ${height}px exceeds server limit of 720px`);
        }

        if (width % 2 !== 0 || height % 2 !== 0) {
          criticalIssues.push(`❌ Odd dimensions (${width}x${height}) cause FFmpeg scale filter failures`);
        }

        if (totalPixels > 1280 * 720) {
          criticalIssues.push(`❌ Total resolution ${width}x${height} exceeds server processing capacity`);
        }

        // Duration recommendations
        if (duration > 10) {
          criticalIssues.push(`❌ Duration ${duration.toFixed(1)}s exceeds 10-second server limit`);
        } else if (duration > 8) {
          recommendations.push(`⚠️ Duration ${duration.toFixed(1)}s is close to limit. Consider 8s for safety`);
        }

        // Optimal settings recommendations
        if (width !== 1280 || height !== 720) {
          recommendations.push(`🎯 Use exactly 1280x720 for optimal server compatibility`);
        }

        // Format recommendations
        if (file.type !== 'video/mp4') {
          criticalIssues.push(`❌ Format ${file.type} not optimal. Use MP4 with H.264/AAC`);
        }

        // Size recommendations
        if (file.size > 20 * 1024 * 1024) { // 20MB
          recommendations.push(`📦 Large file size: ${(file.size / 1024 / 1024).toFixed(1)}MB. Consider compression`);
        }

        // Provide exact FFmpeg command for optimal conversion
        if (criticalIssues.length > 0 || recommendations.length > 0) {
          recommendations.push(`🔧 Optimal conversion command:\nffmpeg -i input.mp4 -c:v libx264 -c:a aac -vf "scale=1280:720" -r 30 -t 8 output.mp4`);
        }
      }

      return {
        success: true,
        recommendations,
        criticalIssues,
        hasIssues: recommendations.length > 0 || criticalIssues.length > 0,
        canUpload: criticalIssues.length === 0
      };
    } catch (error) {
      return {
        success: false,
        error: 'Could not analyze video file'
      };
    }
  },

  /**
   * Check if video is likely to pass server FFmpeg processing
   *
   * @param {File} file - Video file to check
   * @returns {Promise<Object>} Compatibility assessment
   */
  checkFFmpegCompatibility: async (file) => {
    try {
      const validation = await profileService.validateVideoDuration(file);

      if (!validation.isValid) {
        return {
          compatible: false,
          issues: validation.errors,
          recommendation: 'Fix validation errors before upload'
        };
      }

      const { width, height, duration } = validation.metadata;
      const issues = [];

      // Check exact server FFmpeg command compatibility
      // Backend uses: scale=1280:-2 which can fail with certain inputs

      if (width > 1280) {
        issues.push(`Width ${width}px > 1280px server limit`);
      }

      if (height > 720) {
        issues.push(`Height ${height}px > 720px server limit`);
      }

      // Critical: Check for dimensions that cause scale=-2 issues
      if (width % 2 !== 0) {
        issues.push(`Odd width ${width}px causes FFmpeg scale filter errors`);
      }

      if (height % 2 !== 0) {
        issues.push(`Odd height ${height}px causes FFmpeg scale filter errors`);
      }

      if (duration > 10) {
        issues.push(`Duration ${duration.toFixed(1)}s exceeds 10s server limit`);
      }

      // Check for aspect ratios that might cause issues
      const aspectRatio = width / height;
      if (aspectRatio < 0.5 || aspectRatio > 3.0) {
        issues.push(`Extreme aspect ratio ${aspectRatio.toFixed(2)} may cause processing issues`);
      }

      return {
        compatible: issues.length === 0,
        issues,
        recommendation: issues.length === 0
          ? 'Video should upload successfully'
          : 'Convert video to fix compatibility issues'
      };
    } catch (error) {
      return {
        compatible: false,
        issues: ['Could not analyze video compatibility'],
        recommendation: 'Try a different video file'
      };
    }
  },

  /**
   * Upload media to gallery
   *
   * @param {File} file - File to upload
   * @param {string} type - Media type (image, video)
   * @returns {Promise<Object>} Upload result
   */
  uploadMedia: async (file, type = 'image') => {
    try {
      const formData = new FormData();

      // Use correct field names based on backend API requirements
      if (type === 'video') {
        // For videos, use 'video' field name
        formData.append('video', file);
      } else {
        // For images, use 'photos[]' field name (array format)
        formData.append('photos[]', file);
      }

      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value instanceof File ? `File(${value.name}, ${value.size} bytes)` : value);
      }

      const response = await api.post('/user/media', formData);

      console.log('Media upload response:', response.data);

      const resp = response.data;
      if (resp && typeof resp === 'object') {
        // Normalise to { success, data }
        if (Object.prototype.hasOwnProperty.call(resp, 'success')) {
          return resp;
        }

        if (resp.data || resp.id || resp.url) {
          return { success: true, data: resp.data || resp };
        }
      }

      // Fallback: assume success and return entire payload
      return { success: true, data: resp };
    } catch (error) {
      console.error('Error uploading media:', error);
      console.error('Error response:', error.response?.data);

      // Enhanced error handling for video upload failures
      let errorMessage = 'Failed to upload media';

      if (error.response?.data) {
        const errorData = error.response.data;

        if (errorData.message) {
          errorMessage = errorData.message;

          // Provide specific guidance for video processing failures based on actual FFmpeg issues
          if (type === 'video' && (errorData.message.includes('video failed to upload') || errorData.message.includes('Failed to process profile video'))) {
            errorMessage = '🎥 Video Processing Failed on Server\n\n' +
                          '❌ Root Cause: Server FFmpeg processing error\n\n' +
                          '🔧 This is likely due to:\n' +
                          '• FFmpeg scale command incompatibility with your video dimensions\n' +
                          '• Video codec not supported by server FFmpeg installation\n' +
                          '• Server resource constraints during processing\n' +
                          '• Odd pixel dimensions causing scale filter issues\n\n' +
                          '✅ Recommended Solutions:\n' +
                          '1. Use EXACTLY 1280x720 resolution (no higher, even dimensions)\n' +
                          '2. Convert with: ffmpeg -i input.mp4 -c:v libx264 -c:a aac -vf "scale=1280:720" -r 30 output.mp4\n' +
                          '3. Ensure video has both video AND audio tracks\n' +
                          '4. Keep duration under 8 seconds (not 10) for safety\n' +
                          '5. Use 30fps frame rate\n' +
                          '6. Try a different MP4 file if issue persists\n\n' +
                          '⚠️ Note: This is a server-side processing limitation, not a file format issue.';
          }
        } else if (errorData.errors) {
          // Handle Laravel validation errors
          const errorMessages = [];
          Object.keys(errorData.errors).forEach(field => {
            if (Array.isArray(errorData.errors[field])) {
              errorMessages.push(...errorData.errors[field]);
            } else {
              errorMessages.push(errorData.errors[field]);
            }
          });
          errorMessage = errorMessages.join(', ');
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  },

  /**
   * Get third-party access settings
   *
   * @returns {Promise<Object>} Third-party access settings
   */
  getThirdPartyAccess: async () => {
    try {
      const response = await api.get('/user/allow-3rd-party-access');
      return {
        success: true,
        data: {
          enabled: response.data,
          connected_apps: []
        }
      };
    } catch (error) {
      console.error('Error fetching third-party access settings:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch third-party access settings'
      };
    }
  },

  /**
   * Update third-party access settings
   *
   * @param {boolean} enabled - Whether to enable or disable third-party access
   * @returns {Promise<Object>} Update result
   */
  updateThirdPartyAccess: async (enabled) => {
    try {
      const response = await api.put('/user/allow-3rd-party-access', { allow_3rd_party_access: enabled });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating third-party access settings:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update third-party access settings'
      };
    }
  },

  /**
   * Get user voice note
   *
   * @returns {Promise<Object>} Voice note data
   */
  getVoiceNote: async () => {
    try {
      const response = await api.get('/user/voice-note');
      return response.data;
    } catch (error) {
      // If 404, it means the user doesn't have a voice note yet
      if (error.response && error.response.status === 404) {
        return {
          success: true,
          data: null
        };
      }

      console.error('Error fetching voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch voice note'
      };
    }
  },

  /**
   * Upload voice note
   *
   * @param {Blob} audioBlob - Audio blob to upload
   * @returns {Promise<Object>} Upload result
   */
  uploadVoiceNote: async (audioBlob) => {

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);

      const response = await api.post('/user/voice-note', formData);

      return response.data;
    } catch (error) {
      console.error('Error uploading voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to upload voice note'
      };
    }
  },

  /**
   * Delete voice note
   *
   * @returns {Promise<Object>} Delete result
   */
  deleteVoiceNote: async () => {
    try {
      const response = await api.delete('/user/voice-note');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error deleting voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete voice note'
      };
    }
  },

  /**
   * Delete media from gallery
   *
   * @param {number} mediaId - ID of the media to delete
   * @returns {Promise<Object>} Delete result
   */
  deleteMedia: async (mediaId) => {

    try {
      const response = await api.delete('/user/media', { data: { id: mediaId } });
      const resp = response.data;

      if (resp && typeof resp === 'object') {
        if (Object.prototype.hasOwnProperty.call(resp, 'success')) {
          return resp;
        }

        return { success: true, data: resp };
      }

      return { success: true, data: resp };
    } catch (error) {
      console.error('Error deleting media:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete media'
      };
    }
  },

  /**
   * Reorder media in gallery
   *
   * @param {{photos: Array<{order: number, new_order: number}>}} payload - Object with photos array for reordering
   * @returns {Promise<Object>} Reorder result
   */
  reorderMedia: async (payload) => {
    try {
      // Validate input
      if (!payload || !Array.isArray(payload.photos) || payload.photos.length === 0) {
        throw new Error('Invalid media order data');
      }

      // Ensure each item has required fields
      const isValidOrder = payload.photos.every(item =>
        typeof item === 'object' &&
        typeof item.order === 'number' &&
        typeof item.new_order === 'number'
      );

      if (!isValidOrder) {
        throw new Error('Invalid media order format');
      }

      const response = await api.put('/user/media/reorder', payload);
      const resp = response.data;

      if (resp && typeof resp === 'object') {
        if (Object.prototype.hasOwnProperty.call(resp, 'success')) {
          return resp;
        }
        return { success: true, data: resp };
      }
      return { success: true, data: resp };
    } catch (error) {
      console.error('Error reordering media:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to reorder media'
      };
    }
  },

  /**
   * Set media as cover (profile cover photo or video)
   *
   * @param {number} mediaId - ID of the media to set as cover
   * @param {string} coverType - Type of cover ('photo' or 'video')
   * @returns {Promise<Object>} Update result
   */
  setMediaAsCover: async (mediaId, coverType) => {
    try {
      const response = await api.post('/user/profile-picture', {
        media_id: mediaId,
        cover_type: coverType
      });

      const resp = response.data;

      if (resp && typeof resp === 'object') {
        if (Object.prototype.hasOwnProperty.call(resp, 'success')) {
          return resp;
        }

        return { success: true, data: resp };
      }

      return { success: true, data: resp };
    } catch (error) {
      console.error('Error setting media as cover:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to set media as cover'
      };
    }
  },

  /**
   * Get user experience history
   *
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Number of items per page
   * @returns {Promise<Object>} Experience history data
   */
  getExperienceHistory: async (options = { page: 1, limit: 10 }) => {
    try {
      const response = await api.get('/user/experience/history', {
        params: {
          page: options.page,
          limit: options.limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching experience history:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch experience history'
      };
    }
  },

  /**
   * Get user level information
   *
   * @returns {Promise<Object>} Level information
   */
  getLevelInfo: async () => {

    try {
      const response = await api.get('/user/level');
      return response.data;
    } catch (error) {
      console.error('Error fetching level information:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch level information'
      };
    }
  },

  /**
   * Get user race information
   *
   * @returns {Promise<Object>} Race information
   */
  getRaceInfo: async () => {
    try {
      const response = await api.get('/races');
      return response.data;
    } catch (error) {
      console.error('Error fetching race information:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch race information'
      };
    }
  },

  /**
   * Get all races from the backoffice
   *
   * @returns {Promise<Object>} Races data
   */
  getRaces: async () => {
    try {
      // This endpoint doesn't require authentication as per the API docs
      const response = await api.get('/races');

      // The response should be an array of race objects with id, name, and description
      if (Array.isArray(response.data)) {
        return {
          success: true,
          data: response.data
        };
      } else {
        // If the response is not in the expected format, return the data as is
        return {
          success: true,
          data: response.data
        };
      }
    } catch (error) {
      console.error('Error fetching races from backoffice:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch races from backoffice'
      };
    }
  },

  /**
   * Update user race
   *
   * @param {number} raceId - ID of the race to select
   * @returns {Promise<Object>} Update result
   */
  updateRace: async (raceId) => {
    try {
      // Update the user's profile with the selected race
      const response = await api.put('/user/profile', {
        race_id: raceId
      });

      // If the update is successful, return the updated profile data
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating user race:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update user race'
      };
    }
  },

  /**
   * Get user gifts
   *
   * @returns {Promise<Object>} User gifts data
   */
  getUserGifts: async () => {
    try {
      const response = await api.get('/user/gifts');
      return {
        success: true,
        data: response.data.user_gifts || []
      };
    } catch (error) {
      console.error('Error fetching user gifts:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch user gifts'
      };
    }
  },

  /**
   * Clear the profile cache
   */
  clearProfileCache: () => {
    profileCache.clear();
  },

  getLikedPosts: async () => {
    try {
      const response = await api.get('/social-posts/user/liked');
      return response.data;
    } catch (error) {
      console.error('Error fetching liked posts:', error);
      throw error;
    }
  }
};

export default profileService;
