import api from './api';
import errorHandlingService from './errorHandlingService';

// Enhanced error handling wrapper
const withErrorHandling = async (operation, context = 'bank_account') => {
  try {
    return await operation();
  } catch (error) {
    const processedError = errorHandlingService.processError(error, context, {
      operation: operation.name || 'unknown',
      timestamp: Date.now()
    });
    throw processedError;
  }
};

// Real API implementations only
const bankAccountService = {
  getBankAccounts: async () => {
    return withErrorHandling(async () => {
      const response = await api.get('/user/bank-accounts');
      return response;
    }, 'get_bank_accounts');
  },

  getBankAccount: async (id) => {
    return withErrorHandling(async () => {
      const response = await api.get(`/user/bank-accounts/${id}`);
      return response;
    }, 'get_bank_account');
  },

  getMalaysianBanks: async () => {
    return withErrorHandling(async () => {
      const response = await api.get('/malaysian-banks');
      return response;
    }, 'get_malaysian_banks');
  },

  addBankAccount: async (accountData) => {
    return withErrorHandling(async () => {
      const response = await api.post('/user/bank-accounts', accountData);
      return response;
    }, 'add_bank_account');
  },

  updateBankAccount: async (id, accountData) => {
    return withErrorHandling(async () => {
      const response = await api.put(`/user/bank-accounts/${id}`, accountData);
      return response;
    }, 'update_bank_account');
  },

  deleteBankAccount: async (id) => {
    return withErrorHandling(async () => {
      const response = await api.delete(`/user/bank-accounts/${id}`);
      return response;
    }, 'delete_bank_account');
  },

  setPrimaryBankAccount: async (id) => {
    return withErrorHandling(async () => {
      const response = await api.post(`/user/bank-accounts/${id}/set-primary`);
      return response;
    }, 'set_primary_bank_account');
  },

  // Bank account verification (future implementation)
  requestVerification: async (id) => {
    return withErrorHandling(async () => {
      const response = await api.post(`/user/bank-accounts/${id}/request-verification`);
      return response;
    }, 'request_verification');
  },

  checkVerificationStatus: async (id) => {
    return withErrorHandling(async () => {
      const response = await api.get(`/user/bank-accounts/${id}/verification-status`);
      return response;
    }, 'check_verification_status');
  },

  getDuitNowOptions: async () => {
    return withErrorHandling(async () => {
      const response = await api.get('/withdrawals/duitnow-options');
      return response;
    }, 'get_duitnow_options');
  },
};

export default bankAccountService;