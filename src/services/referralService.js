import api from './api';

/**
 * Referral Service
 * Handles all referral-related API calls and statistics
 */
class ReferralService {
    /**
     * Get referred users for the authenticated user
     * @returns {Promise<Object>} Response with referred users data
     */
    async getReferredUsers() {
        try {
            const response = await api.get('/referrals');
            return {
                success: true,
                data: response.data.referred_users || []
            };
        } catch (error) {
            console.error('Error fetching referred users:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to fetch referred users'
            };
        }
    }

    /**
     * Get referral statistics for the authenticated user
     * This calculates stats from the referred users data
     * @returns {Promise<Object>} Response with referral statistics
     */
    async getReferralStats() {
        try {
            const response = await this.getReferredUsers();
            
            if (!response.success) {
                return response;
            }

            // Ensure we always work with an array to avoid runtime errors
            const referredUsers = Array.isArray(response.data) ? response.data : [];
            
            // Calculate statistics
            const stats = {
                totalReferrals: referredUsers.length,
                activeReferrals: referredUsers.filter(user => 
                    user.is_verified && user.profile_completed
                ).length,
                pointsEarned: 0, // This would need to be calculated from point transactions
                recentActivity: referredUsers
                    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                    .slice(0, 5)
                    .map(user => ({
                        id: user.id,
                        name: user.nickname || user.name,
                        date: user.created_at,
                        status: user.is_verified ? 'verified' : 'pending',
                        profileCompleted: user.profile_completed || false
                    }))
            };

            return {
                success: true,
                data: stats
            };
        } catch (error) {
            console.error('Error calculating referral stats:', error);
            return {
                success: false,
                error: 'Failed to calculate referral statistics'
            };
        }
    }

    /**
     * Search for referred users by referral code
     * @param {string} referralCode - The referral code to search for
     * @returns {Promise<Object>} Response with referred users data
     */
    async searchReferredUsersByCode(referralCode) {
        try {
            const response = await api.post('/referrals/search', {
                referral_code: referralCode
            });
            return {
                success: true,
                data: response.data.referred_users || []
            };
        } catch (error) {
            console.error('Error searching referred users:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to search referred users'
            };
        }
    }

    /**
     * Search for referred users by phone number
     * @param {string} phoneNumber - The phone number to search for
     * @returns {Promise<Object>} Response with referred users data
     */
    async searchReferredUsersByPhone(phoneNumber) {
        try {
            const response = await api.post('/referrals/search', {
                phone_number: phoneNumber
            });
            return {
                success: true,
                data: response.data.referred_users || []
            };
        } catch (error) {
            console.error('Error searching referred users by phone:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Failed to search referred users'
            };
        }
    }
}

export default new ReferralService(); 