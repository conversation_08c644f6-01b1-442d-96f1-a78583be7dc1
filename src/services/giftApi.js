import { createApi } from './api';

const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// Debug flag for diagnostic logging
const DEBUG_ENABLED = true;

// Diagnostic logging function
const debugLog = (message, data) => {
  if (!DEBUG_ENABLED) return;
  
  console.group(`🎁 Gift API: ${message}`);
  if (data) {
    console.log('Data:', data);
    
    // Log object structure for analysis
    if (typeof data === 'object' && data !== null) {
      console.log('Object structure:');
      const structure = {};
      
      Object.keys(data).forEach(key => {
        const value = data[key];
        if (Array.isArray(value) && value.length > 0) {
          structure[key] = `Array[${value.length}]: ${typeof value[0]} with properties [${Object.keys(value[0]).join(', ')}]`;
        } else {
          structure[key] = typeof value;
        }
      });
      
      console.table(structure);
    }
  }
  console.groupEnd();
};

// Create axios instance with default config
const api = createApi({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add authorization header to all requests
api.interceptors.request.use((config) => {
  // Log outgoing requests
  if (DEBUG_ENABLED) {
    debugLog(`Request: ${config.method.toUpperCase()} ${config.url}`, {
      headers: config.headers,
      params: config.params,
      data: config.data
    });
  }
  
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Log successful responses
    if (DEBUG_ENABLED) {
      debugLog(`Response: ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });
    }
    return response;
  },
  (error) => {
    const errorResponse = {
      success: false,
      message: 'An error occurred',
      error: 'Unknown error'
    };

    if (error.response) {
      errorResponse.message = error.response.data.message || 'Request failed';
      errorResponse.error = error.response.data.error || error.response.statusText;
      
      if (error.response.data.errors) {
        errorResponse.errors = error.response.data.errors;
      }
      
      // Log API error responses
      debugLog(`API Error Response: ${error.response.status}`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        url: error.config.url,
        method: error.config.method
      });
    } else if (error.request) {
      errorResponse.message = 'No response from server';
      errorResponse.error = 'Network error';
      
      // Log network errors
      debugLog('Network Error (No response)', {
        request: error.request,
        url: error.config?.url,
        method: error.config?.method
      });
    } else {
      errorResponse.message = 'Request configuration error';
      errorResponse.error = error.message;
      
      // Log configuration errors
      debugLog('Request Error', {
        message: error.message,
        config: error.config
      });
    }

    return Promise.reject(errorResponse);
  }
);

export const giftApi = {
  /**
   * Get the current wallet balance for the authenticated user
   * @returns {Promise<Object>} Wallet balance data
   */
  getWalletBalance: async () => {
    debugLog('Fetching wallet balance');
    try {
      const response = await api.get('/credits/balance');
      debugLog('Fetched wallet balance', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      throw error;
    }
  },

  /**
   * Fetch available gift items
   * @returns {Promise<Object>} Response with gifts
   */
  getGiftItems: async () => {
    debugLog('Fetching gift items');
    try {
      const response = await api.get('/gifts');
      debugLog('Fetched gift items', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching gift items:', error);
      throw error;
    }
  },
  /**
   * Get all available virtual gifts
   * @returns {Promise<Object>} Response with available gifts
   */
  getAvailableGifts: async () => {
    debugLog('Fetching available gifts');
    try {
      const response = await api.get('/gifts');
      debugLog('Fetched available gifts', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching available gifts:', error);
      throw error;
    }
  },

  /**
   * Send a gift to a user in a conversation
   * @param {number} conversation_id - ID of the conversation
   * @param {number} gift_id - ID of the gift to send
   * @param {string} message - Optional message to accompany the gift
   * @returns {Promise<Object>} Response data
   */
  sendGift: async (conversation_id, gift_id, message = '') => {
    debugLog('Sending gift', { conversation_id, gift_id, message });
    try {
      const response = await api.post('/gifts/send', {
        conversation_id,
        gift_id,
        message
      });
      debugLog('Gift sent successfully', response.data);
      return response.data;
    } catch (error) {
      console.error('Error sending gift:', error);
      throw error;
    }
  },

  /**
   * Get gift history for the current user
   * @param {number} page - Page number for pagination (default: 1)
   * @param {number} per_page - Items per page (default: 20)
   * @returns {Promise<Object>} Response with gift history
   */
  getGiftHistory: async (page = 1, per_page = 20) => {
    debugLog('Fetching gift history', { page, per_page });
    try {
      const response = await api.get('/gifts/history', {
        params: { page, per_page }
      });
      debugLog('Fetched gift history', {
        total: response.data.total,
        count: response.data.gifts?.length,
        sample: response.data.gifts?.slice(0, 2)
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching gift history:', error);
      throw error;
    }
  }
}; 