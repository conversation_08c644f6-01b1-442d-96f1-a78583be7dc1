import { createApi } from './api';

// Create API instance with proper configuration
const api = createApi({
  timeout: 60000, // 60 seconds for file uploads
});

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('E-KYC API Error:', error);
    return Promise.reject(error);
  }
);

/**
 * E-KYC Service
 *
 * Provides integration with backend E-KYC endpoints for identity verification.
 * Supports both Malaysian IC and foreign passport verification.
 */
const ekycService = {
  // Cache configuration
  cache: {
    verificationStatus: null,
    lastStatusUpdate: null,
    CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  },

  /**
   * Check if cache is valid
   */
  isCacheValid: function() {
    if (!this.cache.verificationStatus || !this.cache.lastStatusUpdate) {
      return false;
    }
    return Date.now() - this.cache.lastStatusUpdate < this.cache.CACHE_DURATION;
  },

  /**
   * Clear verification status cache
   */
  clearCache: function() {
    this.cache.verificationStatus = null;
    this.cache.lastStatusUpdate = null;
  },

  /**
   * Verify Malaysian citizen identity using IC documents
   *
   * @param {FormData} formData - Form data containing:
   *   - ic_front: File (IC front image)
   *   - ic_back: File (IC back image)
   *   - selfie_photo: File (selfie image)
   *   - verification_method: string (optional, defaults to 'manual')
   * @returns {Promise} API response
   */
  verifyMalaysian: async (formData) => {
    try {
      const response = await api.post('/ekyc/malaysian', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Malaysian E-KYC verification failed:', error);
      throw error;
    }
  },

  /**
   * Verify foreigner identity using passport documents
   *
   * @param {FormData} formData - Form data containing:
   *   - full_name: string
   *   - passport_number: string
   *   - country: string
   *   - passport_photo: File (passport image)
   *   - selfie_photo: File (selfie image)
   *   - verification_method: string (optional, defaults to 'manual')
   * @returns {Promise} API response
   */
  verifyForeigner: async (formData) => {
    try {
      const response = await api.post('/ekyc/foreigner', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Foreigner E-KYC verification failed:', error);
      throw error;
    }
  },

  /**
   * Get user's current E-KYC verification status
   *
   * @param {string} verificationMethod - Optional verification method filter
   * @param {boolean} forceRefresh - Force refresh the cache
   * @returns {Promise} API response with verification status
   */
  getVerificationStatus: async (verificationMethod = null) => {
    try {
      const params = verificationMethod ? { verification_method: verificationMethod } : {};
      const response = await api.get('/ekyc/status', { params });
      
      // Validate response
      if (!response || !response.data) {
        throw new Error('Invalid response from E-KYC API');
      }

      // Ensure response has required fields
      const status = {
        is_verified: response.data.is_verified || false,
        verified_at: response.data.verified_at || null,
        status: response.data.status || 'not_submitted',
        ...response.data
      };

      return { data: status };
    } catch (error) {
      console.error('Failed to fetch E-KYC verification status:', error);
      throw error;
    }
  },

  /**
   * Get user's E-KYC verification history
   *
   * @returns {Promise} API response with verification history
   */
  getVerificationHistory: async () => {
    try {
      const response = await api.get('/ekyc/history');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch E-KYC verification history:', error);
      throw error;
    }
  },

  // Note: getAvailableMethods() endpoint not implemented yet in backend

  /**
   * Helper method to determine document type from user selection
   *
   * @param {string} documentType - Document type from UI
   * @returns {string} Verification type ('malaysian' or 'foreigner')
   */
  getVerificationType: (documentType) => {
    const malaysianTypes = ['malaysian_ic', 'ic', 'mykad'];
    const foreignerTypes = ['passport', 'foreign_passport'];

    if (malaysianTypes.includes(documentType?.toLowerCase())) {
      return 'malaysian';
    } else if (foreignerTypes.includes(documentType?.toLowerCase())) {
      return 'foreigner';
    }

    // Default to Malaysian if unclear
    return 'malaysian';
  },

  /**
   * Helper method to prepare FormData for Malaysian verification
   *
   * @param {Object} documents - Document files
   * @param {File} selfie - Selfie file
   * @param {Object} userInfo - User information (full_name, ic_number)
   * @param {Object} metadata - Additional metadata
   * @returns {FormData} Prepared form data
   */
  prepareMalaysianFormData: (documents, selfie, userInfo = {}, metadata = {}) => {
    const formData = new FormData();

    // Add IC documents with correct field names expected by backend
    const frontDoc = documents.find(doc => doc.side === 'front');
    const backDoc = documents.find(doc => doc.side === 'back');

    if (frontDoc) {
      formData.append('front_ic_photo', frontDoc.file);
    }

    if (backDoc) {
      formData.append('back_ic_photo', backDoc.file);
    }

    // Add selfie
    if (selfie) {
      formData.append('selfie_photo', selfie.file);
    }

    // Add required user information
    if (userInfo.full_name) {
      formData.append('full_name', userInfo.full_name);
    }

    if (userInfo.ic_number) {
      formData.append('ic_number', userInfo.ic_number);
    }

    // Add verification method
    formData.append('verification_method', metadata.verification_method || 'manual');

    // Add session metadata
    if (metadata.session_id) {
      formData.append('session_id', metadata.session_id);
    }

    return formData;
  },

  /**
   * Helper method to prepare FormData for Foreigner verification
   *
   * @param {Object} documents - Document files
   * @param {File} selfie - Selfie file
   * @param {Object} userInfo - User information
   * @param {Object} metadata - Additional metadata
   * @returns {FormData} Prepared form data
   */
  prepareForeignerFormData: (documents, selfie, userInfo, metadata = {}) => {
    const formData = new FormData();

    // Add passport document
    const passportDoc = documents.find(doc => doc.side === 'front') || documents[0];

    if (passportDoc) {
      formData.append('passport_photo', passportDoc.file);
    }

    // Add selfie
    if (selfie) {
      formData.append('selfie_photo', selfie.file);
    }

    // Add user information
    if (userInfo.full_name) {
      formData.append('full_name', userInfo.full_name);
    }

    if (userInfo.passport_number) {
      formData.append('passport_number', userInfo.passport_number);
    }

    if (userInfo.country) {
      formData.append('country', userInfo.country);
    }

    // Add verification method
    formData.append('verification_method', metadata.verification_method || 'manual');

    // Add session metadata
    if (metadata.session_id) {
      formData.append('session_id', metadata.session_id);
    }

    return formData;
  }
};

export default ekycService;
