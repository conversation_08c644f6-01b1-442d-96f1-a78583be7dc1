import api from './api';

/**
 * Scheduled Order Service
 *
 * Provides API calls for creating and managing scheduled orders as well as
 * retrieving talent availability information.
 */

const scheduledOrderService = {
  /**
   * Create a new scheduled order
   * @param {Object} orderData - Data for the new scheduled order
   * @returns {Promise<Object>} API response data
   */
  createScheduledOrder: async (orderData) => {
    const response = await api.post('/scheduled-orders', orderData);
    return response.data;
  },

  /**
   * Respond to a scheduled order (accept or reject)
   * @param {number|string} orderId - Scheduled order ID
   * @param {Object} data - Response payload, e.g. { action: 'accept' }
   * @returns {Promise<Object>} API response data
   */
  respondToScheduledOrder: async (orderId, data) => {
    const response = await api.post(`/scheduled-orders/${orderId}/respond`, data);
    return response.data;
  },

  /**
   * Mark a scheduled order as completed
   * @param {number|string} orderId - Scheduled order ID
   * @returns {Promise<Object>} API response data
   */
  completeScheduledOrder: async (orderId) => {
    const response = await api.post(`/scheduled-orders/${orderId}/complete`);
    return response.data;
  },

  /**
   * Cancel a scheduled order
   * @param {number|string} orderId - Scheduled order ID
   * @returns {Promise<Object>} API response data
   */
  cancelScheduledOrder: async (orderId) => {
    const response = await api.post(`/scheduled-orders/${orderId}/cancel`);
    return response.data;
  },

  /**
   * Get details of a scheduled order
   * @param {number|string} orderId - Scheduled order ID
   * @returns {Promise<Object>} API response data
   */
  getScheduledOrder: async (orderId) => {
    const response = await api.get(`/scheduled-orders/${orderId}`);
    return response.data;
  },

  /**
   * Get scheduled orders for the authenticated user
   * @param {Object} params - Optional query parameters
   * @returns {Promise<Object>} API response data
   */
  getUserScheduledOrders: async (params = {}) => {
    const response = await api.get('/scheduled-orders', { params });
    return response.data;
  },

  /**
   * Get a talent's availability schedule
   * @param {number|string} talentId - Talent ID
   * @returns {Promise<Object>} API response data
   */
  getTalentAvailability: async (talentId) => {
    const response = await api.get(`/scheduled-orders/talents/${talentId}/availability`);
    return response.data;
  },

  /**
   * Get a talent's special hours
   * @param {number|string} talentId - Talent ID
   * @returns {Promise<Object>} API response data
   */
  getTalentSpecialHours: async (talentId) => {
    const response = await api.get(`/scheduled-orders/talents/${talentId}/special-hours`);
    return response.data;
  },

  /**
   * Get available time slots for a talent
   * @param {number|string} talentId - Talent ID
   * @param {Object} params - Optional query parameters (date, etc.)
   * @returns {Promise<Object>} API response data
   */
  getTalentAvailableTimeSlots: async (talentId, params = {}) => {
    const response = await api.get(`/scheduled-orders/talents/${talentId}/available-time-slots`, { params });
    return response.data;
  },

  /**
   * Check if a talent is available for a specific time slot
   * @param {Object} payload - { talent_id, start_time, end_time, ... }
   * @returns {Promise<Object>} API response data
   */
  checkTalentAvailability: async (payload) => {
    const response = await api.post('/scheduled-orders/check-availability', payload);
    return response.data;
  }
};

export default scheduledOrderService;
