import walletAPI from './walletService';

/**
 * Transaction Service
 * Handles transaction hooks, validation, and error management
 * for credit-based operations like sending gifts
 */
export const transactionService = {
  /**
   * Validates if a transaction can be processed
   * @param {number} amount - Amount required for the transaction
   * @returns {Promise<{isValid: boolean, message: string}>} Validation result
   */
  validateTransaction: async (amount) => {
    try {
      // Get current balance
      const response = await walletAPI.getBalance();
      const currentBalance = response.data.credits_balance;
      
      // Check if sufficient balance
      if (currentBalance < amount) {
        return {
          isValid: false,
          message: `Insufficient balance. Required: ${amount}, Available: ${currentBalance}`,
          currentBalance
        };
      }
      
      return {
        isValid: true,
        message: 'Transaction can be processed',
        currentBalance
      };
    } catch (error) {
      console.error('Error validating transaction:', error);
      return {
        isValid: false,
        message: 'Unable to validate transaction at this time',
        error
      };
    }
  },
  
  /**
   * Execute a transaction with pre and post hooks
   * @param {Object} options - Transaction options
   * @param {number} options.amount - Amount to deduct
   * @param {Function} options.transactionFn - The main transaction function
   * @param {Function} options.onValidationFailed - Called when validation fails
   * @param {Function} options.onSuccess - Called after successful transaction
   * @param {Function} options.onError - Called when transaction fails
   * @returns {Promise<Object>} Transaction result
   */
  executeTransaction: async ({
    amount,
    transactionFn,
    onValidationFailed,
    onSuccess,
    onError
  }) => {
    let validationResult;
    
    // PRE-TRANSACTION HOOK: Validate balance
    try {
      validationResult = await transactionService.validateTransaction(amount);
      
      if (!validationResult.isValid) {
        if (onValidationFailed) {
          onValidationFailed(validationResult);
        }
        return {
          success: false,
          message: validationResult.message
        };
      }
    } catch (validationError) {
      console.error('Transaction validation error:', validationError);
      if (onError) {
        onError(validationError);
      }
      return {
        success: false,
        message: 'Failed to validate transaction'
      };
    }
    
    // TRANSACTION EXECUTION
    try {
      // Execute the main transaction
      const result = await transactionFn();
      
      // POST-TRANSACTION HOOK: Update state after successful transaction
      if (onSuccess) {
        onSuccess(result, validationResult.currentBalance);
      }
      
      return {
        success: true,
        data: result
      };
    } catch (transactionError) {
      console.error('Transaction execution error:', transactionError);
      
      // POST-TRANSACTION ERROR HOOK
      if (onError) {
        onError(transactionError);
      }
      
      return {
        success: false,
        message: transactionError.message || 'Transaction failed',
        error: transactionError
      };
    }
  },
  
  /**
   * Execute gift transaction
   * @param {Object} options - Gift transaction options
   * @param {number} options.amount - Gift price
   * @param {Function} options.sendGiftFn - Function to send gift
   * @param {Function} options.onInsufficientBalance - Called when balance is insufficient
   * @param {Function} options.onSuccess - Called after successful transaction
   * @param {Function} options.onError - Called when transaction fails
   * @returns {Promise<Object>} Transaction result
   */
  executeGiftTransaction: async ({
    amount,
    sendGiftFn,
    onInsufficientBalance,
    onSuccess,
    onError
  }) => {
    return transactionService.executeTransaction({
      amount,
      transactionFn: sendGiftFn,
      onValidationFailed: (result) => {
        if (onInsufficientBalance) {
          onInsufficientBalance(result);
        }
      },
      onSuccess,
      onError
    });
  }
}; 