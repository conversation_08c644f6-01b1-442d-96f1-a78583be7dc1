// Reuse the shared API instance
import api from './api';
import { showErrorToast, showWarningToast, showInfoToast, showSuccessToast } from '../utils/toast';

// Add response interceptor to handle common error scenarios
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle specific error types
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const data = error.response.data;

      // Map specific API error codes to user-friendly messages
      switch (status) {
        case 401:
          // Unauthorized - token may be invalid or expired
          showWarningToast(`[walletService] Unauthorized: ${data.message}`);
          break;

        case 403:
          // Forbidden - handle permission errors
          showErrorToast(`Permission error: ${data.message}`);
          // Handle verification requirements
          if (data.code === 'verification_required') {
            return Promise.reject({
              ...error,
              isVerificationError: true,
              requirements: data.missing_requirements || []
            });
          }
          break;

        case 422:
          // Validation errors
          showErrorToast(`Validation error: ${data.message}`);
          break;

        case 429:
          // Rate limiting
          showWarningToast('Too many requests. Please try again later.');
          break;

        case 500:
          // Server error
          showErrorToast(`Server error: ${data.message}`);
          break;

        default:
          // Other errors
          showErrorToast(`Unhandled error (${status}): ${data.message}`);
      }
    }

    return Promise.reject(error);
  }
);

// Wallet API endpoints
export const walletAPI = {
      // Get current credit balance
      getBalance: async () => {
        try {
          const response = await api.get('/credits/balance');
          return response;
        } catch (error) {
          showErrorToast(`Error fetching wallet balance: ${error.message || error}`);
          throw error;
        }
      },

      // Get transaction history with optional filters
      getTransactions: async (page = 1, filters = {}) => {
        try {
          // Build query parameters
          const params = new URLSearchParams();
          params.append('page', page);

          // Add filters if provided
          if (filters.type === 'credit-in') {
            params.append('type', 'add');
          } else if (filters.type === 'credit-out') {
            params.append('type', 'deduct');
          }

          if (filters.startDate) {
            params.append('start_date', filters.startDate);
          }

          if (filters.endDate) {
            params.append('end_date', filters.endDate);
          }

          const response = await api.get(`/credits/transactions?${params.toString()}`);
          return response;
        } catch (error) {
          showErrorToast(`Error fetching transactions: ${error.message || error}`);
          throw error;
        }
      },

      // Get credit packages
      getCreditPackages: async (channel = 'default') => {
        try {
          const response = await api.get(`/credits/${channel}/packages`);
          return response;
        } catch (error) {
          showErrorToast(`Error fetching credit packages: ${error.message || error}`);
          throw error;
        }
      },

      // Create a payment for a credit package
      // If autoRedirect is true, the browser will be redirected to the payment
      // gateway once the API responds with a valid URL.
      createPayment: async (creditPackageId, redirectUrl, autoRedirect = false) => {
        try {
          const response = await api.post('/payments', {
            credit_package_id: creditPackageId,
            redirect_url: redirectUrl,
            payment_method: 'billplz'
          });

          if (autoRedirect && response.data?.payment?.redirect_url) {
            window.location.href = response.data.payment.redirect_url;
          }

          return response;
        } catch (error) {
          showErrorToast(`Error creating payment: ${error.message || error}`);
          throw error;
        }
      },

      // Forward payment return parameters to the backend for processing
      handlePaymentReturn: async (params) => {
        try {
          const response = await api.get('/payments/return', { params });
          return response;
        } catch (error) {
          showErrorToast(`Error handling payment return: ${error.message || error}`);
          throw error;
        }
      },

      // Check payment status
      checkPaymentStatus: async (transactionId) => {
        try {
          const response = await api.get(`/payments/${transactionId}`);
          return response;
        } catch (error) {
          showErrorToast(`Error checking payment status: ${error.message || error}`);
          throw error;
        }
      },

      // Retry a payment
      retryPayment: async (transactionId) => {
        try {
          const response = await api.post('/payments/retry', {
            transaction_id: transactionId
          });
          return response;
        } catch (error) {
          showErrorToast(`Error retrying payment: ${error.message || error}`);
          throw error;
        }
      },

      // Add this method to support retrying payment with authentication
      retryPayment: async (payload) => {
        // payload: { transaction_id }
        try {
          const response = await api.post('/payments/retry', payload);
          return response;
        } catch (error) {
          // Optionally show a toast or handle error
          throw error;
        }
      },

      // Request withdrawal (legacy method)
      requestWithdrawal: async (amount, bankDetails = {}) => {
        try {
          const response = await api.post('/credits/withdrawals/request', {
            amount,
            ...bankDetails
          });
          return response;
        } catch (error) {
          showErrorToast(`Error requesting withdrawal: ${error.message || error}`);
          throw error;
        }
      },

      // Enhanced withdrawal with bank account selection and currency support
      withdraw: async (withdrawData) => {
        try {
          const response = await api.post('/withdrawals/credits', {
            amount: withdrawData.amount,
            bank_account_id: withdrawData.bank_account_id,
            fiat_currency: withdrawData.fiat_currency,
            payment_mode_id: withdrawData.payment_mode_id, // Optional, defaults to DuitNow (2) if not provided
            recipient_reference: withdrawData.recipient_reference,
            other_payment_details: withdrawData.other_payment_details,
            id_validation_type: withdrawData.id_validation_type,
            id_validation_value: withdrawData.id_validation_value,
            transaction_type: withdrawData.transaction_type,
            purpose_of_transfer: withdrawData.purpose_of_transfer,
            email: withdrawData.email
          });
          return response;
        } catch (error) {
          showErrorToast(`Error processing withdrawal: ${error.message || error}`);
          throw error;
        }
      },

      // Unified withdrawal history with deduplication
      _withdrawalHistoryCache: {},
      _withdrawalHistoryRequests: {},
      getWithdrawalHistory: async (limit = 10, offset = 0, forceRefresh = false) => {
        const cacheKey = `${limit}_${offset}`;
        // Use cache unless forceRefresh is true
        if (!forceRefresh && walletAPI._withdrawalHistoryCache[cacheKey]) {
          return walletAPI._withdrawalHistoryCache[cacheKey];
        }
        // Deduplicate in-flight requests
        if (walletAPI._withdrawalHistoryRequests[cacheKey]) {
          return walletAPI._withdrawalHistoryRequests[cacheKey];
        }
          const params = new URLSearchParams();
          params.append('limit', limit);
          params.append('offset', offset);
        const requestPromise = api.get(`/withdrawals/history?${params.toString()}`)
          .then(response => {
            walletAPI._withdrawalHistoryCache[cacheKey] = response;
            delete walletAPI._withdrawalHistoryRequests[cacheKey];
          return response;
          })
          .catch(error => {
            delete walletAPI._withdrawalHistoryRequests[cacheKey];
          throw error;
          });
        walletAPI._withdrawalHistoryRequests[cacheKey] = requestPromise;
        return requestPromise;
      },

      // Get withdrawal history (unified method)
      getWithdrawalHistory: async (limit = null, offset = 0) => {
        try {
          const params = new URLSearchParams();
          if (limit !== null) {
          params.append('limit', limit);
          params.append('offset', offset);
          }
          const response = await api.get(`/withdrawals/history${limit !== null ? `?${params.toString()}` : ''}`);
          return response;
        } catch (error) {
          throw error;
        }
      },

      // Get withdrawal history without pagination
      getWithdrawalHistory: async () => {
        try {
          const response = await api.get('/withdrawals/history');
          return response;
        } catch (error) {
          throw error;
        }
      },

      // Get currencies for withdrawals
      getWithdrawalCurrencies: async (type = 'credits') => {
        try {
          const response = await api.get(`/withdrawals/currencies?type=${type}`);
          return response;
        } catch (error) {
          showErrorToast(`Error fetching withdrawal currencies: ${error.message || error}`);
          throw error;
        }
      },

      // Get withdrawal by ID
      getWithdrawal: async (withdrawalId) => {
        try {
          const response = await api.get(`/withdrawals/${withdrawalId}`);
          return response;
        } catch (error) {
          showErrorToast(`Error fetching withdrawal details: ${error.message || error}`);
          throw error;
        }
      },

      // Note: Cancel withdrawal endpoint doesn't exist in backend
      // cancelWithdrawal functionality would need to be implemented in backend first

      // Note: Get credit channels endpoint is for backend/admin use only
      // getCreditChannels functionality is not exposed to frontend users

      // Get point transaction history with optional filters
      getPointTransactions: async (page = 1, filters = {}) => {
        try {
          // Build query parameters
          const params = new URLSearchParams();
          params.append('page', page);

          if (filters.startDate) {
            params.append('start_date', filters.startDate);
          }

          if (filters.endDate) {
            params.append('end_date', filters.endDate);
          }

          // Add more filters as needed

          const response = await api.get(`/points/transactions?${params.toString()}`);
          return response;
        } catch (error) {
          showErrorToast(`Error fetching point transactions: ${error.message || error}`);
          throw error;
        }
      },

      // Get credit conversion rate
      getCreditConversionRate: async (toCurrency = 'MYR') => {
        try {
          const response = await api.get('/credits/conversion-rate');
          if (response.data && Array.isArray(response.data.rates)) {
            const found = response.data.rates.find(r => r.to_currency === toCurrency);
            return found ? found.rate : null;
          }
          return null;
        } catch (error) {
          showErrorToast(`Error fetching credit conversion rate: ${error.message || error}`);
          throw error;
        }
      },
    };

export default walletAPI;