import axios from 'axios';

// Ensure the base URL always ends with /api to match backend routing
const rawBaseUrl = process.env.REACT_APP_API_URL || 'http://localhost:8001';
const API_BASE_URL = rawBaseUrl.endsWith('/api')
  ? rawBaseUrl
  : `${rawBaseUrl.replace(/\/$/, '')}/api`;

/**
 * Helper to create an axios instance with the common configuration
 * used across the services. This prevents each service from re-
 * implementing the base URL and auth token logic.
 */
export const createApi = (config = {}) => {
    const instance = axios.create({
        baseURL: API_BASE_URL,
        withCredentials: true,
        headers: {
            'Accept': 'application/json',
            ...(config.headers || {})
        },
        ...config,
    });

    instance.interceptors.request.use(
        (cfg) => {
            const token = localStorage.getItem('token');
            if (token) {
                cfg.headers.Authorization = `Bearer ${token}`;
            }
            return cfg;
        },
        (error) => Promise.reject(error)
    );

    return instance;
};

// Shared API instance for most services
const api = createApi();



// Profile API endpoints
export const profileAPI = {
    getProfile: async () => {
        try {
            const response = await api.get('/user/profile');
            return response.data;
        } catch (error) {
            console.error('Error fetching profile:', error);
            throw error;
        }
    },
    getBiography: async () => {
        try {
            const response = await api.get('/user/biography');
            return response.data;
        } catch (error) {
            console.error('Error fetching biography:', error);
            throw error;
        }
    },
    getFollowers: async () => {
        try {
            const response = await api.get('/user/followers');
            return response.data;
        } catch (error) {
            console.error('Error fetching followers:', error);
            throw error;
        }
    },
    updateProfile: async (data) => {
        try {
            const response = await api.put('/user/profile', data);
            return response.data;
        } catch (error) {
            console.error('Error updating profile:', error);
            throw error;
        }
    },
    updateBiography: async (data) => {
        try {
            // Ensure data has the correct format
            const formattedData = {
                bio: data.bio || data.biography || ""
            };

            const response = await api.put('/user/biography', formattedData);
            return response.data;
        } catch (error) {
            console.error('Error updating biography:', error);
            throw error;
        }
    },
    uploadMedia: async (file) => {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await api.post('/user/media', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error uploading media:', error);
            throw error;
        }
    },
    updateSkills: async (skills) => {
        try {
            const response = await api.put('/user/profile/skills', { skills });
            return response.data;
        } catch (error) {
            console.error('Error updating skills:', error);
            throw error;
        }
    },
    deleteSkill: async (skillId) => {
        try {
            const response = await api.delete(`/user/profile/skills/${skillId}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting skill:', error);
            throw error;
        }
    },

    // Language management
    getUserLanguages: async () => {
        try {
            const response = await api.get('/users/languages');
            return response.data;
        } catch (error) {
            console.error('Error fetching user languages:', error);
            throw error;
        }
    },

    updateUserLanguages: async (language_ids) => {
        try {
            const response = await api.post('/users/languages', { language_ids });
            return response.data;
        } catch (error) {
            console.error('Error updating user languages:', error);
            throw error;
        }
    },

    getAvailableLanguages: async () => {
        try {
            const response = await api.get('/languages');
            return response.data;
        } catch (error) {
            console.error('Error fetching available languages:', error);
            throw error;
        }
    },

    // Personality management
    getUserPersonalities: async () => {
        try {
            const response = await api.get('/users/personalities');
            return response.data;
        } catch (error) {
            console.error('Error fetching user personalities:', error);
            throw error;
        }
    },

    updateUserPersonalities: async (personality_ids) => {
        try {
            const response = await api.post('/users/personalities', { personality_ids });
            return response.data;
        } catch (error) {
            console.error('Error updating user personalities:', error);
            throw error;
        }
    },

    getAvailablePersonalities: async () => {
        try {
            const response = await api.get('/personalities');
            return response.data;
        } catch (error) {
            console.error('Error fetching available personalities:', error);
            throw error;
        }
    }
};



// OAuth API endpoints
const oauthAPI = {
    // redirect: async (provider) => {
    //     try {
    //         const response = await api.get(`/auth/oauth/${provider}/redirect`);
    //         return response.data;
    //     } catch (error) {
    //         throw error;
    //     }
    // },

    redirect: (provider) => {
        // Navigate to the backend's OAuth redirect endpoint
        window.location.href = `${API_BASE_URL}/auth/oauth/${provider}/redirect`;
    },

    callback: async (provider, code) => {
        try {
            const response = await api.get(`/auth/oauth/${provider}/callback`, {
                params: { code }
            });
            return response.data;
        } catch (error) {
            throw error;
        }
    }
};

export default api;

export {
    // ... existing exports ...
    oauthAPI
};
