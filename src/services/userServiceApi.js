/**
 * User Service API
 *
 * This service handles all API calls related to user services management.
 */

import api from './api';
import { cacheManager } from '../utils/cacheManager';

// Environment detection
const NODE_ENV = process.env.NODE_ENV || 'development';
const isProd = NODE_ENV === 'production';
const isDevelopment = NODE_ENV === 'development';

// Feature flag to disable mocks even in development (for testing real API in dev)
const USE_MOCK_API = isDevelopment && process.env.REACT_APP_USE_MOCK_API !== 'false';

// Mock service data for development
const mockServices = [
  {
    id: 1,
    user_id: 1,
    service_category_id: 1,
    service_type_id: 2,
    title: 'Professional Gaming Coaching',
    description: 'One-on-one coaching sessions to improve your gaming skills. I specialize in FPS games and can help with aim, strategy, and game sense.',
    service_elements: ['Strategy Development', 'Aim Training', 'Game Sense Improvement'],
    status: 'approved',
    is_active: true,
    created_at: '2023-02-10T09:20:00Z',
    updated_at: '2023-05-15T11:30:00Z',
    service_category: {
      id: 1,
      name: 'Gaming',
      description: 'Gaming related services'
    },
    service_type: {
      id: 2,
      name: 'Coaching',
      description: 'Coaching services'
    },
    pricing_options: [
      {
        id: 1,
        user_service_id: 1,
        pricing_option_type_id: 1,
        is_active: true,
        pricing_option_type: {
          id: 1,
          name: 'Hourly',
          description: 'Charged per hour'
        }
      }
    ],
    service_styles: [
      {
        id: 1,
        name: 'Casual',
        price: 50
      },
      {
        id: 2,
        name: 'Competitive',
        price: 75
      }
    ]
  },
  {
    id: 2,
    user_id: 1,
    service_category_id: 1,
    service_type_id: 3,
    title: 'Duo Queue Partner',
    description: 'Looking to climb the ranked ladder? I can help you reach your desired rank with my experience and skills.',
    service_elements: ['Ranked Climbing', 'Strategy Coordination', 'Communication Skills'],
    status: 'approved',
    is_active: true,
    created_at: '2023-02-15T14:40:00Z',
    updated_at: '2023-05-18T10:25:00Z',
    service_category: {
      id: 1,
      name: 'Gaming',
      description: 'Gaming related services'
    },
    service_type: {
      id: 3,
      name: 'Duo Partner',
      description: 'Play together as a duo'
    },
    pricing_options: [
      {
        id: 2,
        user_service_id: 2,
        pricing_option_type_id: 1,
        is_active: true,
        pricing_option_type: {
          id: 1,
          name: 'Hourly',
          description: 'Charged per hour'
        }
      }
    ],
    service_styles: [
      {
        id: 1,
        name: 'Casual',
        price: 35
      },
      {
        id: 2,
        name: 'Competitive',
        price: 60
      }
    ]
  }
];

/**
 * User Service API
 */
const userServiceApi = {
  /**
   * Get all services for the authenticated user
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Services data
   */
  getUserServices: async (params = {}) => {
    const cacheKey = 'user-services';
    
    // Try to get from cache first
    const cachedData = cacheManager.get(cacheKey, params);
    if (cachedData) {
      return cachedData;
    }
    
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return cacheManager.set(cacheKey, {
        success: true,
        data: mockServices
      }, params);
    }
    
    try {
      const response = await api.get('/user/services', { params });
      return cacheManager.set(cacheKey, {
        success: true,
        data: response.data
      }, params);
    } catch (error) {
      console.error('Error fetching user services:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch user services'
      };
    }
  },
  
  /**
   * Get a specific service by ID
   * 
   * @param {number} id - Service ID
   * @returns {Promise<Object>} Service data
   */
  getServiceById: async (id) => {
    const cacheKey = `user-service-${id}`;
    
    // Try to get from cache first
    const cachedData = cacheManager.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }
    
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const service = mockServices.find(s => s.id === parseInt(id));
      
      if (!service) {
        return {
          success: false,
          error: 'Service not found'
        };
      }
      
      return cacheManager.set(cacheKey, {
        success: true,
        data: service
      });
    }
    
    try {
      const response = await api.get(`/user/services/${id}`);
      return cacheManager.set(cacheKey, {
        success: true,
        data: response.data
      });
    } catch (error) {
      console.error(`Error fetching service ${id}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch service'
      };
    }
  },
  
  /**
   * Create a new service
   * 
   * @param {Object} serviceData - Service data
   * @returns {Promise<Object>} Created service
   */
  createService: async (serviceData) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newService = {
        id: mockServices.length + 1,
        user_id: 1,
        ...serviceData,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      mockServices.push(newService);
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      
      return {
        success: true,
        data: {
          message: 'Service created successfully and pending approval',
          services: [newService]
        }
      };
    }
    
    try {
      const response = await api.post('/user/services', {
        services: [serviceData]
      });
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error creating service:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to create service'
      };
    }
  },
  
  /**
   * Update an existing service
   * 
   * @param {Object} serviceData - Service data with ID
   * @returns {Promise<Object>} Updated service
   */
  updateService: async (serviceData) => {
    if (!serviceData.id) {
      return {
        success: false,
        error: 'Service ID is required for updates'
      };
    }
    
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const index = mockServices.findIndex(s => s.id === serviceData.id);
      
      if (index === -1) {
        return {
          success: false,
          error: 'Service not found'
        };
      }
      
      mockServices[index] = {
        ...mockServices[index],
        ...serviceData,
        status: 'pending',
        updated_at: new Date().toISOString()
      };
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${serviceData.id}`);
      
      return {
        success: true,
        data: {
          message: 'Service updated successfully and pending approval',
          services: [mockServices[index]]
        }
      };
    }
    
    try {
      const response = await api.put('/user/services', {
        services: [serviceData]
      });
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${serviceData.id}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating service:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update service'
      };
    }
  },
  
  /**
   * Toggle service active status
   * 
   * @param {number} id - Service ID
   * @returns {Promise<Object>} Result
   */
  toggleServiceActive: async (id) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockServices.findIndex(s => s.id === parseInt(id));
      
      if (index === -1) {
        return {
          success: false,
          error: 'Service not found'
        };
      }
      
      mockServices[index].is_active = !mockServices[index].is_active;
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${id}`);
      
      return {
        success: true,
        data: {
          message: mockServices[index].is_active ? 'Service enabled successfully' : 'Service disabled successfully',
          is_active: mockServices[index].is_active
        }
      };
    }
    
    try {
      const response = await api.put(`/user/services/${id}/toggle-active`);
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${id}`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error(`Error toggling service ${id} active status:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to toggle service active status'
      };
    }
  },

  /**
   * Update service styles' active status
   *
   * @param {Object} serviceData - Service object containing updated service_style array
   * @returns {Promise<Object>} Result
   */
  updateServiceStyles: async (serviceData) => {
    if (!serviceData) {
      return { success: false, error: 'Invalid service data' };
    }

    try {
      const response = await api.post('/user/services', { services: [serviceData] });

      cacheManager.invalidate('user-services');
      if (serviceData.id) {
        cacheManager.invalidate(`user-service-${serviceData.id}`);
      }

      return { success: true, data: response.data };
    } catch (error) {
      console.error('Error updating service styles:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update service styles',
      };
    }
  },
  
  /**
   * Delete a service (simulated by updating the services list without the specified service)
   * 
   * @param {number} id - Service ID
   * @returns {Promise<Object>} Result
   */
  deleteService: async (id) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockServices.findIndex(s => s.id === parseInt(id));
      
      if (index === -1) {
        return {
          success: false,
          error: 'Service not found'
        };
      }
      
      mockServices.splice(index, 1);
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${id}`);
      
      return {
        success: true,
        data: {
          message: 'Service deleted successfully'
        }
      };
    }
    
    try {
      // The API doesn't have a direct delete endpoint, so we update with an empty services array
      // which will delete all services not included in the update
      const currentServices = await userServiceApi.getUserServices();
      
      if (!currentServices.success) {
        return {
          success: false,
          error: 'Failed to fetch current services for deletion'
        };
      }
      
      const servicesToKeep = currentServices.data
        .filter(service => service.id !== parseInt(id))
        .map(service => ({
          id: service.id,
          service_category_id: service.service_category_id,
          service_type_id: service.service_type_id,
          title: service.title,
          description: service.description,
          service_type_title: service.service_type_title,
          service_type_description: service.service_type_description,
          price: service.price,
          service_elements: service.service_elements,
          pricing_option_id:
            service.pricing_option_id ??
            service.pricing_options?.[0]?.pricing_option_type_id ??
            null,
          service_style:
            service.service_style?.map(s => ({
              service_style_id: s.service_style_id ?? s.id,
              is_active: s.is_active ?? true,
              price: s.price
            })) ||
            service.service_styles?.map(s => ({
              service_style_id: s.service_style_id ?? s.id,
              is_active: s.is_active ?? true,
              price: s.price
            })) ||
            []
        }));
      
      const response = await api.put('/user/services', {
        services: servicesToKeep
      });
      
      // Invalidate cache
      cacheManager.invalidate('user-services');
      cacheManager.invalidate(`user-service-${id}`);
      
      return {
        success: true,
        data: {
          message: 'Service deleted successfully',
          ...response.data
        }
      };
    } catch (error) {
      console.error(`Error deleting service ${id}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete service'
      };
    }
  }
};

export default userServiceApi;
