/**
 * Cache Manager
 * 
 * A simple in-memory cache manager for API responses.
 * Helps reduce API calls for frequently accessed data.
 */

// Cache storage
const cache = new Map();

// Default cache expiration time (5 minutes)
const DEFAULT_EXPIRATION = 5 * 60 * 1000;

/**
 * Generate a cache key with parameters
 * 
 * @param {string} baseKey - Base cache key
 * @param {Object} params - Parameters to include in the key
 * @returns {string} Full cache key
 */
const generateKey = (baseKey, params = {}) => {
  if (Object.keys(params).length === 0) {
    return baseKey;
  }
  
  // Sort params to ensure consistent keys regardless of object property order
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((acc, key) => {
      acc[key] = params[key];
      return acc;
    }, {});
  
  return `${baseKey}:${JSON.stringify(sortedParams)}`;
};

/**
 * Cache Manager
 */
export const cacheManager = {
  /**
   * Get data from cache
   * 
   * @param {string} baseKey - Base cache key
   * @param {Object} params - Parameters to include in the key
   * @returns {any|null} Cached data or null if not found/expired
   */
  get: (baseKey, params = {}) => {
    const key = generateKey(baseKey, params);
    const cachedItem = cache.get(key);
    
    if (!cachedItem) {
      return null;
    }
    
    // Check if expired
    if (cachedItem.expiration < Date.now()) {
      cache.delete(key);
      return null;
    }
    
    return cachedItem.data;
  },
  
  /**
   * Set data in cache
   * 
   * @param {string} baseKey - Base cache key
   * @param {any} data - Data to cache
   * @param {Object} params - Parameters to include in the key
   * @param {number} expiration - Cache expiration time in milliseconds
   * @returns {any} The data that was cached
   */
  set: (baseKey, data, params = {}, expiration = DEFAULT_EXPIRATION) => {
    const key = generateKey(baseKey, params);
    
    cache.set(key, {
      data,
      expiration: Date.now() + expiration
    });
    
    return data;
  },
  
  /**
   * Invalidate a cache entry or all entries with a prefix
   * 
   * @param {string} keyPrefix - Cache key or prefix to invalidate
   */
  invalidate: (keyPrefix) => {
    // If exact key exists, delete it
    if (cache.has(keyPrefix)) {
      cache.delete(keyPrefix);
      return;
    }
    
    // Otherwise, delete all keys that start with the prefix
    for (const key of cache.keys()) {
      if (key.startsWith(`${keyPrefix}:`)) {
        cache.delete(key);
      }
    }
  },
  
  /**
   * Clear the entire cache
   */
  clear: () => {
    cache.clear();
  }
};

export default cacheManager;
