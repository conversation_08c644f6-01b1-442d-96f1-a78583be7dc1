/**
 * Share Utilities - Common configurations and patterns for sharing
 * 
 * This file provides reusable share configurations for different content types
 * and common patterns used throughout the application.
 */

import {
  FaWhatsapp,
  FaFacebookF,
  FaLinkedinIn,
  FaEnvelope,
  FaQrcode,
  FaLink,
  FaShareAlt,
  FaInstagram,
  FaTelegram
} from 'react-icons/fa';

/**
 * Default share options for most content types
 */
export const defaultShareOptions = [
  {
    label: 'WhatsApp',
    icon: <FaWhatsapp className="w-6 h-6" />,
    color: 'bg-green-500 hover:bg-green-600 text-white',
    getUrl: (url, title) => `https://wa.me/?text=${encodeURIComponent(title ? `${title} ${url}` : url)}`
  },
  {
    label: 'Facebook',
    icon: <FaFacebookF className="w-6 h-6" />,
    color: 'bg-blue-600 hover:bg-blue-700 text-white',
    getUrl: (url) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
  },
  {
    label: 'X',
    icon: (
      <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
      </svg>
    ),
    color: 'bg-black hover:bg-gray-800 text-white',
    getUrl: (url, title) => `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}${title ? `&text=${encodeURIComponent(title)}` : ''}`
  },
  {
    label: 'LinkedIn',
    icon: <FaLinkedinIn className="w-6 h-6" />,
    color: 'bg-blue-800 hover:bg-blue-900 text-white',
    getUrl: (url, title) => `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}${title ? `&title=${encodeURIComponent(title)}` : ''}`
  },
  {
    label: 'Email',
    icon: <FaEnvelope className="w-6 h-6" />,
    color: 'bg-amber-500 hover:bg-amber-600 text-white',
    getUrl: (url, title, description) => `mailto:?subject=${encodeURIComponent(title || 'Check this out!')}&body=${encodeURIComponent(description ? `${description}\n${url}` : url)}`
  }
];

/**
 * Extended share options including Instagram and Telegram
 */
export const extendedShareOptions = [
  ...defaultShareOptions,
  {
    label: 'Instagram',
    icon: <FaInstagram className="w-6 h-6" />,
    color: 'bg-pink-500 hover:bg-pink-600 text-white',
    getUrl: (url) => `https://www.instagram.com/?url=${encodeURIComponent(url)}`
  },
  {
    label: 'Telegram',
    icon: <FaTelegram className="w-6 h-6" />,
    color: 'bg-blue-500 hover:bg-blue-600 text-white',
    getUrl: (url, title) => `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title || 'Check this out!')}`
  }
];

/**
 * Minimal share options (just WhatsApp and Copy Link)
 */
export const minimalShareOptions = [
  {
    label: 'WhatsApp',
    icon: <FaWhatsapp className="w-6 h-6" />,
    color: 'bg-green-500 hover:bg-green-600 text-white',
    getUrl: (url, title) => `https://wa.me/?text=${encodeURIComponent(title ? `${title} ${url}` : url)}`
  }
];

/**
 * Generate share URL for different content types
 */
export const generateShareUrl = {
  // Post sharing
  post: (postId, baseUrl = window.location.origin) => `${baseUrl}/post/${postId}`,
  
  // Profile sharing
  profile: (userId, baseUrl = window.location.origin) => `${baseUrl}/profile/${userId}`,
  
  // Referral sharing
  referral: (referralCode, baseUrl = window.location.origin) => `${baseUrl}/ref/${referralCode}`,
  
  // Service sharing
  service: (serviceId, baseUrl = window.location.origin) => `${baseUrl}/service/${serviceId}`,
  
  // Custom URL
  custom: (path, baseUrl = window.location.origin) => `${baseUrl}${path.startsWith('/') ? path : `/${path}`}`
};

/**
 * Generate share title for different content types
 */
export const generateShareTitle = {
  post: (post) => `${post.author || 'Anonymous'}'s post`,
  profile: (profile) => `${profile.name || 'User'}'s profile`,
  referral: (referralCode) => 'Join MissionX',
  service: (service) => `${service.name} service`,
  custom: (title) => title
};

/**
 * Generate share description for different content types
 */
export const generateShareDescription = {
  post: (post) => {
    const maxLength = 100;
    const content = post.content || '';
    return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
  },
  profile: (profile) => `Check out ${profile.name || 'this user'}'s amazing profile on MissionX!`,
  referral: (referralCode) => 'I\'m using MissionX and you should too! Join me and discover amazing services.',
  service: (service) => `Amazing ${service.name} service available on MissionX. Check it out!`,
  custom: (description) => description
};

/**
 * Pre-configured share configurations for common use cases
 */
export const shareConfigs = {
  // Post sharing configuration
  post: {
    modalTitle: "Share this post",
    modalDescription: "Share this post with your friends and followers",
    showQRCode: true,
    shareOptions: defaultShareOptions
  },
  
  // Profile sharing configuration
  profile: {
    modalTitle: "Share this profile",
    modalDescription: "Share this amazing profile with others",
    showQRCode: true,
    shareOptions: extendedShareOptions
  },
  
  // Referral sharing configuration
  referral: {
    modalTitle: "Invite friends to MissionX",
    modalDescription: "Share MissionX with your friends and earn rewards",
    showQRCode: false,
    shareOptions: defaultShareOptions
  },
  
  // Service sharing configuration
  service: {
    modalTitle: "Share this service",
    modalDescription: "Share this amazing service with others",
    showQRCode: true,
    shareOptions: defaultShareOptions
  },
  
  // Minimal sharing configuration
  minimal: {
    modalTitle: "Share",
    modalDescription: "Choose how to share",
    showQRCode: false,
    shareOptions: minimalShareOptions
  }
};

/**
 * Create a complete share configuration for any content type
 */
export const createShareConfig = (contentType, customData = {}) => {
  const baseConfig = shareConfigs[contentType] || shareConfigs.minimal;
  
  return {
    ...baseConfig,
    ...customData
  };
};

/**
 * Analytics tracking for share actions
 */
export const trackShareAction = (platform, content) => {
  // This can be integrated with your analytics service
  console.log('Share action:', { platform, content });
  
  // Example: Google Analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'share', {
      method: platform,
      content_type: content.type,
      content_id: content.id
    });
  }
};

/**
 * Validate share URL
 */
export const validateShareUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Sanitize share text for different platforms
 */
export const sanitizeShareText = (text, platform) => {
  if (!text) return '';
  
  const maxLengths = {
    x: 280,
    whatsapp: 1000,
    facebook: 1000,
    linkedin: 1000,
    email: 2000
  };
  
  const maxLength = maxLengths[platform] || 1000;
  return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
}; 