/**
 * Responsive Design Utilities
 * Utilities for consistent responsive design across authentication components
 */

/**
 * Responsive breakpoints matching Tailwind CSS defaults
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

/**
 * Get responsive container classes
 * @param {string} size - Container size variant
 * @returns {string} Tailwind CSS classes
 */
export const getContainerClasses = (size = 'default') => {
  const variants = {
    sm: 'max-w-sm mx-auto px-4',
    default: 'max-w-md mx-auto px-4',
    lg: 'max-w-lg mx-auto px-4',
    xl: 'max-w-xl mx-auto px-4',
    '2xl': 'max-w-2xl mx-auto px-4',
    full: 'w-full px-4'
  };
  
  return variants[size] || variants.default;
};

/**
 * Get responsive spacing classes
 * @param {string} size - Spacing size
 * @returns {string} Tailwind CSS classes
 */
export const getSpacingClasses = (size = 'default') => {
  const variants = {
    xs: 'space-y-2',
    sm: 'space-y-3',
    default: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8'
  };
  
  return variants[size] || variants.default;
};

/**
 * Get responsive text classes
 * @param {string} variant - Text variant
 * @returns {string} Tailwind CSS classes
 */
export const getTextClasses = (variant = 'body') => {
  const variants = {
    h1: 'text-2xl sm:text-3xl lg:text-4xl font-bold',
    h2: 'text-xl sm:text-2xl lg:text-3xl font-semibold',
    h3: 'text-lg sm:text-xl lg:text-2xl font-semibold',
    h4: 'text-base sm:text-lg lg:text-xl font-medium',
    body: 'text-sm sm:text-base',
    caption: 'text-xs sm:text-sm',
    button: 'text-sm sm:text-base font-medium'
  };
  
  return variants[variant] || variants.body;
};

/**
 * Get responsive button classes
 * @param {string} size - Button size
 * @returns {string} Tailwind CSS classes
 */
export const getButtonClasses = (size = 'default') => {
  const variants = {
    xs: 'px-2 py-1 text-xs rounded-md',
    sm: 'px-3 py-2 text-sm rounded-lg',
    default: 'px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base rounded-xl',
    lg: 'px-6 py-3 sm:px-8 sm:py-4 text-base sm:text-lg rounded-xl',
    xl: 'px-8 py-4 sm:px-10 sm:py-5 text-lg sm:text-xl rounded-2xl'
  };
  
  return variants[size] || variants.default;
};

/**
 * Get responsive grid classes
 * @param {Object} options - Grid options
 * @returns {string} Tailwind CSS classes
 */
export const getGridClasses = (options = {}) => {
  const {
    cols = { default: 1, sm: 1, md: 2, lg: 3 },
    gap = 'default'
  } = options;
  
  const gapClasses = {
    xs: 'gap-2',
    sm: 'gap-3',
    default: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };
  
  const gridCols = `grid-cols-${cols.default} sm:grid-cols-${cols.sm} md:grid-cols-${cols.md} lg:grid-cols-${cols.lg}`;
  const gapClass = gapClasses[gap] || gapClasses.default;
  
  return `grid ${gridCols} ${gapClass}`;
};

/**
 * Get responsive card classes
 * @param {string} variant - Card variant
 * @returns {string} Tailwind CSS classes
 */
export const getCardClasses = (variant = 'default') => {
  const baseClasses = 'rounded-xl sm:rounded-2xl lg:rounded-3xl p-4 sm:p-6 lg:p-8';
  
  const variants = {
    default: `${baseClasses} bg-white/90 backdrop-blur-2xl border border-white/30 shadow-2xl`,
    glass: `${baseClasses} bg-white/10 backdrop-blur-2xl border border-white/20 shadow-xl`,
    solid: `${baseClasses} bg-white shadow-lg border border-gray-200`,
    gradient: `${baseClasses} bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl border border-white/30 shadow-2xl`
  };
  
  return variants[variant] || variants.default;
};

/**
 * Get responsive form field classes
 * @param {Object} options - Form field options
 * @returns {string} Tailwind CSS classes
 */
export const getFormFieldClasses = (options = {}) => {
  const { size = 'default', state = 'default' } = options;
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-lg',
    default: 'px-4 py-3 text-sm sm:text-base rounded-xl',
    lg: 'px-6 py-4 text-base sm:text-lg rounded-xl'
  };
  
  const stateClasses = {
    default: 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500',
    error: 'border-red-300 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-300 focus:border-green-500 focus:ring-green-500'
  };
  
  const baseClasses = 'w-full border transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-opacity-50 bg-white/90 backdrop-blur-sm';
  
  return `${baseClasses} ${sizeClasses[size]} ${stateClasses[state]}`;
};

/**
 * Check if device is mobile based on screen width
 * @returns {boolean} True if mobile device
 */
export const isMobile = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < 768; // md breakpoint
};

/**
 * Check if device is tablet based on screen width
 * @returns {boolean} True if tablet device
 */
export const isTablet = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= 768 && window.innerWidth < 1024; // md to lg
};

/**
 * Check if device is desktop based on screen width
 * @returns {boolean} True if desktop device
 */
export const isDesktop = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= 1024; // lg breakpoint and above
};

/**
 * Get device type
 * @returns {string} Device type ('mobile', 'tablet', 'desktop')
 */
export const getDeviceType = () => {
  if (isMobile()) return 'mobile';
  if (isTablet()) return 'tablet';
  return 'desktop';
};

/**
 * Get responsive animation duration based on device
 * @returns {number} Animation duration in milliseconds
 */
export const getAnimationDuration = () => {
  const deviceType = getDeviceType();
  
  const durations = {
    mobile: 200,    // Faster on mobile for better performance
    tablet: 300,    // Standard duration
    desktop: 400    // Slightly longer for desktop
  };
  
  return durations[deviceType] || durations.desktop;
};

/**
 * Get responsive motion settings for Framer Motion
 * @returns {Object} Motion settings
 */
export const getMotionSettings = () => {
  const deviceType = getDeviceType();
  
  const settings = {
    mobile: {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.2, ease: "easeOut" }
    },
    tablet: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.3, ease: "easeOut" }
    },
    desktop: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.4, ease: "easeOut" }
    }
  };
  
  return settings[deviceType] || settings.desktop;
};

export default {
  breakpoints,
  getContainerClasses,
  getSpacingClasses,
  getTextClasses,
  getButtonClasses,
  getGridClasses,
  getCardClasses,
  getFormFieldClasses,
  isMobile,
  isTablet,
  isDesktop,
  getDeviceType,
  getAnimationDuration,
  getMotionSettings
};
