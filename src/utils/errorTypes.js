/**
 * Error Types and Messages
 *
 * This file defines the error types and messages used in the application.
 */

/**
 * API Error Types
 * These are the types of errors that can occur when making API requests.
 */
export const API_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * Wallet-Specific Error Types
 * These are specific to wallet and financial operations.
 */
export const WALLET_ERROR_TYPES = {
  // Verification Errors
  EMAIL_VERIFICATION_REQUIRED: 'EMAIL_VERIFICATION_REQUIRED',
  EKYC_VERIFICATION_REQUIRED: 'EKYC_VERIFICATION_REQUIRED',
  VERIFICATION_PENDING: 'VERIFICATION_PENDING',
  VERIFICATION_FAILED: 'VERIFICATION_FAILED',

  // Balance & Transaction Errors
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  BALANCE_FETCH_FAILED: 'BALANCE_FETCH_FAILED',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  TRANSACTION_PENDING: 'TRANSACTION_PENDING',

  // Payment Errors
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  PAYMENT_TIMEOUT: 'PAYMENT_TIMEOUT',
  INVALID_PAYMENT_METHOD: 'INVALID_PAYMENT_METHOD',

  // Withdrawal Errors
  WITHDRAWAL_FAILED: 'WITHDRAWAL_FAILED',
  WITHDRAWAL_LIMIT_EXCEEDED: 'WITHDRAWAL_LIMIT_EXCEEDED',
  INVALID_BANK_ACCOUNT: 'INVALID_BANK_ACCOUNT',
  WITHDRAWAL_NOT_ELIGIBLE: 'WITHDRAWAL_NOT_ELIGIBLE',

  // Service Errors
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  FEATURE_DISABLED: 'FEATURE_DISABLED'
};

/**
 * Get a human-readable error message for a given error type
 * @param {string} errorType - The type of error
 * @param {string|null} details - Optional details to include in the message
 * @returns {string} A human-readable error message
 */
export const getErrorMessage = (errorType, details = null) => {
  // Handle API error types
  switch (errorType) {
    case API_ERROR_TYPES.NETWORK_ERROR:
      return 'Network error. Please check your connection and try again.';
    case API_ERROR_TYPES.AUTHENTICATION_ERROR:
      return 'Authentication error. Please log in again.';
    case API_ERROR_TYPES.VALIDATION_ERROR:
      return details || 'Invalid input. Please check your data and try again.';
    case API_ERROR_TYPES.SERVER_ERROR:
      return 'Server error. Please try again later.';
    case API_ERROR_TYPES.NOT_FOUND_ERROR:
      return 'The requested resource was not found.';
    case API_ERROR_TYPES.RATE_LIMIT_ERROR:
      return 'Too many requests. Please try again later.';
    case API_ERROR_TYPES.UNKNOWN_ERROR:
      return 'An unexpected error occurred. Please try again.';
  }

  // Handle wallet-specific error types
  switch (errorType) {
    // Verification Errors
    case WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED:
      return 'Email verification required. Please verify your email address to continue.';
    case WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED:
      return 'Identity verification (E-KYC) required. Please complete your identity verification.';
    case WALLET_ERROR_TYPES.VERIFICATION_PENDING:
      return 'Your verification is being processed. Please wait for approval.';
    case WALLET_ERROR_TYPES.VERIFICATION_FAILED:
      return 'Verification failed. Please try again or contact support.';

    // Balance & Transaction Errors
    case WALLET_ERROR_TYPES.INSUFFICIENT_BALANCE:
      return 'Insufficient balance. Please add credits to your account.';
    case WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED:
      return 'Unable to fetch balance. Please refresh and try again.';
    case WALLET_ERROR_TYPES.TRANSACTION_FAILED:
      return 'Transaction failed. Please try again.';
    case WALLET_ERROR_TYPES.TRANSACTION_PENDING:
      return 'Transaction is being processed. Please wait.';

    // Payment Errors
    case WALLET_ERROR_TYPES.PAYMENT_FAILED:
      return 'Payment failed. Please check your payment method and try again.';
    case WALLET_ERROR_TYPES.PAYMENT_CANCELLED:
      return 'Payment was cancelled. You can try again when ready.';
    case WALLET_ERROR_TYPES.PAYMENT_TIMEOUT:
      return 'Payment timed out. Please try again.';
    case WALLET_ERROR_TYPES.INVALID_PAYMENT_METHOD:
      return 'Invalid payment method. Please select a different payment option.';

    // Withdrawal Errors
    case WALLET_ERROR_TYPES.WITHDRAWAL_FAILED:
      return 'Withdrawal failed. Please check your details and try again.';
    case WALLET_ERROR_TYPES.WITHDRAWAL_LIMIT_EXCEEDED:
      return 'Withdrawal limit exceeded. Please reduce the amount.';
    case WALLET_ERROR_TYPES.INVALID_BANK_ACCOUNT:
      return 'Invalid bank account. Please check your bank details.';
    case WALLET_ERROR_TYPES.WITHDRAWAL_NOT_ELIGIBLE:
      return 'You are not eligible for withdrawals. Please complete verification.';

    // Service Errors
    case WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE:
      return 'Service temporarily unavailable. Please try again later.';
    case WALLET_ERROR_TYPES.MAINTENANCE_MODE:
      return 'System maintenance in progress. Please try again later.';
    case WALLET_ERROR_TYPES.FEATURE_DISABLED:
      return 'This feature is currently disabled. Please contact support.';

    default:
      return details || 'An unexpected error occurred. Please try again.';
  }
};

/**
 * Get a user-friendly error message from an API error response
 * @param {Object} error - The error object from the API
 * @returns {string} A user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error) => {
  // If the error has a message property, use that
  if (error.message) {
    return error.message;
  }

  // If the error has a response with a message property, use that
  if (error.response && error.response.data && error.response.data.message) {
    return error.response.data.message;
  }

  // Otherwise, return a generic error message
  return 'An error occurred. Please try again.';
};

/**
 * Get error action suggestions for wallet errors
 * @param {string} errorType - The type of error
 * @returns {Array} Array of suggested actions
 */
export const getErrorActions = (errorType) => {
  switch (errorType) {
    case WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED:
      return [
        { label: 'Verify Email', action: 'verify_email', primary: true },
        { label: 'Resend Email', action: 'resend_email', primary: false }
      ];

    case WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED:
      return [
        { label: 'Complete E-KYC', action: 'start_ekyc', primary: true },
        { label: 'Learn More', action: 'ekyc_info', primary: false }
      ];

    case WALLET_ERROR_TYPES.INSUFFICIENT_BALANCE:
      return [
        { label: 'Add Credits', action: 'add_credits', primary: true },
        { label: 'View Packages', action: 'view_packages', primary: false }
      ];

    case WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED:
    case WALLET_ERROR_TYPES.TRANSACTION_FAILED:
    case WALLET_ERROR_TYPES.PAYMENT_FAILED:
    case WALLET_ERROR_TYPES.WITHDRAWAL_FAILED:
      return [
        { label: 'Try Again', action: 'retry', primary: true },
        { label: 'Contact Support', action: 'contact_support', primary: false }
      ];

    case API_ERROR_TYPES.NETWORK_ERROR:
      return [
        { label: 'Retry', action: 'retry', primary: true },
        { label: 'Check Connection', action: 'check_connection', primary: false }
      ];

    default:
      return [
        { label: 'Try Again', action: 'retry', primary: true }
      ];
  }
};

/**
 * Determine if an error is recoverable
 * @param {string} errorType - The type of error
 * @returns {boolean} Whether the error is recoverable
 */
export const isRecoverableError = (errorType) => {
  const recoverableErrors = [
    API_ERROR_TYPES.NETWORK_ERROR,
    API_ERROR_TYPES.RATE_LIMIT_ERROR,
    WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED,
    WALLET_ERROR_TYPES.TRANSACTION_FAILED,
    WALLET_ERROR_TYPES.PAYMENT_TIMEOUT,
    WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE
  ];

  return recoverableErrors.includes(errorType);
};

/**
 * Get error severity level
 * @param {string} errorType - The type of error
 * @returns {string} Severity level: 'low', 'medium', 'high', 'critical'
 */
export const getErrorSeverity = (errorType) => {
  switch (errorType) {
    case API_ERROR_TYPES.NETWORK_ERROR:
    case WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED:
    case WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE:
      return 'medium';

    case API_ERROR_TYPES.AUTHENTICATION_ERROR:
    case WALLET_ERROR_TYPES.VERIFICATION_FAILED:
    case WALLET_ERROR_TYPES.PAYMENT_FAILED:
    case WALLET_ERROR_TYPES.WITHDRAWAL_FAILED:
      return 'high';

    case API_ERROR_TYPES.SERVER_ERROR:
    case WALLET_ERROR_TYPES.FEATURE_DISABLED:
    case WALLET_ERROR_TYPES.MAINTENANCE_MODE:
      return 'critical';

    default:
      return 'low';
  }
};

export default {
  API_ERROR_TYPES,
  WALLET_ERROR_TYPES,
  getErrorMessage,
  getUserFriendlyErrorMessage,
  getErrorActions,
  isRecoverableError,
  getErrorSeverity
};
