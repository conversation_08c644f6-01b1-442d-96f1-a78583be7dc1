import imageCompression from 'browser-image-compression';
import { useState, useEffect, useRef } from 'react';
import { cn } from '../utils/classnames';  // Adjust this import path based on your project structure

export const compressImage = async (file, options = {}) => {
    const {
        maxWidth = 1200,
        maxHeight = 1200,
        quality = 0.8,
        format = 'image/jpeg'
    } = options;
    
    // Skip non-image files
    if (!file.type.startsWith('image/')) {
        return file;
    }
    
    // Skip GIFs to preserve animation
    if (file.type === 'image/gif') {
        return file;
    }
    
    try {
        // Create a preview
        const preview = URL.createObjectURL(file);
        
        // Load the image
        const img = await new Promise((resolve, reject) => {
            const image = new Image();
            image.onload = () => resolve(image);
            image.onerror = reject;
            image.src = preview;
        });
        
        // Release the object URL
        URL.revokeObjectURL(preview);
        
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;
        
        if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
        }
        
        if (height > maxHeight) {
            width = Math.round((width * maxHeight) / height);
            height = maxHeight;
        }
        
        // Create canvas for resizing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = width;
        canvas.height = height;
        
        // Draw image on canvas with new dimensions
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert to blob
        const blob = await new Promise(resolve => 
            canvas.toBlob(resolve, format, quality)
        );
        
        // Create new File object from blob
        return new File([blob], file.name, {
            type: format,
            lastModified: Date.now()
        });
    } catch (error) {
        console.error('Error compressing image:', error);
        return file; // Return original file on error
    }
};

export const generateThumbnail = async (file, maxWidth = 100) => {
    return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                const aspectRatio = img.width / img.height;
                const height = maxWidth / aspectRatio;
                
                canvas.width = maxWidth;
                canvas.height = height;
                
                ctx.drawImage(img, 0, 0, maxWidth, height);
                
                canvas.toBlob((blob) => {
                    resolve(URL.createObjectURL(blob));
                }, 'image/jpeg', 0.7);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });
};

export const preloadImage = (src) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
    });
};

/**
 * LazyImage component for efficient image loading with fallback and error handling
 * @param {string} src - Image source URL
 * @param {string} alt - Alt text for the image
 * @param {string} className - CSS classes for the image
 * @param {string} fallbackSrc - Fallback image to show on error (optional)
 * @param {Object} props - Additional props to pass to the img element
 */
export const LazyImage = ({ 
    src, 
    alt, 
    className = '', 
    fallbackSrc = null,
    ...props 
}) => {
    const [loaded, setLoaded] = useState(false);
    const [error, setError] = useState(false);
    const [imageSrc, setImageSrc] = useState(null);

    useEffect(() => {
        // Reset state when src changes
        setLoaded(false);
        setError(false);
        
        // Start loading the image
        const img = new Image();
        img.src = src;
        
        img.onload = () => {
            setImageSrc(src);
            setLoaded(true);
        };
        
        img.onerror = () => {
            if (fallbackSrc) {
                setImageSrc(fallbackSrc);
                setLoaded(true);
            } else {
                setError(true);
            }
        };
        
        // Set initial src right away
        setImageSrc(src);
        
        // Cleanup function
        return () => {
            img.onload = null;
            img.onerror = null;
        };
    }, [src, fallbackSrc]);

    // Placeholder while loading
    if (!loaded && !error) {
        return (
            <div className={`${className} bg-gray-200 animate-pulse flex items-center justify-center`}>
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className={`${className} bg-red-100 flex items-center justify-center`}>
                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
            </div>
        );
    }

    // Loaded state
    return (
        <img
            src={imageSrc}
            alt={alt}
            className={className}
            loading="lazy"
            {...props}
        />
    );
};

/**
 * Get dimensions of an image file
 * @param {File} file - Image file
 * @returns {Promise<{width: number, height: number}>} Image dimensions
 */
export const getImageDimensions = async (file) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        const objectUrl = URL.createObjectURL(file);
        
        img.onload = () => {
            URL.revokeObjectURL(objectUrl);
            resolve({
                width: img.width,
                height: img.height
            });
        };
        
        img.onerror = () => {
            URL.revokeObjectURL(objectUrl);
            reject(new Error('Failed to load image'));
        };
        
        img.src = objectUrl;
    });
}; 