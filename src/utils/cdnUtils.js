/**
 * Utility functions for handling CDN URLs
 */

// Remove trailing slashes from CDN URL
// IMPORTANT: Set REACT_APP_CDN_URL in your .env file to your CDN base URL
const CDN_URL = (process.env.REACT_APP_CDN_URL || 'https://your-default-cdn.com').replace(/\/+$/, '');

/**
 * Converts a relative path to a full CDN URL
 * @param {string} path - The relative path to the asset
 * @returns {string} The full CDN URL
 */
export const getCdnUrl = (path) => {
  if (!path || typeof path !== 'string') return '';
  // Accept full URLs, protocol-relative URLs, and local object URLs
  if (
    path.startsWith('http://') ||
    path.startsWith('https://') ||
    path.startsWith('//') ||
    path.startsWith('blob:')
  ) {
    return path;
  }
  // Replace any backslashes with forward slashes then trim extraneous slashes
  const cleanPath = path
    .replace(/\\+/g, '/')
    .replace(/^\/+/, '')
    .replace(/\/+$/, '');
  return `${CDN_URL}/${cleanPath}`;
};

/**
 * Transforms image URLs in an object or array of objects
 * @param {Object|Array} data - The data containing image URLs
 * @param {string|Array} imageFields - The field(s) containing image URLs
 * @returns {Object|Array} The data with transformed image URLs
 */
export const transformImageUrls = (data, imageFields = ['image_url', 'profile_picture', 'cover_image']) => {
  if (!data) return data;
  
  // Convert single field to array
  const fields = Array.isArray(imageFields) ? imageFields : [imageFields];
  
  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => transformImageUrls(item, fields));
  }
  
  // Handle objects
  if (typeof data === 'object') {
    const result = { ...data };
    
    fields.forEach(field => {
      if (result[field] && typeof result[field] === 'string') {
        result[field] = getCdnUrl(result[field]);
      }
    });
    
    return result;
  }
  
  return data;
}; 