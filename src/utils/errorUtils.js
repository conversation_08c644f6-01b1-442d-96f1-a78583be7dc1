/**
 * Utility functions for handling API errors
 */

/**
 * Extract error message from API error response
 * @param {Error} error - The error object
 * @returns {string} - The error message
 */
export const getApiErrorMessage = (error) => {
  if (!error) return 'An unknown error occurred';

  // Handle axios error response
  if (error.response) {
    const { data } = error.response;
    if (data && data.message) {
      return data.message;
    }
    if (data && data.error) {
      return data.error;
    }
  }

  // Handle error with message property
  if (error.message) {
    return error.message;
  }

  return 'An error occurred while processing your request';
}; 