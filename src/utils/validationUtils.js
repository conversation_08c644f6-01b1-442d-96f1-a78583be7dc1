/**
 * Validation Utilities
 * Utility functions for form validation
 */

/**
 * Validate an email address
 * @param {string} email - The email to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const validateEmail = (email) => {
  if (!email) return false;

  // Basic email validation regex
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

/**
 * Validate a password using exact backend Password rule
 * Backend rule: minimum 6 characters, must contain at least one letter and one number
 * Regex: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W]+$/
 * @param {string} password - The password to validate
 * @returns {Object} Validation result with valid status and message
 */
export const validatePassword = (password) => {
  if (!password) {
    return { valid: false, message: 'Password is required' };
  }

  // Backend Password rule: minimum 6 characters
  if (password.length < 6) {
    return { valid: false, message: 'Password must be at least 6 characters long' };
  }

  // Backend Password rule: must contain at least one letter and one number
  const hasLetter = /[A-Za-z]/.test(password);
  const hasNumber = /\d/.test(password);

  if (!hasLetter || !hasNumber) {
    return {
      valid: false,
      message: 'Password must contain at least one letter and one number'
    };
  }

  return { valid: true, message: 'Password is valid' };
};

/**
 * Calculate password strength on a scale of 0-4
 * @param {string} password - The password to evaluate
 * @returns {number} Strength score (0-4)
 */
export const calculatePasswordStrength = (password) => {
  if (!password) return 0;

  let strength = 0;

  // Length check
  if (password.length >= 8) strength += 1;

  // Uppercase check
  if (/[A-Z]/.test(password)) strength += 1;

  // Number check
  if (/[0-9]/.test(password)) strength += 1;

  // Special character check
  if (/[!@#$%^&*]/.test(password)) strength += 1;

  return strength;
};

/**
 * Get password strength label based on score
 * @param {number} strength - Strength score (0-4)
 * @returns {string} Strength label
 */
export const getPasswordStrengthLabel = (strength) => {
  const labels = ['Too Weak', 'Weak', 'Fair', 'Good', 'Strong'];
  return labels[strength] || 'Too Weak';
};

/**
 * Get password strength color based on score
 * @param {number} strength - Strength score (0-4)
 * @returns {string} CSS color class
 */
export const getPasswordStrengthColor = (strength) => {
  const colors = [
    'bg-red-500', // Too Weak
    'bg-orange-500', // Weak
    'bg-yellow-500', // Fair
    'bg-blue-500', // Good
    'bg-green-500' // Strong
  ];

  return colors[strength] || 'bg-red-500';
};

/**
 * Get password strength text color based on score
 * @param {number} strength - Strength score (0-4)
 * @returns {string} CSS text color class
 */
export const getPasswordStrengthTextColor = (strength) => {
  const colors = [
    'text-red-500', // Too Weak
    'text-orange-500', // Weak
    'text-yellow-600', // Fair
    'text-blue-600', // Good
    'text-green-600' // Strong
  ];

  return colors[strength] || 'text-red-500';
};

/**
 * Validate a name (first name, last name)
 * @param {string} name - The name to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const validateName = (name) => {
  if (!name) return false;

  // Name should be at least 2 characters and contain only letters, spaces, hyphens, and apostrophes
  return /^[A-Za-z\s'-]{2,}$/.test(name.trim());
};

/**
 * Validate a date of birth
 * @param {string} dob - The date of birth to validate (YYYY-MM-DD)
 * @param {number} minAge - Minimum age required (default: 13)
 * @returns {Object} Validation result with valid status and message
 */
export const validateDateOfBirth = (dob, minAge = 13) => {
  if (!dob) {
    return { valid: false, message: 'Date of birth is required' };
  }

  try {
    const birthDate = new Date(dob);
    const today = new Date();

    // Check if date is valid
    if (isNaN(birthDate.getTime())) {
      return { valid: false, message: 'Invalid date format' };
    }

    // Calculate age
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Check minimum age
    if (age < minAge) {
      return { valid: false, message: `You must be at least ${minAge} years old` };
    }

    // Check if date is in the future
    if (birthDate > today) {
      return { valid: false, message: 'Date of birth cannot be in the future' };
    }

    return { valid: true, message: 'Date of birth is valid' };
  } catch (error) {
    return { valid: false, message: 'Invalid date format' };
  }
};

/**
 * Validate a nickname using exact backend Nickname rule
 * Backend rule: maximum 20 characters, emoji support enabled
 * @param {string} nickname - The nickname to validate
 * @returns {Object} Validation result with valid status and message
 */
export const validateNickname = (nickname) => {
  if (!nickname || nickname.trim() === '') {
    return { valid: false, message: 'Nickname is required' };
  }

  // Backend Nickname rule: maximum 20 characters (using mb_strlen for emoji support)
  if (nickname.length > 20) {
    return { valid: false, message: 'Nickname must not exceed 20 characters' };
  }

  return { valid: true, message: 'Nickname is valid' };
};

/**
 * Validate a Malaysian phone number using exact backend MalaysianPhone rule
 * Backend regex: /^(\+?6?01)(?:1[0-9]{8}|[0-46-9][0-9]{7})(?:-[0-9]+)*$/
 * @param {string} phoneNumber - The phone number to validate
 * @returns {Object} Validation result with valid status and message
 */
export const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber || phoneNumber.trim() === '') {
    return { valid: false, message: 'Phone number is required' };
  }

  // Backend MalaysianPhone rule regex
  const malaysianPhoneRegex = /^(\+?6?01)(?:1[0-9]{8}|[0-46-9][0-9]{7})(?:-[0-9]+)*$/;

  if (!malaysianPhoneRegex.test(phoneNumber.trim())) {
    return { valid: false, message: 'Please enter a valid Malaysian phone number' };
  }

  return { valid: true, message: 'Phone number is valid' };
};