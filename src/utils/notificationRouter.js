export const getNotificationRoute = (notification) => {
  const type =
    notification.type ||
    notification.data?.type ||
    notification.data?.notification_type;
  switch (type) {
    case 'new_order':
    case 'order_response':
    case 'order_completed':
    case 'prebooked_order_request':
    case 'scheduled_order_response':
      return notification.order_id || notification.data?.order_id
        ? `/orders/${notification.order_id || notification.data.order_id}`
        : null;
    case 'chat_message':
      return notification.conversation_id || notification.data?.conversation_id
        ? `/chat/${notification.conversation_id || notification.data.conversation_id}`
        : '/chat';
    case 'new_follower':
      return notification.follower_id || notification.data?.follower_id
        ? `/profile/${notification.follower_id || notification.data.follower_id}`
        : null;
    case 'referral_reward':
      return '/wallet';
    case 'mission_auto_cancelled':
      return notification.mission_id || notification.data?.mission_id
        ? `/orders/${notification.mission_id || notification.data.mission_id}`
        : null;
    default:
      return null;
  }
};

export const handleNotificationNavigation = (notification, navigate) => {
  const route = getNotificationRoute(notification);
  if (route) {
    navigate(route);
  }
};

export const validateRouteParams = (notification) => {
  const type =
    notification.type ||
    notification.data?.type ||
    notification.data?.notification_type;
  if (!type) return false;
  if (type.includes('order') && !(notification.order_id || notification.data?.order_id)) {
    return false;
  }
  return true;
};
