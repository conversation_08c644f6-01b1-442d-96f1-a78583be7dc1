/**
 * Extract error message from API error response
 * @param {Object} error - Error object from axios
 * @returns {String} - Formatted error message
 */
export const getApiErrorMessage = (error) => {
  // Default error message
  let message = 'An unexpected error occurred. Please try again.';
  
  if (!error) return message;
  
  // Handle axios error structure
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const { data, status } = error.response;
    
    // Handle validation errors
    if (status === 422 && data.errors) {
      const validationErrors = Object.values(data.errors)
        .flat()
        .join(', ');
      
      return `Validation error: ${validationErrors}`;
    }
    
    // Handle other status codes
    if (data.message) {
      return data.message;
    }
    
    // Handle specific status codes
    switch (status) {
      case 401:
        return 'You are not authorized. Please log in again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `Error: ${status}`;
    }
  } else if (error.request) {
    // The request was made but no response was received
    return 'No response from server. Please check your internet connection.';
  } else {
    // Something happened in setting up the request that triggered an Error
    return error.message || message;
  }
};

/**
 * Format validation errors from API response
 * @param {Object} errors - Errors object from API response
 * @returns {Object} - Formatted errors object for form display
 */
export const formatValidationErrors = (errors) => {
  if (!errors) return {};
  
  const formattedErrors = {};
  
  // Convert API error format to form error format
  Object.entries(errors).forEach(([field, messages]) => {
    // Convert snake_case to camelCase for frontend
    const camelCaseField = field.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
    
    // Use the first error message
    formattedErrors[camelCaseField] = Array.isArray(messages) ? messages[0] : messages;
  });
  
  return formattedErrors;
};

/**
 * Handle API errors in a consistent way
 * @param {Object} error - Error object from axios
 * @param {Function} setFormErrors - Function to set form errors
 * @param {Function} showToast - Function to show toast notification
 * @returns {Object} - Formatted errors object
 */
export const handleApiError = (error, setFormErrors = null, showToast = null) => {
  const errorMessage = getApiErrorMessage(error);
  
  // Show toast notification if function is provided
  if (showToast) {
    showToast(errorMessage, 'error');
  }
  
  // Set form errors if function is provided and error is validation error
  if (setFormErrors && error.response && error.response.status === 422 && error.response.data.errors) {
    const formErrors = formatValidationErrors(error.response.data.errors);
    setFormErrors(formErrors);
    return formErrors;
  }
  
  // Log error to console
  console.error('API Error:', error);
  
  return {};
};
