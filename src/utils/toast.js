// src/utils/toast.js
let toastHandler = null;

export function setToastHandler(handler) {
  toastHandler = handler;
}

export function showErrorToast(message, duration = 4000) {
  if (toastHandler) toastHandler({ title: message, type: 'error', duration });
}

export function showWarningToast(message, duration = 4000) {
  if (toastHandler) toastHandler({ title: message, type: 'warning', duration });
}

export function showInfoToast(message, duration = 4000) {
  if (toastHandler) toastHandler({ title: message, type: 'info', duration });
}

export function showSuccessToast(message, duration = 4000) {
  if (toastHandler) toastHandler({ title: message, type: 'success', duration });
} 