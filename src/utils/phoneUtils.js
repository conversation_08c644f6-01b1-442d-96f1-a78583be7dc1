/**
 * Phone Utilities
 * Utility functions for handling phone numbers
 */

/**
 * Normalize a phone number by removing non-digit characters and country code
 * @param {string} phoneNumber - The phone number to normalize
 * @param {string} countryCode - The country code (e.g., +60)
 * @returns {string} Normalized phone number
 */
export const normalizePhoneNumber = (phoneNumber, countryCode) => {
  if (!phoneNumber) return '';
  
  // Remove any non-digit characters
  let normalized = phoneNumber.replace(/\D/g, '');
  
  // Remove country code if it's at the beginning of the phone number
  if (countryCode && countryCode.startsWith('+')) {
    const code = countryCode.substring(1); // Remove the '+'
    if (normalized.startsWith(code)) {
      normalized = normalized.substring(code.length);
    }
  }
  
  return normalized;
};

/**
 * Format a phone number for display
 * @param {string} phoneNumber - The phone number to format
 * @param {string} countryCode - The country code (e.g., +60)
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber, countryCode = '') => {
  if (!phoneNumber) return '';
  
  // Remove any non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Different formatting based on length and country
  if (countryCode === '+60') { // Malaysia
    // Format: +60 12-345-6789
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
    } else {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 5)}-${cleaned.slice(5)}`;
    }
  } else if (countryCode === '+65') { // Singapore
    // Format: +65 1234-5678
    if (cleaned.length <= 4) {
      return cleaned;
    } else {
      return `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
    }
  } else {
    // Generic formatting
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 7) {
      return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
    } else {
      return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
  }
};

/**
 * Validate a phone number
 * @param {string} phoneNumber - The phone number to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return false;
  
  // Remove any non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if the cleaned number has between 9 and 15 digits (international standard)
  return /^\d{9,15}$/.test(cleaned);
};

/**
 * Get country code options
 * @returns {Array} Array of country code objects with code and name
 */
export const getCountryCodes = () => {
  return [
    { code: '+60', name: 'Malaysia' },
    { code: '+65', name: 'Singapore' },
    { code: '+62', name: 'Indonesia' },
    { code: '+66', name: 'Thailand' },
    { code: '+63', name: 'Philippines' },
    { code: '+84', name: 'Vietnam' },
    { code: '+95', name: 'Myanmar' },
    { code: '+673', name: 'Brunei' },
    { code: '+855', name: 'Cambodia' },
    { code: '+856', name: 'Laos' }
  ];
};

/**
 * Get country name from country code
 * @param {string} countryCode - The country code (e.g., +60)
 * @returns {string} Country name
 */
export const getCountryNameFromCode = (countryCode) => {
  const country = getCountryCodes().find(c => c.code === countryCode);
  return country ? country.name : 'Unknown';
};

/**
 * Format phone number with country code for display
 * @param {string} phoneNumber - The phone number
 * @param {string} countryCode - The country code (e.g., +60)
 * @returns {string} Formatted phone number with country code
 */
export const formatPhoneWithCountryCode = (phoneNumber, countryCode) => {
  if (!phoneNumber) return '';
  
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);
  const formattedPhone = formatPhoneNumber(normalizedPhone, countryCode);
  
  return `${countryCode} ${formattedPhone}`;
};
