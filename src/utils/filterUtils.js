/**
 * Filter Utilities
 *
 * This file contains utility functions for working with filters
 * in the talent filtering system.
 */

/**
 * Convert frontend filter format to API filter format
 * @param {Object} filters - Frontend filter object
 * @returns {Object} API filter object
 */
export const convertFiltersToApiFormat = (filters = {}) => {
  return {
    // Basic service filters - correctly mapped
    service_category_id: filters.serviceCategoryId,
    service_type_id: filters.serviceTypeId,
    service_style_id: filters.serviceStyleId,

    // Experience level - fixed parameter names
    min_experience_level: filters.experienceLevel ? filters.experienceLevel[0] : undefined,
    max_experience_level: filters.experienceLevel ? filters.experienceLevel[1] : undefined,

    // Price range - fixed parameter names
    price_from: filters.priceRange ? filters.priceRange[0] : undefined,
    price_to: filters.priceRange ? filters.priceRange[1] : undefined,

    // Other filters
    gender: filters.gender ? filters.gender.toLowerCase() : undefined,
    language: filters.languages ? filters.languages.join(',') : undefined,

    // Search term mapping
    keyword: filters.searchTerm,

    // Race filter
    race_id: filters.raceId,

    // Sorting
    sort_by: filters.sortBy || 'created_at',
    sort_direction: filters.sortDirection || 'desc'
  };
};

/**
 * Convert API filter format to frontend filter format
 * @param {Object} apiFilters - API filter object
 * @returns {Object} Frontend filter object
 */
export const convertApiFiltersToFrontendFormat = (apiFilters = {}) => {
  return {
    // Basic service filters
    serviceCategoryId: apiFilters.service_category_id,
    serviceTypeId: apiFilters.service_type_id,
    serviceStyleId: apiFilters.service_style_id,

    // Experience level - using correct parameter names
    experienceLevel: apiFilters.min_experience_level || apiFilters.max_experience_level
      ? [apiFilters.min_experience_level || 1, apiFilters.max_experience_level || 99]
      : undefined,

    // Price range - using correct parameter names
    priceRange: apiFilters.price_from || apiFilters.price_to
      ? [apiFilters.price_from || 0, apiFilters.price_to || 300]
      : undefined,

    // Other filters
    gender: apiFilters.gender ? apiFilters.gender.charAt(0).toUpperCase() + apiFilters.gender.slice(1) : '',
    languages: apiFilters.language ? apiFilters.language.split(',') : [],

    // Search term
    searchTerm: apiFilters.keyword || '',

    // Race filter
    raceId: apiFilters.race_id,

    // Sorting
    sortBy: apiFilters.sort_by || 'created_at',
    sortDirection: apiFilters.sort_direction || 'desc'
  };
};

/**
 * Check if a filter object has any active filters
 * @param {Object} filters - Filter object
 * @returns {boolean} True if any filters are active
 */
export const hasActiveFilters = (filters = {}) => {
  if (!filters) return false;

  return !!(
    // Service filters
    filters.serviceCategoryId ||
    filters.serviceTypeId ||
    filters.serviceStyleId ||

    // Experience level
    (filters.experienceLevel && (
      filters.experienceLevel[0] > 1 ||
      filters.experienceLevel[1] < 99
    )) ||

    // Price range
    (filters.priceRange && (
      filters.priceRange[0] > 0 ||
      filters.priceRange[1] < 300
    )) ||

    // Other filters
    filters.gender ||
    (filters.languages && filters.languages.length > 0) ||

    // Search term
    filters.searchTerm ||

    // Race filter
    filters.raceId ||

    // Sorting
    (filters.sortBy && filters.sortBy !== 'created_at') ||
    (filters.sortDirection && filters.sortDirection !== 'desc')
  );
};

/**
 * Count the number of active filters
 * @param {Object} filters - Filter object
 * @returns {number} Number of active filters
 */
export const countActiveFilters = (filters = {}) => {
  if (!filters) return 0;

  let count = 0;

  // Service filters
  if (filters.serviceCategoryId) count++;
  if (filters.serviceTypeId) count++;
  if (filters.serviceStyleId) count++;

  // Experience level
  if (filters.experienceLevel && (
    filters.experienceLevel[0] > 1 ||
    filters.experienceLevel[1] < 99
  )) count++;

  // Price range
  if (filters.priceRange && (
    filters.priceRange[0] > 0 ||
    filters.priceRange[1] < 300
  )) count++;

  // Other filters
  if (filters.gender) count++;

  if (filters.languages && filters.languages.length > 0) {
    count += filters.languages.length;
  }

  // Search term
  if (filters.searchTerm) count++;

  // Race filter
  if (filters.raceId) count++;

  // Sorting
  if (filters.sortBy && filters.sortBy !== 'created_at') count++;
  if (filters.sortDirection && filters.sortDirection !== 'desc') count++;

  return count;
};

/**
 * Get a human-readable description of a filter
 * @param {string} filterKey - The filter key
 * @param {any} filterValue - The filter value
 * @returns {string} Human-readable description
 */
export const getFilterDescription = (filterKey, filterValue) => {
  switch (filterKey) {
    // Service filters
    case 'serviceCategoryId':
      return `Category: ${filterValue}`;
    case 'serviceTypeId':
      return `Type: ${filterValue}`;
    case 'serviceStyleId':
      return `Style: ${filterValue}`;

    // Experience level
    case 'experienceLevel':
      return `Level: ${filterValue[0]}-${filterValue[1]}`;

    // Price range
    case 'priceRange':
      return `Price: ${filterValue[0]}-${filterValue[1]} Credits`;

    // Other filters
    case 'gender':
      return `Gender: ${filterValue}`;
    case 'languages':
      return `Language: ${filterValue}`;

    // Search term
    case 'searchTerm':
      return `Search: ${filterValue}`;

    // Race filter
    case 'raceId':
      return `Race: ${filterValue}`;

    // Sorting
    case 'sortBy':
      return `Sort by: ${filterValue}`;
    case 'sortDirection':
      return `Order: ${filterValue === 'asc' ? 'Ascending' : 'Descending'}`;

    default:
      return `${filterKey}: ${filterValue}`;
  }
};

export default {
  convertFiltersToApiFormat,
  convertApiFiltersToFrontendFormat,
  hasActiveFilters,
  countActiveFilters,
  getFilterDescription
};
