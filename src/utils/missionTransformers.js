import { getCdnUrl } from './cdnUtils';

/**
 * Transforms a mission from the API format to the frontend format
 * @param {Object} apiMission - Mission data from the API
 * @returns {Object} - Mission data in frontend format
 */
export const transformApiMissionToFrontend = (apiMission) => {
  if (!apiMission) return null;

  // Use title and description directly from API
  const title = apiMission.title || 'Untitled Mission';
  const description = apiMission.description || '';

  // Get primary image or default
  const primaryImage = apiMission.images && apiMission.images.length > 0
    ? getCdnUrl(apiMission.images[0].optimized_path)
    : '/images/mission-default.jpg';

  // Count approved applicants
  const approvedApplicants = apiMission.applicants
    ? apiMission.applicants.filter(a => a.status === 'approved')
    : [];

  const slots_filled = approvedApplicants.length;

  // Transform participants from approved applicants
  const participants = approvedApplicants.map(a => {
    const user = a.user || {};
    return {
      id: user.id ?? a.user_id,
      user_id: a.user_id,
      uid: user.uid,
      name: user.name || 'Unknown',
      nickname: user.nickname || user.name || 'Unknown',
      biography: user.biography || user.bio || '',
      avatar: user.profile_picture ? getCdnUrl(user.profile_picture) : '/images/default-avatar.jpg',
      profile_picture: user.profile_picture,
      level: user.level && user.level.level ? user.level.level : 0,
      status: a.status,
      role: a.role || '' // Default role if not provided
    };
  });

  // Map API status to frontend status
  const statusMap = {
    'open': 'open',
    'closed': 'closed',
    'completed': 'completed',
    'in_progress': 'in progress'
  };

  return {
    id: apiMission.id,
    title,
    description,
    image: primaryImage,
    images: (apiMission.images || []).map(img =>
      typeof img === 'string' ? getCdnUrl(img) : getCdnUrl(img.optimized_path || img.url)
    ),
    bounty: apiMission.bounty || 0,
    date: apiMission.service_start_date,
    end_date: apiMission.service_end_date,
    slots_total: apiMission.pax_required || 0,
    slots_filled,
    level_requirement: {
      min: apiMission.min_level ? apiMission.min_level.level : 1,
      max: apiMission.max_level ? apiMission.max_level.level : 99
    },
    style: apiMission.service_style ? apiMission.service_style.name : 'Casual',
    category: apiMission.service_category ? apiMission.service_category.name : 'Gamer',
    status: statusMap[apiMission.status] || apiMission.status,
    host: (() => {
      const hostUser = apiMission.user || apiMission.host || {};
      return {
        id: hostUser.id ?? apiMission.user_id,
        name: hostUser.nickname || hostUser.name || 'Mission Host',
        avatar: hostUser.profile_picture
          ? getCdnUrl(hostUser.profile_picture)
          : '/images/default-avatar.jpg',
        level:
          (hostUser.level && hostUser.level.level) ||
          hostUser.level_id ||
          0
      };
    })(),
    participants,
    // Add additional fields needed by the frontend
    service_type_id: apiMission.service_type_id,
    service_style_id: apiMission.service_style_id,
    min_level_id: apiMission.min_level_id,
    is_bookmarked: apiMission.is_bookmarked ?? false,
    // Application related fields
    is_applied: apiMission.is_applied ?? false,
    apply_status: apiMission.apply_status || '',
    can_withdraw: apiMission.can_withdraw ?? false,
    current_user_child_id: apiMission.current_user_child_id
  };
};

/**
 * Transforms a list of missions from API format to frontend format
 * @param {Array} apiMissions - List of missions from the API
 * @returns {Array} - List of missions in frontend format
 */
export const transformApiMissionsToFrontend = (apiMissions) => {
  if (!apiMissions || !Array.isArray(apiMissions)) return [];

  return apiMissions.map(transformApiMissionToFrontend);
};

/**
 * Transforms an applicant from API format to frontend format
 * @param {Object} apiApplicant - Applicant data from the API
 * @returns {Object} - Applicant data in frontend format
 */
export const transformApiApplicantToFrontend = (apiApplicant) => {
  if (!apiApplicant) return null;

  return {
    id: apiApplicant.id,
    user_id: apiApplicant.user_id,
    mission_id: apiApplicant.mission_id,
    status: apiApplicant.status,
    message: apiApplicant.notes,
    applied_at: apiApplicant.created_at,
    user: {
      id: apiApplicant.user ? apiApplicant.user.id : apiApplicant.user_id,
      name: apiApplicant.user ? apiApplicant.user.name : 'Unknown',
      avatar: apiApplicant.user ? getCdnUrl(apiApplicant.user.profile_picture) : '/images/default-avatar.jpg',
      level: apiApplicant.user && apiApplicant.user.level ? apiApplicant.user.level.level : 0,
      rating: 0, // Default rating since API doesn't provide this
      reviews: 0, // Default reviews since API doesn't provide this
      completed_missions: 0 // Default completed missions since API doesn't provide this
    }
  };
};

/**
 * Transforms a mission from the frontend format to the API format
 * @param {Object} frontendMission - Mission data from the frontend
 * @returns {Object} - Mission data in API format
 */
export const transformFrontendMissionToApi = (frontendMission) => {
  if (!frontendMission) return null;

  // Validate required fields
  const requiredFields = [
    'service_type_id',
    'service_style_id',
    'bounty',
    'slots_total',
    'date'
  ];

  const missingFields = requiredFields.filter(field => {
    return frontendMission[field] === undefined || frontendMission[field] === null;
  });

  if (missingFields.length > 0) {
    console.error('Missing required fields for API transformation:', missingFields);
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }

  // Combine title and description for API format
  const fullDescription = frontendMission.title
    ? `${frontendMission.title}\n\n${frontendMission.description || ''}`
    : frontendMission.description;

  return {
    service_type_id: frontendMission.service_type_id,
    service_style_id: frontendMission.service_style_id,
    description: fullDescription,
    bounty: frontendMission.bounty,
    pax_required: frontendMission.slots_total,
    min_level_id: frontendMission.min_level_id,
    service_start_date: frontendMission.date,
    service_end_date: frontendMission.end_date,
    ...(frontendMission.is_anonymous !== undefined && {
      is_anonymous: frontendMission.is_anonymous,
    }),
    // Only include status if it's being updated
    ...(frontendMission.status && { status: frontendMission.status })
  };
};

/**
 * Transforms application data from frontend format to API format
 * @param {Object} frontendApplication - Application data from frontend
 * @returns {Object} - Application data in API format
 */
export const transformFrontendApplicationToApi = (frontendApplication) => {
  if (!frontendApplication) return null;

  return {
    notes: frontendApplication.message || ''
  };
};

/**
 * Transforms rejection data from frontend format to API format
 * @param {Object} frontendRejection - Rejection data from frontend
 * @returns {Object} - Rejection data in API format
 */
export const transformFrontendRejectionToApi = (frontendRejection) => {
  if (!frontendRejection) return {};

  return {
    rejection_reason: frontendRejection.reason || ''
  };
};
