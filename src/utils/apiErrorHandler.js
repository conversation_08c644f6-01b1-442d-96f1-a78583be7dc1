/**
 * API Error Handler
 * 
 * This utility handles API errors and converts them into a standardized format.
 */

import { API_ERROR_TYPES, getErrorMessage } from './errorTypes';

/**
 * Handle an API error and convert it to a standardized format
 * @param {Error} error - The error object from axios
 * @returns {Object} A standardized error object
 */
export const handleApiError = (error) => {
  // If there's no response, it's a network error
  if (!error.response) {
    return {
      type: API_ERROR_TYPES.NETWORK_ERROR,
      message: getErrorMessage(API_ERROR_TYPES.NETWORK_ERROR),
      originalError: error
    };
  }

  const { status, data } = error.response;

  // Handle different HTTP status codes
  switch (status) {
    case 401:
      return {
        type: API_ERROR_TYPES.AUTHENTICATION_ERROR,
        message: getErrorMessage(API_ERROR_TYPES.AUTHENTICATION_ERROR),
        originalError: error
      };
    case 403:
      return {
        type: API_ERROR_TYPES.AUTHENTICATION_ERROR,
        message: 'You do not have permission to access this resource.',
        originalError: error
      };
    case 404:
      return {
        type: API_ERROR_TYPES.NOT_FOUND_ERROR,
        message: getErrorMessage(API_ERROR_TYPES.NOT_FOUND_ERROR),
        originalError: error
      };
    case 422:
      return {
        type: API_ERROR_TYPES.VALIDATION_ERROR,
        message: data.message || getErrorMessage(API_ERROR_TYPES.VALIDATION_ERROR),
        validationErrors: data.errors || {},
        originalError: error
      };
    case 429:
      return {
        type: API_ERROR_TYPES.RATE_LIMIT_ERROR,
        message: getErrorMessage(API_ERROR_TYPES.RATE_LIMIT_ERROR),
        retryAfter: error.response.headers['retry-after'],
        originalError: error
      };
    case 500:
    case 502:
    case 503:
    case 504:
      return {
        type: API_ERROR_TYPES.SERVER_ERROR,
        message: getErrorMessage(API_ERROR_TYPES.SERVER_ERROR),
        originalError: error
      };
    default:
      return {
        type: API_ERROR_TYPES.UNKNOWN_ERROR,
        message: getErrorMessage(API_ERROR_TYPES.UNKNOWN_ERROR),
        originalError: error
      };
  }
};

/**
 * Extract validation errors from an API error response
 * @param {Object} error - The error object from handleApiError
 * @returns {Object} An object with field names as keys and error messages as values
 */
export const extractValidationErrors = (error) => {
  if (error.type !== API_ERROR_TYPES.VALIDATION_ERROR || !error.validationErrors) {
    return {};
  }

  return error.validationErrors;
};

/**
 * Check if an error is a specific type
 * @param {Object} error - The error object from handleApiError
 * @param {string} errorType - The error type to check
 * @returns {boolean} True if the error is of the specified type
 */
export const isErrorType = (error, errorType) => {
  return error && error.type === errorType;
};

export default {
  handleApiError,
  extractValidationErrors,
  isErrorType
};
