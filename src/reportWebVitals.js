/**
 * Report Web Vitals metrics for performance monitoring
 *
 * Core Web Vitals:
 * - CLS (Cumulative Layout Shift): Visual stability
 * - FID (First Input Delay): Interactivity
 * - LCP (Largest Contentful Paint): Loading performance
 *
 * Additional metrics:
 * - FCP (First Contentful Paint): Initial rendering
 * - TTFB (Time to First Byte): Server response time
 *
 * @param {Function} onPerfEntry - Callback function to handle metrics
 */
const reportWebVitals = onPerfEntry => {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      // Core Web Vitals
      getCLS(metric => {
        // Log CLS with attribution data to help identify layout shifts
        const attribution = metric.entries[0]?.sources || [];
        onPerfEntry({
          ...metric,
          name: 'CLS',
          attribution,
          rating: metric.value <= 0.1 ? 'good' : metric.value <= 0.25 ? 'needs-improvement' : 'poor'
        });
      });

      getFID(metric => {
        onPerfEntry({
          ...metric,
          name: 'FID',
          rating: metric.value <= 100 ? 'good' : metric.value <= 300 ? 'needs-improvement' : 'poor'
        });
      });

      getLCP(metric => {
        onPerfEntry({
          ...metric,
          name: 'LCP',
          rating: metric.value <= 2500 ? 'good' : metric.value <= 4000 ? 'needs-improvement' : 'poor'
        });
      });

      // Additional metrics
      getFCP(metric => {
        onPerfEntry({
          ...metric,
          name: 'FCP',
          rating: metric.value <= 1800 ? 'good' : metric.value <= 3000 ? 'needs-improvement' : 'poor'
        });
      });

      getTTFB(metric => {
        onPerfEntry({
          ...metric,
          name: 'TTFB',
          rating: metric.value <= 800 ? 'good' : metric.value <= 1800 ? 'needs-improvement' : 'poor'
        });
      });
    });
  }
};

export default reportWebVitals;
