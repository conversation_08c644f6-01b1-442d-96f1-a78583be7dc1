.App {
  text-align: center;
}

.App.with-padding {
  padding: 20px;
}

form {
  display: flex;
  flex-direction: column;
  max-width: 300px;
  margin: 0 auto;
}

form div {
  margin-bottom: 15px;
}

label {
  margin-bottom: 5px;
  display: block;
}

input {
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
}

button {
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}

/* Custom animations for MissionCard */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

@keyframes pulse-light {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.animate-pulse-light {
  animation: pulse-light 2s ease-in-out infinite;
}

/* Safe area for mobile devices */
.h-safe-bottom {
  height: env(safe-area-inset-bottom, 0px);
}