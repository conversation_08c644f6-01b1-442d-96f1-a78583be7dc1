import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import orderAPI from '../services/orderService';
import { parseNotificationPayload } from '../services/notificationService';
import { firebaseMessaging } from '../services/firebaseMessaging';
import { useAuth } from '../contexts/AuthContext';
import { connectSocket, onSocketEvent, offSocketEvent, disconnectSocket } from '../services/socketService';
import { useToast } from '../components/common/ToastProvider';

// Create context
const NotificationContext = createContext();

// Maximum number of notifications to keep
const MAX_NOTIFICATIONS = 50;

// Placeholder path for new chat message audio. Ensure the file exists in
// the public directory at the specified location.
const NEW_MESSAGE_SOUND = '/sounds/new-message.mp3';

const playMessageSound = () => {
  try {
    const audio = new Audio(NEW_MESSAGE_SOUND);
    audio.play().catch(() => { });
  } catch (err) {
    // Fails silently if the audio file is missing or cannot play
    console.error('Failed to play message sound', err);
  }
};

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const { info: showInfoToast } = useToast();
  const orderIdsRef = useRef(new Set());

  // Handle incoming notifications
  const handleNotification = (notification) => {
    const type = notification.data?.type || notification.data?.notification_type || '';
    const category = notification.category
      || (type === 'chat_message'
        ? 'chat'
        : type.includes('order')
          ? 'order'
          : 'other');

    const normalized = { ...notification, category };

    setNotifications(prev => {
      const key = notification.data?.message_id || notification.data?.notification_id;
      if (key && prev.some(n => n.data?.message_id === key || n.data?.notification_id === key)) {
        return prev;
      }
      const updated = [normalized, ...prev].slice(0, MAX_NOTIFICATIONS);
      const unread = updated.filter(n => !n.read).length;
      setUnreadCount(unread);
      saveNotificationsToLocalStorage(updated);
      return updated;
    });
  };

  // Mark notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev => {
      const updated = prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      );
      const unread = updated.filter(n => !n.read).length;
      setUnreadCount(unread);
      saveNotificationsToLocalStorage(updated);
      return updated;
    });
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => {
      const updated = prev.map(notification => ({ ...notification, read: true }));
      setUnreadCount(0);
      saveNotificationsToLocalStorage(updated);
      return updated;
    });
  };

  // Remove notification
  const removeNotification = (notificationId) => {
    setNotifications(prev => {
      const removed = prev.find(n => n.id === notificationId);
      const updated = prev.filter(notification => notification.id !== notificationId);
      const unread = updated.filter(n => !n.read).length;
      setUnreadCount(unread);
      saveNotificationsToLocalStorage(updated);
      if (removed) {
        const raw =
          removed.data?.order_id ||
          removed.data?.orderId ||
          removed.data?.id;
        if (raw != null) orderIdsRef.current.delete(String(raw));
      }
      return updated;
    });
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
    localStorage.removeItem('missionx_notifications');
    orderIdsRef.current.clear();
  };

  // Add order notification specifically
  const addOrderNotification = useCallback((orderData) => {
    const rawId =
      orderData?.data?.order_id ||
      orderData?.data?.orderId ||
      orderData?.data?.id ||
      orderData.order_id ||
      orderData.orderId ||
      orderData.id;
    const orderId = rawId != null ? String(rawId) : null;

    if (orderId) {
      if (orderIdsRef.current.has(orderId)) {
        return null;
      }
      // Mark this order ID immediately to prevent race conditions when
      // notifications arrive simultaneously from multiple sources
      orderIdsRef.current.add(orderId);
    }

    const notification = {
      id: Date.now() + Math.random(),
      title: orderData.title,
      body: orderData.body,
      data: orderData.data,
      read: false,
      timestamp: new Date().toISOString(),
      category: 'order'
    };

    setNotifications(prev => {
      const exists = orderId
        ? prev.some(
          n =>
            (n.data?.order_id == orderId ||
              n.data?.orderId == orderId ||
              n.data?.id == orderId) &&
            n.category === 'order'
        )
        : false;
      if (exists) {
        return prev;
      }

      const updated = [notification, ...prev];
      const unread = updated.filter(n => !n.read).length;
      setUnreadCount(unread);
      saveNotificationsToLocalStorage(updated);
      return updated;
    });

    return notification.id;
  }, []);

  // Add notification coming directly from FCM payload
  const addIncomingNotification = useCallback(
    (payload) => {
      const parsed = parseNotificationPayload(payload);
      if (parsed) {
        const type = parsed.data?.type;
        if (type && type.includes('order')) {
          addOrderNotification(parsed);
        } else {
          handleNotification(parsed);
        }
      }
    },
    [addOrderNotification, handleNotification]
  );


  // Save notifications to localStorage
  const saveNotificationsToLocalStorage = (notifications) => {
    localStorage.setItem('missionx_notifications', JSON.stringify(notifications));
  };

  // Load notifications from localStorage
  const loadNotificationsFromLocalStorage = () => {
    const stored = localStorage.getItem('missionx_notifications');
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        setNotifications(parsed);

        // Populate orderIdsRef with IDs from stored notifications to prevent
        // duplicates when the page reloads and the same order arrives again.
        const ids = new Set();
        parsed.forEach(n => {
          const raw =
            n.data?.order_id ||
            n.data?.orderId ||
            n.data?.id;
          if (raw != null) ids.add(String(raw));
        });
        orderIdsRef.current = ids;

        // Calculate unread count
        const count = parsed.filter(notification => !notification.read).length;
        setUnreadCount(count);
      } catch (error) {
        console.error('Error parsing stored notifications:', error);
      }
    }
  };

  // Set up notification listener
  useEffect(() => {
    // Load stored notifications
    loadNotificationsFromLocalStorage();


    // Register notification handler
    firebaseMessaging.registerHandler('chat_message', handleNotification);

    // Handler for chat messages
    const chatHandler = (payload) => {
      const conversationId =
        payload.data?.conversationId || payload.data?.conversation_id;
      if (window.location.pathname === `/chat/${conversationId}`) {
        // Optionally skip or mark as read if already in chat
        return;
      }
      addChatNotification({
        ...payload.data,
        conversationId,
        title: payload.notification?.title,
        body: payload.notification?.body,
        senderName: payload.data?.senderName || payload.data?.sender_name,
        content: payload.data?.content,
      });
    };

    // Handler for order and scheduled order notifications
    const orderHandler = async (payload) => {
      console.log('[OrderHandler] Called with payload:', payload);
      const type =
        payload.data?.type || payload.data?.notification_type || '';

      const orderId = payload.data?.order_id || payload.data?.orderId;
      let extra = {};
      if (orderId && (!payload.data?.customer_name || !payload.data?.service_name)) {
        try {
          const details = await orderAPI.getOrderDetails(orderId);
          extra = {
            customer_name:
              payload.data?.customer_name || details.customer_name || details.customer?.name,
            service_name:
              payload.data?.service_name || details.service_name || details.service?.name,
            amount: payload.data?.amount || details.amount || details.total_amount,
          };
        } catch (err) {
          console.error('Failed to fetch order details:', err);
        }
      }

      addOrderNotification({
        ...payload.data,
        ...extra,
        order_id: orderId,
        title: payload.notification?.title,
        body: payload.notification?.body,
        data: { ...payload.data, ...extra, order_id: orderId },
      });

      // Only show a generic toast for status updates, not for new order alerts
      const isNewOrder =
        type === 'order_placed' ||
        type === 'new_order' ||
        type === 'prebooked_order_request' ||
        type === 'scheduled_order';

      if (!isNewOrder && (payload.notification?.title || payload.notification?.body)) {
        showInfoToast(
          {
            title: payload.notification?.title || 'Order Update',
            description: payload.notification?.body || '',
          },
          5000
        );
      }
    };

    // Handler for gift notifications
    const giftHandler = (payload) => {
      addIncomingNotification(payload);
      if (payload.notification?.title || payload.notification?.body) {
        showInfoToast(
          {
            title: payload.notification?.title || 'Gift Received',
            description: payload.notification?.body || '',
          },
          5000
        );
      }
    };

    // Handler for follow notifications
    const followHandler = (payload) => {
      addIncomingNotification(payload);
      if (payload.notification?.title || payload.notification?.body) {
        showInfoToast(
          {
            title: payload.notification?.title || 'New Follower',
            description: payload.notification?.body || '',
          },
          5000
        );
      }
    };

    // Handler for referral notifications
    const referralHandler = (payload) => {
      addIncomingNotification(payload);
      if (payload.notification?.title || payload.notification?.body) {
        showInfoToast(
          {
            title: payload.notification?.title || 'Referral Reward',
            description: payload.notification?.body || '',
          },
          5000
        );
      }
    };

    // Register handlers for all relevant types
    firebaseMessaging.registerHandler('new_order', orderHandler);
    firebaseMessaging.registerHandler('prebooked_order_request', orderHandler);
    firebaseMessaging.registerHandler('order_response', orderHandler);
    firebaseMessaging.registerHandler('order_completed', orderHandler);
    firebaseMessaging.registerHandler('order_accepted', orderHandler);
    firebaseMessaging.registerHandler('order_rejected', orderHandler);
    firebaseMessaging.registerHandler('order_cancelled', orderHandler);
    firebaseMessaging.registerHandler('scheduled_order', orderHandler);
    firebaseMessaging.registerHandler('scheduled_order_accepted', orderHandler);
    firebaseMessaging.registerHandler('scheduled_order_rejected', orderHandler);
    firebaseMessaging.registerHandler('scheduled_order_cancelled', orderHandler);
    firebaseMessaging.registerHandler('scheduled_order_completed', orderHandler);
    firebaseMessaging.registerHandler('chat_message', chatHandler);
    firebaseMessaging.registerHandler('gift', giftHandler);
    firebaseMessaging.registerHandler('new_follower', followHandler);
    firebaseMessaging.registerHandler('referral_reward', referralHandler);
    firebaseMessaging.registerHandler('default', (payload) => {
      addIncomingNotification(payload);
      if (payload.notification?.title || payload.notification?.body) {
        showInfoToast(
          {
            title: payload.notification?.title || 'Notification',
            description: payload.notification?.body || '',
          },
          5000
        );
      }
    });

    // Clean up
    return () => {
      firebaseMessaging.unregisterHandler('chat_message', handleNotification);
    };
  }, []);

  useEffect(() => {
    // Optionally, clear notifications on logout
    if (!isAuthenticated) {
      setNotifications([]);
      setUnreadCount(0);
      localStorage.removeItem('missionx_notifications');
    }
  }, [isAuthenticated, user]);

  const addNotification = (message, type = 'info', duration = 3000) => {
    const id = Date.now().toString();
    const newNotification = {
      id,
      message,
      type,
      duration
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto dismiss
    setTimeout(() => {
      dismissNotification(id);
    }, duration);

    return id;
  };

  const dismissNotification = (id) => {
    setNotifications(prev => {
      const removed = prev.find(n => n.id === id);
      const updated = prev.filter(notification => notification.id !== id);
      if (removed) {
        const raw =
          removed.data?.order_id ||
          removed.data?.orderId ||
          removed.data?.id;
        if (raw != null) orderIdsRef.current.delete(String(raw));
      }
      return updated;
    });
  };

  // Add chat message notification
  const addChatNotification = useCallback((messageData) => {
    const giftRegex = /gift/i;
    const isGift = giftRegex.test(
      messageData.giftName ||
      messageData.gift_name ||
      messageData.title ||
      messageData.body || ''
    );

    const notification = {
      id: Date.now() + Math.random(),
      title:
        messageData.title || `${messageData.senderName || 'Someone'} sent a message`,
      body: messageData.body || messageData.content || '',
      data: {
        ...messageData,
        type: isGift ? 'gift' : 'chat_message',
        conversation_id: messageData.conversationId,
        sender_id: messageData.senderId,
        // Include message_id so duplicates can be detected across different handlers
        message_id: messageData.messageId || messageData.message_id
      },
      read: false,
      timestamp: new Date().toISOString(),
      category: 'chat'
    };

    let isNew = false;
    setNotifications(prev => {
      const msgId = messageData.messageId || messageData.message_id;
      // Deduplicate using message_id when available, otherwise fall back to conversation and content
      const exists = prev.some(n => {
        if (msgId) {
          return n.data?.message_id === msgId;
        }
        return (
          (n.data?.type === 'chat_message' || n.data?.type === 'gift') &&
          n.data?.conversation_id === messageData.conversationId &&
          n.data?.sender_id === messageData.senderId &&
          n.body === (messageData.body || messageData.content || '')
        );
      });
      if (exists) return prev;
      isNew = true;
      const updated = [notification, ...prev];
      const unread = updated.filter(n => !n.read).length;
      setUnreadCount(unread);
      saveNotificationsToLocalStorage(updated);
      return updated;
    });

    playMessageSound();

    // Show toast only if it's a new notification and not already viewing the chat
    if (isNew && !(window.location.pathname === `/chat/${messageData.conversationId}`)) {
      // Always use the real sender's name if available
      const sender = messageData.senderName || messageData.sender_name || messageData.title || 'Unknown';
      showInfoToast(
        `${sender}: ${messageData.body || messageData.content || ''}`,
        5000
      );
    }

    // Dispatch real-time chat message event for chat window
    window.dispatchEvent(new CustomEvent('realtime-chat-message', {
      detail: {
        conversationId: messageData.conversationId,
        message: messageData
      }
    }));

    return notification.id;
  }, []);

  // Listen for external chat notifications (from push handler)
  useEffect(() => {
    const handler = (event) => {
      addChatNotification(event.detail);
    };
    window.addEventListener('external-chat-notification', handler);
    return () => window.removeEventListener('external-chat-notification', handler);
  }, [addChatNotification]);

  // Get order notifications only
  const getOrderNotifications = useCallback(() => {
    return notifications.filter(notification => {
      const type = notification.data?.type || '';
      return type.includes('order') || notification.title?.toLowerCase().includes('order');
    });
  }, [notifications]);

  // Get unread order notifications count
  const getOrderUnreadCount = useCallback(() => {
    return getOrderNotifications().filter(n => !n.read).length;
  }, [getOrderNotifications]);

  const getChatUnreadCount = useCallback(() => {
    return notifications.filter(
      n => (n.data?.type === 'chat_message' || n.category === 'chat') && !n.read
    ).length;
  }, [notifications]);

  const formatNotificationTime = (timestamp) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (_) {
      return '';
    }
  };

  const getNotificationRoute = (notification) => {
    try {
      const type = notification.data?.type || notification.data?.notification_type;
      const id = notification.data?.order_id || notification.data?.mission_id;
      switch (type) {
        case 'new_order':
        case 'order_response':
        case 'order_completed':
        case 'prebooked_order_request':
        case 'scheduled_order_response':
          return id ? `/orders/${id}` : null;
        case 'chat_message':
          return notification.data?.conversation_id ? `/chat/${notification.data.conversation_id}` : '/chat';
        case 'new_follower':
          return notification.data?.follower_id ? `/profile/${notification.data.follower_id}` : null;
        case 'referral_reward':
          return '/wallet';
        case 'mission_auto_cancelled':
          return id ? `/orders/${id}` : null;
        default:
          return null;
      }
    } catch (_) {
      return null;
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'chat_message':
        return '💬';
      case 'new_order':
      case 'order_response':
      case 'order_completed':
      case 'prebooked_order_request':
      case 'scheduled_order_response':
        return '📦';
      case 'new_follower':
        return '👤';
      case 'referral_reward':
        return '🎁';
      case 'mission_auto_cancelled':
        return '⚠️';
      default:
        return '🔔';
    }
  };

  const deleteNotification = removeNotification;

  // Persist notifications whenever they change
  useEffect(() => {
    saveNotificationsToLocalStorage(notifications);
  }, [notifications]);

  // Listen for service worker messages (background notifications)
  // useEffect(() => {
  //   if ('serviceWorker' in navigator) {
  //     const handler = (event) => {
  //       console.log('[SW->App] Received message:', event.data);
  //       const payload = event.data?.payload || event.data;
  //       // Only process background notifications from the service worker to
  //       // avoid duplicate handling with firebaseMessaging.onMessage
  //       if (payload && event.data?.type === 'FCM_BACKGROUND_NOTIFICATION') {
  //         addIncomingNotification(payload);
  //       }
  //     };
  //     navigator.serviceWorker.addEventListener('message', handler);
  //     return () => navigator.serviceWorker.removeEventListener('message', handler);
  //   }
  // }, [addIncomingNotification]);

  // --- SOCKET.IO INTEGRATION FOR REAL-TIME ORDER NOTIFICATIONS ---
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      connectSocket(user.id);
      const handleOrderPlaced = (order) => {
        const serviceName =
          order.service_name ||
          order.serviceName ||
          order.user_service?.title ||
          order.user_service?.service_type_title;
        const amount =
          order.amount ||
          order.credit_amount ||
          order.total_amount;

        const id = order.order_id || order.orderId || order.id;
        // Prefer customer_name from order, order.data, or order.customer.name
        const customerName = order.customer_name || order.data?.customer_name || order.customer?.name;

        addOrderNotification({
          ...order,
          order_id: id,
          service_name: serviceName,
          amount,
          title: order.title || order.data?.title || 'New Order',
          body: order.body || order.data?.body || `Order from ${customerName || 'a customer'}`,
          data: {
            ...order,
            order_id: id,
            service_name: serviceName,
            amount,
            type: 'order_placed'
          },
        });
      };
      onSocketEvent('order.placed', handleOrderPlaced);
      return () => {
        offSocketEvent('order.placed', handleOrderPlaced);
        disconnectSocket();
      };
    }
    disconnectSocket();
  }, [isAuthenticated, user, showInfoToast]);

  // Context value
  const value = {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    removeNotification,
    deleteNotification,
    clearAllNotifications,
    addNotification,
    dismissNotification,
    addOrderNotification,
    addChatNotification,
    getNotificationIcon,
    getNotificationRoute,
    formatNotificationTime,
    getOrderNotifications,
    getOrderUnreadCount,
    getChatUnreadCount
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook for using notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    // In development, provide helpful error message
    if (process.env.NODE_ENV === 'development') {
      console.error('useNotifications must be used within a NotificationProvider. Make sure your component is wrapped with NotificationProvider.');
    }

    // Provide fallback values to prevent app crash
    return {
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      markAsRead: () => { },
      markAllAsRead: () => { },
      removeNotification: () => { },
      deleteNotification: () => { },
      clearAllNotifications: () => { },
      addNotification: () => { },
      dismissNotification: () => { },
      addOrderNotification: () => { },
      addChatNotification: () => { },
      getNotificationIcon: () => '🔔',
      getNotificationRoute: () => null,
      formatNotificationTime: () => '',
      getOrderNotifications: () => [],
      getOrderUnreadCount: () => 0,
      getChatUnreadCount: () => 0
    };
  }
  return context;
};

export default NotificationContext;
