/**
 * RTL (Right-to-Left) specific styles
 * These styles are applied when the document direction is set to RTL
 */

/* Base RTL adjustments */
[dir="rtl"] {
  /* Text alignment */
  text-align: right;
}

/* Flip icons that indicate direction */
[dir="rtl"] .icon-direction {
  transform: scaleX(-1);
}

/* Adjust margins and paddings */
[dir="rtl"] .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-1 {
  margin-right: 0;
  margin-left: 0.25rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .pl-1 {
  padding-left: 0;
  padding-right: 0.25rem;
}

[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-1 {
  padding-right: 0;
  padding-left: 0.25rem;
}

[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* Adjust flexbox direction */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

/* Adjust text alignment classes */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Adjust border radius for input groups */
[dir="rtl"] .input-group .input-group-prepend {
  border-radius: 0 0.375rem 0.375rem 0;
}

[dir="rtl"] .input-group .input-group-append {
  border-radius: 0.375rem 0 0 0.375rem;
}

/* Adjust form elements */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="number"],
[dir="rtl"] input[type="tel"],
[dir="rtl"] textarea {
  text-align: right;
}

/* Adjust checkboxes and radio buttons */
[dir="rtl"] .form-check {
  padding-left: 0;
  padding-right: 1.5rem;
}

[dir="rtl"] .form-check-input {
  margin-left: 0;
  margin-right: -1.5rem;
}

/* Adjust dropdown menus */
[dir="rtl"] .dropdown-menu {
  text-align: right;
}

[dir="rtl"] .dropdown-item {
  text-align: right;
}

/* Adjust modal dialogs */
[dir="rtl"] .modal-header .close {
  margin: -1rem auto -1rem -1rem;
}

/* Adjust navigation */
[dir="rtl"] .navbar-nav {
  padding-right: 0;
}

[dir="rtl"] .nav {
  padding-right: 0;
}

/* Adjust list groups */
[dir="rtl"] .list-group {
  padding-right: 0;
}

/* Adjust tables */
[dir="rtl"] th {
  text-align: right;
}

/* Adjust pagination */
[dir="rtl"] .pagination {
  padding-right: 0;
}

/* Adjust breadcrumbs */
[dir="rtl"] .breadcrumb-item {
  text-align: right;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  padding-left: 0.5rem;
  padding-right: 0;
}

/* Adjust alerts */
[dir="rtl"] .alert-dismissible {
  padding-right: 1.25rem;
  padding-left: 4rem;
}

[dir="rtl"] .alert-dismissible .close {
  right: auto;
  left: 0;
}

/* Adjust cards */
[dir="rtl"] .card-header {
  text-align: right;
}

/* Adjust progress bars */
[dir="rtl"] .progress-bar {
  float: right;
}

/* Adjust tooltips and popovers */
[dir="rtl"] .tooltip,
[dir="rtl"] .popover {
  text-align: right;
}

/* Adjust accordion */
[dir="rtl"] .accordion-button::after {
  margin-left: 0;
  margin-right: auto;
}

/* Adjust tabs */
[dir="rtl"] .nav-tabs {
  padding-right: 0;
}

/* Adjust badges */
[dir="rtl"] .badge {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Adjust spinners */
[dir="rtl"] .spinner-border,
[dir="rtl"] .spinner-grow {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Adjust toasts */
[dir="rtl"] .toast-header .close {
  margin-left: -0.375rem;
  margin-right: auto;
}

/* Adjust input groups */
[dir="rtl"] .input-group > .form-control:not(:last-child),
[dir="rtl"] .input-group > .custom-select:not(:last-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .input-group > .form-control:not(:first-child),
[dir="rtl"] .input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

/* Adjust button groups */
[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
[dir="rtl"] .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

[dir="rtl"] .btn-group > .btn:not(:first-child),
[dir="rtl"] .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

/* Adjust form validation icons */
[dir="rtl"] .form-control.is-valid,
[dir="rtl"] .was-validated .form-control:valid {
  padding-right: 0.75rem;
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

[dir="rtl"] .form-control.is-invalid,
[dir="rtl"] .was-validated .form-control:invalid {
  padding-right: 0.75rem;
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

/* Adjust custom select */
[dir="rtl"] .custom-select {
  padding: 0.375rem 0.75rem 0.375rem 1.75rem;
  background-position: left 0.75rem center;
}

/* Adjust custom file input */
[dir="rtl"] .custom-file-label {
  text-align: right;
}

[dir="rtl"] .custom-file-label::after {
  right: auto;
  left: 0;
  border-right: inherit;
  border-left: none;
  border-radius: 0.375rem 0 0 0.375rem;
}
