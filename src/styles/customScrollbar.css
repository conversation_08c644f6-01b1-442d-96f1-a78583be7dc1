/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a5b4fc;
}

/* Firefox scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f1f1f1;
}

/* Enhanced scrollbar for filter panels */
.filter-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.filter-scrollbar::-webkit-scrollbar-track {
  background: #f5f7ff;
  border-radius: 10px;
}

.filter-scrollbar::-webkit-scrollbar-thumb {
  background: #c7d2fe;
  border-radius: 10px;
  transition: background 0.3s ease;
}

.filter-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #818cf8;
}

/* Firefox scrollbar for filter panels */
.filter-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #c7d2fe #f5f7ff;
}

/* Global scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 10px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

/* Firefox global scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb #f9fafb;
}
