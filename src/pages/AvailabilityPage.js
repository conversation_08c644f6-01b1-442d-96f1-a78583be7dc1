import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import availabilityAPI from '../services/availabilityService';
import AvailabilityCalendar from '../components/availability/AvailabilityCalendar';
import AvailabilityManager from '../components/availability/AvailabilityManager';
import AvailabilityOverrideModal from '../components/availability/AvailabilityOverrideModal';
import useTranslation from '../hooks/useTranslation';
import { authService } from '../services/authService';
import ReactDOM from 'react-dom';

/**
 * AvailabilityPage component
 * 
 * This page allows users to view and manage their availability.
 */
const AvailabilityModal = ({ isOpen = true, onClose }) => {
  const { t } = useTranslation('profile');
  
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('calendar');
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [availabilityStatus, setAvailabilityStatus] = useState('#00FF00'); // Default to green hex
  
  // Fetch user data and availability status when component mounts
  useEffect(() => {
    fetchUserData();
    fetchAvailabilityStatus();
  }, []);
  
  // Fetch user data
  const fetchUserData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.getCurrentUser();
      if (response.success && response.data) {
        setUser(response.data);
        setLoading(false);
      } else {
        setError(response.error || 'Failed to load user data');
      setLoading(false);
      }
    } catch (err) {
      console.error('Error fetching user data:', err);
      setError('Failed to load user data');
      setLoading(false);
    }
  };
  
  // Fetch availability status
  const fetchAvailabilityStatus = async () => {
    try {
      const res = await availabilityAPI.getAvailabilityStatus();
      // Expecting { status: '#00FF00' }
      const status = res?.status || res?.data?.status;
      if (status) {
        setAvailabilityStatus(status);
      }
    } catch (err) {
      console.error('Error fetching availability status:', err);
      // Don't set error state here to avoid blocking the UI
    }
  };
  
  // Handle availability saved
  const handleAvailabilitySaved = (availability) => {
    console.log('Availability saved:', availability);
    setActiveTab('calendar');
    fetchAvailabilityStatus();
  };
  
  // Handle override set
  const handleOverrideSet = (override) => {
    console.log('Override set:', override);
    fetchAvailabilityStatus();
  };
  
  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 20 },
    visible: { opacity: 1, scale: 1, y: 0 },
    exit: { opacity: 0, scale: 0.95, y: 20 }
  };
  
  // If loading
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="w-12 h-12 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-3 text-lg font-medium text-gray-700 dark:text-gray-200">{t('common:loading')}</span>
      </div>
    );
  }
  
  // If error
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 max-w-md w-full">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-red-800 dark:text-red-400">{t('availabilityPage.error')}</h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => window.location.reload()}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  {t('availabilityPage.tryAgain')}
                </button>
                {onClose && (
                  <button
                    type="button"
                    onClick={onClose}
                    className="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-700 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {t('availabilityPage.goBack')}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 dark:bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="relative bg-gradient-to-br from-white/95 to-white/90 dark:from-gray-900/95 dark:to-gray-800/90 backdrop-blur-2xl border border-white/30 dark:border-gray-700 rounded-3xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Animated background decorations */}
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-full blur-xl animate-pulse delay-1000" />

        {/* Header */}
            <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 rounded-2xl shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
            <div>
                    <h2 className="text-2xl text-left font-bold bg-gradient-to-r from-indigo-700 to-purple-700 dark:from-indigo-300 dark:to-purple-300 bg-clip-text text-transparent">
                      {t('availabilityPage.title')}
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                {t('availabilityPage.description')}
              </p>
            </div>
                </div>
                
            <div className="flex items-center space-x-4">
                  {/* Status Indicator */}
                  <div className="flex items-center space-x-2 px-4 py-2 bg-white/50 dark:bg-gray-900/60 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-200">Status:</span>
                <div className="w-3 h-3 rounded-full animate-pulse" style={{ backgroundColor: availabilityStatus }}></div>
              </div>
                  {/* Set Override Button */}
                  <motion.button
                type="button"
                onClick={() => setShowOverrideModal(true)}
                    className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 text-white font-semibold rounded-xl hover:from-indigo-600 hover:to-purple-700 dark:hover:from-indigo-600 dark:hover:to-purple-700 transition-all duration-300 shadow-lg flex items-center space-x-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
              >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                    <span>{t('availabilityPage.setOverride')}</span>
                  </motion.button>
                  
                  {/* Close Button */}
                  <motion.button
                    onClick={onClose}
                    className="p-3 bg-white/20 dark:bg-gray-900/40 backdrop-blur-sm rounded-xl border border-white/30 dark:border-gray-700 hover:bg-white/30 dark:hover:bg-gray-800 transition-all duration-300 shadow-sm hover:shadow-md"
                    whileHover={{ 
                      scale: 1.05,
                      rotate: 90,
                      backgroundColor: "rgba(255, 255, 255, 0.3)"
                    }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg className="w-5 h-5 text-gray-600 dark:text-gray-200 hover:text-gray-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </motion.button>
            </div>
          </div>
        </div>
        
            {/* Content */}
            <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
        {/* Tabs */}
        <div className="mb-6">
                <div className="flex space-x-1 bg-white/50 dark:bg-gray-900/60 backdrop-blur-sm rounded-xl p-1 border border-white/30 dark:border-gray-700">
                  {[
                    { key: 'calendar', label: t('availabilityPage.tabs.calendar'), icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
                    { key: 'manage', label: t('availabilityPage.tabs.manage'), icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' }
                  ].map((tab) => (
                    <motion.button
                      key={tab.key}
                      onClick={() => setActiveTab(tab.key)}
                      className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                        activeTab === tab.key
                          ? 'bg-gradient-to-r from-indigo-500 to-purple-600 dark:from-indigo-700 dark:to-purple-800 text-white shadow-lg'
                          : 'text-gray-700 dark:text-gray-200 bg-transparent hover:bg-transparent'
                      }`}
                      whileHover={{ scale: activeTab === tab.key ? 1 : 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={tab.icon} />
                      </svg>
                      <span>{tab.label}</span>
                    </motion.button>
                  ))}
          </div>
        </div>
        
        {/* Tab content */}
              <div className="bg-white/50 dark:bg-gray-900/60 backdrop-blur-sm rounded-2xl border border-white/30 dark:border-gray-700 p-6">
          {activeTab === 'calendar' && (
            <AvailabilityCalendar
              talent={user}
              onSelectTimeSlot={(dateTime) => console.log('Selected time slot:', dateTime)}
              readOnly={true}
            />
          )}
          
          {activeTab === 'manage' && (
            <AvailabilityManager
              user={user}
              onSave={handleAvailabilitySaved}
              onCancel={() => setActiveTab('calendar')}
            />
          )}
        </div>
      </div>
      
      {/* Availability Override Modal */}
        {/* Render the override modal in a portal so it is not clipped by the parent modal */}
        {showOverrideModal && ReactDOM.createPortal(
        <AvailabilityOverrideModal
          isOpen={showOverrideModal}
          onClose={() => setShowOverrideModal(false)}
          onOverrideSet={handleOverrideSet}
          />,
          document.body
        )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AvailabilityModal;
