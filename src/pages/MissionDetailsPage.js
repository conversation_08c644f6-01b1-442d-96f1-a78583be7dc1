import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import MissionReviewModal from '../components/modals/MissionReviewModal';
import missionReviewService from '../services/missionReviewService';
import { useToast } from '../components/common/ToastProvider';

const MissionDetailsPage = () => {
    const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
    const { showToast } = useToast();

    const handleSubmitReview = async (reviewData) => {
        try {
            await missionReviewService.createReview({
                ...reviewData,
                mission_id: mission.id
            });
            showToast('Review submitted successfully', 'success');
            // Refresh mission data to show the new review
            fetchMissionDetails();
        } catch (error) {
            showToast(error.message || 'Failed to submit review', 'error');
            throw error;
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
            {/* Add Review Button */}
            {mission?.status === 'completed' && (
                <motion.button
                    onClick={() => setIsReviewModalOpen(true)}
                    className="fixed bottom-6 right-6 px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 text-white font-medium rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-300 shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                >
                    Write a Review
                </motion.button>
            )}

            {/* Review Modal */}
            <MissionReviewModal
                isOpen={isReviewModalOpen}
                onClose={() => setIsReviewModalOpen(false)}
                mission={mission}
                onSubmitReview={handleSubmitReview}
            />
        </div>
    );
};

export default MissionDetailsPage; 