import React, { useState } from 'react';
import BankAccountModal from '../features/wallet/components/modals/BankAccountModal';

// Mock account data for edit mode
const mockAccount = {
  id: 'acc-123',
  bankName: 'Maybank',
  accountNumber: '**********',
  accountHolder: '<PERSON>',
  bankType: 'conventional',
  // Add more fields as needed for the form
};

const BankAccountModalTestPage = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add');

  const handleOpenAdd = () => {
    setModalMode('add');
    setModalOpen(true);
  };
  const handleOpenEdit = () => {
    setModalMode('edit');
    setModalOpen(true);
  };
  const handleClose = () => {
    setModalOpen(false);
  };
  const handleSuccess = (data) => {
    // For demo, just close and log
    setModalOpen(false);
    // eslint-disable-next-line no-console
    console.log('BankAccountModal success:', data);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-8">
      <h1 className="text-3xl font-bold mb-8">BankAccountModal Test Page</h1>
      <div className="flex space-x-4 mb-8">
        <button
          onClick={handleOpenAdd}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition"
        >
          Open Add Bank Account Modal
        </button>
        <button
          onClick={handleOpenEdit}
          className="px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition"
        >
          Open Edit Bank Account Modal
        </button>
      </div>
      <BankAccountModal
        isOpen={modalOpen}
        mode={modalMode}
        account={modalMode === 'edit' ? mockAccount : null}
        onClose={handleClose}
        onSuccess={handleSuccess}
      />
    </div>
  );
};

export default BankAccountModalTestPage; 