import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCdnUrl } from '../utils/cdnUtils';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import { missionApi } from '../services/missionApi';
import { useToast } from '../components/common/ToastProvider';
import useTranslation from '../hooks/useTranslation';
import { useAuth } from '../contexts/AuthContext';
import MissionCard from '../components/mission/MissionCard';
import HostedMissionCard from '../components/mission/HostedMissionCard';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';
import { FaTrophy, FaRocket, FaUsers, FaCalendarAlt, FaCheckCircle, FaClock, FaTimesCircle, FaBan } from 'react-icons/fa';

const StatCard = ({ icon, label, value, iconClass, gradient }) => (
  <motion.div
    className={`flex flex-col items-center justify-center p-6 rounded-2xl bg-gradient-to-br ${gradient} backdrop-blur-md border border-white/20 dark:border-gray-700/50 shadow-xl dark:shadow-indigo-900/30 hover:shadow-2xl dark:hover:shadow-indigo-900/50 transition-all duration-300 group cursor-pointer`}
    whileHover={{ scale: 1.05, y: -5 }}
    whileTap={{ scale: 0.98 }}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    <motion.span
      className={`mb-3 text-3xl ${iconClass} group-hover:scale-110 transition-transform duration-300`}
      whileHover={{ rotate: 360 }}
      transition={{ duration: 0.6 }}
    >
      {icon}
    </motion.span>
    <div className="text-lg font-semibold text-white/90 mb-1 text-center">{label}</div>
    <div className="text-3xl font-bold text-white drop-shadow-lg">{value}</div>

    {/* Animated shine effect */}
    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
    </div>
  </motion.div>
);

const MyMissionsPage = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation('mission');
  const { user } = useAuth();
  const [statistics, setStatistics] = useState(null);
  const [mainTab, setMainTab] = useState('applied'); // 'applied' or 'host'
  const [subTab, setSubTab] = useState('open'); // 'open', 'in_progress', 'completed', 'closed', 'cancelled'
  const [allMissions, setAllMissions] = useState([]);
  const [loadingMissions, setLoadingMissions] = useState(false);
  const [errorMissions, setErrorMissions] = useState(null);

  // Fetch statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        if (!user) return;
        const stats = await missionApi.getUserStatistics();
        setStatistics(stats.data || stats);
      } catch (err) {
        setStatistics(null);
      }
    };
    fetchStatistics();
  }, [user]);

  // Fetch missions from backend with role and status
  useEffect(() => {
    const fetchMissions = async () => {
      setLoadingMissions(true);
      setErrorMissions(null);
      try {
        if (!user) return;
        const role = mainTab === 'host' ? 'host' : 'applicant';
        const status = subTab;
        const params = new URLSearchParams({ role, status });
        const data = await missionApi.getMyMissions(params.toString());
        const missions = Array.isArray(data.data) ? data.data : [];
        const transformed = transformApiMissionsToFrontend(missions);
        setAllMissions(transformed);
      } catch (err) {
        setErrorMissions('Failed to load missions');
        setAllMissions([]);
      } finally {
        setLoadingMissions(false);
      }
    };
    fetchMissions();
  }, [user, mainTab, subTab]);

  // Workaround: filter by status on frontend in case backend does not filter
  const missionsToShow = allMissions.filter(
    (m) => m.status && m.status.toLowerCase().replace(' ', '_') === subTab
  );

  return (
    <MissionLayout title={t('myMissions.title')} backPath="/missions" useMainNav={true} useMissionMobileNav={true}>
      {/* Enhanced Hero Section */}
      <motion.div
        className="bg-gradient-to-r from-indigo-600 via-blue-700 to-purple-800 dark:from-gray-900 dark:via-indigo-900 dark:to-purple-900 rounded-3xl overflow-hidden mb-12 relative group shadow-2xl dark:shadow-indigo-900/50"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, ease: 'easeOut' }}
      >
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 opacity-20 bg-gradient-to-br from-blue-600/30 to-purple-600/30 mix-blend-overlay"></div>
        <div className="absolute -top-20 -right-20 w-96 h-96 bg-gradient-to-br from-yellow-400/30 via-pink-400/20 to-indigo-400/30 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-gradient-to-tr from-indigo-400/20 via-blue-400/30 to-purple-400/20 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>

        {/* Enhanced Gaming Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute h-4 w-4 rounded-full bg-yellow-400/60 top-[15%] left-[10%] animate-float-slow" style={{ animationDelay: '0s' }}></div>
          <div className="absolute h-3 w-3 rounded-full bg-pink-400/50 top-[35%] left-[15%] animate-float-slow" style={{ animationDelay: '1s' }}></div>
          <div className="absolute h-5 w-5 rounded-full bg-blue-400/40 top-[65%] left-[5%] animate-float-slow" style={{ animationDelay: '2s' }}></div>
          <div className="absolute h-2 w-2 rounded-full bg-purple-400/60 top-[75%] left-[50%] animate-float-slow" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute h-3 w-3 rounded-full bg-indigo-400/50 top-[25%] right-[30%] animate-float-slow" style={{ animationDelay: '1.5s' }}></div>
          <div className="absolute h-4 w-4 rounded-full bg-fuchsia-400/40 top-[45%] right-[10%] animate-float-slow" style={{ animationDelay: '0.7s' }}></div>
          <div className="absolute h-2 w-2 rounded-full bg-cyan-400/60 top-[85%] right-[20%] animate-float-slow" style={{ animationDelay: '1.2s' }}></div>
        </div>

        {/* Gaming Mascot */}
        <div className="absolute bottom-0 right-8 w-32 h-32 opacity-60 pointer-events-none select-none hidden md:block">
          <img src="/MissionXMascot.png" alt="Mission X Mascot" className="w-full h-full object-contain" />
        </div>

        {/* Enhanced Title Section */}
        <div className="relative z-10 flex flex-col items-center mb-8 p-8">
          <motion.div
            className="flex items-center gap-4 mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Enhanced Glowing Trophy Icon */}
            <motion.div
              className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 shadow-2xl shadow-yellow-400/50 p-3"
              animate={{
                boxShadow: [
                  '0 0 20px rgba(251, 191, 36, 0.5)',
                  '0 0 40px rgba(251, 191, 36, 0.8)',
                  '0 0 20px rgba(251, 191, 36, 0.5)'
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <FaTrophy className="w-12 h-12 text-white drop-shadow-lg" />
            </motion.div>

            <motion.h1
              className="text-4xl md:text-6xl font-extrabold text-white dark:text-yellow-400 text-center drop-shadow-2xl tracking-tight leading-tight"
              style={{
                fontFamily: 'Poppins, sans-serif',
                background: 'linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))'
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {t('myMissions.title')}
            </motion.h1>
          </motion.div>

          {/* Enhanced Animated Underlines */}
          <motion.div
            className="flex flex-col items-center gap-2"
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 'auto' }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <div className="h-1 w-32 bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 rounded-full shadow-lg"></div>
            <div className="h-0.5 w-24 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-70"></div>
          </motion.div>
        </div>

        {/* Enhanced Statistics Grid */}
        <motion.div
          className="relative z-10 grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-4 w-full max-w-5xl mx-auto pb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <StatCard
            icon={<FaUsers />}
            label={t('myMissions.tabs.hosted')}
            value={statistics?.hosted ?? '-'}
            iconClass="text-white"
            gradient="from-indigo-500 via-purple-500 to-pink-500"
          />
          <StatCard
            icon={<FaRocket />}
            label={t('myMissions.tabs.applied')}
            value={statistics?.applied ?? '-'}
            iconClass="text-white"
            gradient="from-blue-500 via-cyan-500 to-teal-500"
          />
          <StatCard
            icon={<FaCalendarAlt />}
            label={t('myMissions.tabs.upcoming')}
            value={statistics?.upcoming ?? '-'}
            iconClass="text-white"
            gradient="from-emerald-500 via-green-500 to-lime-500"
          />
          <StatCard
            icon={<FaTrophy />}
            label={t('myMissions.tabs.completed')}
            value={statistics?.completed ?? '-'}
            iconClass="text-white"
            gradient="from-yellow-500 via-orange-500 to-red-500"
          />
        </motion.div>
      </motion.div>

      {/* Enhanced Main Tab Navigation */}
      <motion.div
        className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <div className="flex w-full mx-auto rounded-2xl shadow-xl dark:shadow-indigo-900/50 bg-white/20 dark:bg-gray-900/40 backdrop-blur-xl border border-white/30 dark:border-gray-700/50 overflow-hidden p-2">
          <motion.button
            className={`flex-1 py-4 px-8 text-lg font-bold transition-all duration-300 rounded-xl text-center flex items-center justify-center gap-3 ${
              mainTab === 'applied'
                ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/30 transform scale-105'
                : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-cyan-400 hover:bg-white/10 dark:hover:bg-gray-800/30'
            }`}
            onClick={() => setMainTab('applied')}
            whileHover={{ scale: mainTab === 'applied' ? 1.05 : 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaRocket className="w-5 h-5" />
            {t('myMissions.tabs.applied')}
          </motion.button>
          <motion.button
            className={`flex-1 py-4 px-8 text-lg font-bold transition-all duration-300 rounded-xl text-center flex items-center justify-center gap-3 ${
              mainTab === 'host'
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/30 transform scale-105'
                : 'text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-pink-400 hover:bg-white/10 dark:hover:bg-gray-800/30'
            }`}
            onClick={() => setMainTab('host')}
            whileHover={{ scale: mainTab === 'host' ? 1.05 : 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FaUsers className="w-5 h-5" />
            {t('myMissions.tabs.hosted')}
          </motion.button>
        </div>
      </motion.div>

      {/* Enhanced Secondary Tab Navigation */}
      <motion.div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <div className="flex flex-wrap gap-2 md:gap-4 justify-center bg-white/20 dark:bg-gray-900/40 backdrop-blur-xl rounded-2xl shadow-xl dark:shadow-indigo-900/50 p-3 border border-white/30 dark:border-gray-700/50">
          {[
            { key: 'open', label: 'Open', icon: FaClock, color: 'from-green-500 to-emerald-500' },
            { key: 'in_progress', label: 'In Progress', icon: FaRocket, color: 'from-blue-500 to-cyan-500' },
            { key: 'completed', label: 'Completed', icon: FaCheckCircle, color: 'from-purple-500 to-pink-500' },
            { key: 'closed', label: 'Closed', icon: FaTimesCircle, color: 'from-orange-500 to-red-500' },
            { key: 'cancelled', label: 'Cancelled', icon: FaBan, color: 'from-gray-500 to-gray-600' }
          ].map(({ key, label, icon: Icon, color }) => (
            <motion.button
              key={key}
              className={`py-3 px-4 md:px-6 text-sm md:text-base font-semibold transition-all duration-300 rounded-xl flex items-center gap-2 ${
                subTab === key
                  ? `bg-gradient-to-r ${color} text-white shadow-lg transform scale-105`
                  : 'text-gray-600 dark:text-gray-300 hover:bg-white/10 dark:hover:bg-gray-800/30 hover:scale-102'
              }`}
              onClick={() => setSubTab(key)}
              whileHover={{ scale: subTab === key ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Icon className="w-4 h-4" />
              <span className="hidden sm:inline">{label}</span>
              <span className="sm:hidden">{label.split(' ')[0]}</span>
            </motion.button>
          ))}
        </div>
      </motion.div>

      {/* Enhanced Content Section */}
      <motion.div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
      >
        <div className="bg-white/20 dark:bg-gray-900/40 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/50 shadow-2xl dark:shadow-indigo-900/50 overflow-hidden">
          {loadingMissions ? (
            <div className="py-16 text-center">
              <motion.div
                className="inline-flex items-center gap-3 text-xl font-semibold text-gray-600 dark:text-gray-300"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <div className="w-8 h-8 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
                Loading missions...
              </motion.div>
            </div>
          ) : errorMissions ? (
            <div className="py-16 text-center">
              <motion.div
                className="flex flex-col items-center gap-4"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                  <FaTimesCircle className="w-8 h-8 text-red-500" />
                </div>
                <div className="text-xl font-semibold text-red-600 dark:text-red-400">{errorMissions}</div>
                <motion.button
                  className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </motion.button>
              </motion.div>
            </div>
          ) : (
            <>
              {missionsToShow.length === 0 ? (
                <div className="py-16 text-center">
                  <motion.div
                    className="flex flex-col items-center gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
                      <FaRocket className="w-10 h-10 text-blue-500 dark:text-blue-400" />
                    </div>
                    <div className="text-xl font-semibold text-gray-600 dark:text-gray-300">No missions found</div>
                    <div className="text-gray-500 dark:text-gray-400">Try switching to a different tab or create a new mission!</div>
                  </motion.div>
                </div>
              ) : (
                <div className="p-8">
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.6, staggerChildren: 0.1 }}
                  >
                    <AnimatePresence>
                      {missionsToShow.map((mission, index) => (
                        <motion.div
                          key={mission.id}
                          initial={{ opacity: 0, y: 20, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -20, scale: 0.9 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                          whileHover={{ y: -5 }}
                        >
                          {mainTab === 'applied' ? (
                            <MissionCard mission={mission} hideApplyButton={true} />
                          ) : (
                            <HostedMissionCard mission={mission} />
                          )}
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </motion.div>
                </div>
              )}
            </>
          )}
        </div>
      </motion.div>
    </MissionLayout>
  );
};

export default MyMissionsPage;
