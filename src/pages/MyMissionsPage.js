import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCdnUrl } from '../utils/cdnUtils';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import { missionApi } from '../services/missionApi';
import { useToast } from '../components/common/ToastProvider';
import useTranslation from '../hooks/useTranslation';
import { useAuth } from '../contexts/AuthContext';
import MissionCard from '../components/mission/MissionCard';
import HostedMissionCard from '../components/mission/HostedMissionCard';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';

const StatCard = ({ icon, label, value, iconClass }) => (
  <div className="flex flex-col items-center justify-center p-6 rounded-2xl bg-white/70 dark:bg-gray-900/80 backdrop-blur-md border border-white/30 dark:border-gray-700 shadow-lg dark:shadow-indigo-900/30 hover:shadow-xl dark:hover:shadow-indigo-900/40 transition-all duration-300">
    <span className={`mb-2 ${iconClass}`}>{icon}</span>
    <div className="text-lg font-semibold text-gray-800 dark:text-yellow-400 mb-1">{label}</div>
    <div className="text-2xl font-bold text-gray-900 dark:text-yellow-400">{value}</div>
  </div>
);

const MyMissionsPage = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation('mission');
  const { user } = useAuth();
  const [statistics, setStatistics] = useState(null);
  const [mainTab, setMainTab] = useState('applied'); // 'applied' or 'host'
  const [subTab, setSubTab] = useState('open'); // 'open', 'in_progress', 'completed', 'closed', 'cancelled'
  const [allMissions, setAllMissions] = useState([]);
  const [loadingMissions, setLoadingMissions] = useState(false);
  const [errorMissions, setErrorMissions] = useState(null);

  // Fetch statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        if (!user) return;
        const stats = await missionApi.getUserStatistics();
        setStatistics(stats.data || stats);
      } catch (err) {
        setStatistics(null);
      }
    };
    fetchStatistics();
  }, [user]);

  // Fetch missions from backend with role and status
  useEffect(() => {
    const fetchMissions = async () => {
      setLoadingMissions(true);
      setErrorMissions(null);
      try {
        if (!user) return;
        const role = mainTab === 'host' ? 'host' : 'applicant';
        const status = subTab;
        const params = new URLSearchParams({ role, status });
        const data = await missionApi.getMyMissions(params.toString());
        const missions = Array.isArray(data.data) ? data.data : [];
        const transformed = transformApiMissionsToFrontend(missions);
        setAllMissions(transformed);
      } catch (err) {
        setErrorMissions('Failed to load missions');
        setAllMissions([]);
      } finally {
        setLoadingMissions(false);
      }
    };
    fetchMissions();
  }, [user, mainTab, subTab]);

  // Workaround: filter by status on frontend in case backend does not filter
  const missionsToShow = allMissions.filter(
    (m) => m.status && m.status.toLowerCase().replace(' ', '_') === subTab
  );

  return (
    <MissionLayout title={t('myMissions.title')} backPath="/missions" useMainNav={true} useMissionMobileNav={true}>
      {/* Section 1: Title */}
      <motion.div
        className="bg-gradient-to-r from-indigo-600 via-indigo-700 to-blue-700 dark:from-yellow-400 dark:via-pink-400 dark:to-indigo-400 rounded-2xl overflow-hidden mb-12 relative group shadow-xl dark:shadow-indigo-900/30"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, ease: 'easeOut' }}
      >
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 opacity-15 bg-noise mix-blend-overlay"></div>
        <div className="absolute right-0 bottom-0 w-64 h-64 bg-white/10 dark:bg-gray-900/20 rounded-full transform translate-x-20 translate-y-20 blur-xl"></div>
        <div className="absolute left-0 top-0 w-40 h-40 bg-white/10 dark:bg-gray-900/20 rounded-full transform -translate-x-20 -translate-y-20 blur-xl"></div>

        {/* Enhanced Animated Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute h-3 w-3 rounded-full bg-white/30 top-[15%] left-[10%] animate-float-particle" style={{ animationDuration: '8s' }}></div>
          <div className="absolute h-4 w-4 rounded-full bg-white/20 top-[35%] left-[15%] animate-float-particle" style={{ animationDuration: '12s', animationDelay: '1s' }}></div>
          <div className="absolute h-3 w-3 rounded-full bg-white/25 top-[65%] left-[5%] animate-float-particle" style={{ animationDuration: '10s', animationDelay: '2s' }}></div>
          <div className="absolute h-3 w-3 rounded-full bg-white/20 top-[75%] left-[50%] animate-float-particle" style={{ animationDuration: '9s', animationDelay: '0.5s' }}></div>
          <div className="absolute h-4 w-4 rounded-full bg-white/25 top-[25%] right-[30%] animate-float-particle" style={{ animationDuration: '11s', animationDelay: '1.5s' }}></div>
          <div className="absolute h-2 w-2 rounded-full bg-white/30 top-[45%] right-[10%] animate-float-particle" style={{ animationDuration: '13s', animationDelay: '0.7s' }}></div>
          <div className="absolute h-2 w-2 rounded-full bg-white/30 top-[85%] right-[20%] animate-float-particle" style={{ animationDuration: '10s', animationDelay: '1.2s' }}></div>
        </div>

        {/* Title and animated underline */}
        <div className="flex flex-col items-center mb-8 p-8">
          <div className="flex items-center gap-3">
            {/* Glowing trophy icon */}
            <span className="inline-flex items-center mt-4 mr-3 justify-center rounded-full bg-gradient-to-r from-orange-400 to-yellow-400 shadow-lg shadow-yellow-400/40 animate-pulse p-2">
              <svg className="w-10 h-10 text-white drop-shadow" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 2a1 1 0 00-1 1v1H5a1 1 0 00-1 1v2a5 5 0 004 4.9V15H6a1 1 0 100 2h8a1 1 0 100-2h-2v-3.1A5 5 0 0016 7V5a1 1 0 00-1-1h-3V3a1 1 0 00-1-1zm-3 4h2v2a3 3 0 01-2-2zm8 0v2a3 3 0 01-2 2V6h2z" />
              </svg>
            </span>
            <h1
              className="text-5xl md:text-5xl font-semibold text-white dark:text-yellow-400 mt-6 text-center [text-shadow:0_2px_5px_rgba(0,0,0,0.2)] tracking-tight leading-tight pb-2"
            style={{
                fontFamily: 'Poppins, sans-serif',
              lineHeight: 1.15,
                fontWeight: '700',
              letterSpacing: '0.01em',
              WebkitTextStroke: '0.5px rgba(80,60,180,0.08)'
            }}
          >
            {t('myMissions.title')}
          </h1>
        </div>
          {/* Animated gradient underline */}
          <div className="h-1 w-24 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-full animate-underline-expand mb-2" style={{animationFillMode: 'forwards', animationDuration: '0.8s'}} />
         {/* Dark mode underline */}
         <div className="h-1 w-24 bg-gradient-to-r from-yellow-400 to-pink-400 rounded-full animate-underline-expand mb-2 hidden dark:block" style={{animationFillMode: 'forwards', animationDuration: '0.8s', marginTop: '-0.25rem'}} />
        </div>

        {/* Responsive statistics grid */}
        <div className="relative z-10 grid grid-cols-2 gap-4 md:grid-cols-2 lg:grid-cols-4 w-full max-w-3xl mx-auto pb-8">
          <StatCard
            icon={<svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>}
            label={t('myMissions.tabs.hosted')}
            value={statistics?.hosted ?? '-'}
            iconClass="text-indigo-400"
          />
          <StatCard
            icon={<svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
            label={t('myMissions.tabs.applied')}
            value={statistics?.applied ?? '-'}
            iconClass="text-blue-400"
          />
          <StatCard
            icon={<svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>}
            label={t('myMissions.tabs.upcoming')}
            value={statistics?.upcoming ?? '-'}
            iconClass="text-emerald-400"
          />
          <StatCard
            icon={<svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>}
            label={t('myMissions.tabs.completed')}
            value={statistics?.completed ?? '-'}
            iconClass="text-pink-400"
          />
        </div>
      </motion.div>

      {/* Section 2: Main Tab Navigation - Glassy, Centered, 50% width per tab */}
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 mb-6">
        <div className="flex w-full mx-auto rounded-2xl shadow-md dark:shadow-indigo-900/30 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md border border-white/20 dark:border-gray-700 overflow-hidden">
          <button
            className={`flex-1 py-3 px-6 text-lg font-semibold border-b-2 transition-colors duration-200 rounded-none text-center ${mainTab === 'applied' ? 'border-indigo-500 dark:border-yellow-400 text-indigo-700 dark:text-yellow-400 bg-white/80 dark:bg-gray-900/80 shadow font-bold' : 'border-transparent text-gray-500 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-yellow-400 bg-transparent'}`}
            onClick={() => setMainTab('applied')}
            style={{ minWidth: 0 }}
          >
            {t('myMissions.tabs.applied')}
          </button>
          <button
            className={`flex-1 py-3 px-6 text-lg font-semibold border-b-2 transition-colors duration-200 rounded-none text-center ${mainTab === 'host' ? 'border-indigo-500 dark:border-yellow-400 text-indigo-700 dark:text-yellow-400 bg-white/80 dark:bg-gray-900/80 shadow font-bold' : 'border-transparent text-gray-500 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-yellow-400 bg-transparent'}`}
            onClick={() => setMainTab('host')}
            style={{ minWidth: 0 }}
          >
            {t('myMissions.tabs.hosted')}
          </button>
        </div>
      </div>

      {/* Section 3: Secondary Tab Navigation - Glassy */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="flex space-x-2 md:space-x-4 border-b border-white/20 dark:border-gray-700 bg-white/40 dark:bg-gray-900/60 backdrop-blur-md rounded-2xl shadow-md dark:shadow-indigo-900/30 p-2">
          {['open', 'in_progress', 'completed', 'closed', 'cancelled'].map(tab => (
            <button
              key={tab}
              className={`py-2 px-4 md:px-6 text-base font-medium border-b-2 transition-colors duration-200 rounded-xl ${subTab === tab ? 'border-blue-500 dark:border-yellow-400 text-blue-700 dark:text-yellow-400 bg-white/80 dark:bg-gray-900/80 shadow dark:shadow-yellow-400/10' : 'border-transparent text-gray-500 dark:text-gray-300 hover:text-blue-600 dark:hover:text-yellow-400 bg-transparent shadow-none'}`}
              onClick={() => setSubTab(tab)}
            >
              {tab === 'in_progress' ? 'In Progress' : tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Section 4: Content (to be implemented) - Glassy */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-12 text-center text-gray-400 dark:text-gray-300 bg-white/60 dark:bg-gray-900/80 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700 shadow-xl dark:shadow-indigo-900/30">
          {loadingMissions ? (
            <div className="text-lg text-gray-500 dark:text-gray-300">Loading missions...</div>
          ) : errorMissions ? (
            <div className="text-lg text-red-500 dark:text-red-400">{errorMissions}</div>
          ) : (
            <>
              {missionsToShow.length === 0 ? (
                <div className="text-gray-400 dark:text-gray-500">No missions found for this tab/status.</div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {missionsToShow.map(mission => (
                    mainTab === 'applied' ? (
                      <MissionCard key={mission.id} mission={mission} hideApplyButton={true} />
                    ) : (
                      <HostedMissionCard key={mission.id} mission={mission} />
                    )
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MissionLayout>
  );
};

export default MyMissionsPage;
