import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../hooks/useTranslation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faSpinner, faArrowRight, faQuestionCircle, faTimesCircle, faRedo, faWallet } from '@fortawesome/free-solid-svg-icons';
import axios from 'axios';
import walletAPI from '../services/walletService';
import globalBalanceService from '../services/globalBalanceService';
import { useAuth } from '../contexts/AuthContext';

/**
 * PaymentReturnPage component
 * 
 * This page handles the return from payment gateways and processes the payment status.
 * It's a dedicated page that users are redirected to after completing a payment.
 */
const PaymentReturnPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation(['wallet', 'common']);
  const [status, setStatus] = useState('processing'); // 'processing', 'success', 'failed'
  const [redirectCountdown, setRedirectCountdown] = useState(5);
  const [transactionId, setTransactionId] = useState(null);
  const [backendTransactionId, setBackendTransactionId] = useState(null); // <-- Add this line
  const [retryLoading, setRetryLoading] = useState(false);
  const [retryError, setRetryError] = useState('');
  const { isAuthenticated } = useAuth();

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

  // Verify payment status with backend
  useEffect(() => {
    const txId =
      searchParams.get('transaction_id') ||
      searchParams.get('billplz[transaction_id]') ||
      searchParams.get('billplz[id]');

    setTransactionId(txId);

    const paramsObj = {};
    for (const [key, value] of searchParams.entries()) {
      paramsObj[key] = value;
    }

    // Try to extract UID and credits from params if available
    const uid = searchParams.get('uid') || '';
    const credits = searchParams.get('credits') || '';

    const verifyPayment = async () => {
      try {
        const res = await walletAPI.handlePaymentReturn(paramsObj);
        const paymentStatus = res.data?.status;
        setBackendTransactionId(res.data?.transaction_id || null); // <-- Store backend transaction_id

        if (paymentStatus === 'success' || paymentStatus === 'completed') {
          setStatus('success');
          if (isAuthenticated) {
            // Refresh global balance cache only for authenticated users
            globalBalanceService.invalidateCache();
            await globalBalanceService.getBalance(true);
          } else {
            // Store toast info for unauthenticated users
            localStorage.setItem('topup_success_toast', JSON.stringify({ uid, credits }));
          }
          // Store toast info for all users so Wallet.js can show TopUpModal success
          if (credits) {
            localStorage.setItem('topup_success_toast', JSON.stringify({ credits }));
          }
        } else if (paymentStatus === 'failed' || paymentStatus === 'cancelled' || paymentStatus === 'pending' || paymentStatus === 'processing') {
          setStatus('failed');
        } else {
          // Treat any unknown or missing status as failed
          console.warn('Unknown payment status:', paymentStatus, res.data);
          setStatus('failed');
        }
      } catch (err) {
        console.error('Error verifying payment:', err);
        setStatus('failed');
      }
    };

    verifyPayment();
  }, [searchParams, isAuthenticated]);

  // Success redirect logic
  useEffect(() => {
    let timer;
    if (status === 'success' && redirectCountdown > 0) {
      timer = setTimeout(() => setRedirectCountdown(c => c - 1), 1000);
    } else if (status === 'success' && redirectCountdown === 0) {
      if (isAuthenticated) {
        navigate('/wallet?status=success');
      } else {
        navigate('/home?status=success');
      }
    }
    return () => clearTimeout(timer);
  }, [status, redirectCountdown, navigate, isAuthenticated]);

  // Retry payment handler
  const handleRetryPayment = async () => {
    // Use backendTransactionId for retry
    if (!backendTransactionId) return;
    setRetryLoading(true);
    setRetryError('');
    try {
      // Use walletAPI (which uses the authenticated api instance)
      const res = await walletAPI.retryPayment({ transaction_id: backendTransactionId });
      if (res.data && res.data.payment && res.data.payment.redirect_url) {
        window.location.href = res.data.payment.redirect_url;
      } else {
        setRetryError('No redirect URL returned from server.');
      }
    } catch (err) {
      setRetryError(
        err.response?.data?.message ||
        err.message ||
        'Failed to retry payment. Please try again.'
      );
    } finally {
      setRetryLoading(false);
    }
  };

  // Success button handler
  const handleSuccessRedirect = () => {
    if (isAuthenticated) {
      navigate('/wallet?status=success');
    } else {
      navigate('/home?status=success');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-100 via-purple-100 to-white dark:bg-gradient-to-br dark:from-gray-900 dark:via-gray-900 dark:to-gray-950 flex flex-col">
      {/* Animated Gradient Header */}
      <motion.div
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, type: 'spring' }}
        className="relative bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 shadow-lg rounded-b-3xl px-0 md:px-0 py-8 md:py-12 flex flex-col items-center justify-center"
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
          className="mb-4"
        >
          {status === 'processing' && (
            <FontAwesomeIcon icon={faSpinner} spin className="text-white text-5xl drop-shadow-lg animate-spin" />
          )}
          {status === 'success' && (
            <FontAwesomeIcon icon={faCheckCircle} className="text-green-300 text-5xl drop-shadow-lg animate-bounce" />
          )}
          {status === 'failed' && (
            <FontAwesomeIcon icon={faTimesCircle} className="text-red-300 text-5xl drop-shadow-lg animate-bounce" />
          )}
        </motion.div>
        <h1 className="text-3xl md:text-4xl font-extrabold text-white text-center drop-shadow-lg tracking-tight">
          {status === 'processing'
            ? t('paymentReturn.processingTitle')
            : status === 'success'
            ? t('paymentReturn.successTitle')
            : t('paymentReturn.failureTitle')}
        </h1>
        <p className="text-white/80 text-lg mt-2 text-center max-w-xl mx-auto">
          {status === 'processing'
            ? t('paymentReturn.processingMessage')
            : status === 'success'
            ? t('paymentReturn.successMessage')
            : t('paymentReturn.failureMessage')}
        </p>
      </motion.div>

      {/* Main Content Card */}
      <div className="flex-1 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, type: 'spring' }}
          className="backdrop-blur-xl bg-white/80 rounded-3xl shadow-2xl max-w-md w-full mx-4 overflow-hidden border border-white/40 mt-[-60px] dark:bg-gray-800 dark:border-gray-700"
        >
          <div className="p-1">
            <div className="rounded-t-3xl p-6 pb-2 text-center">
              <AnimatePresence mode="wait">
                {status === 'processing' && (
                  <motion.div
                    key="progress"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
                      <motion.div
                        className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full"
                        initial={{ width: '0%' }}
                        animate={{ width: '80%' }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <div className="text-gray-700 text-lg font-medium mb-2 dark:text-gray-300">
                      {t('paymentReturn.verifying')}
                    </div>
                    <div className="text-gray-500 text-sm mb-4 dark:text-gray-400">
                      {t('paymentReturn.redirecting')}
                    </div>
                  </motion.div>
                )}
                {status === 'success' && (
                  <motion.div
                    key="success"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <div className="text-green-600 text-2xl font-bold mb-2 flex items-center justify-center gap-2">
                      <FontAwesomeIcon icon={faCheckCircle} className="text-green-400 animate-bounce" />
                      {t('paymentReturn.successTitle')}
                    </div>
                    <div className="text-gray-700 text-lg mb-4 dark:text-gray-300">
                      {t('paymentReturn.successMessage')}
                    </div>
                    <button
                      onClick={handleSuccessRedirect}
                      className="mt-2 px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl font-semibold shadow-lg hover:from-green-600 hover:to-emerald-600 transition-all dark:bg-green-600 dark:hover:from-green-700 dark:hover:to-emerald-700"
                    >
                      {isAuthenticated ? t('paymentReturn.goWallet') : 'Go to Home'}
                    </button>
                  </motion.div>
                )}
                {status === 'failed' && (
                  <motion.div
                    key="failed"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <div className="text-red-600 text-2xl font-bold mb-2 flex items-center justify-center gap-2">
                      <FontAwesomeIcon icon={faTimesCircle} className="text-red-400 animate-bounce" />
                      {t('paymentReturn.failureTitle')}
                    </div>
                    <div className="text-gray-700 text-lg mb-4 dark:text-gray-300">
                      {t('paymentReturn.failureMessage')}
                    </div>
                    {retryError && (
                      <div className="bg-red-50 text-red-700 rounded-lg p-3 mb-3 text-sm dark:bg-red-900 dark:text-red-300">
                        {retryError}
                      </div>
                    )}
                    <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mt-2">
                      <button
                        onClick={handleRetryPayment}
                        disabled={retryLoading}
                        className="px-6 py-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-semibold shadow-lg hover:from-red-600 hover:to-pink-600 transition-all dark:bg-red-600 dark:hover:from-red-700 dark:hover:to-pink-700 flex items-center justify-center gap-2 disabled:opacity-60"
                      >
                        {retryLoading ? (
                          <>
                            <FontAwesomeIcon icon={faSpinner} spin />
                            {t('paymentReturn.processingTitle')}
                          </>
                        ) : (
                          <>
                            <FontAwesomeIcon icon={faRedo} />
                            Retry Payment with Billplz
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => navigate('/wallet')}
                        className="px-6 py-2 bg-gradient-to-r from-gray-400 to-gray-600 text-white rounded-xl font-semibold shadow-lg hover:from-gray-500 hover:to-gray-700 transition-all dark:bg-gray-700 dark:hover:from-gray-600 dark:hover:to-gray-800 flex items-center justify-center gap-2"
                      >
                        <FontAwesomeIcon icon={faWallet} />
                        Return to Wallet
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <footer className="bg-white/80 border-t border-gray-200 py-4 mt-8 shadow-inner rounded-t-3xl dark:bg-gray-900 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-2 md:gap-0">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              &copy; {new Date().getFullYear()} MissionX. {t('paymentReturn.footerCopyright')}
            </p>
            <button
              onClick={() => navigate('/help')}
              className="flex items-center gap-2 text-sm text-indigo-600 bg-transparent hover:bg-transparent hover:text-indigo-800 transition-colors font-medium dark:text-indigo-400 dark:hover:text-indigo-600"
            >
              <FontAwesomeIcon icon={faQuestionCircle} className="text-indigo-400" />
              {t('paymentReturn.footerNeedHelp')}
            </button>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PaymentReturnPage;
