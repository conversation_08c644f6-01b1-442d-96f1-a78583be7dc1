import React, { useState, useRef, useCallback } from "react";
import useTranslation from "../hooks/useTranslation";
import { motion, AnimatePresence } from "framer-motion";
import { getCdnUrl } from "../utils/cdnUtils";
import CreatePostModal from "../components/CreatePostModal";
import PostView from "../components/PostView";
import MainNavigation from "../components/navigation/MainNavigation";
import MobileNavigation from "../components/navigation/MobileNavigation";
import { SectionLoader, InlineLoader } from "../components/ui/LoadingIndicator";
import { useExploreFeed } from "../hooks/explore/useExploreFeed";
import { useLikePost } from "../hooks/explore/usePostMutations";
import { useQueryClient } from '@tanstack/react-query';
import socialPostService from "../services/socialPostService";
import { Link } from "react-router-dom";
import ReactDOM from "react-dom";
// Add CSS for masonry grid
const masonryStyles = `
  .masonry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    grid-auto-rows: 200px;
    grid-auto-flow: dense;
    gap: 1rem;
  }
  
  .masonry-item {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .masonry-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  .masonry-item img,
  .masonry-item video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
  
  @media (min-width: 640px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
  }
  
  @media (min-width: 768px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
  }
  
  @media (min-width: 1024px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
  }
  
  @media (min-width: 1280px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }
`;

// Utility to calculate age from date_of_birth
function getAge(dateString) {
  if (!dateString) return null;
  const today = new Date();
  const birthDate = new Date(dateString);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

// Custom hook to detect desktop
function useIsDesktop() {
  const [isDesktop, setIsDesktop] = useState(() => typeof window !== 'undefined' ? window.innerWidth >= 1024 : false);
  React.useEffect(() => {
    function handleResize() {
      setIsDesktop(window.innerWidth >= 1024);
    }
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return isDesktop;
}

const PostImageCarousel = ({ mediaFiles, postId }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isHovered, setIsHovered] = useState(false);

    const handlePrev = (e) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === 0 ? mediaFiles.length - 1 : prev - 1));
    };

    const handleNext = (e) => {
        e.stopPropagation();
        setCurrentIndex((prev) => (prev === mediaFiles.length - 1 ? 0 : prev + 1));
    };

    return (
        <div 
            className="relative w-full group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Main Image/Video Container */}
            <div className="relative w-full">
                {mediaFiles[currentIndex].type.startsWith('image/') ? (
                    <img
                        src={mediaFiles[currentIndex].url}
                        alt={`Post ${postId} - Image ${currentIndex + 1}`}
                        className="w-full h-auto object-contain rounded-t-2xl"
                        loading="lazy"
                    />
                ) : (
                    <video
                        className="w-full h-auto object-contain rounded-t-2xl"
                        poster={mediaFiles[currentIndex].thumbnail || ""}
                        preload="none"
                    >
                        <source
                            src={mediaFiles[currentIndex].url}
                            type={mediaFiles[currentIndex].type}
                        />
                    </video>
                )}
            </div>

            {/* Navigation Arrows */}
            {mediaFiles.length > 1 && (
                <>
                    <motion.button
                        onClick={handlePrev}
                        className={`absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white backdrop-blur-sm transition-all duration-200 ${
                            isHovered ? 'opacity-100' : 'opacity-0'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </motion.button>
                    <motion.button
                        onClick={handleNext}
                        className={`absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/50 text-white backdrop-blur-sm transition-all duration-200 ${
                            isHovered ? 'opacity-100' : 'opacity-0'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </motion.button>
                </>
            )}

            {/* Multiple Media Indicator */}
            {mediaFiles.length > 1 && (
                <motion.div
                    className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full p-2"
                    whileHover={{ scale: 1.1 }}
                >
                    <svg
                        className="w-4 h-4 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                        />
                    </svg>
                </motion.div>
            )}
        </div>
    );
};

const Explore = () => {
  const [activeTab, setActiveTab] = useState("for-you");
  const { t } = useTranslation(["explore", "common"]);
  const [isCreatePostModalOpen, setIsCreatePostModalOpen] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [isPostViewOpen, setIsPostViewOpen] = useState(false);

  const feedType = activeTab === "for-you" ? "for_you" : "following";
  const { 
    data, 
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useExploreFeed(feedType);

  const posts = data?.posts || [];
  const hasMore = data?.hasMore || false;
  const initialLoading = isLoading && posts.length === 0;

  const { mutate: likePostMutation } = useLikePost(feedType);
  const queryClient = useQueryClient();

  const observer = useRef();
  const lastPostElementRef = useCallback(
    (node) => {
      if (isLoading || isFetchingNextPage) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      });
      if (node) observer.current.observe(node);
    },
    [isLoading, isFetchingNextPage, hasNextPage, fetchNextPage],
  );

  const handlePostClick = (postId) => {
    queryClient.prefetchQuery({
      queryKey: ['socialPost', postId],
      queryFn: () => socialPostService.getPostById(postId),
    });
    setSelectedPostId(postId);
    setIsPostViewOpen(true);
  };

  const handlePostCreated = () => {
    queryClient.invalidateQueries({ queryKey: ['explore', 'feed', feedType] });
  };

  const handleLikePost = (postId, wasLiked, e) => {
    e.stopPropagation();
    likePostMutation({ postId, isLiked: wasLiked });
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInSeconds = Math.floor((now - postDate) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return postDate.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Shared Navigation */}
      <MainNavigation activeItem="/explore" />

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8">
            <button
              className={`py-4 px-1 font-medium text-sm border-b-2 transition-colors ${
                activeTab === "for-you"
                  ? "border-indigo-600 text-indigo-600 bg-white dark:bg-gray-950 dark:text-indigo-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 bg-white dark:bg-gray-950 dark:text-gray-400 dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("for-you")}
            >
              {t("explore:tabs.forYou")}
            </button>
            <button
              className={`py-4 px-1 font-medium text-sm border-b-2 transition-colors ${
                activeTab === "following"
                  ? "border-indigo-600 text-indigo-600 bg-white dark:bg-gray-950 dark:text-indigo-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 bg-white dark:bg-gray-950 dark:text-gray-400 dark:hover:text-gray-200"
              }`}
              onClick={() => setActiveTab("following")}
            >
              {t("explore:tabs.following")}
            </button>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="w-full py-6 px-2 sm:px-4 lg:px-8 xl:px-12">
        {/* Error Message */}
        {isError && (
          <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-6 flex items-center justify-between">
            <span>{error?.message || 'An error occurred'}</span>
            <button
              onClick={() => refetch()}
              className="bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 text-red-800 dark:text-red-200 px-3 py-1 rounded-md text-sm transition-colors"
            >
              {t("explore:retry")}
            </button>
          </div>
        )}

        {/* Initial Loading State */}
        {initialLoading && (
          <SectionLoader
            type="wave"
            size="large"
            message={t("explore:loading")}
            color="indigo"
          />
        )}

        {/* Posts Grid */}
        {!initialLoading && posts.length > 0 && (
          <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 auto-rows-[200px]">
            <AnimatePresence>
              {posts.map((post, index) => {
                const media = post.media_files?.[0];
                const hasDimensions = media && media.dimensions && media.dimensions.width && media.dimensions.height;
                const aspectRatio = hasDimensions
                  ? `${media.dimensions.width} / ${media.dimensions.height}`
                  : "1 / 1";
                const gridRowSpan = hasDimensions
                  ? `span ${Math.max(1, Math.ceil((media.dimensions.height / media.dimensions.width) * 2))}`
                  : "span 1";
                return (
                  <motion.div
                    key={post.id}
                    ref={index === posts.length - 1 ? lastPostElementRef : null}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="group relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/20 dark:border-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.2] cursor-pointer"
                    onClick={() => handlePostClick(post.id)}
                    whileHover={{ y: -4 }}
                    layout
                    style={{
                      gridRow: gridRowSpan
                    }}
                  >
                    {/* Media Section - Preserves Aspect Ratio */}
                    <div className="relative w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
                      {media ? (
                        <div className="w-full h-full">
                          {media.type && media.type.startsWith('image/') ? (
                            <img
                              src={media.url}
                              alt={`Post ${post.id}`}
                              className="w-full h-full object-cover"
                              loading="lazy"
                              style={{ aspectRatio }}
                            />
                          ) : (
                            <video
                              className="w-full h-full object-cover"
                              poster={media.thumbnail || ""}
                              preload="none"
                              style={{ aspectRatio }}
                            >
                              <source
                                src={media.url}
                                type={media.type}
                              />
                            </video>
                          )}
                          {/* Multiple Media Indicator */}
                          {post.media_files.length > 1 && (
                            <motion.div
                              className="absolute top-3 right-3 bg-black/60 dark:bg-white/20 backdrop-blur-sm rounded-full p-2"
                              whileHover={{ scale: 1.1 }}
                            >
                              <svg
                                className="w-4 h-4 text-white dark:text-gray-900"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                                />
                              </svg>
                            </motion.div>
                          )}
                        </div>
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-800 dark:to-gray-900">
                          <motion.svg
                            className="w-16 h-16 text-indigo-300 dark:text-indigo-700"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            whileHover={{ scale: 1.1 }}
                          >
                            <path
                              fillRule="evenodd"
                              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                              clipRule="evenodd"
                            />
                          </motion.svg>
                        </div>
                      )}
                    </div>

                    {/* Content Overlay - Positioned at bottom */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-black/40 to-transparent dark:from-gray-900/90 dark:to-gray-900/40 p-4">
                      {/* User info and title */}
                      <div className="flex items-center mb-2">
                        <ProfileHoverCard user={post.user}>
                          <Link
                            to={post.user.role === 'talent' ? `/talents/${post.user.id}` : `/talents/${post.user.id}`}
                            aria-label={`View ${post.user.name}'s profile`}
                            className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center ring-2 ring-white dark:ring-gray-700 shadow-sm hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400"
                            tabIndex={0}
                          >
                            {post.user?.profile_picture ? (
                              <img
                                src={getCdnUrl(post.user.profile_picture)}
                                alt={post.user.username || post.user.name}
                                className="w-full h-full rounded-full object-cover"
                              />
                            ) : (
                              <span className="text-indigo-600 dark:text-indigo-300 font-bold text-xs">
                                {(post.user?.username || post.user?.name || "?").charAt(0).toUpperCase()}
                              </span>
                            )}
                          </Link>
                        </ProfileHoverCard>
                        <div className="flex-1 min-w-0">
                          <ProfileHoverCard user={post.user}>
                            <Link
                              to={post.user.role === 'talent' ? `/talents/${post.user.id}` : `/talents/${post.user.id}`}
                              aria-label={`View ${post.user.name}'s profile`}
                              className="block focus:outline-none focus:ring-2 focus:ring-indigo-400"
                              tabIndex={0}
                            >
                              <h3 className="text-sm text-left font-semibold text-white dark:text-gray-100 truncate mb-1 hover:underline">
                                {post.title}
                              </h3>
                              <div className="flex items-center space-x-2">
                                <p className="text-xs text-left font-medium text-gray-200 dark:text-gray-300 truncate hover:underline">
                                  {post.user?.username || post.user?.name || "Unknown User"}
                                </p>
                                <span className="text-gray-300 dark:text-gray-400">•</span>
                                <p className="text-xs text-left text-gray-300 dark:text-gray-400">
                                  {formatTimeAgo(post.created_at)}
                                </p>
                              </div>
                            </Link>
                          </ProfileHoverCard>
                        </div>
                      </div>

                      {/* Description preview */}
                      {post.description && (
                        <p className="text-xs text-left text-gray-200 dark:text-gray-300 mb-2 line-clamp-2 leading-relaxed">
                          {post.description}
                        </p>
                      )}

                      {/* Interaction stats */}
                      <div className="flex items-center justify-between pt-2 border-t border-white/20">
                        <div className="flex items-center space-x-3">
                          <motion.div
                            className="flex items-center space-x-1"
                            whileHover={{ scale: 1.05 }}
                            onClick={(e) => handleLikePost(post.id, post.is_liked, e)}
                          >
                            <svg
                              className={`w-4 h-4 ${post.is_liked ? "text-red-400" : "text-gray-300 dark:text-gray-400"}`}
                              fill={post.is_liked ? "currentColor" : "none"}
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                              />
                            </svg>
                            <span className="text-xs font-medium text-gray-200 dark:text-gray-300">
                              {post.total_liked || 0}
                            </span>
                          </motion.div>

                          <motion.div
                            className="flex items-center space-x-1"
                            whileHover={{ scale: 1.05 }}
                          >
                            <svg
                              className="w-4 h-4 text-gray-400 dark:text-gray-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                              />
                            </svg>
                            <span className="text-xs font-medium text-gray-200 dark:text-gray-300">
                              {post.total_comments || 0}
                            </span>
                          </motion.div>
                        </div>

                        <motion.button
                          className="text-xs text-white dark:text-gray-100 bg-white/20 dark:bg-gray-800/40 backdrop-blur-sm border border-white/30 dark:border-gray-700 rounded-2xl font-medium hover:bg-white/30 dark:hover:bg-gray-700 transition-colors px-3 py-1"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          View
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        )}

        {/* Loading More State */}
        {isFetchingNextPage && (
          <div className="flex justify-center mt-6">
            <InlineLoader size="medium" color="indigo" />
          </div>
        )}

        {/* Empty State */}
        {!initialLoading && !isLoading && posts.length === 0 && (
          <motion.div
            className="text-center py-16 text-gray-900 dark:text-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-24 h-24 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-gray-800 dark:to-gray-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-12 h-12 text-indigo-500 dark:text-indigo-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              No posts yet
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
              {activeTab === "for-you"
                ? "We don't have any recommended posts for you yet. Be the first to share your gaming moments!"
                : "People you follow haven't posted anything yet. Check out the For You tab or create your first post!"}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {activeTab === "following" && (
                <motion.button
                  onClick={() => setActiveTab("for-you")}
                  className="bg-white dark:bg-gray-900 border-2 border-indigo-600 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400 px-6 py-3 rounded-2xl font-semibold hover:bg-indigo-50 dark:hover:bg-gray-800 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Discover Posts
                </motion.button>
              )}
              <motion.button
                onClick={() => setIsCreatePostModalOpen(true)}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-800 dark:to-purple-800 text-white px-8 py-3 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Create Your First Post
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* End of Results Message */}
        {!isLoading && !hasMore && posts.length > 0 && (
          <motion.div
            className="text-center py-8 text-gray-900 dark:text-gray-100"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-green-500 dark:text-green-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300 font-medium">
              You've reached the end of the feed 🎉
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
              Check back later for more amazing content!
            </p>
          </motion.div>
        )}
      </div>

      {/* Fixed Create Post Button */}
      <motion.div
        className="fixed bottom-8 right-8 z-[60]"
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: "spring", stiffness: 260, damping: 20, delay: 0.5 }}
      >
        <motion.button
          onClick={() => setIsCreatePostModalOpen(true)}
          className="relative bg-gradient-to-br from-indigo-500 to-indigo-600 dark:from-indigo-800 dark:to-indigo-900 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg hover:shadow-indigo-500/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500/30 overflow-hidden group"
          whileHover={{
            scale: 1.05,
            rotate: 45,
            boxShadow: "0 20px 25px -5px rgba(99, 102, 241, 0.2)",
          }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Subtle gradient overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent dark:from-gray-700/20 dark:to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          />

          {/* Plus icon */}
          <motion.svg
            className="w-7 h-7 relative z-10 text-white dark:text-gray-100"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            whileHover={{ rotate: -45 }}
            transition={{ duration: 0.3 }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2.5"
              d="M12 4v16m8-8H4"
            />
          </motion.svg>

          {/* Subtle pulse effect */}
          <motion.div
            className="absolute inset-0 rounded-full bg-white/10 dark:bg-gray-700/20"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0, 0.3],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />

          {/* Tooltip */}
          <motion.div
            className="absolute right-full mr-4 px-3 py-1.5 bg-gray-900/90 dark:bg-gray-800/90 text-white dark:text-gray-100 text-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none backdrop-blur-sm"
            initial={{ opacity: 0, x: 10 }}
            whileHover={{ opacity: 1, x: 0 }}
          >
            Create New Post
            <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 rotate-45 w-2 h-2 bg-gray-900/90"></div>
          </motion.div>
        </motion.button>
      </motion.div>

      {/* Create Post Modal */}
      <CreatePostModal
        isOpen={isCreatePostModalOpen}
        onClose={() => setIsCreatePostModalOpen(false)}
        onPostCreated={handlePostCreated}
      />

      {/* Post View Modal */}
      <PostView
        postId={selectedPostId}
        isOpen={isPostViewOpen}
        onClose={() => {
          setIsPostViewOpen(false);
          setSelectedPostId(null);
        }}
      />

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/explore" />
    </div>
  );
};

export default Explore;

function ProfileHoverCard({ user, children }) {
  const isDesktop = useIsDesktop();
  const [open, setOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  const [cardDims, setCardDims] = useState({ width: 0, height: 0 });
  const triggerRef = React.useRef();
  const cardRef = React.useRef();
  let timeout;

  // Handlers for hover/focus
  const handleMouseEnter = () => {
    clearTimeout(timeout);
    setOpen(true);
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
        width: rect.width
      });
    }
  };
  const handleMouseLeave = () => {
    timeout = setTimeout(() => setOpen(false), 120);
  };
  const handleFocus = () => {
    setOpen(true);
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
        width: rect.width
      });
    }
  };
  const handleBlur = (e) => {
    // Only close if focus moves outside the card and trigger
    if (
      cardRef.current &&
      !cardRef.current.contains(e.relatedTarget) &&
      triggerRef.current &&
      !triggerRef.current.contains(e.relatedTarget)
    ) {
      setOpen(false);
    }
  };

  // Keyboard: close on Escape
  React.useEffect(() => {
    if (!open) return;
    function onKeyDown(e) {
      if (e.key === 'Escape') setOpen(false);
    }
    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [open]);

  // Calculate age and profile details
  const age = user ? getAge(user.date_of_birth) : null;
  const level = user?.level?.name || user?.level?.level || null;
  const profileLink = user
    ? user.role === 'talent'
      ? `/talents/${user.id}`
      : `/talents/${user.id}`
    : '';

  // Responsive and overflow handling
  React.useEffect(() => {
    if (open && cardRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      setCardDims({ width: cardRect.width, height: cardRect.height });
    }
  }, [open]);

  // Calculate left/top to keep card in viewport
  let left = position.left;
  let top = position.top + 8;
  const padding = 8;
  if (typeof window !== 'undefined') {
    if (cardDims.width && left - cardDims.width / 2 < padding) {
      left = cardDims.width / 2 + padding;
    }
    if (cardDims.width && left + cardDims.width / 2 > window.innerWidth - padding) {
      left = window.innerWidth - cardDims.width / 2 - padding;
    }
    if (cardDims.height && top + cardDims.height > window.innerHeight - padding) {
      top = window.innerHeight - cardDims.height - padding;
    }
  }

  // If there's no user data or not on desktop, simply render children
  if (!user || !isDesktop) {
    return <span ref={triggerRef}>{children}</span>;
  }

  return (
    <span
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0}
      ref={triggerRef}
      aria-haspopup="dialog"
      aria-expanded={open}
      aria-label={`Show profile preview for ${user.name}`}
      role="button"
    >
      {children}
      {open && ReactDOM.createPortal(
        <AnimatePresence>
          <motion.div
            ref={cardRef}
            initial={{ opacity: 0, y: 10, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.98 }}
            transition={{ type: 'spring', stiffness: 300, damping: 24 }}
            className="z-[9999] fixed pointer-events-auto min-w-[220px] max-w-xs w-[90vw] rounded-2xl shadow-2xl border border-indigo-200 p-0 flex flex-col items-center bg-gradient-to-br from-white/80 via-indigo-50/80 to-blue-100/80 backdrop-blur-xl ring-1 ring-indigo-100/60"
            style={{
              top,
              left,
              transform: 'translateX(-50%)',
              boxShadow: '0 12px 40px 0 rgba(80,80,180,0.18), 0 2px 12px 0 rgba(80,80,180,0.10)'
            }}
            role="dialog"
            aria-modal="true"
            aria-label={`Profile preview for ${user.name}`}
            tabIndex={-1}
          >
            {/* Gradient accent bar */}
            <div className="absolute top-0 left-0 w-full h-2 rounded-t-2xl bg-gradient-to-r from-indigo-400 via-blue-400 to-violet-400" />
            {/* Arrow */}
            <div
              className="absolute -top-2 left-1/2 -translate-x-1/2 w-4 h-4 z-10"
              style={{ pointerEvents: 'none' }}
              aria-hidden="true"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" className="block">
                <polygon points="8,0 16,16 0,16" fill="#f8fafc" stroke="#a5b4fc" strokeWidth="1" />
              </svg>
            </div>
            <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-indigo-200 to-blue-200 flex items-center justify-center mb-2 mt-6 shadow-lg ring-4 ring-indigo-200/40 border-2 border-white overflow-hidden">
              {user.profile_picture ? (
                <img
                  src={getCdnUrl(user.profile_picture)}
                  alt={user.username || user.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-indigo-600 font-extrabold text-3xl">
                  {(user.username || user.name || "?").charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div className="text-center w-full px-2 pb-2">
              <div className="font-extrabold text-lg text-gray-900 truncate leading-tight mb-0.5">{user.name}</div>
              <div className="text-xs text-indigo-500 truncate mb-1 font-semibold">@{user.username || user.name}</div>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mb-1">
                {age && <span className="font-medium">Age: {age}</span>}
                {level && <span className="inline-block px-2 py-0.5 bg-indigo-100 text-indigo-700 rounded-full ml-1 font-semibold shadow">{level}</span>}
              </div>
              {user.biography && (
                <div className="text-xs text-gray-600 mb-2 line-clamp-2 italic">{user.biography}</div>
              )}
              <Link
                to={profileLink}
                className="inline-block mt-2 px-5 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-xl font-bold text-sm shadow-lg hover:from-indigo-600 hover:to-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-400"
                aria-label={`View ${user.name}'s profile`}
                role="link"
              >
                View Profile
              </Link>
            </div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}
    </span>
  );
}
