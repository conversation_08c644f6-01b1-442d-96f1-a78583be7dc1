import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import MissionCard from '../components/mission/MissionCard';
import ApplicationModal from '../components/mission/ApplicationModal';
import ParticipantsModal from '../components/mission/ParticipantsModal';
import EnhancedParticipantsTab from '../components/mission/EnhancedParticipantsTab';
import { missionApi } from '../services/missionApi';
import { transformApiMissionToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';
import {
  FaTrophy, FaRocket, FaUsers, FaCalendarAlt, FaCheckCircle, FaClock,
  FaTimesCircle, FaBan, FaStar, FaHeart, FaBookmark, FaPlay, FaPause,
  FaEdit, FaTrash, FaEye, FaComments, FaGamepad, FaMicrophone,
  FaDiscord, FaShieldAlt, FaFire, FaGem, FaCrown, FaLightbulb
} from 'react-icons/fa';

const MissionDetailPage = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const [mission, setMission] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasApplied, setHasApplied] = useState(false);
  const [applyStatus, setApplyStatus] = useState('');
  const [canWithdraw, setCanWithdraw] = useState(false);
  const [currentChildId, setCurrentChildId] = useState(null);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [bookmarkLoading, setBookmarkLoading] = useState(false);
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [similarMissions, setSimilarMissions] = useState([]);
  const [activeTab, setActiveTab] = useState('details');
  const [isCurrentUserHost, setIsCurrentUserHost] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [missionTimeStatus, setMissionTimeStatus] = useState('upcoming'); // 'upcoming', 'ongoing', 'completed'

  // Image carousel state for mission images
  const [currentImage, setCurrentImage] = useState(0);
  const images = mission && mission.images && mission.images.length > 0 ? mission.images : [mission && mission.image ? mission.image : '/images/mission-default.jpg'];
  const hasMultipleImages = images.length > 1;
  const handlePrev = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };
  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  // Check for delete intent from navigation state
  useEffect(() => {
    if (location.state?.showDeleteModal) {
      setShowDeleteModal(true);
    }
  }, [location.state]);

  // Fetch mission details from API
  useEffect(() => {
    const fetchMissionDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch mission details
        const response = await missionApi.getMissionById(missionId);

        // Transform API mission to frontend format
        const transformedMission = transformApiMissionToFrontend(response.data);

        if (!transformedMission) {
          setError('Mission not found');
          setIsLoading(false);
          return;
        }

        setMission(transformedMission);
        setIsBookmarked(transformedMission.is_bookmarked ?? false);

        // Application details
        setHasApplied(response.data.is_applied ?? false);
        setApplyStatus(response.data.apply_status || '');
        setCanWithdraw(response.data.can_withdraw ?? false);
        setCurrentChildId(response.data.current_user_child_id ?? null);

        // Check if current user is the host
        // Determine logged in user id from stored user object or fallback key
        let currentUserId = null;
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            currentUserId = JSON.parse(storedUser).id;
          } catch (err) {
            currentUserId = null;
          }
        }
        if (!currentUserId) {
          currentUserId = localStorage.getItem('user_id');
        }

        const hostId = response.data.user?.id || response.data.user_id;
        setIsCurrentUserHost(
          currentUserId && String(currentUserId) === String(hostId)
        );

        // Fetch similar missions
        try {
          // Get missions with the same theme or style
          const similarResponse = await missionApi.getMissions({
            type_id: response.data.service_type_id,
            style_id: response.data.service_style_id
          });

          // Transform API missions to frontend format
          const transformedSimilarMissions = similarResponse.data.data
            .filter(m => m.id !== parseInt(missionId)) // Filter out current mission
            .slice(0, 3) // Limit to 3 similar missions
            .map(transformApiMissionToFrontend);

          setSimilarMissions(transformedSimilarMissions);
        } catch (error) {
          console.error('Error fetching similar missions:', error);
          // Don't set error state for similar missions, as it's not critical
          setSimilarMissions([]);
        }
      } catch (error) {
        console.error('Error fetching mission details:', error);
        setError(getApiErrorMessage(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissionDetails();
  }, [missionId]);



  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Format time duration
  const formatDuration = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationMs = end - start;
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours === 0) {
      return `${minutes} minutes`;
    } else if (minutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  };

  // Calculate time remaining and mission time status
  useEffect(() => {
    if (!mission || !mission.date) return;

    const updateTimeRemaining = () => {
      const now = new Date();
      const missionStartDate = new Date(mission.date);
      const missionEndDate = new Date(mission.end_date);

      // Calculate time difference
      const timeToStart = missionStartDate - now;
      const timeToEnd = missionEndDate - now;

      // Determine mission time status
      if (timeToStart > 0) {
        // Mission hasn't started yet
        setMissionTimeStatus('upcoming');

        // Calculate days, hours, minutes
        const days = Math.floor(timeToStart / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeToStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeToStart % (1000 * 60 * 60)) / (1000 * 60));

        if (days > 0) {
          setTimeRemaining(`Starts in ${days}d ${hours}h`);
        } else if (hours > 0) {
          setTimeRemaining(`Starts in ${hours}h ${minutes}m`);
        } else if (minutes > 0) {
          setTimeRemaining(`Starts in ${minutes}m`);
        } else {
          setTimeRemaining('Starting now!');
        }
      } else if (timeToEnd > 0) {
        // Mission is in progress
        setMissionTimeStatus('ongoing');

        // Calculate hours and minutes remaining
        const hours = Math.floor(timeToEnd / (1000 * 60 * 60));
        const minutes = Math.floor((timeToEnd % (1000 * 60 * 60)) / (1000 * 60));

        if (hours > 0) {
          setTimeRemaining(`${hours}h ${minutes}m remaining`);
        } else if (minutes > 0) {
          setTimeRemaining(`${minutes}m remaining`);
        } else {
          setTimeRemaining('Ending soon');
        }
      } else {
        // Mission has ended
        setMissionTimeStatus('completed');
        setTimeRemaining('Mission completed');
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Update every minute
    const interval = setInterval(updateTimeRemaining, 60000);

    return () => clearInterval(interval);
  }, [mission]);

  // Toggle bookmark
  const toggleBookmark = async () => {
    if (bookmarkLoading) return;
    setBookmarkLoading(true);
    try {
      await missionApi.bookmarkMission(missionId);
      setIsBookmarked((prev) => !prev);
      toast.success(!isBookmarked ? 'Mission added to bookmarks' : 'Mission removed from bookmarks');
    } catch (error) {
      toast.error('Failed to update bookmark');
    } finally {
      setBookmarkLoading(false);
    }
  };

  // Submit application
  const submitApplication = async (applicationData) => {
    console.log('MissionDetailPage submitApplication called', applicationData);
    try {
      setShowApplicationModal(false);
      // Show info toast instead of loading (since loading is not supported)
      toast.info('Submitting your application...');
      // Send application to API
      const response = await missionApi.applyForMission(missionId, {
        notes: applicationData.message || ''
      });
      console.log('API response from applyForMission', response);
      setHasApplied(true);
      toast.success(response.data?.message || 'Application submitted successfully!');
    } catch (error) {
      console.error('Error applying for mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to submit application');
    }
  };

  // Withdraw application
  const handleWithdrawApplication = async () => {
    if (!currentChildId || isWithdrawing) return;
    try {
      setIsWithdrawing(true);
      await missionApi.withdrawApplication(missionId, currentChildId);
      setApplyStatus('withdrawn');
      setCanWithdraw(false);
      toast.success('Application withdrawn');
    } catch (error) {
      console.error('Error withdrawing application:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to withdraw application');
    } finally {
      setIsWithdrawing(false);
    }
  };

  // View participants
  const viewParticipants = () => {
    setShowParticipantsModal(true);
  };

  // Handle edit mission
  const handleEditMission = () => {
    navigate(`/missions/${missionId}/edit`);
  };

  // Handle delete mission
  const handleDeleteMission = async () => {
    setIsDeleting(true);

    try {
      await missionApi.deleteMission(missionId);

      // Show success toast
      toast.success('Mission deleted successfully');

      // Navigate back to missions page
      navigate('/missions/my-missions');
    } catch (error) {
      console.error('Error deleting mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to delete mission');
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  };

  // Handle start mission
  const handleStartMission = async () => {
    try {
      // Show loading toast
      toast.loading('Starting mission...');

      // Call API to start mission
      await missionApi.startMission(missionId);

      // Show success toast
      toast.success('Mission started successfully');

      // Navigate to mission execution page
      navigate(`/missions/${missionId}/execute`);
    } catch (error) {
      console.error('Error starting mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to start mission');
    }
  };

  // Read hideApplyButton from navigation state (for MyMissionsPage -> details)
  const hideApplyButton = location.state?.hideApplyButton || false;

  // Show Manage Applicants tab only for hosts and for relevant statuses
  const showManageTab =
    isCurrentUserHost &&
    ['open', 'completed', 'closed', 'cancelled'].includes(
      (mission?.status || '').toLowerCase()
    );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-indigo-900 dark:to-purple-900 flex justify-center items-center relative overflow-hidden">
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute -top-20 -left-20 w-96 h-96 bg-gradient-to-br from-indigo-400/30 via-blue-400/20 to-purple-400/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-20 -right-20 w-96 h-96 bg-gradient-to-tr from-pink-400/20 via-yellow-400/30 to-indigo-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        {/* Enhanced Loading Spinner */}
        <motion.div
          className="relative z-10 flex flex-col items-center gap-6"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="relative">
            <div className="w-16 h-16 border-4 border-indigo-200 dark:border-indigo-800 rounded-full animate-spin border-t-indigo-600 dark:border-t-yellow-400"></div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent rounded-full animate-ping border-t-indigo-400 dark:border-t-yellow-300"></div>
          </div>
          <motion.div
            className="text-center"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">Loading Mission Details</h3>
            <p className="text-gray-500 dark:text-gray-400">Preparing your gaming experience...</p>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  if (error || !mission) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-indigo-900 dark:to-purple-900 flex justify-center items-center relative overflow-hidden">
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute -top-20 -left-20 w-96 h-96 bg-gradient-to-br from-red-400/30 via-pink-400/20 to-orange-400/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-20 -right-20 w-96 h-96 bg-gradient-to-tr from-purple-400/20 via-indigo-400/30 to-blue-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <motion.div
          className="relative z-10 bg-white/20 dark:bg-gray-900/40 backdrop-blur-xl rounded-3xl shadow-2xl dark:shadow-indigo-900/50 p-8 text-center max-w-md mx-4 border border-white/30 dark:border-gray-700/50"
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 0.6, type: 'spring', damping: 25, stiffness: 500 }}
        >
          <motion.div
            className="w-20 h-20 bg-gradient-to-br from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30 rounded-full flex items-center justify-center mx-auto mb-6"
            animate={{
              boxShadow: [
                '0 0 20px rgba(239, 68, 68, 0.3)',
                '0 0 40px rgba(239, 68, 68, 0.6)',
                '0 0 20px rgba(239, 68, 68, 0.3)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <FaTimesCircle className="w-10 h-10 text-red-500" />
          </motion.div>

          <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-3">
            {error ? 'Mission Loading Failed' : 'Mission Not Found'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
            {error || "The mission you're looking for doesn't exist or has been removed from our gaming universe."}
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-3">
            <motion.button
              onClick={() => navigate('/missions')}
              className="px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <FaRocket className="w-4 h-4" />
              Back to Missions
            </motion.button>
            {error && (
              <motion.button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-white/20 dark:bg-gray-800/40 backdrop-blur-md border border-white/30 dark:border-gray-700/50 text-gray-700 dark:text-gray-300 rounded-xl font-semibold hover:bg-white/30 dark:hover:bg-gray-800/60 transition-all duration-300 flex items-center justify-center gap-2"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaClock className="w-4 h-4" />
                Try Again
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <MissionLayout title="Mission Details" backPath="/missions">
      {/* Enhanced Background Effects */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-20 -left-20 w-96 h-96 bg-gradient-to-br from-indigo-400/20 via-blue-400/10 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -right-20 w-96 h-96 bg-gradient-to-tr from-pink-400/10 via-yellow-400/20 to-indigo-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }}></div>

        {/* Gaming Particles */}
        <div className="absolute top-1/4 left-1/3 w-4 h-4 bg-yellow-400/30 rounded-full animate-float" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-2/3 right-1/4 w-3 h-3 bg-pink-400/40 rounded-full animate-float" style={{ animationDelay: '1.2s' }}></div>
        <div className="absolute bottom-1/3 left-1/2 w-2 h-2 bg-blue-400/50 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 right-1/2 w-5 h-5 bg-fuchsia-400/30 rounded-full animate-float" style={{ animationDelay: '2.5s' }}></div>
      </div>

      {/* Mission Detail Content */}
      <div className="relative z-10 flex flex-col gap-6 md:gap-10 px-2 md:px-0">
        {/* Enhanced Mission Image Banner */}
        <motion.div
          className="relative h-64 md:h-80 rounded-3xl overflow-hidden mb-6 md:mb-8 shadow-2xl dark:shadow-indigo-900/50 group"
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          whileHover={{
            y: -8,
            scale: 1.02,
            transition: { duration: 0.3, ease: 'easeOut' }
          }}
          transition={{ duration: 0.7, type: 'spring', damping: 25, stiffness: 500 }}
        >
          {/* Enhanced Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-blue-500/20 to-purple-500/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 scale-110"></div>
          {/* Enhanced Mission Image Carousel */}
          <>
            <motion.img
              key={images[currentImage]}
              src={images[currentImage]}
              alt={mission.title}
              className="w-full h-full object-cover rounded-3xl transition-transform duration-700 group-hover:scale-105"
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
            />

            {/* Enhanced Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-3xl"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/20 via-transparent to-purple-900/20 rounded-3xl"></div>

            {/* Enhanced Carousel Arrows */}
            {hasMultipleImages && (
              <>
                <motion.button
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white p-3 rounded-full shadow-lg border border-white/20 hover:border-white/40 transition-all duration-300 z-10"
                  onClick={handlePrev}
                  aria-label="Previous image"
                  whileHover={{ scale: 1.1, x: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </motion.button>
                <motion.button
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white p-3 rounded-full shadow-lg border border-white/20 hover:border-white/40 transition-all duration-300 z-10"
                  onClick={handleNext}
                  aria-label="Next image"
                  whileHover={{ scale: 1.1, x: 2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </motion.button>
              </>
            )}

            {/* Enhanced Dot Indicators */}
            {hasMultipleImages && (
              <div className="absolute bottom-5 left-1/2 -translate-x-1/2 flex gap-2 z-10">
                {images.map((_, idx) => (
                  <motion.button
                    key={idx}
                    onClick={() => setCurrentImage(idx)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      idx === currentImage
                        ? 'bg-white shadow-lg scale-125'
                        : 'bg-white/50 hover:bg-white/70 hover:scale-110'
                    }`}
                    whileHover={{ scale: idx === currentImage ? 1.25 : 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  />
                ))}
              </div>
            )}
          </>
          {/* Enhanced Status Badge */}
          <motion.div
            className={`absolute top-5 left-5 px-4 py-2 rounded-full text-sm font-bold flex items-center shadow-xl backdrop-blur-md border transition-all duration-300 cursor-default z-20
              ${mission.status === 'open'
                ? 'bg-gradient-to-r from-green-400/90 to-emerald-500/90 text-white border-green-300/50 shadow-green-500/25' :
                mission.status === 'in_progress' || mission.status === 'in progress'
                ? 'bg-gradient-to-r from-blue-400/90 to-indigo-500/90 text-white border-blue-300/50 shadow-blue-500/25' :
                mission.status === 'completed'
                ? 'bg-gradient-to-r from-purple-400/90 to-pink-500/90 text-white border-purple-300/50 shadow-purple-500/25' :
                'bg-gradient-to-r from-gray-400/90 to-gray-500/90 text-white border-gray-300/50 shadow-gray-500/25'}
            `}
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2, type: 'spring', damping: 25, stiffness: 500 }}
            whileHover={{ scale: 1.05, y: -2 }}
          >
            {mission.status === 'open' && <FaCheckCircle className="w-4 h-4 mr-2" />}
            {(mission.status === 'in_progress' || mission.status === 'in progress') && <FaClock className="w-4 h-4 mr-2" />}
            {mission.status === 'completed' && <FaTrophy className="w-4 h-4 mr-2" />}
            {mission.status === 'open' ? 'Open for Applications' :
              mission.status === 'in_progress' || mission.status === 'in progress' ? 'Mission Active' :
                mission.status === 'completed' ? 'Mission Complete' : mission.status}
          </motion.div>

          {/* Enhanced Tags Overlay */}
          <motion.div
            className="absolute bottom-5 left-5 right-5 flex flex-wrap gap-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            {mission.level_requirement && (
              <motion.span
                className="bg-gradient-to-r from-indigo-900/90 to-blue-900/90 text-indigo-100 text-xs font-bold px-3 py-1.5 rounded-full backdrop-blur-md border border-indigo-400/30 shadow-lg flex items-center transition-all duration-300 cursor-pointer hover:shadow-indigo-500/25"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.8 }}
              >
                <FaShieldAlt className="w-3 h-3 mr-1.5 text-yellow-400" />
                LV{mission.level_requirement.min || 1}-{mission.level_requirement.max || 99}
              </motion.span>
            )}
            {mission.style && (
              <motion.span
                className="bg-gradient-to-r from-purple-900/90 to-pink-900/90 text-purple-100 text-xs font-bold px-3 py-1.5 rounded-full backdrop-blur-md border border-purple-400/30 shadow-lg flex items-center transition-all duration-300 cursor-pointer hover:shadow-purple-500/25"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.9 }}
              >
                <FaLightbulb className="w-3 h-3 mr-1.5 text-yellow-400" />
                {mission.style}
              </motion.span>
            )}
            {mission.category && (
              <motion.span
                className="bg-gradient-to-r from-blue-900/90 to-cyan-900/90 text-blue-100 text-xs font-bold px-3 py-1.5 rounded-full backdrop-blur-md border border-blue-400/30 shadow-lg flex items-center transition-all duration-300 cursor-pointer hover:shadow-blue-500/25"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 1.0 }}
              >
                <FaGamepad className="w-3 h-3 mr-1.5 text-cyan-400" />
                {mission.category}
              </motion.span>
            )}
            {mission.platform && (
              <motion.span
                className="bg-gradient-to-r from-green-900/90 to-emerald-900/90 text-green-100 text-xs font-bold px-3 py-1.5 rounded-full backdrop-blur-md border border-green-400/30 shadow-lg flex items-center transition-all duration-300 cursor-pointer hover:shadow-green-500/25"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 1.1 }}
              >
                <FaDiscord className="w-3 h-3 mr-1.5 text-green-400" />
                {mission.platform}
              </motion.span>
            )}
            {mission.language && (
              <motion.span
                className="bg-gradient-to-r from-yellow-900/90 to-orange-900/90 text-yellow-100 text-xs font-bold px-3 py-1.5 rounded-full backdrop-blur-md border border-yellow-400/30 shadow-lg flex items-center transition-all duration-300 cursor-pointer hover:shadow-yellow-500/25"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 1.2 }}
              >
                <FaMicrophone className="w-3 h-3 mr-1.5 text-orange-400" />
                {mission.language}
              </motion.span>
            )}
          </motion.div>

          {/* Enhanced Bookmark Button */}
          <motion.button
            className={`absolute top-5 right-5 w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 shadow-xl backdrop-blur-md border z-20 ${
              isBookmarked
                ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white border-pink-300/50 shadow-pink-500/25'
                : 'bg-white/10 text-white hover:bg-white/20 border-white/20 hover:border-white/40 hover:text-yellow-300'
            }`}
            onClick={async (e) => {
              e.preventDefault();
              await toggleBookmark();
            }}
            aria-label={isBookmarked ? "Remove from bookmarks" : "Add to bookmarks"}
            disabled={bookmarkLoading}
            initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
            animate={{ opacity: 1, scale: 1, rotate: 0 }}
            transition={{ duration: 0.6, delay: 0.1, type: 'spring', damping: 25, stiffness: 500 }}
            whileHover={{ scale: 1.1, y: -2, rotate: isBookmarked ? 0 : 10 }}
            whileTap={{ scale: 0.9 }}
          >
            {bookmarkLoading ? (
              <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <FaBookmark className={`w-5 h-5 ${isBookmarked ? 'text-white' : ''}`} />
            )}
          </motion.button>
        </motion.div>

        {/* Enhanced Info Grid Section */}
        <motion.div
          className="mb-6 md:mb-8 px-0 md:px-0"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6">
            {/* Enhanced Credits Card */}
            <motion.div
              className="relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 dark:from-gray-900/40 dark:to-gray-800/30 backdrop-blur-xl border border-white/30 dark:border-gray-700/50 rounded-2xl shadow-xl dark:shadow-indigo-900/20 py-6 px-6 min-h-[80px] group"
              whileHover={{ scale: 1.02, y: -4 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10 flex items-center justify-center">
                <FaGem className="w-6 h-6 text-green-500 mr-3" />
                <div className="text-center">
                  <div className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">{mission.bounty || mission.reward}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide font-semibold">Credits</div>
                </div>
              </div>
            </motion.div>

            {/* Enhanced Countdown Card */}
            {timeRemaining && (
              <motion.div
                className="relative overflow-hidden bg-gradient-to-br from-indigo-500/90 to-blue-500/90 backdrop-blur-xl border border-indigo-300/50 rounded-2xl shadow-xl py-6 px-6 min-h-[80px] group"
                whileHover={{ scale: 1.02, y: -4 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10 flex items-center justify-center text-white">
                  <FaClock className="w-6 h-6 mr-3" />
                  <div className="text-center">
                    <div className="text-xl font-bold">{timeRemaining}</div>
                    <div className="text-xs uppercase tracking-wide font-semibold opacity-90">Remaining</div>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Enhanced Slots Card */}
            <motion.div
              className="relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 dark:from-gray-900/40 dark:to-gray-800/30 backdrop-blur-xl border border-white/30 dark:border-gray-700/50 rounded-2xl shadow-xl dark:shadow-indigo-900/20 py-6 px-6 min-h-[80px] group"
              whileHover={{ scale: 1.02, y: -4 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10 flex items-center justify-center">
                <FaUsers className="w-6 h-6 text-blue-500 mr-3" />
                <div className="text-center">
                  <div className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">{mission.slots_filled}/{mission.slots_total || mission.max_participants}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide font-semibold">Slots</div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Enhanced Mission Details Container */}
        <motion.div
          className="bg-gradient-to-br from-white/20 to-white/10 dark:from-gray-900/40 dark:to-gray-800/30 backdrop-blur-xl border border-white/30 dark:border-gray-700/50 rounded-3xl shadow-2xl dark:shadow-indigo-900/30 overflow-hidden mb-6 md:mb-8 relative"
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.7, delay: 0.1, type: 'spring', damping: 25, stiffness: 500 }}
        >
          {/* Enhanced Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-blue-500/5 to-purple-500/5 opacity-50"></div>
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-400/10 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-purple-400/10 to-transparent rounded-full blur-3xl"></div>

          {/* Enhanced Header Section */}
          <div className="relative z-10 p-6 border-b border-white/20 dark:border-gray-700/50">
            <motion.div
              className="mb-4"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-indigo-600 via-blue-600 to-purple-600 bg-clip-text text-transparent drop-shadow-lg relative inline-block">
                {mission.title}
                <motion.span
                  className="block h-1 w-20 mt-3 bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: 80 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                ></motion.span>
              </h1>
            </motion.div>
          </div>

          {/* Enhanced Tabs Navigation */}
          <motion.div
            className="relative z-10 bg-white/10 dark:bg-gray-800/20 backdrop-blur-xl rounded-2xl p-2 flex gap-2 mb-6 border border-white/20 dark:border-gray-700/30 shadow-lg overflow-x-auto"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <motion.button
              className={`px-6 py-3 font-bold text-sm rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center gap-2 ${
                activeTab === 'details'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-lg scale-105'
                  : 'bg-white/20 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-white/30 dark:hover:bg-gray-700/50 hover:text-indigo-700 dark:hover:text-indigo-400'
              }`}
              onClick={() => setActiveTab('details')}
              whileHover={{ scale: activeTab === 'details' ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FaEye className="w-4 h-4" />
              Details
            </motion.button>

            <motion.button
              className={`px-6 py-3 font-bold text-sm rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center gap-2 ${
                activeTab === 'host'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-lg scale-105'
                  : 'bg-white/20 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-white/30 dark:hover:bg-gray-700/50 hover:text-indigo-700 dark:hover:text-indigo-400'
              }`}
              onClick={() => setActiveTab('host')}
              whileHover={{ scale: activeTab === 'host' ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FaCrown className="w-4 h-4" />
              Host
            </motion.button>

            <motion.button
              className={`px-6 py-3 font-bold text-sm rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center gap-2 ${
                activeTab === 'participants'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-lg scale-105'
                  : 'bg-white/20 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-white/30 dark:hover:bg-gray-700/50 hover:text-indigo-700 dark:hover:text-indigo-400'
              }`}
              onClick={() => setActiveTab('participants')}
              whileHover={{ scale: activeTab === 'participants' ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FaUsers className="w-4 h-4" />
              Participants
              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-bold ${
                activeTab === 'participants'
                  ? 'bg-white/20 text-white'
                  : 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400'
              }`}>
                {mission.participants?.length || 0}
              </span>
            </motion.button>

            {showManageTab && (
              <motion.button
                className={`px-6 py-3 font-bold text-sm rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-pink-400/60 flex items-center gap-2 ${
                  activeTab === 'manage'
                    ? 'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white shadow-lg scale-105'
                    : 'bg-white/20 dark:bg-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-pink-50 dark:hover:bg-pink-900/20 hover:text-pink-700 dark:hover:text-pink-400'
                }`}
                onClick={() => setActiveTab('manage')}
                whileHover={{ scale: activeTab === 'manage' ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.6 }}
              >
                <FaEdit className="w-4 h-4" />
                Manage
              </motion.button>
            )}
          </motion.div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            className="p-4 md:p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
          >
            {/* Details Tab */}
            {activeTab === 'details' && (
              <div className="flex flex-col gap-6">
                {/* Description Card */}
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800">Description</h2>
                    {mission.description.length > 150 && (
                      <button
                        className="ml-auto text-sm bg-transparent hover:bg-gray-200 hover:rounded-xl text-indigo-600 hover:text-indigo-800 flex items-justify"
                        onClick={() => setShowFullDescription(!showFullDescription)}
                      >
                        {showFullDescription ? (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
                            </svg>
                            Show less
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                            Show more
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  <div className="text-gray-700 text-base whitespace-pre-line">
                      {showFullDescription ? (
                        mission.description
                      ) : (
                        <>
                          {mission.description.length > 150
                            ? `${mission.description.substring(0, 150)}...`
                            : mission.description}
                        </>
                      )}
                  </div>
                </div>

                {/* Requirements Card */}
                {mission.requirements && mission.requirements.length > 0 && (
                  <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                      </svg>
                      <h2 className="text-lg font-semibold text-gray-800">Requirements</h2>
                    </div>
                      <ul className="space-y-3">
                        {mission.requirements.map((requirement, index) => {
                          // Determine icon based on requirement text
                          let icon;
                          const lowerReq = requirement.toLowerCase();

                          if (lowerReq.includes('microphone') || lowerReq.includes('mic')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('discord') || lowerReq.includes('chat') || lowerReq.includes('communication')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('rank') || lowerReq.includes('level') || lowerReq.includes('skill')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('time') || lowerReq.includes('available') || lowerReq.includes('duration')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            );
                          } else {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            );
                          }

                          return (
                          <li key={index} className="flex items-start bg-white/90 p-3 rounded-lg shadow-sm border border-gray-100">
                              {icon}
                              <span className="text-gray-700">{requirement}</span>
                            </li>
                          );
                        })}
                      </ul>
                  </div>
                )}

                {/* Date and Time Card */}
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800">Date & Time</h2>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-stretch gap-6">
                    {/* Start Time */}
                    <div className="flex-1 flex flex-col items-center bg-white p-5 rounded-xl shadow-sm border border-gray-100 mb-2 md:mb-0">
                      <div className="flex items-center mb-2">
                        <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div>
                          <div className="text-xs text-left text-gray-500">Start Time</div>
                          <div className="font-medium text-gray-800">{formatDate(mission.date)}</div>
                          </div>
                        </div>
                        {missionTimeStatus === 'upcoming' && (
                        <div className="mt-1 text-sm text-indigo-600 font-medium flex items-center">
                            <svg className="w-4 h-4 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {timeRemaining}
                          </div>
                        )}
                      </div>
                    {/* Timeline Divider */}
                    <div className="hidden md:flex flex-col items-center justify-center">
                      <div className="w-1 h-8 bg-gradient-to-b from-indigo-400 to-blue-400 rounded-full mb-2"></div>
                      <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3" />
                      </svg>
                      <div className="w-1 h-8 bg-gradient-to-t from-indigo-400 to-blue-400 rounded-full mt-2"></div>
                    </div>
                    {/* End Time */}
                    <div className="flex-1 flex flex-col items-center bg-white p-5 rounded-xl shadow-sm border border-gray-100">
                      <div className="flex items-center mb-2">
                        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                          <div className="text-xs text-left text-gray-500">End Time</div>
                          <div className="font-medium text-gray-800">{formatDate(mission.end_date)}</div>
                          </div>
                        </div>
                      <div className="mt-1 text-sm text-blue-600 font-medium flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Duration: {formatDuration(mission.date, mission.end_date)}
                        </div>
                      </div>
                    </div>
                    {/* Mission progress bar for ongoing missions */}
                    {missionTimeStatus === 'ongoing' && (
                    <div className="mt-6">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>Mission in progress</span>
                          <span>{timeRemaining}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: (() => {
                                const now = new Date();
                                const start = new Date(mission.date);
                                const end = new Date(mission.end_date);
                                const total = end - start;
                                const elapsed = now - start;
                                const percentage = Math.min(100, Math.max(0, (elapsed / total) * 100));
                                return `${percentage}%`;
                              })()
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                </div>

                {/* Tags Card */}
                {mission.tags && mission.tags.length > 0 && (
                  <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                      </svg>
                      <h2 className="text-lg font-semibold text-gray-800">Tags</h2>
                    </div>
                      <div className="flex flex-wrap gap-2">
                        {mission.tags.map((tag, index) => {
                          // Determine tag color based on tag content
                          let tagClass;
                          const lowerTag = tag.toLowerCase();

                          if (lowerTag.includes('tournament') || lowerTag.includes('competition')) {
                            tagClass = "bg-red-100 text-red-800 hover:bg-red-200";
                          } else if (lowerTag.includes('team') || lowerTag.includes('group')) {
                            tagClass = "bg-blue-100 text-blue-800 hover:bg-blue-200";
                          } else if (lowerTag.includes('casual') || lowerTag.includes('fun')) {
                            tagClass = "bg-green-100 text-green-800 hover:bg-green-200";
                          } else if (lowerTag.includes('competitive') || lowerTag.includes('ranked')) {
                            tagClass = "bg-purple-100 text-purple-800 hover:bg-purple-200";
                          } else if (lowerTag.includes('beginner') || lowerTag.includes('new')) {
                            tagClass = "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
                          } else {
                            tagClass = "bg-gray-100 text-gray-800 hover:bg-gray-200";
                          }

                          return (
                            <motion.span
                              key={index}
                              className={`${tagClass} text-sm px-3 py-1.5 rounded-full cursor-pointer transition-colors duration-200 flex items-center`}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => navigate(`/missions?tag=${tag}`)}
                            >
                              <span className="mr-1">#</span>
                              {tag}
                            </motion.span>
                          );
                        })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Host Tab */}
            {activeTab === 'host' && (
              <div>
                <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 mb-6 shadow-lg border border-indigo-100">
                  <div className="flex flex-col md:flex-row md:items-start md:space-x-8">
                    {/* Host Avatar with Gradient Border and Online Indicator */}
                    <div className="relative flex-shrink-0 mb-4 md:mb-0">
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-400 via-blue-400 to-purple-400 p-1 shadow-xl">
                        <div className="w-full h-full rounded-full overflow-hidden bg-white">
                        <img
                          src={mission.host?.avatar || '/images/default-avatar.jpg'}
                          alt={mission.host?.name || 'Host'}
                            className="w-full h-full object-cover rounded-full"
                        />
                        </div>
                      </div>
                      {/* Online status indicator */}
                      {mission.host?.online && (
                        <span className="absolute bottom-2 right-2 w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-4 border-white flex items-center justify-center">
                          <span className="w-2.5 h-2.5 bg-green-500 rounded-full block animate-pulse"></span>
                        </span>
                      )}
                    </div>
                    {/* Host Info Card */}
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-2 mb-2">
                        <h2 className="text-2xl font-bold text-gray-800">
                          {mission.host?.name || 'Anonymous Host'}
                        </h2>
                        <span className="bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center shadow-sm">
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            LV{mission.host?.level || '??'}
                          </span>
                          {mission.host?.verified && (
                          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center border border-blue-200">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Verified
                            </span>
                          )}
                        </div>
                      {/* Host bio/description */}
                      <p className="text-gray-600 text-sm mb-4">
                        {mission.host?.bio || `Professional mission host with experience in ${mission.theme || 'gaming'} missions.`}
                      </p>
                      {/* Host Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                        {/* Rating */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="flex items-center text-yellow-500 mb-1">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span className="ml-1 font-medium text-gray-800">{mission.host?.rating || '4.8'}</span>
                          </div>
                          <p className="text-xs text-gray-500">Rating</p>
                        </div>
                        {/* Reviews */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                            </svg>
                            {mission.host?.reviews || '24'}
                          </div>
                          <p className="text-xs text-gray-500">Reviews</p>
                        </div>
                        {/* Completed Missions */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                            </svg>
                            {mission.host?.completed_missions || '36'}
                          </div>
                          <p className="text-xs text-gray-500">Completed</p>
                        </div>
                        {/* Hosted Missions */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {mission.host?.hosted_missions || '12'}
                          </div>
                          <p className="text-xs text-gray-500">Hosted</p>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-3">
                        <motion.button
                          className="px-4 py-2 rounded-full font-semibold bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.97 }}
                          onClick={() => navigate(`/talents/${mission.host?.id}`)}
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          View Profile
                        </motion.button>
                        <motion.button
                          className="px-4 py-2 rounded-full font-semibold bg-white/90 border border-indigo-200 text-indigo-700 shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:bg-indigo-50 hover:text-indigo-800 hover:scale-105 active:scale-95"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.97 }}
                          onClick={() => navigate('/chat')}
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          Message
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Participants Tab */}
            {activeTab === 'participants' && (
              <EnhancedParticipantsTab
                mission={mission}
                isCurrentUserHost={isCurrentUserHost}
                hasApplied={hasApplied}
                onShowParticipantsModal={() => setShowParticipantsModal(true)}
                onShowApplicationModal={() => setShowApplicationModal(true)}
                user={localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null}
              />
            )}
            {/* Manage Applicants Tab (Host Only) */}
            {activeTab === 'manage' && showManageTab && (
              <div className="flex flex-col gap-6">
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-pink-100 p-6 flex flex-col items-center">
                  <h2 className="text-xl font-bold text-pink-700 mb-4">Applicants Management</h2>
                  <div className="flex flex-col md:flex-row gap-4 w-full justify-center">
                    <button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 hover:scale-105 active:scale-95"
                      onClick={() => {/* TODO: Implement review applicants logic */}}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Review Applicants
                    </button>
                    <button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
                      onClick={() => {/* TODO: Implement dispute applicants logic */}}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Dispute Applicants
                    </button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Action Buttons */}
          <div className="p-4 md:p-6 bg-gray-50 border-t border-gray-100 mt-4 md:mt-6 rounded-b-2xl">
            {isCurrentUserHost ? (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Host Actions</h3>
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => navigate(`/missions/${missionId}/applicants`)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    View Applicants
                  </motion.button>
                  {mission.status === 'open' && mission.slots_filled > 0 && (
                    <motion.button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleStartMission}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Start Mission
                    </motion.button>
                  )}
                  {mission.status === 'in_progress' && (
                    <motion.button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={() => navigate(`/missions/${missionId}/execute`)}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Join Mission
                    </motion.button>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={handleEditMission}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit Mission
                  </motion.button>
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => setShowDeleteModal(true)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Mission
                  </motion.button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                {mission.status === 'in_progress' && (
                  <motion.button
                    className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex-1 flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => navigate(`/missions/${mission.id}/execute`)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Join Mission
                  </motion.button>
                )}
                <motion.button
                  className="px-6 py-3 rounded-full font-semibold bg-white/90 border border-indigo-200 text-indigo-700 shadow-lg flex-1 flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:bg-indigo-50 hover:text-indigo-800 hover:scale-105 active:scale-95"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.97 }}
                  onClick={() => navigate('/chat')}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Chat with Host
                </motion.button>
                {mission.status === 'open' && !hideApplyButton && (
                  <>
                    {!hasApplied && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => setShowApplicationModal(true)}
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Apply
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'pending' && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95 bg-red-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={handleWithdrawApplication}
                        disabled={isWithdrawing}
                      >
                        {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'approved' && canWithdraw && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95 bg-red-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={handleWithdrawApplication}
                        disabled={isWithdrawing}
                      >
                        {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'approved' && !canWithdraw && (
                      <span className="flex-1 text-center font-semibold text-green-600">Application Approved</span>
                    )}
                    {hasApplied && applyStatus === 'rejected' && (
                      <span className="flex-1 text-center font-semibold text-red-600">Application Rejected</span>
                    )}
                    {hasApplied && applyStatus === 'withdrawn' && (
                      <span className="flex-1 text-center text-gray-600">You have withdrawn from this Mission</span>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </motion.div>



        {/* Similar Missions Section */}
        <motion.div
          className="mb-8 md:mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 mb-4 md:mb-6 px-1">
            <h2 className="text-2xl font-bold text-gray-800">Similar Missions</h2>
            <button
              className="text-indigo-600 hover:text-indigo-800 bg-transparent hover:bg-gray-200 hover:rounded-xl font-medium flex items-center"
              onClick={() => navigate('/missions')}
            >
              View All
              <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            {similarMissions.map(mission => (
              <MissionCard key={mission.id} mission={mission} hideApplyButton={hideApplyButton} />
            ))}
          </div>
        </motion.div>
      </div>

      {/* Application Modal */}
      <ApplicationModal
        isOpen={showApplicationModal}
        onClose={() => setShowApplicationModal(false)}
        onSubmit={submitApplication}
        mission={mission}
      />

      {/* Participants Modal */}
      <ParticipantsModal
        isOpen={showParticipantsModal}
        onClose={() => setShowParticipantsModal(false)}
        mission={mission}
      />

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 500 }}
            >
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2 text-center">Delete Mission?</h3>
              <p className="text-gray-600 mb-6 text-center">
                This action cannot be undone. All mission data, including applications, will be permanently deleted.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  className="px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors flex-1"
                  onClick={() => setShowDeleteModal(false)}
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  className="px-6 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors flex-1 flex items-center justify-center"
                  onClick={handleDeleteMission}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    'Delete Mission'
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </MissionLayout>
  );
};

export default MissionDetailPage;
