import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import MissionCard from '../components/mission/MissionCard';
import ApplicationModal from '../components/mission/ApplicationModal';
import ParticipantsModal from '../components/mission/ParticipantsModal';
import EnhancedParticipantsTab from '../components/mission/EnhancedParticipantsTab';
import { missionApi } from '../services/missionApi';
import { transformApiMissionToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';

const MissionDetailPage = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const [mission, setMission] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasApplied, setHasApplied] = useState(false);
  const [applyStatus, setApplyStatus] = useState('');
  const [canWithdraw, setCanWithdraw] = useState(false);
  const [currentChildId, setCurrentChildId] = useState(null);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [bookmarkLoading, setBookmarkLoading] = useState(false);
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [similarMissions, setSimilarMissions] = useState([]);
  const [activeTab, setActiveTab] = useState('details');
  const [isCurrentUserHost, setIsCurrentUserHost] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [missionTimeStatus, setMissionTimeStatus] = useState('upcoming'); // 'upcoming', 'ongoing', 'completed'

  // Image carousel state for mission images
  const [currentImage, setCurrentImage] = useState(0);
  const images = mission && mission.images && mission.images.length > 0 ? mission.images : [mission && mission.image ? mission.image : '/images/mission-default.jpg'];
  const hasMultipleImages = images.length > 1;
  const handlePrev = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };
  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  // Check for delete intent from navigation state
  useEffect(() => {
    if (location.state?.showDeleteModal) {
      setShowDeleteModal(true);
    }
  }, [location.state]);

  // Fetch mission details from API
  useEffect(() => {
    const fetchMissionDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch mission details
        const response = await missionApi.getMissionById(missionId);

        // Transform API mission to frontend format
        const transformedMission = transformApiMissionToFrontend(response.data);

        if (!transformedMission) {
          setError('Mission not found');
          setIsLoading(false);
          return;
        }

        setMission(transformedMission);
        setIsBookmarked(transformedMission.is_bookmarked ?? false);

        // Application details
        setHasApplied(response.data.is_applied ?? false);
        setApplyStatus(response.data.apply_status || '');
        setCanWithdraw(response.data.can_withdraw ?? false);
        setCurrentChildId(response.data.current_user_child_id ?? null);

        // Check if current user is the host
        // Determine logged in user id from stored user object or fallback key
        let currentUserId = null;
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            currentUserId = JSON.parse(storedUser).id;
          } catch (err) {
            currentUserId = null;
          }
        }
        if (!currentUserId) {
          currentUserId = localStorage.getItem('user_id');
        }

        const hostId = response.data.user?.id || response.data.user_id;
        setIsCurrentUserHost(
          currentUserId && String(currentUserId) === String(hostId)
        );

        // Fetch similar missions
        try {
          // Get missions with the same theme or style
          const similarResponse = await missionApi.getMissions({
            type_id: response.data.service_type_id,
            style_id: response.data.service_style_id
          });

          // Transform API missions to frontend format
          const transformedSimilarMissions = similarResponse.data.data
            .filter(m => m.id !== parseInt(missionId)) // Filter out current mission
            .slice(0, 3) // Limit to 3 similar missions
            .map(transformApiMissionToFrontend);

          setSimilarMissions(transformedSimilarMissions);
        } catch (error) {
          console.error('Error fetching similar missions:', error);
          // Don't set error state for similar missions, as it's not critical
          setSimilarMissions([]);
        }
      } catch (error) {
        console.error('Error fetching mission details:', error);
        setError(getApiErrorMessage(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissionDetails();
  }, [missionId]);



  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Format time duration
  const formatDuration = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationMs = end - start;
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours === 0) {
      return `${minutes} minutes`;
    } else if (minutes === 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  };

  // Calculate time remaining and mission time status
  useEffect(() => {
    if (!mission || !mission.date) return;

    const updateTimeRemaining = () => {
      const now = new Date();
      const missionStartDate = new Date(mission.date);
      const missionEndDate = new Date(mission.end_date);

      // Calculate time difference
      const timeToStart = missionStartDate - now;
      const timeToEnd = missionEndDate - now;

      // Determine mission time status
      if (timeToStart > 0) {
        // Mission hasn't started yet
        setMissionTimeStatus('upcoming');

        // Calculate days, hours, minutes
        const days = Math.floor(timeToStart / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeToStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeToStart % (1000 * 60 * 60)) / (1000 * 60));

        if (days > 0) {
          setTimeRemaining(`Starts in ${days}d ${hours}h`);
        } else if (hours > 0) {
          setTimeRemaining(`Starts in ${hours}h ${minutes}m`);
        } else if (minutes > 0) {
          setTimeRemaining(`Starts in ${minutes}m`);
        } else {
          setTimeRemaining('Starting now!');
        }
      } else if (timeToEnd > 0) {
        // Mission is in progress
        setMissionTimeStatus('ongoing');

        // Calculate hours and minutes remaining
        const hours = Math.floor(timeToEnd / (1000 * 60 * 60));
        const minutes = Math.floor((timeToEnd % (1000 * 60 * 60)) / (1000 * 60));

        if (hours > 0) {
          setTimeRemaining(`${hours}h ${minutes}m remaining`);
        } else if (minutes > 0) {
          setTimeRemaining(`${minutes}m remaining`);
        } else {
          setTimeRemaining('Ending soon');
        }
      } else {
        // Mission has ended
        setMissionTimeStatus('completed');
        setTimeRemaining('Mission completed');
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Update every minute
    const interval = setInterval(updateTimeRemaining, 60000);

    return () => clearInterval(interval);
  }, [mission]);

  // Toggle bookmark
  const toggleBookmark = async () => {
    if (bookmarkLoading) return;
    setBookmarkLoading(true);
    try {
      await missionApi.bookmarkMission(missionId);
      setIsBookmarked((prev) => !prev);
      toast.success(!isBookmarked ? 'Mission added to bookmarks' : 'Mission removed from bookmarks');
    } catch (error) {
      toast.error('Failed to update bookmark');
    } finally {
      setBookmarkLoading(false);
    }
  };

  // Submit application
  const submitApplication = async (applicationData) => {
    console.log('MissionDetailPage submitApplication called', applicationData);
    try {
      setShowApplicationModal(false);
      // Show info toast instead of loading (since loading is not supported)
      toast.info('Submitting your application...');
      // Send application to API
      const response = await missionApi.applyForMission(missionId, {
        notes: applicationData.message || ''
      });
      console.log('API response from applyForMission', response);
      setHasApplied(true);
      toast.success(response.data?.message || 'Application submitted successfully!');
    } catch (error) {
      console.error('Error applying for mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to submit application');
    }
  };

  // Withdraw application
  const handleWithdrawApplication = async () => {
    if (!currentChildId || isWithdrawing) return;
    try {
      setIsWithdrawing(true);
      await missionApi.withdrawApplication(missionId, currentChildId);
      setApplyStatus('withdrawn');
      setCanWithdraw(false);
      toast.success('Application withdrawn');
    } catch (error) {
      console.error('Error withdrawing application:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to withdraw application');
    } finally {
      setIsWithdrawing(false);
    }
  };

  // View participants
  const viewParticipants = () => {
    setShowParticipantsModal(true);
  };

  // Handle edit mission
  const handleEditMission = () => {
    navigate(`/missions/${missionId}/edit`);
  };

  // Handle delete mission
  const handleDeleteMission = async () => {
    setIsDeleting(true);

    try {
      await missionApi.deleteMission(missionId);

      // Show success toast
      toast.success('Mission deleted successfully');

      // Navigate back to missions page
      navigate('/missions/my-missions');
    } catch (error) {
      console.error('Error deleting mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to delete mission');
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  };

  // Handle start mission
  const handleStartMission = async () => {
    try {
      // Show loading toast
      toast.loading('Starting mission...');

      // Call API to start mission
      await missionApi.startMission(missionId);

      // Show success toast
      toast.success('Mission started successfully');

      // Navigate to mission execution page
      navigate(`/missions/${missionId}/execute`);
    } catch (error) {
      console.error('Error starting mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to start mission');
    }
  };

  // Read hideApplyButton from navigation state (for MyMissionsPage -> details)
  const hideApplyButton = location.state?.hideApplyButton || false;

  // Show Manage Applicants tab only for hosts and for relevant statuses
  const showManageTab =
    isCurrentUserHost &&
    ['open', 'completed', 'closed', 'cancelled'].includes(
      (mission?.status || '').toLowerCase()
    );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error || !mission) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex justify-center items-center">
        <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            {error ? 'Error Loading Mission' : 'Mission Not Found'}
          </h3>
          <p className="text-gray-500 mb-4">
            {error || "The mission you're looking for doesn't exist or has been removed."}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-3">
            <button
              onClick={() => navigate('/missions')}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
            >
              Back to Missions
            </button>
            {error && (
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <MissionLayout title="Mission Details" backPath="/missions">
      {/* Mission Detail Content */}
      <div className="flex flex-col gap-6 md:gap-10 px-2 md:px-0">
        {/* Mission Image Banner */}
        <motion.div
          className="relative h-64 md:h-80 rounded-3xl overflow-hidden mb-6 md:mb-8 shadow-xl"
          initial={{ opacity: 0, y: 20, scale: 1 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          whileHover={{
            y: -6,
            scale: 1.01,
            boxShadow: '0 12px 32px 0 rgba(60, 80, 180, 0.18)',
            transition: { duration: 0.25, ease: 'easeOut' }
          }}
          transition={{ duration: 0.5 }}
        >
          {/* Mission image carousel with glassmorphism and gradient overlays */}
          <>
            <motion.img
              key={images[currentImage]}
              src={images[currentImage]}
            alt={mission.title}
              className="w-full h-full object-cover rounded-3xl"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            />
            {/* Carousel Arrows */}
            {hasMultipleImages && (
              <>
                <button
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-indigo-700 rounded-full p-2 shadow-md z-10"
                  onClick={handlePrev}
                  aria-label="Previous image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" /></svg>
                </button>
                <button
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-indigo-700 rounded-full p-2 shadow-md z-10"
                  onClick={handleNext}
                  aria-label="Next image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" /></svg>
                </button>
              </>
            )}
            {/* Dot indicators */}
            {hasMultipleImages && (
              <div className="absolute bottom-5 left-1/2 -translate-x-1/2 flex gap-2 z-10">
                {images.map((_, idx) => (
                  <span
                    key={idx}
                    className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentImage ? 'bg-white shadow-lg scale-110' : 'bg-white/40'}`}
                  />
                ))}
              </div>
            )}
            {/* Image overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent rounded-3xl"></div>
          </>
          {/* Status badge (modern pill) */}
          <motion.div
            className={`absolute top-5 left-5 px-3 py-1 rounded-full text-xs font-semibold flex items-center shadow-md backdrop-blur-sm border transition-all duration-200 cursor-default
              ${mission.status === 'open' ? 'bg-green-100 text-green-800 border-green-200/50' :
                mission.status === 'in_progress' || mission.status === 'in progress' ? 'bg-blue-100 text-blue-800 border-blue-200/50' :
                  mission.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200/50' :
                    'bg-gray-100 text-gray-800 border-gray-200/50'}
            `}
            whileHover={{ scale: 1.06 }}
            whileTap={{ scale: 0.97 }}
          >
            <span className={`w-2 h-2 rounded-full mr-2 transition-all duration-200
              ${mission.status === 'open' ? 'bg-green-500 animate-pulse' :
                mission.status === 'in_progress' || mission.status === 'in progress' ? 'bg-blue-500 animate-pulse' :
                  mission.status === 'completed' ? 'bg-purple-500' :
                    'bg-gray-500'}
            `} />
            {mission.status === 'open' ? 'Recruiting' :
             mission.status === 'in_progress' || mission.status === 'in progress' ? 'In Progress' :
             mission.status === 'completed' ? 'Completed' :
             mission.status}
          </motion.div>

          {/* Tags overlay (modern pill badges) */}
          <div className="absolute bottom-5 left-5 right-5 flex flex-wrap gap-2">
            {mission.level_requirement && (
              <motion.span
                className="bg-indigo-900/80 text-indigo-100 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-indigo-700/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-indigo-800/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
                LV{mission.level_requirement.min || 1}-{mission.level_requirement.max || 99}
              </motion.span>
            )}
            {mission.style && (
              <motion.span
                className="bg-purple-900/80 text-purple-100 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-purple-700/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-purple-800/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                {mission.style}
              </motion.span>
            )}
            {mission.category && (
              <motion.span
                className="bg-blue-900/80 text-blue-100 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-blue-700/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-blue-800/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
                </svg>
                {mission.category}
              </motion.span>
            )}
            {mission.platform && (
              <motion.span
                className="bg-green-900/80 text-green-100 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-green-700/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-green-800/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H6zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
                </svg>
                {mission.platform}
              </motion.span>
            )}
            {mission.language && (
              <motion.span
                className="bg-yellow-900/80 text-yellow-100 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-yellow-700/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-yellow-800/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <circle cx="10" cy="10" r="8" />
                </svg>
                {mission.language}
              </motion.span>
            )}
          </div>

          {/* Bookmark button */}
          <button
            className={`absolute top-4 right-4 w-10 h-10 ${isBookmarked ? 'bg-indigo-600' : 'bg-white/80 backdrop-blur-sm'} rounded-full flex items-center justify-center ${isBookmarked ? 'text-white' : 'text-gray-600 hover:text-indigo-600'} transition-all duration-300 shadow-md`}
            onClick={async (e) => {
              e.preventDefault();
              await toggleBookmark();
            }}
            aria-label={isBookmarked ? "Remove from bookmarks" : "Add to bookmarks"}
            disabled={bookmarkLoading}
          >
            <svg className="w-5 h-5" fill={isBookmarked ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
          </button>
        </motion.div>

        {/* Info Grid Section (below banner) */}
        <div className="mb-2 md:mb-2 px-0 md:px-0">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-4">
            {/* Credits Card */}
            <div className="flex items-center justify-center bg-white/80 border border-indigo-100 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
              <svg className="w-6 h-6 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              <span className="font-semibold text-indigo-700 text-lg">{mission.bounty} <span className="font-normal text-base">credits</span></span>
              </div>
            {/* Countdown Card - visually distinct, gradient-backed */}
            {timeRemaining && (
              <div className="flex items-center justify-center text-white font-semibold bg-gradient-to-r from-indigo-500/90 to-blue-500/90 border border-indigo-100 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
                <svg className="w-5 h-5 inline-block mr-2 text-white align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                {timeRemaining} 
                </div>
            )}
            {/* Slots Card */}
            <div className="flex items-center justify-center bg-white/80 border border-blue-100 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
              <svg className="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
              <span className="text-blue-700 text-base font-semibold">{mission.slots_filled}/{mission.slots_total} slots</span>
                  </div>
                </div>

              </div>

        {/* Mission Details */}
        <motion.div
          className="bg-white rounded-2xl shadow-md overflow-hidden mb-6 md:mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          {/* Header Section */}
          <div className="p-6 border-b border-gray-100">
            <div className="mb-4">
              <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent drop-shadow-lg relative inline-block">
                {mission.title}
                <span className="block h-1 w-16 mt-2 bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 rounded-full animate-pulse"></span>
              </h1>
            </div>
          </div>

          {/* Tabs Navigation */}
          <div className="bg-white/60 backdrop-blur-md rounded-2xl p-2 flex gap-2 mb-4 border border-gray-100 shadow-sm overflow-x-auto">
              <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60
                ${activeTab === 'details'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
                onClick={() => setActiveTab('details')}
              >
                Details
              </button>
              <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60
                ${activeTab === 'host'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
                onClick={() => setActiveTab('host')}
              >
                Host
              </button>
              <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center
                ${activeTab === 'participants'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
                onClick={() => setActiveTab('participants')}
              >
                Participants
                <span className="ml-2 bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full text-xs">
                  {mission.participants?.length || 0}
                </span>
              </button>
              {showManageTab && (
                <button
                  className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center
                    ${activeTab === 'manage'
                      ? 'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white shadow-md scale-105'
                      : 'bg-white/40 text-gray-600 hover:bg-pink-50 hover:text-pink-700'}
                  `}
                  onClick={() => setActiveTab('manage')}
                >
                  Manage Applicants
                </button>
              )}
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            className="p-4 md:p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
          >
            {/* Details Tab */}
            {activeTab === 'details' && (
              <div className="flex flex-col gap-6">
                {/* Description Card */}
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800">Description</h2>
                    {mission.description.length > 150 && (
                      <button
                        className="ml-auto text-sm bg-transparent hover:bg-gray-200 hover:rounded-xl text-indigo-600 hover:text-indigo-800 flex items-justify"
                        onClick={() => setShowFullDescription(!showFullDescription)}
                      >
                        {showFullDescription ? (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
                            </svg>
                            Show less
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                            Show more
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  <div className="text-gray-700 text-base whitespace-pre-line">
                      {showFullDescription ? (
                        mission.description
                      ) : (
                        <>
                          {mission.description.length > 150
                            ? `${mission.description.substring(0, 150)}...`
                            : mission.description}
                        </>
                      )}
                  </div>
                </div>

                {/* Requirements Card */}
                {mission.requirements && mission.requirements.length > 0 && (
                  <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                      </svg>
                      <h2 className="text-lg font-semibold text-gray-800">Requirements</h2>
                    </div>
                      <ul className="space-y-3">
                        {mission.requirements.map((requirement, index) => {
                          // Determine icon based on requirement text
                          let icon;
                          const lowerReq = requirement.toLowerCase();

                          if (lowerReq.includes('microphone') || lowerReq.includes('mic')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('discord') || lowerReq.includes('chat') || lowerReq.includes('communication')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('rank') || lowerReq.includes('level') || lowerReq.includes('skill')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                              </svg>
                            );
                          } else if (lowerReq.includes('time') || lowerReq.includes('available') || lowerReq.includes('duration')) {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            );
                          } else {
                            icon = (
                              <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            );
                          }

                          return (
                          <li key={index} className="flex items-start bg-white/90 p-3 rounded-lg shadow-sm border border-gray-100">
                              {icon}
                              <span className="text-gray-700">{requirement}</span>
                            </li>
                          );
                        })}
                      </ul>
                  </div>
                )}

                {/* Date and Time Card */}
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800">Date & Time</h2>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-stretch gap-6">
                    {/* Start Time */}
                    <div className="flex-1 flex flex-col items-center bg-white p-5 rounded-xl shadow-sm border border-gray-100 mb-2 md:mb-0">
                      <div className="flex items-center mb-2">
                        <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div>
                          <div className="text-xs text-left text-gray-500">Start Time</div>
                          <div className="font-medium text-gray-800">{formatDate(mission.date)}</div>
                          </div>
                        </div>
                        {missionTimeStatus === 'upcoming' && (
                        <div className="mt-1 text-sm text-indigo-600 font-medium flex items-center">
                            <svg className="w-4 h-4 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {timeRemaining}
                          </div>
                        )}
                      </div>
                    {/* Timeline Divider */}
                    <div className="hidden md:flex flex-col items-center justify-center">
                      <div className="w-1 h-8 bg-gradient-to-b from-indigo-400 to-blue-400 rounded-full mb-2"></div>
                      <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3" />
                      </svg>
                      <div className="w-1 h-8 bg-gradient-to-t from-indigo-400 to-blue-400 rounded-full mt-2"></div>
                    </div>
                    {/* End Time */}
                    <div className="flex-1 flex flex-col items-center bg-white p-5 rounded-xl shadow-sm border border-gray-100">
                      <div className="flex items-center mb-2">
                        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                          <div className="text-xs text-left text-gray-500">End Time</div>
                          <div className="font-medium text-gray-800">{formatDate(mission.end_date)}</div>
                          </div>
                        </div>
                      <div className="mt-1 text-sm text-blue-600 font-medium flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Duration: {formatDuration(mission.date, mission.end_date)}
                        </div>
                      </div>
                    </div>
                    {/* Mission progress bar for ongoing missions */}
                    {missionTimeStatus === 'ongoing' && (
                    <div className="mt-6">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>Mission in progress</span>
                          <span>{timeRemaining}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: (() => {
                                const now = new Date();
                                const start = new Date(mission.date);
                                const end = new Date(mission.end_date);
                                const total = end - start;
                                const elapsed = now - start;
                                const percentage = Math.min(100, Math.max(0, (elapsed / total) * 100));
                                return `${percentage}%`;
                              })()
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                </div>

                {/* Tags Card */}
                {mission.tags && mission.tags.length > 0 && (
                  <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                      </svg>
                      <h2 className="text-lg font-semibold text-gray-800">Tags</h2>
                    </div>
                      <div className="flex flex-wrap gap-2">
                        {mission.tags.map((tag, index) => {
                          // Determine tag color based on tag content
                          let tagClass;
                          const lowerTag = tag.toLowerCase();

                          if (lowerTag.includes('tournament') || lowerTag.includes('competition')) {
                            tagClass = "bg-red-100 text-red-800 hover:bg-red-200";
                          } else if (lowerTag.includes('team') || lowerTag.includes('group')) {
                            tagClass = "bg-blue-100 text-blue-800 hover:bg-blue-200";
                          } else if (lowerTag.includes('casual') || lowerTag.includes('fun')) {
                            tagClass = "bg-green-100 text-green-800 hover:bg-green-200";
                          } else if (lowerTag.includes('competitive') || lowerTag.includes('ranked')) {
                            tagClass = "bg-purple-100 text-purple-800 hover:bg-purple-200";
                          } else if (lowerTag.includes('beginner') || lowerTag.includes('new')) {
                            tagClass = "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
                          } else {
                            tagClass = "bg-gray-100 text-gray-800 hover:bg-gray-200";
                          }

                          return (
                            <motion.span
                              key={index}
                              className={`${tagClass} text-sm px-3 py-1.5 rounded-full cursor-pointer transition-colors duration-200 flex items-center`}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => navigate(`/missions?tag=${tag}`)}
                            >
                              <span className="mr-1">#</span>
                              {tag}
                            </motion.span>
                          );
                        })}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Host Tab */}
            {activeTab === 'host' && (
              <div>
                <div className="bg-white/80 backdrop-blur-md rounded-2xl p-6 mb-6 shadow-lg border border-indigo-100">
                  <div className="flex flex-col md:flex-row md:items-start md:space-x-8">
                    {/* Host Avatar with Gradient Border and Online Indicator */}
                    <div className="relative flex-shrink-0 mb-4 md:mb-0">
                      <div className="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-400 via-blue-400 to-purple-400 p-1 shadow-xl">
                        <div className="w-full h-full rounded-full overflow-hidden bg-white">
                        <img
                          src={mission.host?.avatar || '/images/default-avatar.jpg'}
                          alt={mission.host?.name || 'Host'}
                            className="w-full h-full object-cover rounded-full"
                        />
                        </div>
                      </div>
                      {/* Online status indicator */}
                      {mission.host?.online && (
                        <span className="absolute bottom-2 right-2 w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-4 border-white flex items-center justify-center">
                          <span className="w-2.5 h-2.5 bg-green-500 rounded-full block animate-pulse"></span>
                        </span>
                      )}
                    </div>
                    {/* Host Info Card */}
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-2 mb-2">
                        <h2 className="text-2xl font-bold text-gray-800">
                          {mission.host?.name || 'Anonymous Host'}
                        </h2>
                        <span className="bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center shadow-sm">
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            LV{mission.host?.level || '??'}
                          </span>
                          {mission.host?.verified && (
                          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full flex items-center border border-blue-200">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Verified
                            </span>
                          )}
                        </div>
                      {/* Host bio/description */}
                      <p className="text-gray-600 text-sm mb-4">
                        {mission.host?.bio || `Professional mission host with experience in ${mission.theme || 'gaming'} missions.`}
                      </p>
                      {/* Host Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                        {/* Rating */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="flex items-center text-yellow-500 mb-1">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span className="ml-1 font-medium text-gray-800">{mission.host?.rating || '4.8'}</span>
                          </div>
                          <p className="text-xs text-gray-500">Rating</p>
                        </div>
                        {/* Reviews */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                            </svg>
                            {mission.host?.reviews || '24'}
                          </div>
                          <p className="text-xs text-gray-500">Reviews</p>
                        </div>
                        {/* Completed Missions */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                            </svg>
                            {mission.host?.completed_missions || '36'}
                          </div>
                          <p className="text-xs text-gray-500">Completed</p>
                        </div>
                        {/* Hosted Missions */}
                        <div className="bg-white/90 p-3 rounded-xl shadow border border-gray-100 flex flex-col items-center">
                          <div className="text-gray-800 font-medium mb-1 flex items-center">
                            <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {mission.host?.hosted_missions || '12'}
                          </div>
                          <p className="text-xs text-gray-500">Hosted</p>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-3">
                        <motion.button
                          className="px-4 py-2 rounded-full font-semibold bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.97 }}
                          onClick={() => navigate(`/talents/${mission.host?.id}`)}
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          View Profile
                        </motion.button>
                        <motion.button
                          className="px-4 py-2 rounded-full font-semibold bg-white/90 border border-indigo-200 text-indigo-700 shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:bg-indigo-50 hover:text-indigo-800 hover:scale-105 active:scale-95"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.97 }}
                          onClick={() => navigate('/chat')}
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          Message
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Participants Tab */}
            {activeTab === 'participants' && (
              <EnhancedParticipantsTab
                mission={mission}
                isCurrentUserHost={isCurrentUserHost}
                hasApplied={hasApplied}
                onShowParticipantsModal={() => setShowParticipantsModal(true)}
                onShowApplicationModal={() => setShowApplicationModal(true)}
                user={localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null}
              />
            )}
            {/* Manage Applicants Tab (Host Only) */}
            {activeTab === 'manage' && showManageTab && (
              <div className="flex flex-col gap-6">
                <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow border border-pink-100 p-6 flex flex-col items-center">
                  <h2 className="text-xl font-bold text-pink-700 mb-4">Applicants Management</h2>
                  <div className="flex flex-col md:flex-row gap-4 w-full justify-center">
                    <button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 hover:scale-105 active:scale-95"
                      onClick={() => {/* TODO: Implement review applicants logic */}}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Review Applicants
                    </button>
                    <button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
                      onClick={() => {/* TODO: Implement dispute applicants logic */}}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Dispute Applicants
                    </button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Action Buttons */}
          <div className="p-4 md:p-6 bg-gray-50 border-t border-gray-100 mt-4 md:mt-6 rounded-b-2xl">
            {isCurrentUserHost ? (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Host Actions</h3>
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => navigate(`/missions/${missionId}/applicants`)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    View Applicants
                  </motion.button>
                  {mission.status === 'open' && mission.slots_filled > 0 && (
                    <motion.button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={handleStartMission}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Start Mission
                    </motion.button>
                  )}
                  {mission.status === 'in_progress' && (
                    <motion.button
                      className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.97 }}
                      onClick={() => navigate(`/missions/${missionId}/execute`)}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Join Mission
                    </motion.button>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={handleEditMission}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit Mission
                  </motion.button>
                  <motion.button
                    className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => setShowDeleteModal(true)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Mission
                  </motion.button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                {mission.status === 'in_progress' && (
                  <motion.button
                    className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-green-400 to-green-600 text-white shadow-lg flex-1 flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => navigate(`/missions/${mission.id}/execute`)}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Join Mission
                  </motion.button>
                )}
                <motion.button
                  className="px-6 py-3 rounded-full font-semibold bg-white/90 border border-indigo-200 text-indigo-700 shadow-lg flex-1 flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:bg-indigo-50 hover:text-indigo-800 hover:scale-105 active:scale-95"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.97 }}
                  onClick={() => navigate('/chat')}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Chat with Host
                </motion.button>
                {mission.status === 'open' && !hideApplyButton && (
                  <>
                    {!hasApplied && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => setShowApplicationModal(true)}
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Apply
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'pending' && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95 bg-red-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={handleWithdrawApplication}
                        disabled={isWithdrawing}
                      >
                        {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'approved' && canWithdraw && (
                      <motion.button
                        className="px-6 py-3 rounded-full font-semibold flex-1 flex items-center justify-center text-base shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95 bg-red-500 text-white"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={handleWithdrawApplication}
                        disabled={isWithdrawing}
                      >
                        {isWithdrawing ? 'Withdrawing...' : 'Withdraw Application'}
                      </motion.button>
                    )}
                    {hasApplied && applyStatus === 'approved' && !canWithdraw && (
                      <span className="flex-1 text-center font-semibold text-green-600">Application Approved</span>
                    )}
                    {hasApplied && applyStatus === 'rejected' && (
                      <span className="flex-1 text-center font-semibold text-red-600">Application Rejected</span>
                    )}
                    {hasApplied && applyStatus === 'withdrawn' && (
                      <span className="flex-1 text-center text-gray-600">You have withdrawn from this Mission</span>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </motion.div>



        {/* Similar Missions Section */}
        <motion.div
          className="mb-8 md:mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 mb-4 md:mb-6 px-1">
            <h2 className="text-2xl font-bold text-gray-800">Similar Missions</h2>
            <button
              className="text-indigo-600 hover:text-indigo-800 bg-transparent hover:bg-gray-200 hover:rounded-xl font-medium flex items-center"
              onClick={() => navigate('/missions')}
            >
              View All
              <svg className="w-5 h-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
            {similarMissions.map(mission => (
              <MissionCard key={mission.id} mission={mission} hideApplyButton={hideApplyButton} />
            ))}
          </div>
        </motion.div>
      </div>

      {/* Application Modal */}
      <ApplicationModal
        isOpen={showApplicationModal}
        onClose={() => setShowApplicationModal(false)}
        onSubmit={submitApplication}
        mission={mission}
      />

      {/* Participants Modal */}
      <ParticipantsModal
        isOpen={showParticipantsModal}
        onClose={() => setShowParticipantsModal(false)}
        mission={mission}
      />

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 500 }}
            >
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2 text-center">Delete Mission?</h3>
              <p className="text-gray-600 mb-6 text-center">
                This action cannot be undone. All mission data, including applications, will be permanently deleted.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  className="px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors flex-1"
                  onClick={() => setShowDeleteModal(false)}
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  className="px-6 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors flex-1 flex items-center justify-center"
                  onClick={handleDeleteMission}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    'Delete Mission'
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </MissionLayout>
  );
};

export default MissionDetailPage;
