import React from 'react';
import { useNavigate } from 'react-router-dom';

const BecomeTalent = () => {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 font-sans">
      {/* Hero Section */}
      <section className="w-full bg-gradient-to-r from-purple-700 via-indigo-700 to-purple-800 py-20 px-4 flex flex-col md:flex-row items-center justify-between shadow-2xl rounded-b-3xl mb-12">
        <div className="flex-1 flex flex-col items-start justify-center max-w-2xl ml-20">
          <h1 className="text-4xl md:text-5xl font-extrabold font-anton text-white mb-4 drop-shadow-xl tracking-tight">
            Become a Mission X Talent!
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 font-semibold">
            High income • Flexible • Make Friends
          </p>
          <button
            className="bg-white text-purple-700 font-bold text-lg px-10 py-4 rounded-full shadow-xl hover:bg-purple-100 hover:scale-105 hover:shadow-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-400"
            onClick={() => navigate('/signup')}
          >
            Apply for Free
          </button>
        </div>
        <div className="flex-1 flex items-center justify-center mt-10 md:mt-0 md:ml-8">
          <img src="/MissionXTalent.png" alt="Mission X Mascot" className="w-96 h-96 md:w-[28rem] md:h-[28rem] object-contain drop-shadow-2xl rounded-3xl border-4 border-white/10" />
        </div>
      </section>

      {/* What’s a Talent? Section */}
      <section className="w-full bg-gray-900 py-16 px-4 flex flex-col md:flex-row items-start justify-between rounded-3xl shadow-xl mb-12">
        <div className="flex-1 flex items-center justify-center mb-4 ml-10">
          <img src="/MissionXMascot.png" alt="Mission X Talent Avatar" className="w-96 h-96 md:w-[28rem] md:h-[28rem] object-contain drop-shadow-xl rounded-3xl border-4 border-white/10" />
        </div>
        <div className="flex-1 max-w-2xl">
          <h2 className="text-2xl md:text-3xl font-bold font-anton text-white mb-4 tracking-tight">What’s a Mission X Talent?</h2>
          <p className="text-lg text-gray-200 font-medium">
            Mission X Talents are passionate individuals who provide gaming, entertainment, or social experiences to our vibrant community. As a Talent, you can share your skills, connect with others, and earn income while doing what you love—all on your own schedule, from anywhere.
          </p>
        </div>
      </section>

      {/* Why become a Talent? Section */}
      <section className="w-full bg-gray-950 py-16 px-4 rounded-3xl shadow-xl mb-12">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold font-anton text-white mb-10 text-center tracking-tight">Why become a Mission X Talent?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* High Income */}
            <div className="flex flex-col items-center bg-gray-900 rounded-2xl p-10 shadow-lg hover:shadow-2xl transition-all duration-200">
              <div className="text-5xl mb-4">💰</div>
              <h3 className="text-xl font-bold text-yellow-400 mb-2 font-anton">High Income</h3>
              <p className="text-gray-200 text-center">Earn more while gaming, entertaining, or socializing with the Mission X community.</p>
            </div>
            {/* Flexible */}
            <div className="flex flex-col items-center bg-gray-900 rounded-2xl p-10 shadow-lg hover:shadow-2xl transition-all duration-200">
              <div className="text-5xl mb-4">🕒</div>
              <h3 className="text-xl font-bold text-cyan-400 mb-2 font-anton">Flexible</h3>
              <p className="text-gray-200 text-center">Set your own schedule and work from anywhere, anytime—Mission X fits your lifestyle.</p>
            </div>
            {/* Make Friends */}
            <div className="flex flex-col items-center bg-gray-900 rounded-2xl p-10 shadow-lg hover:shadow-2xl transition-all duration-200">
              <div className="text-5xl mb-4">🤝</div>
              <h3 className="text-xl font-bold text-pink-400 mb-2 font-anton">Make Friends</h3>
              <p className="text-gray-200 text-center">Meet and connect with gamers and creators from all over the world, and build lasting friendships.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Who can be a Mission X Talent? Section */}
      <section className="w-full bg-gray-900 py-16 px-4 flex flex-col md:flex-row items-center justify-center rounded-3xl shadow-xl mb-12">
        <div className="flex-1 max-w-2xl mb-10 md:mb-0 md:mr-10 ml-20">
          <h2 className="text-2xl md:text-3xl font-bold font-anton text-white mb-4 tracking-tight">Who can be a Mission X Talent?</h2>
          <p className="text-lg text-indigo-200 font-semibold mb-6">We welcome passionate, positive, and community-minded individuals. To join as a Talent, you should meet the following requirements:</p>
          <ul className="space-y-4 mb-8">
            <li className="flex items-start text-lg md:text-xl text-gray-100 font-medium">
              <span className="inline-block w-7 h-7 mr-3 flex-shrink-0 mt-0.5 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xl shadow-md">✓</span>
              18 years old or above
            </li>
            <li className="flex items-start text-lg md:text-xl text-gray-100 font-medium">
              <span className="inline-block w-7 h-7 mr-3 flex-shrink-0 mt-0.5 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xl shadow-md">✓</span>
              Able to provide a service in at least 1 category on Mission X
            </li>
            <li className="flex items-start text-lg md:text-xl text-gray-100 font-medium">
              <span className="inline-block w-7 h-7 mr-3 flex-shrink-0 mt-0.5 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xl shadow-md">✓</span>
              Friendly, inclusive, and passionate about gaming, entertainment, or social experiences
            </li>
            <li className="flex items-start text-lg md:text-xl text-gray-100 font-medium">
              <span className="inline-block w-7 h-7 mr-3 flex-shrink-0 mt-0.5 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xl shadow-md">✓</span>
              Willing to engage and build a positive community
            </li>
          </ul>
          <a
            href="https://discord.gg/missionx" target="_blank" rel="noopener noreferrer"
            className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-bold px-8 py-4 rounded-full shadow-xl hover:scale-105 transition-all duration-200 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 mt-4"
          >
            Join our Official Discord
          </a>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <img src="/MissionXTalent.png" alt="Mission X Talent Mascot" className="w-80 h-80 md:w-[24rem] md:h-[24rem] object-contain drop-shadow-2xl rounded-3xl border-4 border-white/10" />
        </div>
      </section>

      {/* How to be a Mission X Talent? Section */}
      <section className="w-full bg-gray-950 py-16 px-4 flex flex-col items-center justify-center rounded-3xl shadow-xl mb-12">
        <div className="max-w-5xl w-full mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold font-anton text-white mb-10 text-center tracking-tight">How to be a Mission X Talent?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            {/* Step 1: Set up a Service */}
            <div className="bg-gray-900 rounded-2xl shadow-lg p-8 flex flex-col items-center">
              <div className="flex items-center mb-4">
                <span className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-indigo-500 text-white font-bold text-2xl flex items-center justify-center mr-3 shadow">1</span>
                <span className="text-xl font-bold text-white">Set up your Service</span>
              </div>
              <p className="text-gray-200 text-center mb-6">Go to your profile, open <span className='font-semibold text-indigo-300'>Edit Profile</span>, and add a service using the Service Form. Once your service is <span className='text-green-400 font-semibold'>approved</span> by our team, you can proceed to the next step.</p>
              {/* Mock Service Form UI */}
              <div className="w-full bg-white/90 dark:bg-gray-800 rounded-xl border border-indigo-100 dark:border-gray-700 shadow p-4 mb-4">
                <div className="flex items-center mb-2">
                  <span className="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-500 to-blue-500 text-white font-bold flex items-center justify-center mr-2">💼</span>
                  <span className="font-semibold text-indigo-700 dark:text-indigo-200">Service Form (Mock)</span>
                </div>
                <div className="grid grid-cols-1 gap-2 mb-2">
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-700 dark:text-gray-200 mb-1">Service Category</label>
                    <select className="rounded-lg border border-indigo-200 dark:border-gray-700 px-2 py-1 bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100">
                      <option>Game Coaching</option>
                      <option>Entertainment</option>
                    </select>
                  </div>
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-700 dark:text-gray-200 mb-1">Service Title</label>
                    <input className="rounded-lg border border-indigo-200 dark:border-gray-700 px-2 py-1 bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100" placeholder="e.g. Valorant Coaching" />
                  </div>
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-700 dark:text-gray-200 mb-1">Price (Credits)</label>
                    <input className="rounded-lg border border-indigo-200 dark:border-gray-700 px-2 py-1 bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100" placeholder="e.g. 100" />
                  </div>
                </div>
                <button className="mt-2 w-full bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-bold py-2 rounded-lg shadow hover:from-indigo-600 hover:to-blue-600 transition">Submit for Review</button>
              </div>
              {/* Mock Service List UI */}
              <div className="w-full bg-white/90 dark:bg-gray-800 rounded-xl border border-indigo-100 dark:border-gray-700 shadow p-4 flex items-center justify-between">
                <div>
                  <div className="font-semibold text-indigo-700 dark:text-indigo-200">Valorant Coaching</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">Status: <span className="font-bold text-green-500">Approved</span></div>
                </div>
                <span className="px-3 py-1 rounded-full bg-green-100 text-green-700 text-xs font-bold">Approved</span>
              </div>
            </div>
            {/* Step 2: Set up Availability */}
            <div className="bg-gray-900 rounded-2xl shadow-lg p-8 flex flex-col items-center">
              <div className="flex items-center mb-4">
                <span className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-indigo-500 text-white font-bold text-2xl flex items-center justify-center mr-3 shadow">2</span>
                <span className="text-xl font-bold text-white">Set up your Availability</span>
              </div>
              <p className="text-gray-200 text-center mb-6">After your service is approved, go to your profile and open the <span className='font-semibold text-indigo-300'>My Availability</span> card. Set your available time slots so clients can book you easily.</p>
              {/* Mock Availability Calendar UI */}
              <div className="w-full bg-white/90 dark:bg-gray-800 rounded-xl border border-indigo-100 dark:border-gray-700 shadow p-4">
                <div className="flex items-center mb-2">
                  <span className="w-6 h-6 rounded-full bg-gradient-to-br from-indigo-500 to-blue-500 text-white font-bold flex items-center justify-center mr-2">📅</span>
                  <span className="font-semibold text-indigo-700 dark:text-indigo-200">Availability Calendar (Mock)</span>
                </div>
                <div className="grid grid-cols-7 gap-1 text-xs text-center mb-2">
                  <div>Mon</div><div>Tue</div><div>Wed</div><div>Thu</div><div>Fri</div><div>Sat</div><div>Sun</div>
                </div>
                <div className="grid grid-cols-7 gap-1 text-center">
                  <div className="bg-green-200 text-green-800 rounded py-1">9am-12pm</div>
                  <div className="bg-green-200 text-green-800 rounded py-1">9am-12pm</div>
                  <div className="bg-yellow-100 text-yellow-800 rounded py-1">2pm-5pm</div>
                  <div className="bg-green-200 text-green-800 rounded py-1">9am-12pm</div>
                  <div className="bg-gray-200 text-gray-500 rounded py-1">-</div>
                  <div className="bg-green-200 text-green-800 rounded py-1">9am-12pm</div>
                  <div className="bg-green-200 text-green-800 rounded py-1">9am-12pm</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer/Closing CTA */}
      <footer className="w-full bg-gray-950 py-14 px-4 flex flex-col items-center justify-center rounded-3xl shadow-xl mt-12">
        <h2 className="text-2xl md:text-3xl font-bold font-anton text-white mb-4 text-center tracking-tight">Ready to join Mission X?</h2>
        <p className="text-lg text-gray-200 mb-8 text-center max-w-2xl">
          Mission X connects passionate talents with a vibrant community, offering you the chance to earn, grow, and make friends while doing what you love. Start your journey as a Mission X Talent today!
        </p>
        <button
          className="bg-white text-purple-700 font-bold text-lg px-10 py-4 rounded-full shadow-xl hover:bg-purple-100 hover:scale-105 hover:shadow-2xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-400"
          onClick={() => navigate('/signup')}
        >
          Apply for Free
        </button>
      </footer>
    </div>
  );
};

export default BecomeTalent; 