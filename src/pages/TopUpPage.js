import React, { useState } from 'react';
import axios from 'axios';
import { <PERSON>a<PERSON>oi<PERSON>, FaArrowRight } from 'react-icons/fa';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';

const TopUpPage = () => {
  const [uid, setUid] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verifyError, setVerifyError] = useState('');
  const [packages, setPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [topUpLoading, setTopUpLoading] = useState(false);
  const [topUpError, setTopUpError] = useState('');

  // Handle UID verification
  const handleVerify = async (e) => {
    e.preventDefault();
    setVerifying(true);
    setVerifyError('');
    setPackages([]);
    setSelectedPackage(null);
    try {
      const res = await axios.post(`${API_URL}/public/credits/verify`, { uid });
      setPackages(res.data.packages || []);
    } catch (err) {
      setVerifyError(err.response?.data?.message || 'Verification failed.');
    } finally {
      setVerifying(false);
    }
  };

  // Handle Top Up
  const handleTopUp = async () => {
    if (!selectedPackage) return;
    setTopUpLoading(true);
    setTopUpError('');
    try {
      const redirect_url = `${window.location.origin}/payment-return`;
      const res = await axios.post(`${API_URL}/public/credits/topup`, {
        uid,
        credit_package_id: selectedPackage.id,
        redirect_url,
      });
      // Redirect to payment
      window.location.href = res.data.payment.redirect_url;
    } catch (err) {
      setTopUpError(err.response?.data?.message || 'Top up failed.');
    } finally {
      setTopUpLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white dark:from-gray-950 dark:to-gray-950 flex flex-col items-center justify-center px-4 py-12">
      <div className="w-full max-w-md bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 border border-indigo-100 dark:border-gray-800">
        <h1 className="text-2xl font-bold text-center text-indigo-700 dark:text-indigo-200 mb-6">Top Up Credits</h1>
        {/* UID Input */}
        <form onSubmit={handleVerify} className="flex flex-col gap-4 mb-6">
          <label htmlFor="uid" className="font-medium text-gray-700 dark:text-gray-200">Enter your UID</label>
          <input
            id="uid"
            type="text"
            value={uid}
            onChange={e => setUid(e.target.value)}
            className="px-4 py-3 rounded-lg border border-indigo-200 dark:border-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
            placeholder="e.g. QACEOG"
            required
            disabled={verifying || packages.length > 0}
            autoFocus
          />
          <button
            type="submit"
            className="w-full py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-semibold rounded-lg shadow-md hover:from-indigo-700 hover:to-blue-700 transition-all text-lg flex items-center justify-center gap-2 disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            disabled={verifying || !uid || packages.length > 0}
          >
            {verifying ? 'Verifying...' : 'Verify UID'}
            <FaArrowRight />
          </button>
          {verifyError && <div className="text-red-500 text-sm text-center mt-2">{verifyError}</div>}
        </form>

        {/* Packages */}
        {packages.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-indigo-700 dark:text-indigo-200 mb-4 text-center">Select a Credit Package</h2>
            <div className="flex flex-col gap-4 mb-4">
              {packages.map(pkg => (
                <button
                  key={pkg.id}
                  type="button"
                  onClick={() => setSelectedPackage(pkg)}
                  className={`flex items-center justify-between w-full px-5 py-4 rounded-xl border-2 transition-all shadow-sm bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-950 hover:from-indigo-100 hover:to-blue-100 dark:hover:from-gray-800 dark:hover:to-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 gap-4 ${selectedPackage?.id === pkg.id ? 'border-indigo-600 dark:border-indigo-400 ring-2 ring-indigo-400' : 'border-indigo-200 dark:border-gray-700'}`}
                  aria-pressed={selectedPackage?.id === pkg.id}
                >
                  <div className="flex items-center gap-3">
                    <FaCoins className="w-7 h-7 text-yellow-500" />
                    <div className="flex flex-col items-start">
                      <span className="font-bold text-indigo-800 dark:text-indigo-100 text-lg">{pkg.credits.toLocaleString()} Credits</span>
                      <span className="text-sm text-gray-500 dark:text-gray-300">{pkg.currency_code} {pkg.price}</span>
                    </div>
                  </div>
                  {selectedPackage?.id === pkg.id && (
                    <span className="ml-2 px-3 py-1 bg-indigo-600 dark:bg-indigo-400 text-white dark:text-gray-900 rounded-full text-xs font-semibold">Selected</span>
                  )}
                </button>
              ))}
            </div>
            <button
              onClick={handleTopUp}
              className="w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 dark:from-yellow-500 dark:to-yellow-600 text-white font-bold rounded-lg shadow-md hover:from-yellow-500 hover:to-yellow-600 dark:hover:from-yellow-600 dark:hover:to-yellow-700 transition-all text-lg flex items-center justify-center gap-2 disabled:opacity-60 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              disabled={!selectedPackage || topUpLoading}
            >
              {topUpLoading ? 'Processing...' : 'Top Up Now'}
              <FaArrowRight />
            </button>
            {topUpError && <div className="text-red-500 text-sm text-center mt-2">{topUpError}</div>}
          </div>
        )}
      </div>
    </div>
  );
};

export default TopUpPage; 