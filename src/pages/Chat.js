import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import useTranslation from "../hooks/useTranslation";
import { useSearchParams, useLocation, useNavigate } from "react-router-dom";
import ChatList from "../components/chat/ChatList";
import ChatWindow from "../components/chat/ChatWindow";
import { chatApi } from "../services/chatApi";
import { cn } from "../lib/utils";
import MainNavigation from "../components/navigation/MainNavigation";
import MobileNavigation from "../components/navigation/MobileNavigation";
import { getTalentBasicProfile } from "../services/talentService";
import { useNetwork } from "../hooks/useNetwork";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { toast } from "react-hot-toast";
import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useAuth } from "../contexts/AuthContext";

const QUEUE_KEY = 'chat_message_queue';
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAYS = [1000, 2000, 4000]; // 1s, 2s, 4s

const Chat = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const navigate = useNavigate();
  const conversationId = searchParams.get("conversationId");
  const talentId = searchParams.get("talent_id");
  const action = searchParams.get("action");
  const { t } = useTranslation(["chat", "common"]);
  const { isOnline } = useNetwork();
  const { user: currentUser } = useAuth();
  const retryTimeoutsRef = useRef({});
  const queryClient = useQueryClient();

  const [selectedChat, setSelectedChat] = useState(null);
  const [favorites, setFavorites] = useState(() => {
    const savedFavorites = localStorage.getItem("chatFavorites");
    return savedFavorites ? JSON.parse(savedFavorites) : [];
  });

  // Persist favorites to localStorage
  useEffect(() => {
    localStorage.setItem("chatFavorites", JSON.stringify(favorites));
  }, [favorites]);
  const [newConversationData, setNewConversationData] = useState(null);
  const [messageQueue, setMessageQueue] = useLocalStorage(QUEUE_KEY, []);
  const [optimisticUpdates, setOptimisticUpdates] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMoreConversations, setHasMoreConversations] = useState(true);
  const [unreadOverrides, setUnreadOverrides] = useLocalStorage('chatUnreadOverrides', {});

  // Fetch conversations using React Query
  const {
    data: conversationPages,
    isLoading: loading,
    error: fetchError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch: refetchConversations,
  } = useInfiniteQuery({
    queryKey: ['conversations', searchTerm],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await chatApi.getConversations(pageParam, 15, { keyword: searchTerm });
      return {
        data: response.data.data,
        nextPage: response.data.next_page_url ? pageParam + 1 : undefined,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !(talentId && action === 'new_conversation'),
    refetchInterval: 5000,
  });

  const chats = useMemo(() => {
    const base = conversationPages ? conversationPages.pages.flatMap(p => p.data) : [];
    return base.map(c => ({ ...c, unread_count: unreadOverrides[c.id] ?? c.unread_count }));
  }, [conversationPages, unreadOverrides]);

  useEffect(() => {
    if (conversationPages) {
      setUnreadOverrides(prev => {
        const updated = { ...prev };
        conversationPages.pages.forEach(page => {
          page.data.forEach(c => {
            if (typeof c.unread_count === 'number') {
              const current = updated[c.id] ?? 0;
              updated[c.id] = Math.max(current, c.unread_count);
            }
          });
        });
        return updated;
      });
    }
  }, [conversationPages]);

  useEffect(() => {
    setHasMoreConversations(hasNextPage ?? false);
  }, [hasNextPage]);

  // Auto-select chat when a conversationId is present in URL
  useEffect(() => {
    if (conversationId && chats.length > 0) {
      const existing = chats.find(c => String(c.id) === String(conversationId));
      if (existing) {
        setSelectedChat(existing);
      }
    }
  }, [conversationId, chats]);


  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({ conversationId, content }) => {
      return await chatApi.sendMessage({
        conversationId,
        text: content,
      });
    },
    onSuccess: () => {
      // Refresh conversations to reflect latest message
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
    onError: (error) => {
      toast.error('Failed to send message');
      console.error('Error sending message:', error);
    }
  });

  // Process message queue when online
  const processMessageQueue = useCallback(async () => {
    if (!isOnline || messageQueue.length === 0) return;

    const processMessage = async (message, retryCount = 0) => {
      try {
        await sendMessageMutation.mutateAsync({
          conversationId: message.conversationId,
          content: message.content
        });
        
        setMessageQueue(prev => prev.filter(m => m.id !== message.id));
        if (retryTimeoutsRef.current[message.id]) {
          clearTimeout(retryTimeoutsRef.current[message.id]);
          delete retryTimeoutsRef.current[message.id];
        }
      } catch (error) {
        console.error('Error processing queued message:', error);
        if (retryCount < MAX_RETRY_ATTEMPTS) {
          retryTimeoutsRef.current[message.id] = setTimeout(() => {
            processMessage(message, retryCount + 1);
          }, RETRY_DELAYS[retryCount]);
        } else {
          setMessageQueue(prev => 
            prev.map(m => 
              m.id === message.id 
                ? { ...m, status: 'failed' }
                : m
            )
          );
          toast.error('Failed to send message after multiple attempts');
        }
      }
    };

    for (const message of messageQueue) {
      if (message.status !== 'failed') {
        await processMessage(message);
      }
    }
  }, [isOnline, messageQueue, sendMessageMutation]);

  // Process message queue when online status changes
  useEffect(() => {
    if (isOnline) {
      processMessageQueue();
    }
  }, [isOnline, processMessageQueue]);

  // Cleanup retry timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(retryTimeoutsRef.current).forEach(timeout => {
        clearTimeout(timeout);
      });
    };
  }, []);

  // Listen for real-time incoming messages to update conversation list
  useEffect(() => {
    const handleRealtimeMessage = (event) => {
      const { conversationId, message } = event.detail || {};
      if (!conversationId) return;

      queryClient.setQueryData(['conversations', searchTerm], (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          pages: oldData.pages.map((page) => ({
            ...page,
            data: page.data.map((c) => {
              if (String(c.id) === String(conversationId)) {
                const isActive = String(selectedChat?.id) === String(conversationId);
                return {
                  ...c,
                  last_message: message,
                  last_message_at: message.created_at || new Date().toISOString(),
                  unread_count: isActive ? 0 : (c.unread_count || 0) + 1,
                };
              }
              return c;
            }),
          })),
        };
      });

      setUnreadOverrides((prev) => {
        const current = prev[conversationId] ?? 0;
        return {
          ...prev,
          [conversationId]: String(selectedChat?.id) === String(conversationId) ? 0 : current + 1,
        };
      });
    };

    window.addEventListener('realtime-chat-message', handleRealtimeMessage);
    return () => window.removeEventListener('realtime-chat-message', handleRealtimeMessage);
  }, [selectedChat?.id, searchTerm, queryClient]);

  const initializeNewConversation = useCallback(async () => {
    if (talentId && action === "new_conversation") {
      try {
        const talentData = await getTalentBasicProfile(talentId);

        if (!talentData) {
          throw new Error("Failed to fetch talent data");
        }

        setNewConversationData({
          talentId,
          talentName:
            talentData.displayName ||
            talentData.nickname ||
            "Unknown Talent",
          talentImage: talentData.profileImage,
          state: "preparing",
        });
        setSelectedChat({
          id: null,
          talent: talentData,
          isNewConversation: true,
        });
      } catch (error) {
        console.error("Error fetching talent data:", error);
        toast.error("Failed to load talent information. Please try again.");
        setNewConversationData(null);
      }
    }
  }, [talentId, action]);

  useEffect(() => {
    if (talentId && action === 'new_conversation') {
      initializeNewConversation();
    }
  }, [talentId, action, initializeNewConversation]);

  const toggleFavorite = (chatId) => {
    if (!chatId) return;

    setFavorites((prev) => {
      const isFavorite = prev.includes(chatId);
      const newFavorites = isFavorite
        ? prev.filter((id) => id !== chatId)
        : [...prev, chatId];

      return newFavorites;
    });
  };

  const handleChatSelect = (chat) => {
    setSelectedChat(chat);

    setUnreadOverrides((prev) => ({ ...prev, [chat.id]: 0 }));

    // Optimistically reset unread count for the opened chat
    queryClient.setQueryData(['conversations', searchTerm], (oldData) => {
      if (!oldData) return oldData;
      return {
        ...oldData,
        pages: oldData.pages.map((page) => ({
          ...page,
          data: page.data.map((c) =>
            c.id === chat.id ? { ...c, unread_count: 0 } : c
          ),
        })),
      };
    });

    // Refresh conversations to ensure backend state is synced
    queryClient.invalidateQueries({ queryKey: ['conversations'] });
  };

  const loadMoreConversations = () => {
    if (hasMoreConversations && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const handleMessageSent = useCallback(() => {
    // ChatWindow already sends the message. Refresh the conversation list so
    // its last message preview updates without creating a duplicate message.
    queryClient.invalidateQueries({ queryKey: ['conversations'] });
  }, [queryClient]);

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-950">
      {/* Shared Navigation */}
      <MainNavigation activeItem="/chat" />

      {/* Offline Indicator */}
      {!isOnline && (
        <div className="fixed top-[72px] left-0 right-0 bg-yellow-500 text-white text-center py-2 z-50">
          {t("chat:offline.message", "You are offline. Messages will be sent when you're back online.")}
        </div>
      )}

      {/* Main Chat Container */}
      <div className="flex flex-1 overflow-hidden">
        {/* Chat List Section */}
        <div
          className={cn(
            "w-96 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800",
            "transition-all duration-300 ease-in-out",
            selectedChat ? "hidden lg:block" : "",
          )}
        >
          <ChatList
            chats={chats}
            selectedChat={selectedChat}
            onChatSelect={handleChatSelect}
            favorites={favorites}
            onToggleFavorite={toggleFavorite}
            loading={loading}
            error={fetchError?.message}
            onLoadMore={loadMoreConversations}
            onSearchChange={(val) => {
              setSearchTerm(val);
            }}
            hasMore={hasMoreConversations}
            isFetchingMore={isFetchingNextPage}
            optimisticUpdates={optimisticUpdates}
            messageQueue={messageQueue}
            currentUserId={currentUser.id}
          />
        </div>

        {/* Chat Window */}
        <div className="flex-1 flex bg-gradient-to-br from-gray-50 via-white to-indigo-50/30 dark:from-gray-900 dark:via-gray-950 dark:to-indigo-950/30">
          {selectedChat ? (
            <ChatWindow
              chat={selectedChat}
              onBack={() => setSelectedChat(null)}
              onMessageSent={handleMessageSent}
              newConversationData={newConversationData}
              onConversationCreated={(conversation) => {
                const mergedConversation = {
                  ...conversation,
                  talent:
                    conversation.talent ||
                    (newConversationData
                      ? {
                          id: Number(newConversationData.talentId),
                          displayName: newConversationData.talentName,
                          profileImage: newConversationData.talentImage,
                        }
                      : undefined),
                };
                setSelectedChat(mergedConversation);
                navigate(`/chat?conversationId=${conversation.id}`, { replace: true });
                setNewConversationData(null);
                queryClient.invalidateQueries(['conversations']);
              }}
              currentUserId={currentUser?.id}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-indigo-50/30 dark:from-gray-900 dark:via-gray-950 dark:to-indigo-950/30">
              <motion.div
                className="text-center max-w-md mx-auto px-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                <motion.div
                  className="w-32 h-32 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-8 shadow-2xl shadow-indigo-500/25 relative overflow-hidden"
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                  <svg
                    className="w-16 h-16 text-white relative z-10"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                </motion.div>

                <motion.h2
                  className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent dark:from-gray-100 dark:via-indigo-200 dark:to-purple-200 mb-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                >
                  {t("chat:welcome.title")}
                </motion.h2>

                <motion.p
                  className="text-gray-600 dark:text-gray-300 mb-8 leading-relaxed"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.6 }}
                >
                  {t("chat:welcome.description")}
                </motion.p>

                <motion.button
                  className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30 transform hover:-translate-y-0.5 font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                  onClick={() => navigate('/talent')}
                >
                  {t("chat:welcome.start")}
                </motion.button>

                <motion.div
                  className="mt-8 flex justify-center space-x-6 text-sm text-gray-500 dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    {t("chat:welcome.realtime")}
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
                    {t("chat:welcome.fileSharing")}
                  </div>
                </motion.div>
              </motion.div>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/chat" />
    </div>
  );
};

export default Chat;
