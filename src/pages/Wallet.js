import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faWallet,
  faPlus,
  faMinus,
  faSpinner,
  faExclamationTriangle,
  faRefresh,
  faEye,
  faEyeSlash,
  faArrowUp,
  faArrowDown,
  faCheckCircle,
  faChevronRight,
  faEnvelope,
  faIdCard,
  faQuestionCircle
} from '@fortawesome/free-solid-svg-icons';
import { WalletProvider, useWallet } from '../features/wallet/contexts/WalletContext';
import { BankAccountProvider } from '../features/banking/contexts';
import useTranslation from '../hooks/useTranslation';
import {
  PaymentHistoryCard,
  BankAccountManager
} from '../features/wallet/components';
import WalletErrorHandler from '../features/wallet/components/common/WalletErrorHandler';
import TopUpModal from '../features/wallet/components/modals/TopUpModal';
import WithdrawModal from '../features/wallet/components/modals/WithdrawModal';
import BankAccountModal from '../features/wallet/components/modals/BankAccountModal';
import StandaloneWithdrawalHistoryCard from '../features/wallet/components/withdrawal/StandaloneWithdrawalHistoryCard';

import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';
import { PageLoader } from '../components/ui/LoadingIndicator';
import Settings from '../components/Settings';

/**
 * Enhanced Balance Overview Card Component
 */
const BalanceOverviewCard = ({ balance, isLoading, onTopUp, onWithdraw, onToggleVisibility, isVisible }) => {
  const { formatCredits, transactions } = useWallet();

  // Ensure balance is a valid number
  const displayBalance = balance?.credits ?? 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative overflow-hidden"
    >
      {/* Animated background decorations */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-3xl" />
      <div className="absolute -top-10 -right-10 w-40 h-40 rounded-full bg-white/10 blur-2xl animate-pulse" />
      <div className="absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-white/10 blur-xl animate-pulse delay-1000" />

      <div className="relative p-8 text-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <FontAwesomeIcon icon={faWallet} className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Wallet Balance</h2>
              <p className="text-blue-100 text-left text-sm">Available funds</p>
            </div>
          </div>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onToggleVisibility}
            className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center hover:bg-white/30 transition-colors"
          >
            <FontAwesomeIcon icon={isVisible ? faEyeSlash : faEye} className="text-white" />
          </motion.button>
        </div>

        {/* Enhanced Balance Display */}
        <div className="mb-8">
          {isLoading ? (
            <div className="space-y-4">
              <div className="animate-pulse">
                <div className="h-16 bg-white/20 rounded-xl w-80 mb-3" />
                <div className="h-4 bg-white/20 rounded-lg w-48 mb-2" />
                <div className="h-3 bg-white/20 rounded w-32" />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Main Balance */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="text-6xl font-bold mb-2 tracking-tight">
                    {isVisible ? formatCredits(displayBalance) : '••••••'}
                  </div>
                  <div className="text-blue-100 text-lg font-medium">
                    Credits Balance
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.button
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={onTopUp}
            className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-left hover:bg-white/30 transition-all border border-white/20 shadow-lg hover:shadow-xl"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-xl bg-green-500/20 flex items-center justify-center">
                <FontAwesomeIcon icon={faPlus} className="text-green-300 text-xl" />
              </div>
              <FontAwesomeIcon icon={faArrowUp} className="text-white/60 text-lg" />
            </div>
            <div className="text-white font-bold text-lg mb-2">Top Up Credits</div>
            <div className="text-blue-100 text-sm">Add credits to your wallet instantly</div>
            <div className="mt-3 text-green-300 text-xs font-medium">
              • Instant processing
            </div>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={onWithdraw}
            className="bg-white/20 backdrop-blur-sm rounded-2xl p-6 text-left hover:bg-white/30 transition-all border border-white/20 shadow-lg hover:shadow-xl"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-xl bg-blue-500/20 flex items-center justify-center">
                <FontAwesomeIcon icon={faMinus} className="text-blue-300 text-xl" />
              </div>
              <FontAwesomeIcon icon={faArrowDown} className="text-white/60 text-lg" />
            </div>
            <div className="text-white font-bold text-lg mb-2">Withdraw Credits</div>
            <div className="text-blue-100 text-sm">Cash out your credits to bank account</div>
            <div className="mt-3 text-blue-300 text-xs font-medium">
              • 1-3 business days
            </div>
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

/**
 * Account Status Card Component
 */
const AccountStatusCard = () => {
  const { loadVerificationStatus } = useWallet();
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [verificationLoading, setVerificationLoading] = useState(true);

  useEffect(() => {
    const loadStatus = async () => {
      try {
        setVerificationLoading(true);
        const status = await loadVerificationStatus();
        setVerificationStatus(status);
      } catch (error) {
        console.error('Error loading verification status:', error);
      } finally {
        setVerificationLoading(false);
      }
    };

    loadStatus();
  }, [loadVerificationStatus]);

  const getStatusInfo = (type) => {
    if (type === 'email') {
      if (verificationLoading) {
        return {
          status: 'loading',
          title: 'Email Status',
          description: 'Checking...',
          color: 'from-gray-400 to-gray-500',
          icon: faSpinner,
          animate: true
        };
      }

      if (!verificationStatus) {
        return {
          status: 'unknown',
          title: 'Email Status',
          description: 'Unable to verify',
          color: 'from-gray-400 to-gray-500',
          icon: faQuestionCircle
        };
      }

      if (!verificationStatus.requiresEmailVerification) {
        return {
          status: 'verified',
          title: 'Email Verified',
          description: 'Email verification complete',
          color: 'from-green-500 to-emerald-600',
          icon: faCheckCircle
        };
      }

      return {
        status: 'pending',
        title: 'Email Verification Required',
        description: 'Please verify your email',
        color: 'from-yellow-500 to-orange-600',
        icon: faEnvelope
      };
    }

    if (type === 'kyc') {
      if (verificationLoading) {
        return {
          status: 'loading',
          title: 'E-KYC Status',
          description: 'Checking...',
          color: 'from-gray-400 to-gray-500',
          icon: faSpinner,
          animate: true
        };
      }

      if (!verificationStatus) {
        return {
          status: 'unknown',
          title: 'E-KYC Status',
          description: 'Unable to verify',
          color: 'from-gray-400 to-gray-500',
          icon: faQuestionCircle
        };
      }

      if (!verificationStatus.requiresKycVerification) {
        return {
          status: 'verified',
          title: 'E-KYC Verified',
          description: 'Identity verification complete',
          color: 'from-green-500 to-emerald-600',
          icon: faCheckCircle
        };
      }

      return {
        status: 'pending',
        title: 'E-KYC Verification Required',
        description: 'Please complete identity verification',
        color: 'from-yellow-500 to-orange-600',
        icon: faIdCard
      };
    }
  };

  const emailStatus = getStatusInfo('email');
  const kycStatus = getStatusInfo('kyc');

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="space-y-4"
    >
      {/* Email Verification Status */}
      <div className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center space-x-4">
          <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${emailStatus.color} flex items-center justify-center shadow-lg`}>
            <FontAwesomeIcon
              icon={emailStatus.icon}
              className={`text-white text-lg ${emailStatus.animate ? 'animate-spin' : ''}`}
            />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1">{emailStatus.title}</h3>
            <p className="text-sm text-gray-600">{emailStatus.description}</p>
          </div>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </div>
      </div>

      {/* E-KYC Verification Status */}
      <div className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
        <div className="flex items-center space-x-4">
          <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${kycStatus.color} flex items-center justify-center shadow-lg`}>
            <FontAwesomeIcon
              icon={kycStatus.icon}
              className={`text-white text-lg ${kycStatus.animate ? 'animate-spin' : ''}`}
            />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-1">{kycStatus.title}</h3>
            <p className="text-sm text-gray-600">{kycStatus.description}</p>
          </div>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </div>
      </div>
    </motion.div>
  );
};

const EmailVerificationCard = ({ status }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.6, delay: 0.2 }}
    className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-xl flex flex-col items-start mb-6"
  >
    <div className="flex items-center mb-4">
      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg mr-4">
        <FontAwesomeIcon icon={status.icon} className={`text-white text-lg ${status.animate ? 'animate-spin' : ''}`} />
      </div>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1 text-lg">{status.title}</h3>
        <p className="text-sm text-gray-600">{status.description}</p>
      </div>
    </div>
    {status.action && (
      <button
        onClick={status.action.onClick}
        className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
      >
        {status.action.label}
      </button>
    )}
  </motion.div>
);

const EKYCVerificationCard = ({ status }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.6, delay: 0.3 }}
    className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-xl flex flex-col items-start"
  >
    <div className="flex items-center mb-4">
      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-lg mr-4">
        <FontAwesomeIcon icon={status.icon} className={`text-white text-lg ${status.animate ? 'animate-spin' : ''}`} />
      </div>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1 text-lg">{status.title}</h3>
        <p className="text-sm text-gray-600">{status.description}</p>
      </div>
    </div>
    {status.action && (
      <button
        onClick={status.action.onClick}
        className="mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
      >
        {status.action.label}
      </button>
    )}
  </motion.div>
);

const VerificationStatusCard = ({ emailStatus, kycStatus }) => (
  <motion.div
    initial={{ opacity: 0, x: 20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.6, delay: 0.2 }}
    className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl p-6 shadow-xl flex flex-col space-y-6"
  >
    <div className="flex items-center mb-2">
      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg mr-4">
        <FontAwesomeIcon icon={emailStatus.icon} className={`text-white text-lg ${emailStatus.animate ? 'animate-spin' : ''}`} />
      </div>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1 text-lg">{emailStatus.title}</h3>
        <p className="text-sm text-gray-600">{emailStatus.description}</p>
        {emailStatus.action && (
          <button
            onClick={emailStatus.action.onClick}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            {emailStatus.action.label}
          </button>
        )}
      </div>
    </div>
    <div className="flex items-center">
      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-lg mr-4">
        <FontAwesomeIcon icon={kycStatus.icon} className={`text-white text-lg ${kycStatus.animate ? 'animate-spin' : ''}`} />
      </div>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1 text-lg">{kycStatus.title}</h3>
        <p className="text-sm text-gray-600">{kycStatus.description}</p>
        {kycStatus.action && (
          <button
            onClick={kycStatus.action.onClick}
            className="mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
          >
            {kycStatus.action.label}
          </button>
        )}
      </div>
    </div>
  </motion.div>
);

const WalletContent = () => {
  const {
    balance,
    balanceLoading,
    balanceError,
    loadBalance,
    refreshWallet,
    verificationStatus, // Use from context instead of separate call
    verificationLoading // Use from context instead of separate call
  } = useWallet();

  // Local state
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [settingsInitialSection, setSettingsInitialSection] = useState(null);
  const [showBankAccountModal, setShowBankAccountModal] = useState(false);
  const [bankAccountModalMode, setBankAccountModalMode] = useState('add');
  const [selectedBankAccount, setSelectedBankAccount] = useState(null);
  const [topUpModalInitialStep, setTopUpModalInitialStep] = useState('packages');
  const [topUpModalSuccessPackage, setTopUpModalSuccessPackage] = useState(null);

  const [searchParams] = useSearchParams();
  const { t } = useTranslation('wallet');

  // Initialize wallet data on mount - only once
  useEffect(() => {
    const initializeWallet = async () => {
      try {
        // Load all data on initial mount
        await refreshWallet({ 
          includeVerification: true, 
          includeWithdrawals: true, 
          includeCreditPackages: true,
          forceRefresh: false // Don't force refresh on initial load
        });
      } catch (error) {
        console.error('Error initializing wallet:', error);
      }
    };

    initializeWallet();
  }, [refreshWallet]);

  // Handle payment success redirect
  useEffect(() => {
    const paymentStatus = searchParams.get('status');
    // Check for topup success toast in localStorage (set by PaymentReturnPage.js)
    const toastRaw = localStorage.getItem('topup_success_toast');
    let toast = null;
    try { toast = toastRaw ? JSON.parse(toastRaw) : null; } catch { toast = null; }

    if (paymentStatus === 'success' && toast) {
      // Show TopUpModal in success step (show credits if available, else generic)
      setTopUpModalInitialStep('success');
      setTopUpModalSuccessPackage({ credits: toast.credits });
      setShowTopUpModal(true);
      // Remove toast and query param after showing
      localStorage.removeItem('topup_success_toast');
      // Remove ?status=success from URL
      const params = new URLSearchParams(window.location.search);
      params.delete('status');
      window.history.replaceState({}, '', `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`);
      // Refresh wallet data
      refreshWallet({ 
        includeVerification: false, 
        includeWithdrawals: false, 
        includeCreditPackages: false,
        forceRefresh: true 
      });
    }
    else if (paymentStatus === 'success') {
      // Fallback: just refresh wallet
      refreshWallet({ 
        includeVerification: false, 
        includeWithdrawals: false, 
        includeCreditPackages: false,
        forceRefresh: true 
      });
    }
  }, [searchParams, refreshWallet]);

  // Action handlers
  const handleTopUp = () => setShowTopUpModal(true);
  const handleWithdraw = () => setShowWithdrawModal(true);
  const handleToggleBalanceVisibility = () => setBalanceVisible(!balanceVisible);

  // Modal handlers - optimize to only refresh necessary data
  const handleTopUpSuccess = () => {
    setShowTopUpModal(false);
    // Only refresh balance and transactions, not everything
    loadBalance(true);
  };

  const handleWithdrawSuccess = () => {
    setShowWithdrawModal(false);
    // Only refresh balance and withdrawal history, not everything
    loadBalance(true);
  };

  // Handler to open Settings from WithdrawModal
  const handleOpenSettingsFromWithdraw = (section) => {
    setShowWithdrawModal(false);
    setSettingsInitialSection(section);
    setShowSettingsModal(true);
  };

  // Bank Account Modal handlers
  const handleAddBankAccount = () => {
    setBankAccountModalMode('add');
    setSelectedBankAccount(null);
    setShowBankAccountModal(true);
  };

  const handleEditBankAccount = (account) => {
    setBankAccountModalMode('edit');
    setSelectedBankAccount(account);
    setShowBankAccountModal(true);
  };

  const handleBankAccountSuccess = () => {
    setShowBankAccountModal(false);
    setSelectedBankAccount(null);
    // No need to refresh entire wallet for bank account changes
    // Bank account data is managed separately
  };

  // Helper to get status info for each card - use context data
  const getStatusInfo = (type) => {
    if (verificationLoading) {
      return {
        status: 'loading',
        title: type === 'email' ? 'Email Status' : 'E-KYC Status',
        description: 'Checking...',
        icon: faSpinner,
        animate: true
      };
    }
    if (!verificationStatus) {
      return {
        status: 'unknown',
        title: type === 'email' ? 'Email Status' : 'E-KYC Status',
        description: 'Unable to verify',
        icon: faQuestionCircle
      };
    }
    if (type === 'email') {
      if (!verificationStatus.requiresEmailVerification) {
        return {
          status: 'verified',
          title: 'Email Verified',
          description: 'Email verification complete',
          icon: faCheckCircle
        };
      }
      return {
        status: 'pending',
        title: 'Email Verification Required',
        description: 'Please verify your email',
        icon: faEnvelope,
        action: {
          label: 'Verify Email',
          onClick: () => {/* trigger email verification */}
        }
      };
    }
    if (type === 'kyc') {
      if (!verificationStatus.requiresKycVerification) {
        return {
          status: 'verified',
          title: 'E-KYC Verified',
          description: 'Identity verification complete',
          icon: faCheckCircle
        };
      }
      return {
        status: 'pending',
        title: 'E-KYC Verification Required',
        description: 'Please complete identity verification',
        icon: faIdCard,
        action: {
          label: 'Complete E-KYC',
          onClick: () => {/* trigger kyc verification */}
        }
      };
    }
  };


  if (balanceError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/30"
        >
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 text-2xl" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Wallet</h3>
          <p className="text-gray-600 mb-6">{balanceError}</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all"
          >
            <FontAwesomeIcon icon={faRefresh} className="mr-2" />
            Try Again
          </motion.button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-900">
      {/* Navigation */}
      <MainNavigation activeItem="/wallet" />

      {/* Wallet Error Handler - Global wallet error handling */}
      <WalletErrorHandler
        context="general"
        onRetry={refreshWallet}
        onRecovery={(result) => {
          if (result.success) {
            console.log('Wallet error recovered:', result);
          }
        }}
        showToasts={true}
        showInlineErrors={false}
        autoRecovery={true}
      />

      {/* Main Content */}
      <div className="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-24 md:pb-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-8"
        >
          <div>
            <h1 className="text-4xl text-left font-bold text-gray-900 dark:text-gray-100 mb-2">{t('page.title')}</h1>
            <p className="text-gray-600 dark:text-gray-300 text-left">{t('page.subtitle')}</p>
          </div>

          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={refreshWallet}
              className="w-12 h-12 rounded-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/30 dark:border-gray-700 flex items-center justify-center hover:shadow-lg transition-all"
            >
              <FontAwesomeIcon icon={faRefresh} className="text-gray-600 dark:text-gray-200" />
            </motion.button>
          </div>
        </motion.div>

        {/* Balance Overview with Error Handling */}
        <div className="mb-8">
          <WalletErrorHandler
            context="balance"
            onRetry={loadBalance}
            showToasts={false}
            showInlineErrors={true}
            autoRecovery={true}
          />
          <BalanceOverviewCard
            balance={balance}
            isLoading={balanceLoading}
            onTopUp={handleTopUp}
            onWithdraw={handleWithdraw}
            onToggleVisibility={handleToggleBalanceVisibility}
            isVisible={balanceVisible}
          />
        </div>

        {/* Enhanced Dashboard Content with Priority Order */}
        {/* Bank Account Card: Single column, single row */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
          className="mb-8"
            >
              <BankAccountManager
                variant="card"
                size="medium"
                showTitle={true}
                className="shadow-xl dark:bg-gray-900 dark:border-gray-700"
                onAddAccount={handleAddBankAccount}
                onEditAccount={handleEditBankAccount}
              />
            </motion.div>

        {/* Grid for Transaction and Withdrawal History */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <PaymentHistoryCard
              showFilters={true}
              showRefreshButton={true}
              limit={9}
              size="medium"
              variant="card"
              transactionType="credits"
              autoRefresh={false}
              className="shadow-xl dark:bg-gray-900 dark:border-gray-700"
            />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <StandaloneWithdrawalHistoryCard
              limit={3}
              size="medium"
              className="shadow-xl dark:bg-gray-900 dark:border-gray-700"
            />
          </motion.div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/wallet" />

      {/* Modal Components */}
      <TopUpModal
        isOpen={showTopUpModal}
        onClose={() => {
          setShowTopUpModal(false);
          setTopUpModalInitialStep('packages');
          setTopUpModalSuccessPackage(null);
        }}
        onSuccess={handleTopUpSuccess}
        initialStep={topUpModalInitialStep}
        successPackage={topUpModalSuccessPackage}
      />

      <WithdrawModal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        onSuccess={handleWithdrawSuccess}
        onOpenSettings={handleOpenSettingsFromWithdraw}
      />

      <BankAccountModal
        isOpen={showBankAccountModal}
        mode={bankAccountModalMode}
        account={selectedBankAccount}
        onClose={() => {
          setShowBankAccountModal(false);
          setSelectedBankAccount(null);
        }}
        onSuccess={handleBankAccountSuccess}
      />

      {/* Settings Modal */}
      {showSettingsModal && (
        <Settings
          onClose={() => setShowSettingsModal(false)}
          initialSection={settingsInitialSection}
        />
      )}
    </div>
  );
};

/**
 * Main Wallet Component with Enhanced Providers
 */
const Wallet = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading for navigation transition (can be replaced with actual loading logic)
    const timer = setTimeout(() => setLoading(false), 800); // 800ms for smooth transition
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <PageLoader message="Loading your wallet..." color="blue" />;
  }

  return (
    <WalletProvider>
      <BankAccountProvider>
        <WalletContent />
      </BankAccountProvider>
    </WalletProvider>
  );
};
export default Wallet;
