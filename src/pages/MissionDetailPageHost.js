import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import MissionCard from '../components/mission/MissionCard';
import ApplicationModal from '../components/mission/ApplicationModal';
import ParticipantsModal from '../components/mission/ParticipantsModal';
import EnhancedParticipantsTab from '../components/mission/EnhancedParticipantsTab';
import { missionApi } from '../services/missionApi';
import { transformApiMissionToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';
import MissionReviewModal from '../components/modals/MissionReviewModal';
import MissionDisputeModal from '../components/mission/MissionDisputeModal';
import missionReviewService from '../services/missionReviewService';
import { getCdnUrl } from '../utils/cdnUtils';

// Format date for display (copied from MissionDetailPage.js)
const formatDate = (dateString) => {
  const options = {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  return new Date(dateString).toLocaleDateString('en-US', options);
};

// Format time duration (copied from MissionDetailPage.js)
const formatDuration = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const durationMs = end - start;
  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

  if (hours === 0) {
    return `${minutes} minutes`;
  } else if (minutes === 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
};

const MissionDetailPageHost = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const [mission, setMission] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('details');
  const [applicants, setApplicants] = useState([]);
  const [loadingApplicants, setLoadingApplicants] = useState(false);
  const [markingComplete, setMarkingComplete] = useState(false);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [participantForDispute, setParticipantForDispute] = useState(null);
  const [currentImage, setCurrentImage] = useState(0);
  const images = mission && mission.images && mission.images.length > 0 ? mission.images : [mission && mission.image ? mission.image : '/images/mission-default.jpg'];
  const hasMultipleImages = images.length > 1;
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [bookmarkLoading, setBookmarkLoading] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [missionTimeStatus, setMissionTimeStatus] = useState('upcoming');
  const [showFullDescription, setShowFullDescription] = useState(false);

  // Fetch mission details
  useEffect(() => {
    const fetchMissionDetails = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await missionApi.getMissionById(missionId);
        const transformedMission = transformApiMissionToFrontend(response.data);
        setMission(transformedMission);
      } catch (error) {
        setError(getApiErrorMessage(error));
      } finally {
        setIsLoading(false);
      }
    };
    fetchMissionDetails();
  }, [missionId]);

  // Fetch applicants for Manage Applicants tab
  useEffect(() => {
    if (activeTab !== 'manage') return;
    const fetchApplicants = async () => {
      setLoadingApplicants(true);
      try {
        const response = await missionApi.getMissionApplicants(missionId);
        setApplicants(response.data);
      } catch (error) {
        toast.error('Failed to load applicants');
        setApplicants([]);
      } finally {
        setLoadingApplicants(false);
      }
    };
    fetchApplicants();
  }, [activeTab, missionId, toast]);

  // Mark mission as completed
  const handleMarkCompleted = async () => {
    setMarkingComplete(true);
    try {
      await missionApi.completeMission(missionId, {});
      toast.success('Mission marked as completed');
      setMission((prev) => ({ ...prev, status: 'completed' }));
    } catch (error) {
      toast.error('Failed to mark as completed');
    } finally {
      setMarkingComplete(false);
    }
  };

  const handleCloseMission = async () => {
    try {
      await missionApi.closeMission(missionId);
      toast.success('Mission closed');
      setMission((prev) => ({ ...prev, status: 'closed' }));
    } catch (error) {
      toast.error('Failed to close mission');
    }
  };

  const handleStartMission = async () => {
    try {
      await missionApi.startMission(missionId);
      toast.success('Mission started');
      setMission((prev) => ({ ...prev, status: 'open' }));
    } catch (error) {
      toast.error('Failed to start mission');
    }
  };

  // Approve applicant
  const handleApproveApplicant = async (applicant) => {
    try {
      const response = await missionApi.approveApplicant(missionId, applicant.child_id);
      toast.success(response?.message || 'Applicant approved');

      setApplicants((prev) =>
        prev.map((a) => (a.id === applicant.id ? { ...a, status: 'approved' } : a))
      );

      if (response?.mission_status) {
        setMission((prev) => ({
          ...prev,
          status: response.mission_status,
          approved_count: response.approved_count ?? prev.approved_count,
          required_count: response.required_count ?? prev.required_count,
        }));
      }
    } catch (error) {
      toast.error(getApiErrorMessage(error) || 'Failed to approve applicant');
    }
  };

  // Reject applicant
  const handleRejectApplicant = async (applicant) => {
    if (!window.confirm('Are you sure you want to reject this applicant?')) return;

    try {
      const response = await missionApi.rejectApplicant(missionId, applicant.child_id);
      toast.success(response?.message || 'Applicant rejected');

      setApplicants((prev) => prev.filter((a) => a.id !== applicant.id));
    } catch (error) {
      toast.error(getApiErrorMessage(error) || 'Failed to reject applicant');
    }
  };

  // Review handler
  const handleSubmitReview = async (reviews) => {
    try {
      await missionReviewService.createParticipantReviews(mission.id, reviews);
      toast.success('Review submitted successfully');
      // Optionally refetch mission details
      // fetchMissionDetails();
    } catch (error) {
      toast.error(error.message || 'Failed to submit review');
      throw error;
    }
  };

  const openParticipantDispute = (participant) => {
    setParticipantForDispute(participant);
  };

  const closeParticipantDispute = () => {
    setParticipantForDispute(null);
  };

  useEffect(() => {
    if (mission) setIsBookmarked(mission.is_bookmarked ?? false);
  }, [mission]);

  const handlePrev = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleNext = (e) => {
    e.stopPropagation();
    setCurrentImage((prev) => (prev + 1) % images.length);
  };

  const toggleBookmark = async (e) => {
    e && e.preventDefault();
    if (bookmarkLoading) return;
    setBookmarkLoading(true);
    try {
      await missionApi.bookmarkMission(missionId);
      setIsBookmarked((prev) => !prev);
      toast.success(!isBookmarked ? 'Mission added to bookmarks' : 'Mission removed from bookmarks');
    } catch (error) {
      toast.error('Failed to update bookmark');
    } finally {
      setBookmarkLoading(false);
    }
  };

  // Add timeRemaining calculation (copied from MissionDetailPage.js)
  useEffect(() => {
    if (!mission || !mission.date) return;
    const updateTimeRemaining = () => {
      const now = new Date();
      const missionStartDate = new Date(mission.date);
      const missionEndDate = new Date(mission.end_date);
      const timeToStart = missionStartDate - now;
      const timeToEnd = missionEndDate - now;
      if (timeToStart > 0) {
        setMissionTimeStatus('upcoming');
        const days = Math.floor(timeToStart / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeToStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeToStart % (1000 * 60 * 60)) / (1000 * 60));
        if (days > 0) {
          setTimeRemaining(`Starts in ${days}d ${hours}h`);
        } else if (hours > 0) {
          setTimeRemaining(`Starts in ${hours}h ${minutes}m`);
        } else if (minutes > 0) {
          setTimeRemaining(`Starts in ${minutes}m`);
        } else {
          setTimeRemaining('Starting now!');
        }
      } else if (timeToEnd > 0) {
        setMissionTimeStatus('ongoing');
        const hours = Math.floor(timeToEnd / (1000 * 60 * 60));
        const minutes = Math.floor((timeToEnd % (1000 * 60 * 60)) / (1000 * 60));
        if (hours > 0) {
          setTimeRemaining(`${hours}h ${minutes}m remaining`);
        } else if (minutes > 0) {
          setTimeRemaining(`${minutes}m remaining`);
        } else {
          setTimeRemaining('Ending soon');
        }
      } else {
        setMissionTimeStatus('completed');
        setTimeRemaining('Mission completed');
      }
    };
    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 60000);
    return () => clearInterval(interval);
  }, [mission]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-white dark:bg-gray-950">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 dark:border-indigo-400"></div>
      </div>
    );
  }

  if (error || !mission) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-white dark:bg-gray-950">
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-md p-8 text-center max-w-md border border-gray-100 dark:border-gray-800">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
            {error ? 'Error Loading Mission' : 'Mission Not Found'}
          </h3>
          <p className="text-gray-500 dark:text-gray-300 mb-4">
            {error || "The mission you're looking for doesn't exist or has been removed."}
          </p>
          <button
            onClick={() => navigate('/missions')}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800 transition-colors"
          >
            Back to Missions
          </button>
        </div>
      </div>
    );
  }

  return (
    <MissionLayout title="Mission Details (Host)" backPath="/missions">
      <div className="flex flex-col gap-6 md:gap-10 px-2 md:px-0">
        {/* Mission Image Banner and Header */}
        <motion.div
          className="relative h-64 md:h-80 rounded-3xl overflow-hidden mb-6 md:mb-8 shadow-xl bg-white dark:bg-gray-900"
          initial={{ opacity: 0, y: 20, scale: 1 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          whileHover={{
            y: -6,
            scale: 1.01,
            boxShadow: '0 12px 32px 0 rgba(60, 80, 180, 0.18)',
            transition: { duration: 0.25, ease: 'easeOut' }
          }}
          transition={{ duration: 0.5 }}
        >
          {/* Mission image carousel with glassmorphism and gradient overlays */}
          <>
            <motion.img
              key={images[currentImage]}
              src={images[currentImage]}
              alt={mission.title}
              className="w-full h-full object-cover rounded-3xl"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            />
            {/* Carousel Arrows */}
            {hasMultipleImages && (
              <>
                <button
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-indigo-700 rounded-full p-2 shadow-md z-10"
                  onClick={handlePrev}
                  aria-label="Previous image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" /></svg>
                </button>
                <button
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white text-indigo-700 rounded-full p-2 shadow-md z-10"
                  onClick={handleNext}
                  aria-label="Next image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" /></svg>
                </button>
              </>
            )}
            {/* Dot indicators */}
            {hasMultipleImages && (
              <div className="absolute bottom-5 left-1/2 -translate-x-1/2 flex gap-2 z-10">
                {images.map((_, idx) => (
                  <span
                    key={idx}
                    className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentImage ? 'bg-white dark:bg-indigo-200 shadow-lg scale-110' : 'bg-white/40 dark:bg-indigo-900/40'}`}
                  />
                ))}
              </div>
            )}
            {/* Image overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 dark:from-black/80 to-transparent rounded-3xl"></div>
          </>
          {/* Status badge (modern pill) */}
          <motion.div
            className={`absolute top-5 left-5 px-3 py-1 rounded-full text-xs font-semibold flex items-center shadow-md backdrop-blur-sm border transition-all duration-200 cursor-default
              ${mission.status === 'open' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border-green-200/50 dark:border-green-800/50' :
                mission.status === 'in_progress' || mission.status === 'in progress' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border-blue-200/50 dark:border-blue-800/50' :
                  mission.status === 'completed' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 border-purple-200/50 dark:border-purple-800/50' :
                    'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-200/50 dark:border-gray-700/50'}
            `}
            whileHover={{ scale: 1.06 }}
            whileTap={{ scale: 0.97 }}
          >
            <span className={`w-2 h-2 rounded-full mr-2 transition-all duration-200
              ${mission.status === 'open' ? 'bg-green-500 animate-pulse' :
                mission.status === 'in_progress' || mission.status === 'in progress' ? 'bg-blue-500 animate-pulse' :
                  mission.status === 'completed' ? 'bg-purple-500' :
                    'bg-gray-500'}
            `} />
            {mission.status === 'open' ? 'Recruiting' :
             mission.status === 'in_progress' || mission.status === 'in progress' ? 'In Progress' :
             mission.status === 'completed' ? 'Completed' :
             mission.status}
          </motion.div>
          {/* Tags overlay (modern pill badges) */}
          <div className="absolute bottom-5 left-5 right-5 flex flex-wrap gap-2">
            {mission.level_requirement && (
              <motion.span
                className="bg-indigo-900/80 dark:bg-indigo-800/80 text-indigo-100 dark:text-indigo-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-indigo-700/50 dark:border-indigo-600/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-indigo-800/90 dark:hover:bg-indigo-900/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                LV{mission.level_requirement.min || 1}-{mission.level_requirement.max || 99}
              </motion.span>
            )}
            {mission.style && (
              <motion.span
                className="bg-purple-900/80 dark:bg-purple-800/80 text-purple-100 dark:text-purple-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-purple-700/50 dark:border-purple-600/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-purple-800/90 dark:hover:bg-purple-900/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
                {mission.style}
              </motion.span>
            )}
            {mission.category && (
              <motion.span
                className="bg-blue-900/80 dark:bg-blue-800/80 text-blue-100 dark:text-blue-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-blue-700/50 dark:border-blue-600/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-blue-800/90 dark:hover:bg-blue-900/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
                </svg>
                {mission.category}
              </motion.span>
            )}
            {mission.platform && (
              <motion.span
                className="bg-green-900/80 dark:bg-green-800/80 text-green-100 dark:text-green-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-green-700/50 dark:border-green-600/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-green-800/90 dark:hover:bg-green-900/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H6zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z" clipRule="evenodd" />
                </svg>
                {mission.platform}
              </motion.span>
            )}
            {mission.language && (
              <motion.span
                className="bg-yellow-900/80 dark:bg-yellow-800/80 text-yellow-100 dark:text-yellow-200 text-xs font-medium px-2.5 py-1 rounded-full backdrop-blur-sm border border-yellow-700/50 dark:border-yellow-600/50 shadow-sm flex items-center transition-all duration-200 cursor-pointer hover:bg-yellow-800/90 dark:hover:bg-yellow-900/90 hover:shadow-md"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
              >
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <circle cx="10" cy="10" r="8" />
                </svg>
                {mission.language}
              </motion.span>
            )}
          </div>
          {/* Bookmark button */}
          <button
            className={`absolute top-4 right-4 w-10 h-10 ${isBookmarked ? 'bg-indigo-600 dark:bg-indigo-700' : 'bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm'} rounded-full flex items-center justify-center ${isBookmarked ? 'text-white' : 'text-gray-600 dark:text-gray-200 hover:text-indigo-600 dark:hover:text-indigo-300'} transition-all duration-300 shadow-md`}
            onClick={toggleBookmark}
            aria-label={isBookmarked ? "Remove from bookmarks" : "Add to bookmarks"}
            disabled={bookmarkLoading}
          >
            <svg className="w-5 h-5" fill={isBookmarked ? "currentColor" : "none"} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
          </button>
          {/* Header Action Buttons: Edit Mission & Mark as Completed */}
          {mission.status !== 'completed' && (
            <div className="absolute top-5 right-5 flex gap-3 z-20">
              <button
                className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400/60 hover:scale-105 active:scale-95"
                onClick={() => navigate(`/missions/${mission.id}/edit`)}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Mission
              </button>
              {mission.status && mission.status.toLowerCase() === 'open' && (
                <button
                  className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
                  onClick={handleCloseMission}
                >
                  Close Mission
                </button>
              )}
              {mission.status && mission.status.toLowerCase() === 'closed' && (
                <button
                  className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-green-500 to-green-700 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                  onClick={handleStartMission}
                >
                  Start Mission
                </button>
              )}
              <button
                className="px-6 py-3 rounded-full font-semibold bg-gradient-to-r from-green-500 to-green-700 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-400/60 hover:scale-105 active:scale-95"
                onClick={handleMarkCompleted}
                disabled={markingComplete}
              >
                {markingComplete ? 'Marking...' : 'Mark as Completed'}
              </button>
            </div>
          )}
        </motion.div>
        {/* Info Grid Section (below banner) */}
        <div className="mb-2 md:mb-2 px-0 md:px-0">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 md:gap-4">
            {/* Credits Card */}
            <div className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-indigo-100 dark:border-indigo-800 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
              <svg className="w-6 h-6 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              <span className="font-semibold text-indigo-700 dark:text-indigo-200 text-lg">{mission.bounty} <span className="font-normal text-base">credits</span></span>
              </div>
            {/* Countdown Card - visually distinct, gradient-backed */}
            {timeRemaining && (
              <div className="flex items-center justify-center text-white font-semibold bg-gradient-to-r from-indigo-500/90 to-blue-500/90 border border-indigo-100 dark:border-indigo-800 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
                <svg className="w-5 h-5 inline-block mr-2 text-white align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                {timeRemaining} 
                </div>
            )}
            {/* Slots Card */}
            <div className="flex items-center justify-center bg-white/80 dark:bg-gray-900/80 border border-blue-100 dark:border-blue-800 rounded-xl shadow-sm py-4 px-6 min-h-[64px]">
              <svg className="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
              <span className="text-blue-700 dark:text-blue-200 text-base font-semibold">{mission.slots_filled}/{mission.slots_total} slots</span>
                  </div>
                </div>
              </div>
        {/* Mission Details Card Header Section */}
        <motion.div
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-md dark:shadow-indigo-900/10 overflow-hidden mb-6 md:mb-8 border border-gray-100 dark:border-gray-800"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          {/* Header Section */}
          <div className="p-6 border-b border-gray-100 dark:border-gray-800">
            <div className="mb-4">
              <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 dark:from-indigo-300 dark:to-blue-400 bg-clip-text text-transparent drop-shadow-lg relative inline-block">
                {mission.title}
                <span className="block h-1 w-16 mt-2 bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 rounded-full animate-pulse"></span>
              </h1>
            </div>
          </div>

          {/* Tabs Navigation */}
          <div className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-md rounded-2xl p-2 flex gap-2 mb-4 border border-gray-100 dark:border-gray-800 shadow-sm overflow-x-auto">
            <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60
                ${activeTab === 'details'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
              onClick={() => setActiveTab('details')}
            >
              Details
            </button>
            <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60
                ${activeTab === 'host'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
              onClick={() => setActiveTab('host')}
            >
              Host
            </button>
            <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 flex items-center
                ${activeTab === 'participants'
                  ? 'bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'}
              `}
              onClick={() => setActiveTab('participants')}
            >
              Participants
            </button>
            <button
              className={`px-6 py-2.5 font-semibold text-sm rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400/60 flex items-center
                ${activeTab === 'manage'
                  ? 'bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500 text-white shadow-md scale-105'
                  : 'bg-white/40 text-gray-600 hover:bg-pink-50 hover:text-pink-700'}
              `}
              onClick={() => setActiveTab('manage')}
            >
              Manage Applicants
            </button>
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          className="p-4 md:p-6 bg-white dark:bg-gray-900 rounded-2xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
        >
          {/* Details Tab */}
          {activeTab === 'details' && (
            <div className="flex flex-col gap-6">
              {/* Description Card */}
              <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 dark:border-indigo-800 p-6">
                <div className="flex items-center gap-2 mb-3">
                  <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                  <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Description</h2>
                  {mission.description.length > 150 && (
                    <button
                      className="ml-auto text-sm bg-transparent hover:bg-gray-200 hover:rounded-xl text-indigo-600 hover:text-indigo-800 flex items-justify"
                      onClick={() => setShowFullDescription(!showFullDescription)}
                    >
                      {showFullDescription ? (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7" />
                          </svg>
                          Show less
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                          Show more
                        </>
                      )}
                    </button>
                  )}
                </div>
                <div className="text-gray-700 dark:text-gray-200 text-base whitespace-pre-line">
                  <span className="dark:text-gray-200">{showFullDescription ? mission.description : (mission.description.length > 150 ? `${mission.description.substring(0, 150)}...` : mission.description)}</span>
                </div>
              </div>
              {/* Requirements Card */}
              {mission.requirements && mission.requirements.length > 0 && (
                <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 dark:border-indigo-800 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Requirements</h2>
                  </div>
                  <ul className="space-y-3">
                    {mission.requirements.map((requirement, index) => {
                      let icon;
                      const lowerReq = requirement.toLowerCase();
                      if (lowerReq.includes('microphone') || lowerReq.includes('mic')) {
                        icon = (
                          <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                        );
                      } else if (lowerReq.includes('discord') || lowerReq.includes('chat') || lowerReq.includes('communication')) {
                        icon = (
                          <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                        );
                      } else if (lowerReq.includes('rank') || lowerReq.includes('level') || lowerReq.includes('skill')) {
                        icon = (
                          <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                          </svg>
                        );
                      } else if (lowerReq.includes('time') || lowerReq.includes('available') || lowerReq.includes('duration')) {
                        icon = (
                          <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        );
                      } else {
                        icon = (
                          <svg className="w-5 h-5 text-indigo-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        );
                      }
                      return (
                        <li key={index} className="flex items-start bg-white/90 dark:bg-gray-900/90 p-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-800">
                          {icon}
                          <span className="text-gray-700 dark:text-gray-200">{requirement}</span>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
              {/* Date and Time Card */}
              <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 dark:border-indigo-800 p-6">
                <div className="flex items-center gap-2 mb-3">
                  <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Date & Time</h2>
                </div>
                <div className="flex flex-col md:flex-row md:items-stretch gap-6">
                  {/* Start Time */}
                  <div className="flex-1 flex flex-col items-center bg-white dark:bg-gray-900 p-5 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 mb-2 md:mb-0">
                    <div className="flex items-center mb-2">
                      <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                        <div className="text-xs text-left text-gray-500 dark:text-gray-300">Start Time</div>
                        <div className="font-medium text-gray-800 dark:text-gray-100">{formatDate ? formatDate(mission.date) : mission.date}</div>
                        </div>
                      </div>
                      {missionTimeStatus === 'upcoming' && (
                      <div className="mt-1 text-sm text-indigo-600 dark:text-indigo-300 font-medium flex items-center">
                          <svg className="w-4 h-4 mr-1 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {timeRemaining}
                        </div>
                      )}
                    </div>
                  {/* Timeline Divider */}
                  <div className="hidden md:flex flex-col items-center justify-center">
                    <div className="w-1 h-8 bg-gradient-to-b from-indigo-400 to-blue-400 rounded-full mb-2"></div>
                    <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3" />
                    </svg>
                    <div className="w-1 h-8 bg-gradient-to-t from-indigo-400 to-blue-400 rounded-full mt-2"></div>
                  </div>
                  {/* End Time */}
                  <div className="flex-1 flex flex-col items-center bg-white dark:bg-gray-900 p-5 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800">
                    <div className="flex items-center mb-2">
                      <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                        <div className="text-xs text-left text-gray-500 dark:text-gray-300">End Time</div>
                        <div className="font-medium text-gray-800 dark:text-gray-100">{formatDate ? formatDate(mission.end_date) : mission.end_date}</div>
                        </div>
                      </div>
                    <div className="mt-1 text-sm text-blue-600 dark:text-blue-300 font-medium flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Duration: {formatDuration ? formatDuration(mission.date, mission.end_date) : (mission.date && mission.end_date ? `${Math.floor((new Date(mission.end_date) - new Date(mission.date)) / (1000 * 60 * 60))} hours` : '')}
                      </div>
                    </div>
                  </div>
                  {/* Mission progress bar for ongoing missions */}
                  {missionTimeStatus === 'ongoing' && (
                  <div className="mt-6">
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-300 mb-1">
                        <span>Mission in progress</span>
                        <span>{timeRemaining}</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2">
                        <div
                          className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full"
                          style={{
                            width: (() => {
                              const now = new Date();
                              const start = new Date(mission.date);
                              const end = new Date(mission.end_date);
                              const total = end - start;
                              const elapsed = now - start;
                              const percentage = Math.min(100, Math.max(0, (elapsed / total) * 100));
                              return `${percentage}%`;
                            })()
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              {/* Tags Card */}
              {mission.tags && mission.tags.length > 0 && (
                <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow border border-indigo-100 dark:border-indigo-800 p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <svg className="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                    </svg>
                    <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Tags</h2>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {mission.tags.map((tag, index) => {
                      let tagClass;
                      const lowerTag = tag.toLowerCase();
                      if (lowerTag.includes('tournament') || lowerTag.includes('competition')) {
                        tagClass = "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800";
                      } else if (lowerTag.includes('team') || lowerTag.includes('group')) {
                        tagClass = "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800";
                      } else if (lowerTag.includes('casual') || lowerTag.includes('fun')) {
                        tagClass = "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800";
                      } else if (lowerTag.includes('competitive') || lowerTag.includes('ranked')) {
                        tagClass = "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 hover:bg-purple-200 dark:hover:bg-purple-800";
                      } else if (lowerTag.includes('beginner') || lowerTag.includes('new')) {
                        tagClass = "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-900";
                      } else {
                        tagClass = "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700";
                      }
                      return (
                        <motion.span
                          key={index}
                          className={`${tagClass} text-sm px-3 py-1.5 rounded-full cursor-pointer transition-colors duration-200 flex items-center`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => navigate(`/missions?tag=${tag}`)}
                        >
                          <span className="mr-1">#</span>
                          {tag}
                        </motion.span>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          )}
          {/* Host Tab */}
          {activeTab === 'host' && (
            <div>
              <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl p-6 mb-6 shadow-lg border border-indigo-100 dark:border-indigo-800">
                <div className="flex flex-col md:flex-row md:items-start md:space-x-8">
                  {/* Host Avatar with Gradient Border and Online Indicator */}
                  <div className="relative flex-shrink-0 mb-4 md:mb-0">
                    <div className="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-400 via-blue-400 to-purple-400 p-1 shadow-xl">
                      <div className="w-full h-full rounded-full overflow-hidden bg-white dark:bg-gray-900">
                        <img
                          src={mission.host?.avatar || '/images/default-avatar.jpg'}
                          alt={mission.host?.name || 'Host'}
                          className="w-full h-full object-cover rounded-full"
                        />
                      </div>
                    </div>
                    {/* Online status indicator */}
                    {mission.host?.online && (
                      <span className="absolute bottom-2 right-2 w-6 h-6 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-4 border-white dark:border-gray-900 flex items-center justify-center">
                        <span className="w-2.5 h-2.5 bg-green-500 rounded-full block animate-pulse"></span>
                      </span>
                    )}
                  </div>
                  {/* Host Info Card */}
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                        {mission.host?.name || 'Anonymous Host'}
                      </h2>
                      <span className="bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center shadow-sm">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        LV{mission.host?.level || '??'}
                      </span>
                      {mission.host?.verified && (
                        <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-1 rounded-full flex items-center border border-blue-200 dark:border-blue-800">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Verified
                        </span>
                      )}
                    </div>
                    {/* Host bio/description */}
                    <p className="text-gray-600 dark:text-gray-300 text-left text-sm mb-4">
                      {mission.host?.bio || `Professional mission host with experience in ${mission.theme || 'gaming'} missions.`}
                    </p>
                    {/* Host Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                      {/* Rating */}
                      <div className="bg-white/90 dark:bg-gray-900/90 p-3 rounded-xl shadow border border-gray-100 dark:border-gray-800 flex flex-col items-center">
                        <div className="flex items-center text-yellow-500 mb-1">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="ml-1 font-medium text-gray-800 dark:text-gray-100">{mission.host?.rating || '4.8'}</span>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-300">Rating</p>
                      </div>
                      {/* Reviews */}
                      <div className="bg-white/90 dark:bg-gray-900/90 p-3 rounded-xl shadow border border-gray-100 dark:border-gray-800 flex flex-col items-center">
                        <div className="text-gray-800 dark:text-gray-100 font-medium mb-1 flex items-center">
                          <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          {mission.host?.reviews || '24'}
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-300">Reviews</p>
                      </div>
                      {/* Completed Missions */}
                      <div className="bg-white/90 dark:bg-gray-900/90 p-3 rounded-xl shadow border border-gray-100 dark:border-gray-800 flex flex-col items-center">
                        <div className="text-gray-800 dark:text-gray-100 font-medium mb-1 flex items-center">
                          <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                          </svg>
                          {mission.host?.completed_missions || '36'}
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-300">Completed</p>
                      </div>
                      {/* Hosted Missions */}
                      <div className="bg-white/90 dark:bg-gray-900/90 p-3 rounded-xl shadow border border-gray-100 dark:border-gray-800 flex flex-col items-center">
                        <div className="text-gray-800 dark:text-gray-100 font-medium mb-1 flex items-center">
                          <svg className="w-4 h-4 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          {mission.host?.hosted_missions || '12'}
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-300">Hosted</p>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      <motion.button
                        className="px-4 py-2 rounded-full font-semibold bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:scale-105 active:scale-95"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => navigate(`/talents/${mission.host?.id}`)}
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        View Profile
                      </motion.button>
                      <motion.button
                        className="px-4 py-2 rounded-full font-semibold bg-white/90 dark:bg-gray-900/90 border border-indigo-200 dark:border-indigo-800 text-indigo-700 dark:text-indigo-200 shadow-md flex items-center justify-center text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-400/60 hover:bg-indigo-50 dark:hover:bg-indigo-800 hover:text-indigo-800 dark:hover:text-indigo-100 hover:scale-105 active:scale-95"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => navigate('/chat')}
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        Message
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Participants Tab */}
          {activeTab === 'participants' && (
            <EnhancedParticipantsTab
              mission={mission}
              isCurrentUserHost={true}
              hasApplied={false}
              onShowParticipantsModal={() => {}}
              onShowApplicationModal={() => {}}
              onReviewParticipants={() => setIsReviewModalOpen(true)}
              onDisputeParticipant={openParticipantDispute}
              user={localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null}
            />
          )}
          {/* Manage Applicants Tab */}
          {activeTab === 'manage' && (
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md rounded-2xl shadow border border-pink-100 dark:border-pink-900 p-6 flex flex-col items-center w-full">
              <h2 className="text-xl font-bold text-pink-700 dark:text-pink-300 mb-4">Applicants Management</h2>
              {loadingApplicants ? (
                <div className="text-gray-500 dark:text-gray-300">Loading applicants...</div>
              ) : applicants.length === 0 ? (
                <div className="text-gray-400 dark:text-gray-500">No applicants found.</div>
              ) : (
                <ul className="w-full space-y-8">
                  {applicants.map(applicant => {
                    const user = applicant.user || {};
                    const profilePic = user.profile_picture ? getCdnUrl(user.profile_picture) : '/images/default-avatar.jpg';
                    return (
                      <li key={applicant.id} className="w-full bg-white/95 dark:bg-gray-900/95 rounded-3xl shadow-xl border border-gray-100 dark:border-gray-800 p-8 flex flex-col md:flex-row gap-8 items-center hover:shadow-2xl transition-shadow duration-200">
                        {/* Avatar */}
                        <div className="flex-shrink-0 flex flex-col items-center">
                          <img
                            src={profilePic}
                            alt={user.nickname || user.name || 'Applicant'}
                            className="w-32 h-32 rounded-full object-cover border-4 border-pink-200 dark:border-pink-900 shadow-lg bg-gray-100 dark:bg-gray-900"
                            onError={e => { e.target.onerror = null; e.target.src = '/images/default-avatar.jpg'; }}
                          />
                        </div>
                        {/* Info */}
                        <div className="flex-1 flex flex-col gap-3 min-w-0">
                          <div className="flex flex-wrap items-center gap-3 mb-1">
                            <span className="text-2xl font-bold text-gray-800 dark:text-gray-100 truncate">{user.nickname || user.name || 'Unnamed'}</span>
                            {user.uid && (
                              <span className="bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-200 text-sm font-mono px-3 py-1 rounded-full border border-indigo-200 dark:border-indigo-800">{user.uid}</span>
                            )}
                            <span className={`px-4 py-1 rounded-full text-sm font-semibold border shadow-sm ${applicant.status === 'approved' ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-200 border-green-200 dark:border-green-800' : applicant.status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 border-yellow-200 dark:border-yellow-800' : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200 border-gray-200 dark:border-gray-700'}`}>{applicant.status.charAt(0).toUpperCase() + applicant.status.slice(1)}</span>
                          </div>
                          {user.biography && (
                            <div className="text-gray-600 dark:text-gray-300 text-left text-base mt-1 whitespace-pre-line line-clamp-4">{user.biography}</div>
                          )}
                        </div>
                        {/* Approve Button or Status */}
                        <div className="flex flex-col items-center gap-2 mt-6 md:mt-0 md:ml-8">
                          {applicant.status === 'pending' ? (
                            <div className="flex gap-3">
                              <button
                                className="px-6 py-3 rounded-full bg-gradient-to-r from-green-400 to-green-600 dark:from-green-700 dark:to-green-900 text-white font-semibold shadow-lg hover:from-green-500 hover:to-green-700 dark:hover:from-green-800 dark:hover:to-green-900 transition-colors text-lg"
                                onClick={() => handleApproveApplicant(applicant)}
                                title="Approve Applicant"
                              >
                                <svg className="w-6 h-6 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                                Approve
                              </button>
                              <button
                                className="px-6 py-3 rounded-full bg-gradient-to-r from-red-400 to-pink-500 dark:from-red-700 dark:to-pink-900 text-white font-semibold shadow-lg hover:from-red-500 hover:to-pink-600 dark:hover:from-red-800 dark:hover:to-pink-900 transition-colors text-lg"
                                onClick={() => handleRejectApplicant(applicant)}
                                title="Reject Applicant"
                              >
                                <svg className="w-6 h-6 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                Reject
                              </button>
                            </div>
                          ) : (
                            <span className="px-6 py-3 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-600 dark:text-gray-200 font-semibold text-lg">Approved</span>
                          )}
                        </div>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          )}
        </motion.div>

        {/* Action Buttons (Host Only) */}
        <div className="p-4 md:p-6 bg-gray-50 dark:bg-gray-900 border-t border-gray-100 dark:border-gray-800 mt-4 md:mt-6 rounded-b-2xl">
          <div className="flex flex-col sm:flex-row gap-3">
            <motion.button
              className="flex-1 py-3 rounded-full font-semibold bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg flex items-center justify-center text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-400/60 hover:scale-105 active:scale-95"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => {}}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Cancel Mission
            </motion.button>
          </div>
        </div>

        <MissionReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          mission={mission}
          onSubmitReview={handleSubmitReview}
        />

        <MissionDisputeModal
          isOpen={!!participantForDispute}
          onClose={closeParticipantDispute}
          mission={mission}
          participant={participantForDispute}
          onDisputeCreated={() => {}}
        />
      </div>
    </MissionLayout>
  );
};

export default MissionDetailPageHost;
