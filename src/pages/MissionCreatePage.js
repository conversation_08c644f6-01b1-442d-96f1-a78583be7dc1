import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionPreview from '../components/mission/MissionPreview';
import MissionLayout from '../components/mission/MissionLayout';
import WalletBalanceCard from '../components/mission/WalletBalanceCard';
import MissionPaymentSummary from '../components/mission/MissionPaymentSummary';
import MissionImageUploader from '../components/mission/MissionImageUploader';
import { missionApi } from '../services/missionApi';
import { referenceDataApi } from '../services/referenceDataApi';
import { transformFrontendMissionToApi } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';
import missionPaymentService from '../services/missionPaymentService';
import MissionPlatformFeeSummary from '../components/mission/MissionPlatformFeeSummary';
import { FaInfoCircle, FaListAlt, FaImage, FaCheckCircle } from 'react-icons/fa';

// Step 1: Basic Information Component
const BasicInfoStep = ({ formData, setFormData, goToNextStep }) => {
  const [errors, setErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;
    setFormData({
      ...formData,
      [name]: fieldValue
    });
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateStep = () => {
    const newErrors = {};
    if (!formData.title.trim()) {
      newErrors.title = 'Mission title is required';
    } else if (formData.title.length < 5) {
      newErrors.title = 'Mission title must be at least 5 characters';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Mission title must be less than 100 characters';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Mission description is required';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Description must be at least 20 characters';
    } else if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }
    if (!formData.bounty || formData.bounty <= 0) {
      newErrors.bounty = 'Bounty must be greater than 0';
    } else if (formData.bounty < 50) {
      newErrors.bounty = 'Minimum bounty is 50 credits';
    } else if (formData.bounty > 10000) {
      newErrors.bounty = 'Maximum bounty is 10,000 credits';
    }
    if (!formData.slotsTotal || formData.slotsTotal <= 0) {
      newErrors.slotsTotal = 'Number of participants must be greater than 0';
    } else if (formData.slotsTotal > 20) {
      newErrors.slotsTotal = 'Maximum 20 participants allowed';
    }
    if (!formData.date) {
      newErrors.date = 'Start date is required';
    } else {
      const startDate = new Date(formData.date);
      const now = new Date();
      const minDate = new Date(now.getTime() + 30 * 60 * 1000);
      if (startDate < minDate) {
        newErrors.date = 'Start date must be at least 30 minutes from now';
      }
    }
    if (!formData.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (formData.date) {
      const startDate = new Date(formData.date);
      const endDate = new Date(formData.endDate);
      const minDuration = 30 * 60 * 1000;
      if (endDate <= startDate) {
        newErrors.endDate = 'End date must be after start date';
      } else if (endDate - startDate < minDuration) {
        newErrors.endDate = 'Mission must be at least 30 minutes long';
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isStepValid = () => {
    return (
      formData.title.trim() !== '' &&
      formData.description.trim() !== '' &&
      formData.bounty > 0 &&
      formData.slotsTotal > 0 &&
      formData.date !== '' &&
      formData.endDate !== ''
    );
  };

  const handleNext = () => {
    setIsValidating(true);
    if (validateStep()) {
      goToNextStep();
    }
    setIsValidating(false);
  };

  return (
    <div className="p-0 sm:p-8 w-full max-w-4xl mx-auto">
      <div className="flex flex-col gap-8">
        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="title" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaInfoCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Mission Title</span>*
        </label>
        <div className="relative">
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
              className={`w-full border-2 ${errors.title ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
            placeholder="E.g., MLBB - Tank, Mage Support Needed!"
            required
          />
          <div className="absolute right-3 top-2 text-xs text-gray-400 dark:text-indigo-100">
            {formData.title.length}/100
          </div>
        </div>
        {errors.title && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.title}</motion.p>
        )}
          <p className="mt-2 text-xs text-gray-500 dark:text-indigo-100">
          Choose a clear, descriptive title that explains what you need help with
        </p>
      </div>

        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="description" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaListAlt className="mr-2 text-indigo-500 dark:text-indigo-100" /> <span className="dark:text-indigo-100">Description</span>*
        </label>
        <div className="relative">
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows="5"
              className={`w-full border-2 ${errors.description ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
            placeholder="Describe your mission in detail. What game are you playing? What roles do you need? What's the objective?"
            required
          ></textarea>
          <div className="absolute right-3 bottom-2 text-xs text-gray-400 dark:text-indigo-100">
            {formData.description.length}/500
          </div>
        </div>
        {errors.description && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.description}</motion.p>
        )}
          <p className="mt-2 text-xs text-gray-500 dark:text-indigo-100">
          Provide clear details about your mission, including game type, objectives, and what you expect from participants
        </p>
      </div>

        {/* Bounty Field */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="bounty" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaCheckCircle className="mr-2 text-indigo-500 dark:text-indigo-100" /> <span className="dark:text-indigo-100">Bounty (Credits)</span>*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 pt-3 left-0 pl-2 pr-2 flex items-center pointer-events-none">
              <img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-5 h-5 object-contain" />
            </div>
            <input
              type="number"
              id="bounty"
              name="bounty"
              value={formData.bounty}
              onChange={handleChange}
              min="50"
              max="10000"
              className={`w-full border-2 ${errors.bounty ? 'border-red-400' : 'border-gray-200'} rounded-lg pl-8 pr-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
              required
            />
          </div>
          {errors.bounty && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.bounty}</motion.p>
          )}
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Amount of credits to pay each participant (50-10,000)
              </p>
            </div>

        {/* Slots Field */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="slotsTotal" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaListAlt className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Number of Participants</span>*
          </label>
          <input
            type="number"
            id="slotsTotal"
            name="slotsTotal"
            value={formData.slotsTotal}
            onChange={handleChange}
            min="1"
            max="20"
            className={`w-full border-2 ${errors.slotsTotal ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
            required
          />
          {errors.slotsTotal && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.slotsTotal}</motion.p>
          )}
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            How many participants do you need? (1-20)
          </p>
      </div>

        {/* Dates Fields */}
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
            <label htmlFor="date" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
              <FaCheckCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Start Date & Time</span>*
          </label>
          <input
            type="datetime-local"
            id="date"
            name="date"
            value={formData.date}
            onChange={handleChange}
            min={new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16)}
              className={`w-full border-2 ${errors.date ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
            required
          />
          {errors.date && (
              <motion.p
                className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >{errors.date}</motion.p>
          )}
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Mission must start at least 30 minutes from now
          </p>
        </div>
          <div className="flex-1 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
            <label htmlFor="endDate" className="block text-sm font-semibold text-gray-700 dark:text-indigo-100 mb-2 flex items-center">
              <FaCheckCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">End Date & Time</span>*
          </label>
          <input
            type="datetime-local"
            id="endDate"
            name="endDate"
            value={formData.endDate}
            onChange={handleChange}
            min={formData.date || new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16)}
              className={`w-full border-2 ${errors.endDate ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium transition-all duration-200 bg-white/80 dark:bg-gray-900 dark:text-gray-100`}
            required
          />
          {errors.endDate && (
              <motion.p
                className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >{errors.endDate}</motion.p>
          )}
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Mission must be at least 30 minutes long
          </p>
          {formData.date && formData.endDate && (
              <div className="mt-3 p-2 bg-green-50 dark:bg-green-900 rounded-lg text-xs text-green-700 dark:text-green-200">
                <strong>Duration:</strong> {Math.round((new Date(formData.endDate) - new Date(formData.date)) / (1000 * 60))} minutes
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Validation summary */}
      {Object.keys(errors).length > 0 && (
        <motion.div
          className="mt-8 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-xl max-w-2xl mx-auto"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <p className="text-sm text-red-700 font-semibold mb-2 flex items-center">
            <FaInfoCircle className="mr-2 text-red-400 dark:text-red-300" /> Please fix the following issues:
          </p>
          <ul className="text-xs text-red-600 list-disc list-inside">
            {Object.values(errors).map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </motion.div>
      )}

      <div className="flex justify-end mt-10">
        <motion.button
          type="button"
          onClick={handleNext}
          disabled={!isStepValid() || isValidating}
          className={`px-8 py-3 rounded-xl font-semibold flex items-center shadow-md transition-all duration-200 text-base focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
            isStepValid() && !isValidating
              ? 'bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-500 dark:hover:bg-yellow-400 dark:text-gray-900'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-800 dark:text-gray-400'
          }`}
          whileHover={isStepValid() && !isValidating ? { scale: 1.04 } : {}}
          whileTap={isStepValid() && !isValidating ? { scale: 0.97 } : {}}
        >
          {isValidating ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Validating...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
              Continue
            </>
          )}
        </motion.button>
      </div>
    </div>
  );
};

// Step 2: Mission Details Component
const MissionDetailsStep = ({
  formData,
  setFormData,
  goToNextStep,
  goToPreviousStep,
  serviceTypes = [],
  serviceStyles = [],
  levels = [],
  languages = []
}) => {
  const [errors, setErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;

    if (name === 'theme') {
      const selectedType = serviceTypes.find(t => t.name === fieldValue);
      setFormData({
        ...formData,
        theme: fieldValue,
        service_type_id: selectedType ? selectedType.id : null
      });
    } else if (name === 'style') {
      const selectedStyle = serviceStyles.find(s => s.name === fieldValue);
      setFormData({
        ...formData,
        style: fieldValue,
        service_style_id: selectedStyle ? selectedStyle.id : null
      });
    } else {
      setFormData({
        ...formData,
        [name]: fieldValue
      });
    }

    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleNestedChange = (parent, key, value) => {
    if (parent === 'levelRequirement') {
      if (key === 'min') {
        if (value === '') {
          setFormData({
            ...formData,
            [parent]: {
              ...formData[parent],
              [key]: ''
            },
            min_level_id: null
          });
          return;
        }

        const level = levels.find(l => l.level === parseInt(value));
        if (level) {
          setFormData({
            ...formData,
            [parent]: {
              ...formData[parent],
              [key]: parseInt(value)
            },
            min_level_id: level.id
          });
          return;
        }
      }
      if (key === 'max') {
        if (value === '') {
          setFormData({
            ...formData,
            [parent]: {
              ...formData[parent],
              [key]: ''
            },
            max_level_id: null
          });
          return;
        }

        const level = levels.find(l => l.level === parseInt(value));
        if (level) {
          setFormData({
            ...formData,
            [parent]: {
              ...formData[parent],
              [key]: parseInt(value)
            },
            max_level_id: level.id
          });
          return;
        }
      }
    }
    setFormData({
      ...formData,
      [parent]: {
        ...formData[parent],
        [key]: value
      }
    });
  };

  const handleArrayChange = (field, index, value) => {
    const newArray = [...formData[field]];
    newArray[index] = value;
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  const addArrayItem = (field) => {
    setFormData({
      ...formData,
      [field]: [...formData[field], '']
    });
  };

  const removeArrayItem = (field, index) => {
    const newArray = [...formData[field]];
    newArray.splice(index, 1);
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  const validateStep = () => {
    const newErrors = {};
    if (!formData.theme || formData.theme.trim() === '') {
      newErrors.theme = 'Game/Theme is required';
    }
    if (!formData.style || formData.style.trim() === '') {
      newErrors.style = 'Mission Style is required';
    }
    if (!formData.language || formData.language.trim() === '') {
      newErrors.language = 'Language is required';
    }
    if (!formData.service_type_id) {
      newErrors.service_type_id = 'Service type is required';
    }
    if (!formData.service_style_id) {
      newErrors.service_style_id = 'Service style is required';
    }
    if (!formData.min_level_id) {
      newErrors.min_level_id = 'Minimum level is required';
    }
    if (!formData.max_level_id) {
      newErrors.max_level_id = 'Maximum level is required';
    }
    if (
      formData.levelRequirement.min &&
      formData.levelRequirement.max &&
      parseInt(formData.levelRequirement.max) <= parseInt(formData.levelRequirement.min)
    ) {
      newErrors.max_level_id = 'Maximum level must be higher than minimum level';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isStepValid = () => {
    return (
      formData.theme && formData.theme.trim() !== '' &&
      formData.style && formData.style.trim() !== '' &&
      formData.language && formData.language.trim() !== '' &&
      formData.service_type_id != null && formData.service_type_id !== '' &&
      formData.service_style_id != null && formData.service_style_id !== '' &&
      formData.min_level_id != null && formData.min_level_id !== '' &&
      formData.max_level_id != null && formData.max_level_id !== ''
    );
  };

  const handleNext = () => {
    setIsValidating(true);
    if (validateStep()) {
      goToNextStep();
    }
    setIsValidating(false);
  };

  return (
    <div className="p-0 sm:p-8 w-full max-w-4xl mx-auto">
      <div className="flex flex-col gap-8">
        {/* Game/Theme */}
        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="theme" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaListAlt className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Game/Theme</span>*
          </label>
          <select
            id="theme"
            name="theme"
            value={formData.theme}
            onChange={handleChange}
            className={`w-full border-2 ${errors.theme ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200`}
            required
          >
            {serviceTypes.length > 0 ? (
              serviceTypes.map(type => (
                <option key={type.id} value={type.name}>
                  {type.name}
                </option>
              ))
            ) : (
              <option value="">Loading...</option>
            )}
          </select>
          {errors.theme && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.theme}</motion.p>
          )}
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            The game or activity for this mission
          </p>
        </div>
        {/* Mission Style */}
        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label htmlFor="style" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaCheckCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Mission Style</span>*
          </label>
          <select
            id="style"
            name="style"
            value={formData.style}
            onChange={handleChange}
            className={`w-full border-2 ${errors.style ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200`}
            required
          >
            {serviceStyles.length > 0 ? (
              serviceStyles.map(style => (
                <option key={style.id} value={style.name}>
                  {style.name}
                </option>
              ))
            ) : (
              <>
                <option value="Casual">Casual</option>
                <option value="Competitive">Competitive</option>
                <option value="Ranked">Ranked</option>
                <option value="Tournament">Tournament</option>
                <option value="Training">Training</option>
              </>
            )}
          </select>
          {errors.style && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >{errors.style}</motion.p>
          )}
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            The type of gameplay experience
          </p>
        </div>
        {/* Platform & Language */}
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
            <label htmlFor="platform" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
              <FaListAlt className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Platform</span>
          </label>
          <input
            type="text"
            id="platform"
            name="platform"
            value={formData.platform}
            onChange={handleChange}
              className="w-full border-2 border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200"
            placeholder="E.g., Mobile, PC, Console"
          />
        </div>
          <div className="flex-1 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
            <label htmlFor="language" className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
              <FaCheckCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Language</span>*
          </label>
            {(!languages || languages.length === 0) ? (
              <div className="text-xs text-gray-500 py-2">Loading languages...</div>
            ) : (
              <select
            id="language"
            name="language"
            value={formData.language}
            onChange={handleChange}
                className={`w-full border-2 ${errors.language ? 'border-red-400' : 'border-gray-200'} rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200`}
            required
              >
                {languages.map(lang => (
                  <option key={lang.code || lang.id} value={lang.name}>{lang.name}</option>
                ))}
              </select>
            )}
            {errors.language && (
              <motion.p
                className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
                initial={{ opacity: 0, y: -5 }}
                animate={{ opacity: 1, y: 0 }}
              >{errors.language}</motion.p>
            )}
        </div>
      </div>
        {/* Level Requirement */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaListAlt className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Level Requirement</span>
        </label>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <label htmlFor="levelMin" className="block text-xs text-gray-500 dark:text-indigo-100 mb-1">Minimum Level</label>
            <select
              id="levelMin"
                value={formData.levelRequirement.min === undefined || formData.levelRequirement.min === null ? '' : formData.levelRequirement.min}
              onChange={(e) => handleNestedChange('levelRequirement', 'min', e.target.value)}
                className="w-full border-2 border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200"
            >
                <option value="">Select minimum level</option>
                {levels.length > 0 && levels.map(level => (
                  <option key={level.id} value={level.level}>
                    {level.name} (Level {level.level})
                  </option>
                ))}
            </select>
          </div>
            <div className="flex-1">
              <label htmlFor="levelMax" className="block text-xs text-gray-500 dark:text-indigo-100 mb-1">Maximum Level</label>
            <select
              id="levelMax"
                value={formData.levelRequirement.max || ''}
              onChange={(e) => handleNestedChange('levelRequirement', 'max', e.target.value)}
                className="w-full border-2 border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200"
            >
                <option value="">Select maximum level</option>
                {levels.length > 0 && levels.map(level => (
                  <option key={level.id} value={level.level}>
                    {level.name} (Level {level.level})
                  </option>
                ))}
            </select>
          </div>
        </div>
          {(errors.min_level_id || errors.max_level_id) && (
            <motion.p
              className="mt-2 text-sm text-red-600 bg-red-50 rounded-lg px-3 py-2"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
            >
              {errors.min_level_id || errors.max_level_id}
            </motion.p>
          )}
          <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Set the level range for participants
        </p>
      </div>
        {/* Requirements */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaCheckCircle className="mr-2 text-indigo-500" /> <span className="dark:text-indigo-100">Requirements</span>
        </label>
        {formData.requirements.map((req, index) => (
          <div key={index} className="flex mb-2">
            <input
              type="text"
              value={req}
              onChange={(e) => handleArrayChange('requirements', index, e.target.value)}
                className="flex-1 border-2 border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200"
              placeholder="E.g., Microphone required"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('requirements', index)}
                className="ml-2 text-red-500 bg-transparent hover:rounded-2xl hover:bg-indigo-50 dark:hover:bg-gray-800 hover:text-red-700 dark:hover:text-red-400"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('requirements')}
            className="mt-2 text-indigo-600 bg-transparent hover:rounded-2xl hover:bg-indigo-50 dark:hover:bg-gray-800 hover:text-indigo-800 dark:text-yellow-400 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Requirement
        </button>
      </div>
        {/* Tags */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <label className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
            <FaListAlt className="mr-2 text-indigo-500" /> Tags
        </label>
        {formData.tags.map((tag, index) => (
          <div key={index} className="flex mb-2">
            <input
              type="text"
              value={tag}
              onChange={(e) => handleArrayChange('tags', index, e.target.value)}
                className="flex-1 border-2 border-gray-200 dark:border-gray-700 rounded-lg px-4 py-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-base font-medium bg-white/80 dark:bg-gray-900 dark:text-gray-100 transition-all duration-200"
              placeholder="E.g., Tournament"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('tags', index)}
                className="ml-2 text-red-500 bg-transparent hover:rounded-2xl hover:bg-indigo-50 dark:hover:bg-gray-800 hover:text-red-700 dark:hover:text-red-400"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('tags')}
            className="mt-2 text-indigo-600 bg-transparent hover:rounded-2xl hover:bg-indigo-50 dark:hover:bg-gray-800 hover:text-indigo-800 dark:text-yellow-400 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Tag
        </button>
      </div>
      {/* Hide Profile Toggle */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-6 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800 flex items-center">
        <input
          type="checkbox"
          id="is_anonymous"
          name="is_anonymous"
          checked={formData.is_anonymous}
          onChange={handleChange}
          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-700 rounded"
        />
        <label htmlFor="is_anonymous" className="ml-2 block text-sm text-gray-700 dark:text-gray-200">
          Hide Profile and Nickname
        </label>
      </div>
      </div>
      {/* Validation summary */}
      {Object.keys(errors).length > 0 && (
        <motion.div
          className="mt-8 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-xl max-w-2xl mx-auto"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <p className="text-sm text-red-700 font-semibold mb-2 flex items-center">
            <FaInfoCircle className="mr-2 text-red-400 dark:text-red-300" /> Please fix the following issues:
          </p>
          <ul className="text-xs text-red-600 list-disc list-inside">
            {Object.values(errors).map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </motion.div>
      )}
      <div className="flex justify-between mt-10">
        <motion.button
          type="button"
          onClick={goToPreviousStep}
          className="px-8 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
          whileHover={{ scale: 1.04 }}
          whileTap={{ scale: 0.97 }}
        >
          Back
        </motion.button>
        <motion.button
          type="button"
          onClick={handleNext}
          disabled={!isStepValid() || isValidating}
          className={`px-8 py-3 rounded-xl font-semibold flex items-center shadow-md transition-all duration-200 text-base focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
            isStepValid() && !isValidating
              ? 'bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-500 dark:hover:bg-yellow-400 dark:text-gray-900'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-800 dark:text-gray-400'
          }`}
          whileHover={isStepValid() && !isValidating ? { scale: 1.04 } : {}}
          whileTap={isStepValid() && !isValidating ? { scale: 0.97 } : {}}
        >
          {isValidating ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Validating...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
          Continue
            </>
          )}
        </motion.button>
      </div>
    </div>
  );
};

// Step 3: Mission Images Component
const MissionImagesStep = ({ formData, setFormData, goToNextStep, goToPreviousStep, missionId = null }) => {
  const handleImagesChange = (images) => {
    setFormData({
      ...formData,
      images
    });
  };

  return (
    <div className="p-0 sm:p-8 w-full max-w-2xl mx-auto">
      <div className="flex flex-col gap-8">
        <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl p-8 shadow-md dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <div className="flex items-center mb-4">
            <FaImage className="w-6 h-6 text-indigo-500 mr-3" />
            <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100">Mission Images</h3>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Upload images that showcase your mission. The first image will be used as the primary image.
        </p>
        <MissionImageUploader
          images={formData.images}
          onImagesChange={handleImagesChange}
          maxImages={5}
          maxSizeMB={5}
          missionId={missionId}
        />
          <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center mb-2">
              <svg className="w-4 h-4 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
              <span className="font-semibold text-indigo-700 dark:text-yellow-400">Tips for great mission images:</span>
            </div>
          <ul className="list-disc pl-6 mt-1 space-y-1">
            <li>Use high-quality, clear images</li>
            <li>Show gameplay or relevant content</li>
            <li>Avoid text-heavy images</li>
            <li>Ensure images are appropriate and follow community guidelines</li>
          </ul>
        </div>
      </div>
        <div className="flex justify-between mt-10">
          <motion.button
          type="button"
          onClick={goToPreviousStep}
            className="px-8 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200"
            whileHover={{ scale: 1.04 }}
            whileTap={{ scale: 0.97 }}
        >
          Back
          </motion.button>
          <motion.button
          type="button"
          onClick={goToNextStep}
            className="px-8 py-3 rounded-xl font-semibold flex items-center shadow-md transition-all duration-200 text-base focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-500 dark:hover:bg-yellow-400 dark:text-gray-900"
            whileHover={{ scale: 1.04 }}
            whileTap={{ scale: 0.97 }}
        >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          Continue
          </motion.button>
        </div>
      </div>
    </div>
  );
};

// Step 4: Review and Submit Component
const ReviewSubmitStep = ({ formData, goToPreviousStep, handleSubmit, isSubmitting }) => {
  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);

  useEffect(() => {
    const checkBalance = async () => {
      try {
        const hasSufficient = await missionPaymentService.checkSufficientBalance(formData.bounty * formData.slotsTotal);
        setHasSufficientBalance(hasSufficient);
      } catch (error) {
        console.error('Error checking wallet balance:', error);
      }
    };
    checkBalance();
  }, [formData.bounty, formData.slotsTotal]);

  return (
    <div className="p-0 sm:p-8 w-full max-w-6xl xl:max-w-6xl mx-auto">
      <div className="flex flex-col gap-12">
        {/* Mission Overview Section */}
        <section className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-3xl p-10 shadow-lg dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <div className="flex items-center mb-8">
            <FaInfoCircle className="w-7 h-7 text-indigo-500 mr-4" />
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight dark:text-gray-100">Review Your Mission</h2>
          </div>
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 items-start">
        {/* Mission Preview */}
            <div className="rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-800 bg-white/90 backdrop-blur-lg p-6 flex flex-col items-center xl:items-start w-full">
              <h3 className="text-lg font-bold text-indigo-700 dark:text-yellow-400 mb-4 flex items-center">
                <FaImage className="w-5 h-5 mr-2 text-indigo-400" /> Mission Preview
              </h3>
              <div className="w-full">
          <MissionPreview mission={formData} />
        </div>
                </div>
            {/* Mission Details */}
            <div className="rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-800 bg-white/90 backdrop-blur-lg p-6 w-full">
              <h3 className="text-lg font-bold mb-6 flex items-center bg-gradient-to-r from-indigo-50 to-blue-50 px-4 py-2 rounded-xl text-indigo-700">
                <FaListAlt className="w-5 h-5 mr-2 text-indigo-400" /> Mission Details
              </h3>
              <div className="flex flex-col gap-10">
                {/* Basic Info Grid + Mission Details Table */}
                <div>
                  <h4 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                    <FaInfoCircle className="text-indigo-400" /> Mission Information
                  </h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden bg-white/80 dark:bg-gray-900/80">
                      <tbody>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Title</td>
                          <td className="px-4 py-3 text-lg font-bold text-gray-900 dark:text-gray-100 break-words">{formData.title || 'Not provided'}</td>
                        </tr>
                        <tr className="even:bg-indigo-50 dark:even:bg-gray-800/60 items-center">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Bounty</td>
                          <td className="px-4 py-3 text-lg font-bold text-indigo-700 dark:text-yellow-400 flex items-center"><img src="/In-AppAssets/xcoin.png" alt="Credits" className="w-4 h-4 mr-1 object-contain" />{formData.bounty}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Slots</td>
                          <td className="px-4 py-3 text-lg font-semibold text-gray-800 dark:text-gray-100">{formData.slotsTotal}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Start Date & Time</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-700 dark:text-gray-200">{new Date(formData.date).toLocaleString()}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">End Date & Time</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-700 dark:text-gray-200">{new Date(formData.endDate).toLocaleString()}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Game/Theme</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.theme || 'Not provided'}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Style</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.style}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Platform</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.platform || 'Not provided'}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Language</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.language}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Level Requirement</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.levelRequirement.min && formData.levelRequirement.max ? `LV${formData.levelRequirement.min}-LV${formData.levelRequirement.max}` : 'Not provided'}</td>
                        </tr>
                        <tr className="even:bg-indigo-50">
                          <td className="px-4 py-3 font-bold text-xs text-gray-500 uppercase tracking-wide whitespace-nowrap">Hide Profile & Nickname</td>
                          <td className="px-4 py-3 text-base font-medium text-gray-900 dark:text-gray-100">{formData.is_anonymous ? 'Yes' : 'No'}</td>
                        </tr>
                      </tbody>
                    </table>
                </div>
                </div>
                <hr className="my-2 border-gray-200" />
                {/* Mission Details Grid */}
                <div>
                  <h4 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                    <FaListAlt className="text-indigo-400" /> Mission Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-12">
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Game/Theme</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.theme || 'Not provided'}</span>
                </div>
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Style</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.style}</span>
                </div>
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Platform</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.platform || 'Not provided'}</span>
              </div>
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Language</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.language}</span>
            </div>
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Level Requirement</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.levelRequirement.min && formData.levelRequirement.max ? `LV${formData.levelRequirement.min}-LV${formData.levelRequirement.max}` : 'Not provided'}</span>
                </div>
                    <div className="flex items-center gap-4">
                      <span className="w-40 text-xs font-bold text-gray-500 uppercase tracking-wide">Hide Profile & Nickname</span>
                      <span className="flex-1 text-base font-medium text-gray-900 dark:text-gray-100">{formData.is_anonymous ? 'Yes' : 'No'}</span>
                </div>
                </div>
                </div>
                <hr className="my-2 border-gray-200" />
                {/* Requirements */}
                <div>
                  <h4 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                    <FaCheckCircle className="text-indigo-400" /> Requirements
                  </h4>
              {formData.requirements.filter(r => r).length > 0 ? (
                    <ul className="list-disc pl-5 text-gray-800 dark:text-gray-200 text-sm">
                  {formData.requirements.filter(r => r).map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              ) : (
                    <div className="text-gray-500 dark:text-gray-400 text-sm">No requirements specified</div>
              )}
            </div>
                <hr className="my-2 border-gray-200" />
                {/* Tags */}
                <div>
                  <h4 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                    <FaListAlt className="text-indigo-400" /> Tags
                  </h4>
              <div className="flex flex-wrap gap-2">
                {formData.tags.filter(t => t).map((tag, index) => (
                      <span key={index} className="bg-indigo-100 dark:bg-yellow-900/40 text-indigo-800 dark:text-yellow-300 text-xs px-3 py-1 rounded-full font-semibold tracking-wide">
                    {tag}
                  </span>
                ))}
                {formData.tags.filter(t => t).length === 0 && (
                      <span className="text-gray-500 dark:text-gray-400 text-sm">No tags specified</span>
                )}
              </div>
            </div>
                {/* Images (if any) */}
            {formData.images && formData.images.length > 0 && (
                  <>
                    <hr className="my-2 border-gray-200" />
              <div>
                      <h4 className="text-base font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                        <FaImage className="text-indigo-400" /> Images
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {formData.images.map((image, index) => (
                          <div key={index} className={`relative aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden border-2 ${index === 0 ? 'border-indigo-400 dark:border-yellow-400' : 'border-gray-200 dark:border-gray-700'}`}>
                      <img
                        src={typeof image === 'string' ? image : URL.createObjectURL(image)}
                        alt={`Mission ${index + 1}`}
                        className="object-cover w-full h-full"
                      />
                      {index === 0 && (
                              <div className="absolute top-1 left-1 bg-indigo-500 dark:bg-yellow-500 text-white dark:text-gray-900 text-xs px-2 py-0.5 rounded shadow font-bold">
                          Primary
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
                  </>
            )}
          </div>
        </div>
      </div>
        </section>
      {/* Payment Summary Section */}
        <section className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-3xl p-10 shadow-lg dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
          <h3 className="text-xl font-bold text-indigo-700 dark:text-yellow-400 mb-6 flex items-center">
            <FaCheckCircle className="mr-3 text-indigo-400" /> Payment Details
          </h3>
          <div className="flex flex-col lg:flex-row gap-8">
            <div className="flex-1">
          <MissionPaymentSummary
            bounty={formData.bounty}
            totalAmount={formData.bounty * formData.slotsTotal}
            paymentType="creation"
          />
            </div>
            <div className="flex-1">
          <WalletBalanceCard
            requiredAmount={formData.bounty * formData.slotsTotal}
            showAddCredits={true}
            actionType="create"
          />
        </div>
      </div>
        </section>
        {/* Platform Fee Summary */}
        <section className="bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-gray-900 dark:to-gray-800 rounded-3xl p-8 shadow dark:shadow-indigo-900/30 border border-indigo-100 dark:border-gray-800">
        <MissionPlatformFeeSummary
          bounty={formData.bounty}
          paxRequired={formData.slotsTotal}
          platformFeePercentage={10}
          platformFeeAmount={0}
          totalAmount={formData.bounty * formData.slotsTotal}
        />
        </section>
        {/* Terms and Conditions */}
        <section className="bg-yellow-50 dark:bg-yellow-900/30 border-l-4 border-yellow-400 dark:border-yellow-600 p-6 rounded-2xl">
          <div className="flex items-center">
            <svg className="h-6 w-6 text-yellow-400 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="text-base text-yellow-800 dark:text-yellow-200 font-medium">
              By creating this mission, you agree to our Terms of Service and Community Guidelines.
            </span>
          </div>
        </section>
        </div>
      {/* Sticky Action Bar (desktop only) */}
      <div className="hidden md:flex fixed bottom-0 left-0 w-full justify-center z-40 pointer-events-none">
        <div className="pointer-events-auto max-w-3xl xl:max-w-4xl w-full px-4 pb-6 flex justify-between gap-4">
          <motion.button
            type="button"
            onClick={goToPreviousStep}
            className="px-10 py-4 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-2xl font-bold bg-white/90 dark:bg-gray-900/90 shadow-lg dark:shadow-indigo-900/30 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 text-lg"
            whileHover={{ scale: 1.04 }}
            whileTap={{ scale: 0.97 }}
          >
            Back
          </motion.button>
          <motion.button
            type="submit"
            onClick={handleSubmit}
            className={`px-10 py-4 rounded-2xl font-bold flex items-center shadow-lg dark:shadow-indigo-900/30 transition-all duration-200 text-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
              isSubmitting || !hasSufficientBalance
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-800 dark:text-gray-400'
                : 'bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-500 dark:hover:bg-yellow-400 dark:text-gray-900'
            }`}
            disabled={isSubmitting || !hasSufficientBalance}
            whileHover={!(isSubmitting || !hasSufficientBalance) ? { scale: 1.04 } : {}}
            whileTap={!(isSubmitting || !hasSufficientBalance) ? { scale: 0.97 } : {}}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating Mission...
              </>
            ) : !hasSufficientBalance ? (
              'Insufficient Balance'
            ) : (
              <>
                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
                Create Mission
              </>
            )}
          </motion.button>
      </div>
      </div>
      {/* Fallback action bar for mobile */}
      <div className="flex md:hidden justify-between mt-10 gap-4">
        <motion.button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 rounded-xl font-semibold bg-white/90 dark:bg-gray-900/90 shadow dark:shadow-indigo-900/30 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 text-base"
          whileHover={{ scale: 1.04 }}
          whileTap={{ scale: 0.97 }}
        >
          Back
        </motion.button>
        <motion.button
          type="submit"
          onClick={handleSubmit}
          className={`px-6 py-3 rounded-xl font-semibold flex items-center shadow transition-all duration-200 text-base focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
            isSubmitting || !hasSufficientBalance
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-800 dark:text-gray-400'
              : 'bg-indigo-600 text-white hover:bg-indigo-700 dark:bg-yellow-500 dark:hover:bg-yellow-400 dark:text-gray-900'
          }`}
          disabled={isSubmitting || !hasSufficientBalance}
          whileHover={!(isSubmitting || !hasSufficientBalance) ? { scale: 1.04 } : {}}
          whileTap={!(isSubmitting || !hasSufficientBalance) ? { scale: 0.97 } : {}}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Mission...
            </>
          ) : !hasSufficientBalance ? (
            'Insufficient Balance'
          ) : (
            <>
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
              Create Mission
            </>
          )}
        </motion.button>
      </div>
    </div>
  );
};

const stepConfig = [
  { id: 1, title: 'Basic Info', icon: <FaInfoCircle className="w-5 h-5" /> },
  { id: 2, title: 'Details', icon: <FaListAlt className="w-5 h-5" /> },
  { id: 3, title: 'Images', icon: <FaImage className="w-5 h-5" /> },
  { id: 4, title: 'Review', icon: <FaCheckCircle className="w-5 h-5" /> },
];

const MissionCreatePage = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);

  // Reference data
  const [serviceTypes, setServiceTypes] = useState([]);
  const [serviceStyles, setServiceStyles] = useState([]);
  const [levels, setLevels] = useState([]);
  const [languages, setLanguages] = useState([]);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    bounty: 100,
    date: '',
    endDate: '',
    slotsTotal: 2,
    levelRequirement: { min: '', max: 99 },
    style: 'Casual',
    theme: '',
    category: 'Game',
    platform: '',
    language: 'English',
    requirements: [''],
    tags: [''],
    is_anonymous: false,
    payment_method: 'wallet',
    // Image data
    images: [],
    // API-specific fields
    service_type_id: null,
    service_style_id: null,
    min_level_id: null,
    max_level_id: null
  });

  // Store the created mission ID
  const [createdMissionId, setCreatedMissionId] = useState(null);

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch service types, styles, levels, and languages
        const [typesResponse, stylesResponse, levelsResponse, languagesResponse] = await Promise.all([
          referenceDataApi.getServiceTypes(),
          referenceDataApi.getServiceStyles(),
          referenceDataApi.getLevels(),
          referenceDataApi.getLanguages()
        ]);

        // Set reference data
        setServiceTypes(typesResponse.data || []);
        setServiceStyles(stylesResponse.data || []);
        setLevels(levelsResponse.data.levels || []);
        setLanguages(languagesResponse.data || []);

        // Set default values if available
        if (typesResponse.data && typesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            service_type_id: typesResponse.data[0].id,
            theme: typesResponse.data[0].name
          }));
        }

        if (stylesResponse.data && stylesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            service_style_id: stylesResponse.data[0].id,
            style: stylesResponse.data[0].name
          }));
        }

        if (levelsResponse.data && levelsResponse.data.length > 0) {
          const lastLevel = levelsResponse.data[levelsResponse.data.length - 1];
          setFormData(prev => ({
            ...prev,
            min_level_id: null,
            max_level_id: lastLevel.id,
            levelRequirement: {
              min: '',
              max: lastLevel.level
            }
          }));
        }
      } catch (error) {
        console.error('Error fetching reference data:', error);
        setError(getApiErrorMessage(error));
        toast.error('Failed to load reference data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferenceData();
  }, [toast]);

  // Go to next step
  const goToNextStep = () => {
    setCurrentStep(currentStep + 1);
    window.scrollTo(0, 0);
  };

  // Go to previous step
  const goToPreviousStep = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo(0, 0);
  };

  // Update handleSubmit to use FormData for file upload
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formDataPayload = new FormData();

      // Append all fields except images
      formDataPayload.append('title', formData.title);
      formDataPayload.append('service_type_id', formData.service_type_id);
      formDataPayload.append('service_style_id', formData.service_style_id);
      formDataPayload.append('description', formData.description);
      formDataPayload.append('bounty', formData.bounty);
      formDataPayload.append('pax_required', formData.slotsTotal);
      formDataPayload.append('min_level_id', formData.min_level_id);
      formDataPayload.append('max_level_id', formData.max_level_id);
      formDataPayload.append('service_start_date', formData.date);
      formDataPayload.append('service_end_date', formData.endDate);
      formDataPayload.append('is_anonymous', formData.is_anonymous ? 1 : 0);

      // Append only File objects to images[]
      (formData.images || []).forEach(img => {
        if (img instanceof File) {
          formDataPayload.append('images[]', img);
        }
      });

      // Use the correct API call for multipart/form-data
      const response = await missionApi.createMission(formDataPayload);

      // Store the created mission ID
      const missionId = response.data?.mission?.id || response.data.id;
      setCreatedMissionId(missionId);

      // Show success message
      setIsSubmitting(false);
      setShowSuccess(true);

      // Show success toast
      toast.success('Mission created successfully!');

      // Redirect to missions page after showing success message
      setTimeout(() => {
        navigate('/missions/my-missions');
      }, 2000);
    } catch (error) {
      console.error('Error creating mission:', error);
      setIsSubmitting(false);
      toast.error(getApiErrorMessage(error) || 'Failed to create mission');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <MissionLayout title="Create Mission" backPath="/missions">
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </MissionLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <MissionLayout title="Create Mission" backPath="/missions">
        <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md mx-auto">
          <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Data</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </MissionLayout>
    );
  }

  return (
    <MissionLayout title="Create Mission" backPath="/missions">
      {/* Success Message */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl p-8 max-w-md text-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 500 }}
            >
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Mission Created!</h3>
              <p className="text-gray-600 mb-6">
                Your mission has been successfully created and is now available for talents to apply.
              </p>
              <div className="animate-pulse text-sm text-gray-500">
                Redirecting to your missions...
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
        {/* Progress Steps - Modern Animated */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-center w-full max-w-2xl mx-auto mb-4 overflow-x-auto pb-2">
            {stepConfig.map((step, idx) => (
              <motion.div
                key={step.id}
                className="flex items-center flex-shrink-0"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: idx * 0.08 }}
              >
                <motion.div
                  className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 text-lg font-bold shadow-md ${
                    currentStep > step.id
                      ? 'bg-indigo-500 border-indigo-500 text-white dark:bg-yellow-500 dark:border-yellow-500 dark:text-gray-900' :
                    currentStep === step.id
                      ? 'bg-gradient-to-br from-indigo-600 to-blue-500 border-indigo-600 text-white dark:bg-yellow-400 dark:border-yellow-400 dark:text-gray-900' :
                      'bg-white border-gray-300 text-gray-400 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400'
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {step.icon}
                </motion.div>
                <span className={`ml-2 text-sm font-semibold ${
                  currentStep >= step.id ? 'text-indigo-700 dark:text-yellow-400' : 'text-gray-400 dark:text-gray-400'
                } hidden sm:inline`}>{step.title}</span>
                {idx < stepConfig.length - 1 && (
                  <motion.div
                    className={`w-6 h-1 mx-2 rounded-full transition-all duration-300 ${
                      currentStep > step.id ? 'bg-indigo-500 dark:bg-yellow-400' : 'bg-gray-300 dark:bg-gray-700'
                    }`}
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{ delay: idx * 0.08 + 0.1, duration: 0.3 }}
                  />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Form Container - Modern Card */}
        <motion.div
          className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-gray-100 max-w-5xl xl:max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <form onSubmit={handleSubmit} className='max-w-5xl mx-auto px-2 sm:px-4 py-4'>
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <BasicInfoStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
              />
            )}
            {/* Step 2: Mission Details */}
            {currentStep === 2 && (
              <MissionDetailsStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
                goToPreviousStep={goToPreviousStep}
                serviceTypes={serviceTypes}
                serviceStyles={serviceStyles}
                levels={levels}
                languages={languages}
              />
            )}
            {/* Step 3: Mission Images */}
            {currentStep === 3 && (
              <MissionImagesStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
                goToPreviousStep={goToPreviousStep}
                missionId={createdMissionId}
              />
            )}
            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <ReviewSubmitStep
                formData={formData}
                goToPreviousStep={goToPreviousStep}
                handleSubmit={handleSubmit}
                isSubmitting={isSubmitting}
              />
            )}
          </form>
        </motion.div>
    </MissionLayout>
  );
};

export default MissionCreatePage;
