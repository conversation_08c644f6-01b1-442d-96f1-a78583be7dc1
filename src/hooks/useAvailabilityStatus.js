import { useState, useEffect } from 'react';
import { availabilityAPI } from '../services/availabilityService';

export const useAvailabilityStatus = () => {
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    availabilityAPI.getAvailabilityStatus()
      .then((data) => {
        if (isMounted) {
          setStatus(data.status);
          setLoading(false);
        }
      })
      .catch((err) => {
        if (isMounted) {
          setError(err);
          setLoading(false);
        }
      });
    return () => { isMounted = false; };
  }, []);

  return { status, loading, error };
};
