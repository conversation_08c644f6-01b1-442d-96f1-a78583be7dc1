import { useCallback } from 'react';
import { useSocial } from '../contexts/SocialContext';
import socialPostService from '../services/socialPostService';
import followService from '../services/followService';

// Custom hook for optimistic updates
export const useOptimisticUpdates = () => {
    const { 
        updatePostLike, 
        addPostComment, 
        updateFollowStatus, 
        showToast, 
        setLoading 
    } = useSocial();

    // Optimistic like/unlike post
    const optimisticLikePost = useCallback(async (postId, currentLiked, currentLikeCount) => {
        const newLiked = !currentLiked;
        const newLikeCount = newLiked ? currentLikeCount + 1 : currentLikeCount - 1;

        // Optimistic update
        updatePostLike(postId, newLiked, newLikeCount);

        try {
            // Make API call
            if (newLiked) {
                await socialPostService.likePost(postId);
                showToast({
                    type: 'success',
                    message: 'Post liked!',
                    duration: 2000
                });
            } else {
                await socialPostService.unlikePost(postId);
            }
        } catch (error) {
            console.error('Error updating like:', error);
            
            // Revert optimistic update
            updatePostLike(postId, currentLiked, currentLikeCount);
            
            showToast({
                type: 'error',
                title: 'Error',
                message: 'Failed to update like. Please try again.',
                duration: 4000
            });
        }
    }, [updatePostLike, showToast]);

    // Optimistic comment creation
    const optimisticCreateComment = useCallback(async (postId, content, parentId = null) => {
        const tempComment = {
            id: `temp-${Date.now()}`,
            post_id: postId,
            parent_id: parentId,
            content: content,
            created_at: new Date().toISOString(),
            user: {
                id: 'current-user',
                username: 'You',
                name: 'You',
                profile_picture: null
            },
            isOptimistic: true
        };

        // Optimistic update
        addPostComment(postId, tempComment);

        try {
            setLoading(`comment-${postId}`, true);
            
            // Make API call
            const newComment = await socialPostService.createComment(postId, content, parentId);
            
            // Replace optimistic comment with real comment
            // This would require a more sophisticated state management
            // For now, we'll just show success
            showToast({
                type: 'success',
                message: 'Comment posted!',
                duration: 2000
            });

            return newComment;
        } catch (error) {
            console.error('Error creating comment:', error);
            
            // Remove optimistic comment
            // This would require a remove comment action
            
            showToast({
                type: 'error',
                title: 'Error',
                message: 'Failed to post comment. Please try again.',
                duration: 4000
            });
            
            throw error;
        } finally {
            setLoading(`comment-${postId}`, false);
        }
    }, [addPostComment, showToast, setLoading]);

    // Optimistic follow/unfollow user
    const optimisticFollowUser = useCallback(async (userId, currentFollowing) => {
        const newFollowing = !currentFollowing;

        // Optimistic update
        updateFollowStatus(userId, newFollowing);

        try {
            // Make API call
            if (newFollowing) {
                await followService.followUser(userId);
                showToast({
                    type: 'success',
                    message: 'User followed!',
                    duration: 2000
                });
            } else {
                await followService.unfollowUser(userId);
                showToast({
                    type: 'info',
                    message: 'User unfollowed',
                    duration: 2000
                });
            }
        } catch (error) {
            console.error('Error updating follow status:', error);
            
            // Revert optimistic update
            updateFollowStatus(userId, currentFollowing);
            
            showToast({
                type: 'error',
                title: 'Error',
                message: 'Failed to update follow status. Please try again.',
                duration: 4000
            });
        }
    }, [updateFollowStatus, showToast]);

    // Batch operations for better performance
    const batchLikeUpdates = useCallback(async (updates) => {
        // Apply all optimistic updates first
        updates.forEach(({ postId, currentLiked, currentLikeCount }) => {
            const newLiked = !currentLiked;
            const newLikeCount = newLiked ? currentLikeCount + 1 : currentLikeCount - 1;
            updatePostLike(postId, newLiked, newLikeCount);
        });

        // Then make API calls
        const results = await Promise.allSettled(
            updates.map(async ({ postId, currentLiked }) => {
                if (!currentLiked) {
                    return await socialPostService.likePost(postId);
                } else {
                    return await socialPostService.unlikePost(postId);
                }
            })
        );

        // Handle any failures
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                const { postId, currentLiked, currentLikeCount } = updates[index];
                // Revert failed update
                updatePostLike(postId, currentLiked, currentLikeCount);
            }
        });

        const failedCount = results.filter(r => r.status === 'rejected').length;
        if (failedCount > 0) {
            showToast({
                type: 'warning',
                title: 'Partial Success',
                message: `${failedCount} like updates failed. Please try again.`,
                duration: 4000
            });
        }
    }, [updatePostLike, showToast]);

    // Real-time sync function (for future WebSocket integration)
    const syncWithServer = useCallback(async (postId) => {
        try {
            const post = await socialPostService.getPostById(postId);
            updatePost(post);
        } catch (error) {
            console.error('Error syncing with server:', error);
        }
    }, []);

    // Retry failed operations
    const retryOperation = useCallback(async (operation, ...args) => {
        const maxRetries = 3;
        let retries = 0;

        while (retries < maxRetries) {
            try {
                return await operation(...args);
            } catch (error) {
                retries++;
                if (retries === maxRetries) {
                    showToast({
                        type: 'error',
                        title: 'Operation Failed',
                        message: 'Please check your connection and try again.',
                        duration: 5000
                    });
                    throw error;
                }
                
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
            }
        }
    }, [showToast]);

    // Queue for offline operations (for future offline support)
    const queueOperation = useCallback((operation, args) => {
        // This would store operations in localStorage for offline support
        const queue = JSON.parse(localStorage.getItem('socialQueue') || '[]');
        queue.push({
            operation: operation.name,
            args,
            timestamp: Date.now()
        });
        localStorage.setItem('socialQueue', JSON.stringify(queue));
    }, []);

    // Process queued operations when back online
    const processQueue = useCallback(async () => {
        const queue = JSON.parse(localStorage.getItem('socialQueue') || '[]');
        
        for (const item of queue) {
            try {
                // Process each queued operation
                // This would need to map operation names to actual functions
                console.log('Processing queued operation:', item);
            } catch (error) {
                console.error('Error processing queued operation:', error);
            }
        }
        
        // Clear queue after processing
        localStorage.removeItem('socialQueue');
    }, []);

    return {
        optimisticLikePost,
        optimisticCreateComment,
        optimisticFollowUser,
        batchLikeUpdates,
        syncWithServer,
        retryOperation,
        queueOperation,
        processQueue,
    };
};

export default useOptimisticUpdates;
