import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the user prefers reduced motion
 * This respects the user's system preferences for reduced motion
 * 
 * @returns {boolean} - Whether the user prefers reduced motion
 */
const useReducedMotion = () => {
  // Default to false (normal motion) if the media query isn't supported
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    // Check if the browser supports matchMedia
    if (typeof window === 'undefined' || !window.matchMedia) {
      return;
    }

    // Create a media query that detects if the user prefers reduced motion
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    // Set the initial value
    setPrefersReducedMotion(mediaQuery.matches);

    // Create a handler function to update the state when the preference changes
    const handleChange = (event) => {
      setPrefersReducedMotion(event.matches);
    };

    // Add an event listener to detect changes in the preference
    if (mediaQuery.addEventListener) {
      // Modern browsers
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Older browsers
      mediaQuery.addListener(handleChange);
    }

    // Clean up the event listener when the component unmounts
    return () => {
      if (mediaQuery.removeEventListener) {
        // Modern browsers
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);

  return prefersReducedMotion;
};

export default useReducedMotion;
