import { useMutation, useQueryClient } from '@tanstack/react-query';
import { profileAPI } from '../services/api';
import { profileKeys } from './useProfileData';

// Hook for updating user profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => profileAPI.updateProfile(data),
    onMutate: async (newData) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: profileKeys.user(newData.userId) });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData(profileKeys.user(newData.userId));

      // Optimistically update to the new value
      queryClient.setQueryData(profileKeys.user(newData.userId), (old) => ({
        ...old,
        ...newData,
      }));

      // Return a context object with the snapshotted value
      return { previousData };
    },
    onError: (err, newData, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(
        profileKeys.user(newData.userId),
        context.previousData
      );
    },
    onSettled: (data) => {
      // Always refetch after error or success to ensure cache is in sync
      queryClient.invalidateQueries({ queryKey: profileKeys.user(data.userId) });
    },
  });
};

// Hook for updating profile picture
export const useUpdateProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => profileAPI.updateProfilePicture(data),
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: profileKeys.user(newData.userId) });
      const previousData = queryClient.getQueryData(profileKeys.user(newData.userId));

      queryClient.setQueryData(profileKeys.user(newData.userId), (old) => ({
        ...old,
        profilePicture: newData.profilePicture,
      }));

      return { previousData };
    },
    onError: (err, newData, context) => {
      queryClient.setQueryData(
        profileKeys.user(newData.userId),
        context.previousData
      );
    },
    onSettled: (data) => {
      queryClient.invalidateQueries({ queryKey: profileKeys.user(data.userId) });
    },
  });
};

// Hook for updating profile cover
export const useUpdateProfileCover = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => profileAPI.updateProfileCover(data),
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: profileKeys.user(newData.userId) });
      const previousData = queryClient.getQueryData(profileKeys.user(newData.userId));

      queryClient.setQueryData(profileKeys.user(newData.userId), (old) => ({
        ...old,
        coverPicture: newData.coverPicture,
      }));

      return { previousData };
    },
    onError: (err, newData, context) => {
      queryClient.setQueryData(
        profileKeys.user(newData.userId),
        context.previousData
      );
    },
    onSettled: (data) => {
      queryClient.invalidateQueries({ queryKey: profileKeys.user(data.userId) });
    },
  });
};

// Hook for updating profile settings
export const useUpdateProfileSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data) => profileAPI.updateProfileSettings(data),
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: profileKeys.user(newData.userId) });
      const previousData = queryClient.getQueryData(profileKeys.user(newData.userId));

      queryClient.setQueryData(profileKeys.user(newData.userId), (old) => ({
        ...old,
        settings: newData.settings,
      }));

      return { previousData };
    },
    onError: (err, newData, context) => {
      queryClient.setQueryData(
        profileKeys.user(newData.userId),
        context.previousData
      );
    },
    onSettled: (data) => {
      queryClient.invalidateQueries({ queryKey: profileKeys.user(data.userId) });
    },
  });
}; 