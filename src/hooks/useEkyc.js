import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ekycService from '../services/ekycService';

export const useEkyc = () => {
  const queryClient = useQueryClient();

  // Query for verification status
  const useVerificationStatus = (verificationMethod = null) => {
    return useQuery({
      queryKey: ['ekyc', 'status', verificationMethod],
      queryFn: () => ekycService.getVerificationStatus(verificationMethod),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 30 * 60 * 1000, // 30 minutes
    });
  };

  // Query for verification history
  const useVerificationHistory = () => {
    return useQuery({
      queryKey: ['ekyc', 'history'],
      queryFn: () => ekycService.getVerificationHistory(),
      staleTime: 5 * 60 * 1000,
      cacheTime: 30 * 60 * 1000,
    });
  };

  // Mutation for Malaysian verification
  const useMalaysianVerification = () => {
    return useMutation({
      mutationFn: (formData) => ekycService.verifyMalaysian(formData),
      onSuccess: () => {
        // Invalidate and refetch verification status
        queryClient.invalidateQueries({ queryKey: ['ekyc', 'status'] });
        queryClient.invalidateQueries({ queryKey: ['ekyc', 'history'] });
      },
    });
  };

  // Mutation for Foreigner verification
  const useForeignerVerification = () => {
    return useMutation({
      mutationFn: (formData) => ekycService.verifyForeigner(formData),
      onSuccess: () => {
        // Invalidate and refetch verification status
        queryClient.invalidateQueries({ queryKey: ['ekyc', 'status'] });
        queryClient.invalidateQueries({ queryKey: ['ekyc', 'history'] });
      },
    });
  };

  return {
    useVerificationStatus,
    useVerificationHistory,
    useMalaysianVerification,
    useForeignerVerification,
  };
}; 