import { useInfiniteQuery } from '@tanstack/react-query';
import socialPostService from '../../services/socialPostService';
import { commentKeys } from '../../queryKeys/commentKeys';

export const usePostComments = (postId) => {
  return useInfiniteQuery({
    queryKey: commentKeys.list(postId),
    enabled: !!postId,
    queryFn: async ({ pageParam = 1 }) => {
      const response = await socialPostService.getPostComments(postId, pageParam, 10); // Fetch 10 comments per page
      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch comments');
      }
      return {
        comments: response.comments,
        has_more: response.has_more,
        next_page: response.next_page,
        total: response.total,
        current_page: response.current_page,
        last_page: response.last_page,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.has_more ? lastPage.next_page : undefined;
    },
    select: (data) => ({
      pages: data.pages,
      comments: data.pages.flatMap((page) => page.comments || []),
      hasMore: data.pages[data.pages.length - 1]?.has_more || false,
    }),
    staleTime: 1000 * 60, // 1 minute stale time for comments
    cacheTime: 1000 * 60 * 5, // 5 minutes cache time
    retry: 3,
  });
}; 