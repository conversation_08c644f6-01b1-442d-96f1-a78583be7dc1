import { useQuery } from '@tanstack/react-query';
import socialPostService from '../../services/socialPostService';

export const usePostQuery = (postId, options = {}) => {
  return useQuery({
    queryKey: ['socialPost', postId],
    queryFn: () => socialPostService.getPostById(postId),
    enabled: !!postId && (options.enabled ?? true),
    staleTime: 1000 * 60,
    cacheTime: 1000 * 60 * 5,
    ...options,
  });
};
