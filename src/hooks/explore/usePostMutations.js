import { useMutation, useQueryClient } from '@tanstack/react-query';
import socialPostService from '../../services/socialPostService';
import { exploreKeys } from '../../queryKeys/exploreKeys';
import { toast } from 'react-hot-toast';

export const useLikePost = (feedType) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ postId, isLiked }) => {
      if (isLiked) {
        return socialPostService.unlikePost(postId);
      } else {
        return socialPostService.likePost(postId);
      }
    },
    onMutate: async ({ postId, isLiked }) => {
      // Cancel any outgoing refetches for this feed and post
      await queryClient.cancelQueries({ queryKey: exploreKeys.feeds(feedType) });
      await queryClient.cancelQueries({ queryKey: exploreKeys.post(postId) });

      // Snapshot the previous query data for the feed
      const previousFeedData = queryClient.getQueryData(exploreKeys.feed(feedType, {}));

      // Optimistically update the feed data
      queryClient.setQueryData(exploreKeys.feed(feedType, {}), (old) => {
        if (!old) return old; // If no old data, return as is

        return {
          ...old,
          pages: old.pages.map((page) => ({
            ...page,
            posts: page.posts.map((post) =>
              post.id === postId
                ? {
                    ...post,
                    is_liked: !isLiked,
                    total_liked: isLiked ? post.total_liked - 1 : post.total_liked + 1,
                  }
                : post
            ),
          })),
        };
      });

      // Return a context object with the snapshotted value
      return { previousFeedData };
    },
    onError: (err, { postId, isLiked }, context) => {
      // If the mutation fails, use the context to roll back the feed data
      if (context?.previousFeedData) {
        queryClient.setQueryData(exploreKeys.feed(feedType, {}), context.previousFeedData);
      }
      if (err?.response?.status === 429) {
        toast.error('Rate limit exceeded. Please try again later.');
      }
      console.error('Error liking/unliking post:', err);
    },
    onSettled: (data, error, variables) => {
      // Refetch the specific post to ensure consistency
      queryClient.invalidateQueries({ queryKey: exploreKeys.post(variables.postId) });
    },
  });
}; 