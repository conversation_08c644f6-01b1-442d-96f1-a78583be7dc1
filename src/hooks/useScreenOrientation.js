import { useState, useEffect } from 'react';

/**
 * Custom hook to detect screen orientation
 * This helps optimize layouts for different device orientations
 * 
 * @returns {Object} - Object containing orientation information
 * @returns {string} - orientation - 'portrait' or 'landscape'
 * @returns {boolean} - isPortrait - Whether the device is in portrait mode
 * @returns {boolean} - isLandscape - Whether the device is in landscape mode
 */
const useScreenOrientation = () => {
  // Default to portrait if we can't detect orientation
  const [orientation, setOrientation] = useState('portrait');

  useEffect(() => {
    // Function to update orientation state
    const updateOrientation = () => {
      // Check if window is available (for SSR)
      if (typeof window === 'undefined') {
        return;
      }

      // Get screen orientation
      if (window.screen && window.screen.orientation) {
        // Modern API
        const currentOrientation = window.screen.orientation.type;
        setOrientation(
          currentOrientation.includes('portrait') ? 'portrait' : 'landscape'
        );
      } else if (window.matchMedia) {
        // Fallback to matchMedia for older browsers
        const isPortrait = window.matchMedia('(orientation: portrait)').matches;
        setOrientation(isPortrait ? 'portrait' : 'landscape');
      } else if (window.innerHeight > window.innerWidth) {
        // Last resort fallback
        setOrientation('portrait');
      } else {
        setOrientation('landscape');
      }
    };

    // Set initial orientation
    updateOrientation();

    // Add event listeners for orientation changes
    if (typeof window !== 'undefined') {
      if (window.screen && window.screen.orientation) {
        // Modern API
        window.screen.orientation.addEventListener('change', updateOrientation);
      } else {
        // Fallback for older browsers
        window.addEventListener('resize', updateOrientation);
      }
    }

    // Clean up event listeners
    return () => {
      if (typeof window !== 'undefined') {
        if (window.screen && window.screen.orientation) {
          window.screen.orientation.removeEventListener('change', updateOrientation);
        } else {
          window.removeEventListener('resize', updateOrientation);
        }
      }
    };
  }, []);

  return {
    orientation,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape'
  };
};

export default useScreenOrientation;
