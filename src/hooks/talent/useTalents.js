import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import talentService from '../../services/talentService';
import { talentKeys } from '../../queryKeys/talentKeys';
import { useToast } from '../../components/common/ToastProvider';

export const useTalents = (filters, page, perPage) => {
  return useQuery({
    queryKey: talentKeys.list(filters, page, perPage),
    queryFn: async () => {
      const response = await talentService.filterTalents(filters, page, perPage);
      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch talents');
      }
      return response;
    },
    keepPreviousData: true, // Keep previous data while fetching new data for pagination/filters
    staleTime: 1000 * 60 * 1, // 1 minute stale time for talent list
    cacheTime: 1000 * 60 * 5, // 5 minutes cache time
  });
};

export const useTalentFullProfile = (talentId, options = {}) => {
  return useQuery({
    queryKey: talentKeys.fullProfile(talentId),
    queryFn: async () => {
      const response = await talentService.getTalentFullProfile(talentId);
      return response;
    },
    enabled: !!talentId,
    staleTime: 1000 * 60 * 5, // 5 minutes stale time for full profiles
    cacheTime: 1000 * 60 * 10, // 10 minutes cache time
    retry: (failureCount, error) => {
      // Don't retry on 404 errors
      if (error?.response?.status === 404) return false;
      return failureCount < 3;
    },
    ...options,
  });
};

export const useTalentBasicProfile = (talentId, options = {}) => {
  return useQuery({
    queryKey: talentKeys.basicProfile(talentId),
    queryFn: async () => {
      const response = await talentService.getTalentBasicProfile(talentId);
      return response;
    },
    enabled: !!talentId,
    staleTime: 1000 * 60 * 3, // 3 minutes stale time for basic profiles
    cacheTime: 1000 * 60 * 5, // 5 minutes cache time
    retry: (failureCount, error) => {
      // Don't retry on 404 errors
      if (error?.response?.status === 404) return false;
      return failureCount < 2;
    },
    ...options,
  });
};

export const useServiceCategories = (options = {}) => {
  return useQuery({
    queryKey: talentKeys.serviceCategories(),
    queryFn: async () => {
      const response = await talentService.getServiceCategories();
      return response;
    },
    staleTime: 1000 * 60 * 30, // 30 minutes stale time for categories
    cacheTime: 1000 * 60 * 60, // 1 hour cache time
    ...options,
  });
};

export const useServiceTypes = (categoryId, options = {}) => {
  return useQuery({
    queryKey: talentKeys.serviceTypes(categoryId),
    queryFn: async () => {
      const response = await talentService.getServiceTypes(categoryId);
      return response;
    },
    enabled: !!categoryId,
    staleTime: 1000 * 60 * 15, // 15 minutes stale time for service types
    cacheTime: 1000 * 60 * 30, // 30 minutes cache time
    ...options,
  });
};

export const useServiceStyles = (categoryId, options = {}) => {
  return useQuery({
    queryKey: talentKeys.serviceStyles(categoryId),
    queryFn: async () => {
      const response = await talentService.getServiceStyles(categoryId);
      return response;
    },
    enabled: !!categoryId,
    staleTime: 1000 * 60 * 15, // 15 minutes stale time for service styles
    cacheTime: 1000 * 60 * 30, // 30 minutes cache time
    ...options,
  });
};

export const useRaces = (options = {}) => {
  return useQuery({
    queryKey: talentKeys.races(),
    queryFn: async () => {
      const response = await talentService.getRaces();
      return response;
    },
    staleTime: 1000 * 60 * 60, // 1 hour stale time for races
    cacheTime: 1000 * 60 * 120, // 2 hours cache time
    ...options,
  });
};

export const useLanguages = (options = {}) => {
  return useQuery({
    queryKey: talentKeys.languages(),
    queryFn: async () => {
      const response = await talentService.getLanguages();
      return response;
    },
    staleTime: 1000 * 60 * 60, // 1 hour stale time for languages
    cacheTime: 1000 * 60 * 120, // 2 hours cache time
    ...options,
  });
};

export const useTalentSearch = (searchTerm, filters = {}, page = 1, perPage = 15, options = {}) => {
  return useQuery({
    queryKey: talentKeys.search(searchTerm),
    queryFn: async () => {
      const searchFilters = { ...filters, searchTerm };
      const response = await talentService.filterTalents(searchFilters, page, perPage);
      if (response.error) {
        throw new Error(response.error.message || 'Failed to search talents');
      }
      return response;
    },
    enabled: !!searchTerm && searchTerm.trim().length > 0,
    keepPreviousData: true,
    staleTime: 1000 * 60 * 2, // 2 minutes stale time for search results
    cacheTime: 1000 * 60 * 5, // 5 minutes cache time
    ...options,
  });
};

// Mutations
export const useToggleFollow = () => {
  const queryClient = useQueryClient();
  const { success: showSuccessToast, error: showErrorToast } = useToast();

  return useMutation({
    mutationFn: async ({ talentId }) => {
      const response = await talentService.toggleFollow(talentId);
      return response;
    },
    onSuccess: (data, { talentId, isFollowing }) => {
      const finalState =
        typeof data?.is_following === 'boolean' ? data.is_following : isFollowing;
      // Invalidate and refetch talent lists
      queryClient.invalidateQueries({ queryKey: talentKeys.lists() });

      // Update individual talent profiles
      queryClient.invalidateQueries({ queryKey: talentKeys.fullProfile(talentId) });
      queryClient.invalidateQueries({ queryKey: talentKeys.basicProfile(talentId) });

      // Optimistically update the talent data
      queryClient.setQueryData(talentKeys.fullProfile(talentId), (oldData) => {
        if (oldData) {
          return {
            ...oldData,
            isFollowing: finalState,
            followers: finalState
              ? oldData.followers + 1
              : oldData.followers - 1,
          };
        }
        return oldData;
      });

      if (data?.message) {
        showSuccessToast(data.message);
      }
    },
    onError: (error, { talentId }) => {
      console.error('Error toggling follow:', error);
      queryClient.invalidateQueries({ queryKey: talentKeys.fullProfile(talentId) });
      queryClient.invalidateQueries({ queryKey: talentKeys.basicProfile(talentId) });
      showErrorToast('Failed to update follow status');
    },
  });
};
