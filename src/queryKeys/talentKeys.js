export const talentKeys = {
  all: ['talents'],
  lists: () => [...talentKeys.all, 'list'],
  list: (filters, page, perPage) => [...talentKeys.lists(), { ...filters, page, perPage }],
  details: () => [...talentKeys.all, 'detail'],
  detail: (id) => [...talentKeys.details(), id],
  fullProfile: (id) => [...talentKeys.all, 'fullProfile', id],
  basicProfile: (id) => [...talentKeys.all, 'basicProfile', id],
  filters: () => [...talentKeys.all, 'filters'],
  serviceTypes: (categoryId) => [...talentKeys.all, 'serviceTypes', categoryId],
  serviceStyles: (typeId) => [...talentKeys.all, 'serviceStyles', typeId],
  serviceCategories: () => [...talentKeys.all, 'serviceCategories'],
  races: () => [...talentKeys.all, 'races'],
  languages: () => [...talentKeys.all, 'languages'],
  search: (term) => [...talentKeys.all, 'search', term],
}; 