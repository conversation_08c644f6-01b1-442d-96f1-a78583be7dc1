// Scripts for firebase and firebase messaging
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration - using direct values for testing
const firebaseConfig = {
  apiKey: "AIzaSyBx0kU6BVuuOqFPpxbl6quihip9YtFfGp4",
  authDomain: "mission-x-my.firebaseapp.com",
  projectId: "mission-x-my",
  storageBucket: "mission-x-my.firebasestorage.app",
  messagingSenderId: "416785186609",
  appId: "1:416785186609:web:f10866d69dbb31d075fa10",
  measurementId: "G-8NP42P0S51"
};

firebase.initializeApp(firebaseConfig);

// Retrieve firebase messaging
const messaging = firebase.messaging();

// Handle background messages
// messaging.onBackgroundMessage(function(payload) {
//   console.log('Received background message', payload);

//   const notificationTitle = payload.notification.title;
//   const notificationOptions = {
//     body: payload.notification.body,
//     icon: '/logo192.png'
//   };

//   self.registration.showNotification(notificationTitle, notificationOptions);

//   // Post the notification payload to all open clients (tabs)
//   self.clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clients) {
//     clients.forEach(function(client) {
//       client.postMessage({
//         type: 'FCM_BACKGROUND_NOTIFICATION',
//         payload: payload
//       });
//     });
//   });
// });

// Handle notification click
self.addEventListener('notificationclick', function (event) {
  console.log('Notification clicked', event);

  event.notification.close();

  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients.matchAll({
      type: "window"
    })
      .then(function (clientList) {
        for (var i = 0; i < clientList.length; i++) {
          var client = clientList[i];
          if (client.url == '/' && 'focus' in client)
            return client.focus();
        }
        if (clients.openWindow)
          return clients.openWindow('/');
      })
  );
});
