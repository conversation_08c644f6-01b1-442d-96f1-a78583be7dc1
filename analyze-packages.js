#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const allDependencies = {
  ...packageJson.dependencies,
  ...packageJson.devDependencies
};

const installedPackages = Object.keys(allDependencies);
const usedPackages = new Set();
const importPatterns = new Set();

// Function to recursively find all JS/TS/JSX/TSX files
function findSourceFiles(dir, files = []) {
  const entries = fs.readdirSync(dir);
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !entry.startsWith('.') && entry !== 'node_modules') {
      findSourceFiles(fullPath, files);
    } else if (stat.isFile() && /\.(js|jsx|ts|tsx)$/.test(entry)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to extract imports from a file
function extractImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Regular expressions for different import patterns
    const patterns = [
      // import ... from 'package'
      /import\s+(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+['"`]([^'"`]+)['"`]/g,
      // import('package')
      /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      // require('package')
      /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
      // import 'package'
      /import\s+['"`]([^'"`]+)['"`]/g
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        imports.push(match[1]);
        importPatterns.add(match[1]);
      }
    });
    
    return imports;
  } catch (error) {
    console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
    return [];
  }
}

// Function to normalize package name (handle scoped packages and subpaths)
function normalizePackageName(importPath) {
  // Handle relative imports
  if (importPath.startsWith('.') || importPath.startsWith('/')) {
    return null;
  }
  
  // Handle scoped packages (@scope/package)
  if (importPath.startsWith('@')) {
    const parts = importPath.split('/');
    return parts.length >= 2 ? `${parts[0]}/${parts[1]}` : importPath;
  }
  
  // Handle regular packages (package/subpath -> package)
  return importPath.split('/')[0];
}

console.log('📦 Analyzing package usage in React application...\n');

// Find all source files
const sourceFiles = findSourceFiles('src');
console.log(`Found ${sourceFiles.length} source files to analyze\n`);

// Extract all imports
const allImports = [];
sourceFiles.forEach(file => {
  const imports = extractImports(file);
  allImports.push(...imports);
});

// Process imports to find used packages
allImports.forEach(importPath => {
  const packageName = normalizePackageName(importPath);
  if (packageName && installedPackages.includes(packageName)) {
    usedPackages.add(packageName);
  }
});

// Find unused packages
const unusedPackages = installedPackages.filter(pkg => !usedPackages.has(pkg));

// Display results
console.log('📊 PACKAGE USAGE ANALYSIS');
console.log('=' .repeat(50));

console.log(`\n✅ USED PACKAGES (${usedPackages.size}/${installedPackages.length}):`);
Array.from(usedPackages).sort().forEach(pkg => {
  const version = allDependencies[pkg];
  const type = packageJson.dependencies[pkg] ? 'dependency' : 'devDependency';
  console.log(`  • ${pkg} (${version}) - ${type}`);
});

console.log(`\n❌ POTENTIALLY UNUSED PACKAGES (${unusedPackages.length}):`);
unusedPackages.sort().forEach(pkg => {
  const version = allDependencies[pkg];
  const type = packageJson.dependencies[pkg] ? 'dependency' : 'devDependency';
  console.log(`  • ${pkg} (${version}) - ${type}`);
});

console.log(`\n🔍 UNIQUE IMPORT PATTERNS FOUND (${importPatterns.size}):`);
Array.from(importPatterns).sort().forEach(pattern => {
  console.log(`  • ${pattern}`);
});

// Check for packages that might be used indirectly
console.log('\n⚠️  IMPORTANT NOTES:');
console.log('- Some packages might be used indirectly by other packages');
console.log('- Build tools and testing frameworks might not show direct imports');
console.log('- CSS/style packages might be imported in CSS files (not analyzed)');
console.log('- Runtime dependencies might be loaded dynamically');

// Suggestions for potentially removable packages
console.log('\n💡 PACKAGES THAT MIGHT BE SAFE TO REMOVE:');
const safeToRemovePatterns = [
  'react-scripts', // Build tool - check if using custom build
  '@testing-library', // Testing - safe if not running tests
  'web-vitals', // Performance monitoring - optional
];

unusedPackages.forEach(pkg => {
  const isBuildTool = ['react-scripts', 'postcss', 'autoprefixer', 'tailwindcss'].some(tool => pkg.includes(tool));
  const isTestingTool = pkg.includes('@testing-library') || pkg.includes('jest');
  
  if (!isBuildTool && !isTestingTool) {
    console.log(`  • ${pkg} - appears unused in source code`);
  }
});

console.log('\n🔧 NEXT STEPS:');
console.log('1. Review the "potentially unused" packages list');
console.log('2. Search for any CSS imports or dynamic imports not caught by this analysis');
console.log('3. Check if packages are required by build tools or testing');
console.log('4. Test your application after removing packages to ensure functionality');
console.log('5. Remove packages one by one and test: npm uninstall <package-name>');
