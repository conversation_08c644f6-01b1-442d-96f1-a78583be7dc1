# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
tailwindcss-linux-x64

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
public/popular-games-pubg.jpg
public/popular-games-pubg.jpgZone.Identifier
public/profile-1.jpg
public/profile-1.jpgZone.Identifier
public/profile-2.jpeg
public/profile-2.jpegZone.Identifier
public/profile-3.jpg
public/profile-3.jpgZone.Identifier
public/profile-5.jpg
public/profile-5.jpgZone.Identifier
public/profile-6.jpeg
public/profile-6.jpegZone.Identifier
src/services/mockChatService.js
src/services/mockWebSocket.js
project_analysis.md
translation-implementation-plan.md
project_analysis.md
*.Identifier
install-packages.sh
docs/api-implementation-plan.md
docs/api-integration-phases.md
docs/cleanup-summary.md
docs/finalization-steps.md
docs/mock-api-usage.md
docs/orders.md
docs/performance-accessibility-improvements.md
docs/performance-accessibility-summary.md
docs/phase-8-implementation-plan.md
docs/phase-9-implementation-plan.md
docs/README.md
docs/scheduled-orders.md
docs/translation.md
docs/user-availability.md
README.md
src/components/auth/AUTHENTICATION_SYSTEM_SUMMARY.md
src/components/gift/COMPLETE_IMPLEMENTATION_SUMMARY.md
src/components/gift/PHASE_1_SUMMARY.md
src/components/gift/PHASE_2_SUMMARY.md
src/features/wallet/README.md
src/features/banking/docs/INTEGRATION_GUIDE.md
docs/refactoring/EditProfileModal-Refactoring-Plan.md
docscopy
